# Rithmic API Simple Demos Usage Guide

## Quick Start

### Prerequisites
1. Valid Rithmic account credentials (paper trading or production)
2. Python 3.7+ with required packages: `websockets`, `protobuf`, `python-dotenv`
3. Configuration in `.env.simple-demos` or command line arguments

### Authentication Test
Start by verifying your connection and credentials:
```bash
python3 discover_systems.py
```

This will display available Rithmic systems and confirm your authentication is working.

## Configuration Options

### Environment Variables (.env.simple-demos)
All scripts use `.env.simple-demos` for default configuration. Key variables:
```bash
RITHMIC_SYSTEM=Rithmic Paper Trading
RITHMIC_GATEWAY=Chicago Area
RITHMIC_USER=PP-013155
RITHMIC_PASSWORD=b7neA8k6JA
SYMBOL=ESZ5
EXCHANGE=CME
```

### Command Line Arguments
Every script now supports command line arguments that override environment defaults:
```bash
# Connection settings (available in all scripts)
--system "Rithmic Paper Trading"
--gateway "Chicago Area"  
--user "PP-013155"
--password "password"

# Market data settings (realtime scripts)
--symbol "ESZ5"
--exchange "CME"

# Historical data settings (historical scripts)
--symbol "ESZ5"  # Uses HIST_SYMBOL
--exchange "CME" # Uses HIST_EXCHANGE
--bar-specifier 5        # Time/tick bars
--volume-specifier 500   # Volume bars only
--range-specifier 4      # Range bars only
--start-index 1719532800 # Volume/range bars
--finish-index 1719619199 # Volume/range bars
```

## Symbol Discovery Workflow

### Step 1: Find Available Contracts
Search for all ES (S&P 500 E-mini) contract months:
```bash
# Using environment defaults
python3 search/search_contracts.py

# Custom search patterns
python3 search/search_contracts.py --pattern "ES*" --exchange "CME"
python3 search/search_contracts.py --pattern "NQ*" --exchange "CME"
python3 search/search_contracts.py --pattern "*Z5" --exchange "CME"  # December 2025 contracts
```

### Step 2: Use Results for Market Data
Take symbols from search results and subscribe to market data:
```bash
# Level 1 market data (Best Bid/Offer + Last Trade)
python3 realtime/level1_bbo_trades.py --symbol "ESZ5" --exchange "CME"

# Level 3 market data (Full order book depth)
python3 realtime/level3_depth_by_order.py --symbol "ESZ5" --exchange "CME"

# Multiple symbols example
python3 realtime/level1_bbo_trades.py --symbol "NQZ5" --exchange "CME"
python3 realtime/level1_bbo_trades.py --symbol "RTYZ5" --exchange "CME"
```

## Market Data Subscription Patterns

### Level 1 vs Time & Sales Comparison

**Level 1 (level1_bbo_trades.py)**:
- Provides: Best Bid/Offer (Template 151) + Last Trade (Template 150)
- Use case: Complete market picture with quotes and trades
- Bandwidth: Moderate (both bid/offer updates and trades)

**Time & Sales (time_and_sales.py)**:
- Provides: Last Trade (Template 150) only
- Use case: Trade execution analysis only
- Bandwidth: Lower (trades only)

**Recommendation**: Use Level 1 for complete market data. Time & Sales is only needed for specialized trade-only analysis or bandwidth optimization.

### Market Data Script Options

```bash
# Level 1: Best Bid/Offer + Last Trade
python3 realtime/level1_bbo_trades.py --symbol "ESZ5"

# Level 2: Market by Price (price-aggregated order book)
python3 realtime/level2_market_by_price.py --symbol "ESZ5"

# Level 3: Depth by Order (individual order tracking)
python3 realtime/level3_depth_by_order.py --symbol "ESZ5"

# Time & Sales: Trade execution data only
python3 realtime/time_and_sales.py --symbol "ESZ5"
```

## Historical Data Analysis Workflow

### Recommended Analysis Sequence
1. **Time Bars**: Start with time-based analysis
2. **Tick Bars**: Analyze trade frequency patterns
3. **Volume Bars**: Study volume-based price movements
4. **Range Bars**: Examine price volatility patterns

### Step 1: Time Bar Analysis
```bash
# 1-minute bars (default)
python3 historical/historical_time_bars.py --symbol "ESZ5"

# 5-minute bars
python3 historical/historical_time_bars.py --symbol "ESZ5" --bar-specifier 5

# Daily bars
python3 historical/historical_time_bars.py --symbol "ESZ5" --bar-specifier 1440
```

### Step 2: Tick Bar Analysis
```bash
# Every trade (1-tick bars)
python3 historical/historical_tick_bars.py --symbol "ESZ5"

# Every 100 trades
python3 historical/historical_tick_bars.py --symbol "ESZ5" --bar-specifier 100

# High-frequency analysis (every 500 trades)
python3 historical/historical_tick_bars.py --symbol "ESZ5" --bar-specifier 500
```

### Step 3: Volume Bar Analysis
```bash
# Default 500 contracts per bar
python3 historical/historical_volume_bars.py --symbol "ESZ5"

# Higher volume threshold (1000 contracts)
python3 historical/historical_volume_bars.py --symbol "ESZ5" --volume-specifier 1000

# Custom date range
python3 historical/historical_volume_bars.py --symbol "ESZ5" --volume-specifier 500 \
    --start-index 1719532800 --finish-index 1719619199
```

### Step 4: Range Bar Analysis
```bash
# Default 4-tick range (1 point for ES)
python3 historical/historical_range_bars.py --symbol "ESZ5"

# Wider range (8 ticks = 2 points)
python3 historical/historical_range_bars.py --symbol "ESZ5" --range-specifier 8

# Custom date range with tight range
python3 historical/historical_range_bars.py --symbol "ESZ5" --range-specifier 2 \
    --start-index 1719532800 --finish-index 1719619199
```

## Common Workflow Patterns

### Market Analysis Workflow
```bash
# 1. Discover available contracts
python3 search/search_contracts.py --pattern "ES*"

# 2. Monitor real-time Level 1 data
python3 realtime/level1_bbo_trades.py --symbol "ESZ5" &

# 3. Analyze historical patterns (parallel)
python3 historical/historical_time_bars.py --symbol "ESZ5" --bar-specifier 5 &
python3 historical/historical_volume_bars.py --symbol "ESZ5" --volume-specifier 500 &
python3 historical/historical_range_bars.py --symbol "ESZ5" --range-specifier 4 &
```

### Multi-Symbol Monitoring
```bash
# Monitor multiple E-mini futures simultaneously
python3 realtime/level1_bbo_trades.py --symbol "ESZ5" &  # S&P 500
python3 realtime/level1_bbo_trades.py --symbol "NQZ5" &  # NASDAQ 100
python3 realtime/level1_bbo_trades.py --symbol "RTYZ5" & # Russell 2000
```

### Historical Comparison Analysis
```bash
# Compare different time periods for same symbol
python3 historical/historical_time_bars.py --symbol "ESZ5" \
    --start-index 1715731200 --finish-index 1715817599 > may_data.txt

python3 historical/historical_time_bars.py --symbol "ESZ5" \
    --start-index 1719532800 --finish-index 1719619199 > june_data.txt
```

## Parameter Optimization Guidelines

### Bar Type Specifier Optimization

**Time Bars**:
- Range: 1-10080 minutes
- Common: 1, 5, 15, 60, 1440 (1min, 5min, 15min, 1hour, daily)
- Purpose: Time-based OHLCV aggregation

**Tick Bars**:
- Range: 1-1000 trades
- Common: 1, 25, 100, 500 (every trade, 25 trades, 100 trades, high activity)
- Purpose: Trade-count based aggregation

**Volume Bars**:
- Range: 100-25000 contracts
- Common: 500, 1000, 2500 (moderate, active, very active)
- Purpose: Volume-based aggregation
- Note: ES futures typically trade 500-2000 contracts per significant move

**Range Bars**:
- Range: 1-32 ticks
- Common: 4, 8, 16 (1 point, 2 points, 4 points for ES)
- Purpose: Price-movement based aggregation
- Note: ES futures move in 0.25 point increments (1 tick = 0.25 points)

### Date Range Selection
```bash
# Recent active trading day (recommended)
--start-index 1719532800 --finish-index 1719619199  # June 28, 2024

# Alternative date ranges (fallbacks)
--start-index 1715731200 --finish-index 1715817599  # May 15, 2024
--start-index 1712707200 --finish-index 1712793599  # April 10, 2024
```

## Troubleshooting Common Issues

### "No Data" Responses
1. **Check date range**: Avoid weekends and holidays
2. **Increase bar specifiers**: Use larger volume/range thresholds
3. **Try CME E-mini futures**: ES, NQ, RTY are most reliable
4. **Test basic connectivity**: Start with tick bars using same date range

### Authentication Issues
```bash
# Test system discovery first
python3 discover_systems.py

# Verify credentials in .env.simple-demos
# Check RITHMIC_SYSTEM, RITHMIC_USER, RITHMIC_PASSWORD
```

### Permission Denied Errors
- Paper trading accounts typically only have access to CME E-mini futures
- Other exchanges (NYMEX, CBOT, COMEX) may require additional permissions
- Equity symbols (AAPL, MSFT) are not supported in paper trading

### Performance Optimization
```bash
# For high-frequency data collection, use larger thresholds
python3 historical/historical_volume_bars.py --volume-specifier 1000
python3 historical/historical_range_bars.py --range-specifier 8

# For detailed analysis, use smaller thresholds
python3 historical/historical_volume_bars.py --volume-specifier 250
python3 historical/historical_range_bars.py --range-specifier 2
```

## Help and Documentation

### Get Help for Any Script
```bash
python3 discover_systems.py --help
python3 search/search_contracts.py --help
python3 realtime/level1_bbo_trades.py --help
python3 historical/historical_volume_bars.py --help
```

### Environment Variable Reference
See `.env.simple-demos` for complete list of available configuration options and their descriptions.

### Script Categories
- **Connection**: `discover_systems.py`
- **Search**: `search/search_contracts.py`
- **Real-time**: `realtime/level1_bbo_trades.py`, `level2_market_by_price.py`, `level3_depth_by_order.py`, `time_and_sales.py`
- **Historical**: `historical/historical_time_bars.py`, `historical_tick_bars.py`, `historical_volume_bars.py`, `historical_range_bars.py`

This guide provides comprehensive coverage of all simple-demos functionality with practical examples for efficient market data analysis and monitoring.