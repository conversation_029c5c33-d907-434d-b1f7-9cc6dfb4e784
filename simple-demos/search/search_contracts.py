#!/usr/bin/env python3
"""
Search for contracts using configurable pattern.
Demonstrates RequestSearchSymbols with all available fields.
"""

import argparse
import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
sys.path.append('..')
from shared import connect_and_authenticate, disconnect

# Add proto_generated directory for protobuf imports
sys.path.append('../../proto_generated')

import request_search_symbols_pb2
import response_search_symbols_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv('../.env.simple-demos')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Search for contracts using configurable pattern',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection settings
    parser.add_argument('--system',
                       default=os.getenv('RITHMIC_SYSTEM'),
                       help='Rithmic system to connect to')
    parser.add_argument('--gateway',
                       default=os.getenv('RITHMIC_GATEWAY'),
                       help='Gateway location for connection')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'),
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    
    # Search parameters
    parser.add_argument('--pattern', 
                       default=os.getenv('SEARCH_PATTERN', ''),
                       help='Pattern to search for symbols (supports wildcards)')
    parser.add_argument('--exchange',
                       default=os.getenv('SEARCH_EXCHANGE', ''),
                       help='Exchange to search (leave empty for all exchanges)')
    parser.add_argument('--product-code',
                       default=os.getenv('SEARCH_PRODUCT_CODE', ''),
                       help='Product code to search (leave empty for all products)')
    parser.add_argument('--instrument-type',
                       type=int,
                       default=int(os.getenv('SEARCH_INSTRUMENT_TYPE', '1')),
                       help='Instrument type (1=FUTURE, 2=FUTURE_OPTION, 4=EQUITY, etc.)')
    parser.add_argument('--pattern-type',
                       type=int,
                       default=int(os.getenv('SEARCH_PATTERN_TYPE', '2')),
                       help='Pattern matching type (1=EQUALS, 2=CONTAINS)')
    
    return parser.parse_args()

async def search_symbols():
    """Search for symbols using configurable pattern."""
    args = parse_args()
    
    print(f"Searching for symbols with pattern: '{args.pattern}'")
    print(f"Exchange: {args.exchange or 'All'}")
    print(f"Product code: {args.product_code or 'All'}")
    print(f"Instrument type: {args.instrument_type}")
    print(f"Pattern type: {args.pattern_type} ({'EQUALS' if args.pattern_type == 1 else 'CONTAINS'})")
    
    # Connect and authenticate with ticker plant for symbol search
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create search request with all available fields
    search_req = request_search_symbols_pb2.RequestSearchSymbols()
    search_req.template_id = 109
    search_req.user_msg.append("symbol_search_request")
    
    # Set all available fields using command line arguments with env fallbacks
    search_req.search_text = args.pattern
    search_req.exchange = args.exchange
    search_req.product_code = args.product_code
    search_req.instrument_type = args.instrument_type
    search_req.pattern = args.pattern_type
    
    print(f"Sending search request: {search_req}")
    await ws.send(search_req.SerializeToString())
    
    # Wait for response
    response_data = await ws.recv()
    
    # Parse response
    base = base_pb2.Base()
    base.ParseFromString(response_data)
    
    if base.template_id == 110:  # ResponseSearchSymbols
        search_response = response_search_symbols_pb2.ResponseSearchSymbols()
        search_response.ParseFromString(response_data)
        print("\nSearch response:")
        print(search_response)
    else:
        print(f"Unexpected response template_id: {base.template_id}")
        print(f"Raw response: {response_data}")
    
    await disconnect(ws)

if __name__ == "__main__":
    asyncio.run(search_symbols())