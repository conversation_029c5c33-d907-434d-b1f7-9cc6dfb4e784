#!/usr/bin/env python3
"""
Search for contracts using configurable pattern.
Demonstrates RequestSearchSymbols with all available fields.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
from shared import (connect_and_authenticate, disconnect, TemplateIDs, MarketDataParsers, 
                    safe_get_field, safe_get_list_field, safe_get_repeated_string_field, format_symbol_info, ProtobufValidation,
                    ProtobufParsingError, RithmicAPIError, UnexpectedTemplateError, FieldValidationError)
from argument_parser import create_search_parser
from shared_database import get_simple_database

# Add proto_generated directory for protobuf imports
proto_dir = os.path.join(os.path.dirname(parent_dir), 'proto_generated')
sys.path.append(proto_dir)

import request_search_symbols_pb2
import response_search_symbols_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv(os.path.join(parent_dir, '.env.simple-demos'))

def parse_args():
    """Parse command line arguments using centralized argument parser."""
    parser = create_search_parser('Search for contracts using configurable pattern')
    return parser.parse_args()

async def search_symbols():
    """Search for symbols using configurable pattern."""
    args = parse_args()
    
    # Initialize database manager for search result caching
    db = get_simple_database()
    if db.is_enabled():
        print("📊 Database caching enabled for search results")
        db.log_system_event('STARTUP', f'Symbol search started: {args.pattern}')
    else:
        print("📝 Console output mode - database caching disabled")
    
    print(f"Searching for symbols with pattern: '{args.pattern}'")
    print(f"Exchange: {args.exchange or 'All'}")
    print(f"Product code: {args.product_code or 'All'}")
    print(f"Instrument type: {args.instrument_type}")
    print(f"Pattern type: {args.pattern_type} ({'EQUALS' if args.pattern_type == 1 else 'CONTAINS'})")
    
    # Connect and authenticate with ticker plant for symbol search
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create search request with all available fields
    search_req = request_search_symbols_pb2.RequestSearchSymbols()
    search_req.template_id = TemplateIDs.SEARCH_SYMBOLS_REQUEST
    search_req.user_msg.append("symbol_search_request")
    
    # Set all available fields using command line arguments with env fallbacks
    search_req.search_text = args.pattern
    search_req.exchange = args.exchange
    search_req.product_code = args.product_code
    search_req.instrument_type = ProtobufValidation.validate_instrument_type(args.instrument_type)
    search_req.pattern = ProtobufValidation.validate_pattern_type(args.pattern_type)
    
    print(f"Sending search request: {search_req}")
    await ws.send(search_req.SerializeToString())
    
    # Wait for response
    response_data = await ws.recv()
    
    # Parse response with comprehensive error handling
    try:
        search_response = MarketDataParsers.parse_search_symbols_response(response_data, "Symbol Search")
        
        print("\nSearch response:")
        print(f"Response code: {safe_get_list_field(search_response, 'rp_code', 0, 'N/A')}")
        print(f"Template ID: {safe_get_field(search_response, 'template_id', 'N/A')}")
        
        # Extract all available fields using safe access - use repeated string field accessor for string arrays
        symbols = safe_get_repeated_string_field(search_response, 'symbol', [])
        exchanges = safe_get_repeated_string_field(search_response, 'exchange', [])
        symbol_names = safe_get_repeated_string_field(search_response, 'symbol_name', [])
        product_codes = safe_get_repeated_string_field(search_response, 'product_code', [])
        instrument_types = safe_get_field(search_response, 'instrument_type', [])
        expiration_dates = safe_get_field(search_response, 'expiration_date', [])
        
        # Check if we have meaningful data (symbols with non-empty values)
        has_meaningful_data = symbols and any(symbol.strip() for symbol in symbols)
        
        if has_meaningful_data:
            print(f"\nFound {len(symbols)} symbols:")
            for i, symbol in enumerate(symbols):
                print(f"  {i+1}. Symbol: {symbol}")
                if i < len(exchanges):
                    print(f"     Exchange: {exchanges[i]}")
                if i < len(symbol_names):
                    print(f"     Name: {symbol_names[i]}")
                if i < len(product_codes):
                    print(f"     Product Code: {product_codes[i]}")
                if i < len(instrument_types):
                    print(f"     Instrument Type: {instrument_types[i]}")
                if i < len(expiration_dates):
                    print(f"     Expiration: {expiration_dates[i]}")
                print()
            
            # Cache search results to database if enabled
            if db.is_enabled():
                try:
                    # Filter out empty symbols for cleaner database storage
                    valid_symbols = [symbol.strip() for symbol in symbols if symbol.strip()]
                    
                    # Cache search results
                    db.insert_search_result(
                        search_pattern=args.pattern,
                        exchange=args.exchange or 'ALL',
                        symbols=valid_symbols
                    )
                    
                    print(f"📊 Cached {len(valid_symbols)} search results to database")
                except Exception as e:
                    print(f"⚠️ Database error while caching search results: {e}")
        else:
            print(f"\n⚠️  No matching symbols found")
            if symbols:
                print(f"API returned {len(symbols)} record(s) but with empty field values.")
            else:
                print("API returned no records for this search.")
            
            print(f"\n💡 Search suggestions:")
            print(f"   • Try broader search: use --pattern-type=2 (CONTAINS) instead of 1 (EQUALS)")
            print(f"   • Try broader scope: use --product-code=\"\" --exchange=\"\" for all products/exchanges")
            print(f"   • For symbol '{args.pattern}', try: --product-code=\"{args.pattern}\"")
            print(f"   • Verify symbol exists and is available in your account permissions")
            print(f"\n📝 Current search parameters:")
            print(f"   • Pattern: '{args.pattern}' (Type: {args.pattern_type})")
            print(f"   • Product Code: '{args.product_code}' (may be restricting results)")
            print(f"   • Exchange: '{args.exchange}'")
            print(f"   • Instrument Type: {args.instrument_type}")
            
            # Log empty search results to database if enabled
            if db.is_enabled():
                try:
                    db.log_system_event(
                        'SEARCH_NO_RESULTS',
                        f'No symbols found for pattern: {args.pattern}',
                        additional_data={
                            'search_pattern': args.pattern,
                            'exchange': args.exchange,
                            'product_code': args.product_code,
                            'pattern_type': args.pattern_type,
                            'instrument_type': args.instrument_type
                        }
                    )
                except Exception as e:
                    print(f"⚠️ Database error while logging empty search: {e}")
            
    except RithmicAPIError as e:
        print(f"\nRithmic API Error: {e}")
        print(f"Error Code: {e.error_code}, Error Text: {e.error_text}")
    except ProtobufParsingError as e:
        print(f"\nProtobuf Parsing Error: {e}")
        print(f"Template ID: {e.template_id}")
        print(f"Raw response: {response_data}")
    except UnexpectedTemplateError as e:
        print(f"\nUnexpected Template Error: {e}")
        print(f"Expected: {e.expected_id}, Received: {e.received_id}")
        print(f"Raw response: {response_data}")
    except FieldValidationError as e:
        print(f"\nField Validation Error: {e}")
        print(f"Missing fields: {e.missing_fields}")
    except Exception as e:
        print(f"\nUnexpected parsing error: {e}")
        print(f"Raw response: {response_data}")
    
    # Log shutdown event
    if db.is_enabled():
        try:
            db.log_system_event('SHUTDOWN', f'Symbol search completed for pattern: {args.pattern}')
        except Exception as e:
            print(f"⚠️ Database error during shutdown logging: {e}")
    
    await disconnect(ws)

if __name__ == "__main__":
    asyncio.run(search_symbols())