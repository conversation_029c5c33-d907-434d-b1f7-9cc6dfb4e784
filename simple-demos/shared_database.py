#!/usr/bin/env python3
"""
Shared Database Module for Simple Demos

Provides simplified database persistence functionality for simple-demos scripts.
Leverages the existing sophisticated database infrastructure from src/database/ 
while providing an easy-to-use interface for educational demonstrations.

Features:
- Simple database connection management
- Easy data insertion methods for market data
- Database logging for system events
- Environment-based configuration
- Automatic table creation and management
"""

import os
import sys
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from decimal import Decimal
import json

# Add project root to path for database imports
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)

try:
    from src.database.database_manager import get_database_manager, DatabaseManager
    from src.database.models import create_all_tables
except ImportError as e:
    print(f"Warning: Could not import database modules: {e}")
    print("Database functionality will not be available.")
    get_database_manager = None
    DatabaseManager = None
    create_all_tables = None

logger = logging.getLogger(__name__)


class SimpleDatabaseManager:
    """
    Simplified database manager for simple-demos scripts.
    
    Provides easy-to-use methods for persisting market data to the database
    while leveraging the sophisticated infrastructure from src/database/.
    """
    
    def __init__(self, enable_database: bool = True):
        """
        Initialize the simplified database manager.
        
        Args:
            enable_database: Whether to enable database functionality
        """
        self.enabled = enable_database and get_database_manager is not None
        self.db_manager = None
        
        if self.enabled:
            try:
                self.db_manager = get_database_manager()
                self._ensure_tables_exist()
                logger.info("✅ Database connection established for simple-demos")
            except Exception as e:
                logger.warning(f"⚠️ Database initialization failed: {e}")
                self.enabled = False
        else:
            logger.info("📝 Database functionality disabled - using console output only")
    
    def _ensure_tables_exist(self):
        """Ensure all required database tables exist."""
        if self.enabled and create_all_tables:
            try:
                create_all_tables()
                logger.debug("✅ Database tables verified/created")
            except Exception as e:
                logger.error(f"❌ Failed to create database tables: {e}")
                raise
    
    def is_enabled(self) -> bool:
        """Check if database functionality is enabled."""
        return self.enabled
    
    def log_system_event(self, event_type: str, message: str, symbol: str = None, 
                        exchange: str = None, additional_data: Dict[str, Any] = None):
        """
        Log a system event to the database.
        
        Args:
            event_type: Type of event (e.g., 'SUBSCRIPTION', 'ERROR', 'INFO')
            message: Event message
            symbol: Related symbol (optional)
            exchange: Related exchange (optional)
            additional_data: Additional event data (optional)
        """
        if not self.enabled:
            # Fall back to console logging
            print(f"[{datetime.now()}] {event_type}: {message}")
            return
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                insert_query = """
                    INSERT INTO message_log (
                        template_id, message_type, direction, symbol, exchange,
                        session_id, message_timestamp, parsed_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_query, (
                    0,  # template_id (0 for system events)
                    'NOTIFICATION',  # message_type
                    'SENT',  # direction (system-generated)
                    symbol,
                    exchange,
                    f"simple-demos-{os.getpid()}",  # session_id
                    datetime.now(timezone.utc),
                    json.dumps({
                        'event_type': event_type,
                        'message': message,
                        'additional_data': additional_data or {}
                    })
                ))
                
                conn.commit()
                logger.debug(f"📝 Logged system event: {event_type}")
                
        except Exception as e:
            logger.error(f"❌ Failed to log system event: {e}")
            # Fall back to console logging
            print(f"[{datetime.now()}] {event_type}: {message}")
    
    def insert_best_bid_offer(self, symbol: str, exchange: str, 
                             bid_price: Optional[float] = None, bid_size: Optional[int] = None,
                             ask_price: Optional[float] = None, ask_size: Optional[int] = None,
                             market_timestamp: Optional[datetime] = None, 
                             additional_fields: Dict[str, Any] = None):
        """
        Insert best bid/offer data into the database.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            bid_price: Bid price
            bid_size: Bid size
            ask_price: Ask price
            ask_size: Ask size
            market_timestamp: Market timestamp (defaults to current time)
            additional_fields: Additional BBO fields
        """
        if not self.enabled:
            print(f"BBO: {symbol} - Bid: {bid_price}@{bid_size}, Ask: {ask_price}@{ask_size}")
            return
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Prepare fields with defaults
                fields = additional_fields or {}
                timestamp = market_timestamp or datetime.now(timezone.utc)
                
                insert_query = """
                    INSERT INTO best_bid_offer (
                        symbol, exchange, bid_price, bid_size, ask_price, ask_size,
                        bid_orders, ask_orders, bid_implicit_size, ask_implicit_size,
                        lean_price, presence_bits, clear_bits, is_snapshot,
                        ssboe, usecs, market_timestamp, received_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_query, (
                    symbol, exchange, 
                    Decimal(str(bid_price)) if bid_price is not None else None,
                    bid_size,
                    Decimal(str(ask_price)) if ask_price is not None else None,
                    ask_size,
                    fields.get('bid_orders'),
                    fields.get('ask_orders'),
                    fields.get('bid_implicit_size'),
                    fields.get('ask_implicit_size'),
                    Decimal(str(fields.get('lean_price'))) if fields.get('lean_price') else None,
                    fields.get('presence_bits'),
                    fields.get('clear_bits'),
                    fields.get('is_snapshot', False),
                    fields.get('ssboe'),
                    fields.get('usecs'),
                    timestamp,
                    datetime.now(timezone.utc)
                ))
                
                conn.commit()
                logger.debug(f"📊 Inserted BBO data for {symbol}")
                
        except Exception as e:
            logger.error(f"❌ Failed to insert BBO data: {e}")
    
    def insert_last_trade(self, symbol: str, exchange: str,
                         trade_price: Optional[float] = None, trade_size: Optional[int] = None,
                         volume: Optional[int] = None, aggressor: str = None,
                         trade_timestamp: Optional[datetime] = None,
                         additional_fields: Dict[str, Any] = None):
        """
        Insert last trade data into the database.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            trade_price: Trade price
            trade_size: Trade size
            volume: Cumulative volume
            aggressor: Trade aggressor ('BUY' or 'SELL')
            trade_timestamp: Trade timestamp (defaults to current time)
            additional_fields: Additional trade fields
        """
        if not self.enabled:
            print(f"Trade: {symbol} - Price: {trade_price}, Size: {trade_size}, Volume: {volume}")
            return
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Prepare fields with defaults
                fields = additional_fields or {}
                timestamp = trade_timestamp or datetime.now(timezone.utc)
                
                insert_query = """
                    INSERT INTO last_trades (
                        symbol, exchange, trade_price, trade_size, aggressor,
                        exchange_order_id, aggressor_exchange_order_id, net_change, 
                        percent_change, volume, vwap, presence_bits, clear_bits,
                        is_snapshot, ssboe, usecs, source_ssboe, source_usecs, 
                        source_nsecs, jop_ssboe, jop_nsecs, trade_timestamp, received_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_query, (
                    symbol, exchange,
                    Decimal(str(trade_price)) if trade_price is not None else None,
                    trade_size,
                    aggressor,
                    fields.get('exchange_order_id'),
                    fields.get('aggressor_exchange_order_id'),
                    Decimal(str(fields.get('net_change'))) if fields.get('net_change') else None,
                    Decimal(str(fields.get('percent_change'))) if fields.get('percent_change') else None,
                    volume,
                    Decimal(str(fields.get('vwap'))) if fields.get('vwap') else None,
                    fields.get('presence_bits'),
                    fields.get('clear_bits'),
                    fields.get('is_snapshot', False),
                    fields.get('ssboe'),
                    fields.get('usecs'),
                    fields.get('source_ssboe'),
                    fields.get('source_usecs'),
                    fields.get('source_nsecs'),
                    fields.get('jop_ssboe'),
                    fields.get('jop_nsecs'),
                    timestamp,
                    datetime.now(timezone.utc)
                ))
                
                conn.commit()
                logger.debug(f"📈 Inserted trade data for {symbol}")
                
        except Exception as e:
            logger.error(f"❌ Failed to insert trade data: {e}")
    
    def insert_depth_update(self, symbol: str, exchange: str,
                           depth_side: str = None, depth_price: Optional[float] = None,
                           depth_size: Optional[int] = None, exchange_order_id: str = None,
                           depth_order_priority: Optional[int] = None,
                           sequence_number: Optional[int] = None,
                           is_snapshot: bool = False, template_id: int = 116,
                           market_timestamp: Optional[datetime] = None,
                           additional_fields: Dict[str, Any] = None):
        """
        Insert depth by order update data into the database.
        
        This function matches the actual depth_by_order_snapshot table schema.
        Each call inserts a single depth update/level.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            depth_side: Side of the book ('BUY' or 'SELL')
            depth_price: Price level
            depth_size: Size at this price level
            exchange_order_id: Unique order identifier from exchange
            depth_order_priority: Order priority within price level
            sequence_number: Sequence number for this update
            is_snapshot: Whether this is snapshot data (True) or update (False)
            template_id: Template ID (default 116 for depth snapshot)
            market_timestamp: Market timestamp (defaults to current time)
            additional_fields: Additional fields containing ssboe, usecs, etc.
        """
        if not self.enabled:
            side_str = f" {depth_side}" if depth_side else ""
            print(f"Depth Update: {symbol}{side_str} - Price: {depth_price}, Size: {depth_size}")
            return
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Prepare fields with defaults
                fields = additional_fields or {}
                timestamp = market_timestamp or datetime.now(timezone.utc)
                
                # Insert depth update using the correct schema
                insert_query = """
                    INSERT INTO depth_by_order_snapshot (
                        symbol, exchange, sequence_number, depth_side, depth_price, depth_size,
                        depth_order_priority, exchange_order_id, template_id, is_snapshot,
                        ssboe, usecs, market_timestamp
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_query, (
                    symbol, exchange, sequence_number, depth_side,
                    Decimal(str(depth_price)) if depth_price is not None else None,
                    depth_size, depth_order_priority, exchange_order_id,
                    template_id, is_snapshot,
                    fields.get('ssboe'), fields.get('usecs'),
                    timestamp
                ))
                
                conn.commit()
                logger.debug(f"📋 Inserted depth update for {symbol}: {depth_side} {depth_price}@{depth_size}")
                
        except Exception as e:
            logger.error(f"❌ Failed to insert depth update: {e}")
    
    def insert_depth_snapshot(self, symbol: str, exchange: str, levels: List[Dict[str, Any]],
                             sequence_number: Optional[int] = None,
                             market_timestamp: Optional[datetime] = None,
                             additional_fields: Dict[str, Any] = None):
        """
        Insert multiple depth levels as snapshot data (backward compatibility).
        
        This function maintains compatibility with existing calling code while
        using the correct database schema.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            levels: List of order book levels
            sequence_number: Snapshot sequence number
            market_timestamp: Market timestamp (defaults to current time)
            additional_fields: Additional snapshot fields
        """
        if not self.enabled:
            print(f"Depth Snapshot: {symbol} - {len(levels)} levels")
            return
        
        # If no levels provided, just insert a snapshot marker
        if not levels:
            self.insert_depth_update(
                symbol=symbol, exchange=exchange,
                sequence_number=sequence_number,
                is_snapshot=True, template_id=116,
                market_timestamp=market_timestamp,
                additional_fields=additional_fields
            )
            return
        
        # Insert each level as an individual depth update
        for level in levels:
            self.insert_depth_update(
                symbol=symbol, exchange=exchange,
                depth_side=level.get('transaction_type'),  # Only use transaction_type for depth_side
                depth_price=level.get('depth_price'),
                depth_size=level.get('depth_size'),
                exchange_order_id=level.get('exchange_order_id'),
                depth_order_priority=level.get('depth_order_priority'),
                sequence_number=sequence_number,
                is_snapshot=True, template_id=116,
                market_timestamp=market_timestamp,
                additional_fields=additional_fields
            )
    
    def insert_time_bar(self, symbol: str, exchange: str, bar_timestamp: datetime,
                       open_price: Optional[float] = None, high_price: Optional[float] = None,
                       low_price: Optional[float] = None, close_price: Optional[float] = None,
                       volume: Optional[int] = None, num_trades: Optional[int] = None,
                       bar_type: str = 'MINUTE', bar_interval: int = 1,
                       additional_fields: Dict[str, Any] = None):
        """
        Insert time bar data into the database.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            bar_timestamp: Bar timestamp
            open_price: Open price
            high_price: High price
            low_price: Low price
            close_price: Close price
            volume: Volume
            num_trades: Number of trades
            bar_type: Bar type ('MINUTE', 'HOUR', 'DAILY', etc.)
            bar_interval: Bar interval (e.g., 1, 5, 15)
            additional_fields: Additional bar fields
        """
        if not self.enabled:
            print(f"Time Bar: {symbol} - OHLC: {open_price}/{high_price}/{low_price}/{close_price}, Vol: {volume}")
            return
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Prepare fields with defaults
                fields = additional_fields or {}
                
                insert_query = """
                    INSERT INTO time_bars (
                        symbol, exchange, bar_type, period, bar_timestamp,
                        open_price, high_price, low_price, close_price,
                        volume, num_trades, received_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        open_price = VALUES(open_price),
                        high_price = VALUES(high_price),
                        low_price = VALUES(low_price),
                        close_price = VALUES(close_price),
                        volume = VALUES(volume),
                        num_trades = VALUES(num_trades),
                        received_at = VALUES(received_at)
                """
                
                cursor.execute(insert_query, (
                    symbol, exchange, bar_type, str(bar_interval), bar_timestamp,
                    Decimal(str(open_price)) if open_price is not None else None,
                    Decimal(str(high_price)) if high_price is not None else None,
                    Decimal(str(low_price)) if low_price is not None else None,
                    Decimal(str(close_price)) if close_price is not None else None,
                    volume, num_trades,
                    datetime.now(timezone.utc)
                ))
                
                conn.commit()
                logger.debug(f"📊 Inserted time bar for {symbol}")
                
        except Exception as e:
            logger.error(f"❌ Failed to insert time bar: {e}")
    
    def insert_search_result(self, search_pattern: str, exchange: str, symbols: List[str],
                            search_timestamp: Optional[datetime] = None):
        """
        Cache search results in the database for future reference.
        
        Args:
            search_pattern: Search pattern used
            exchange: Exchange searched
            symbols: List of symbols found
            search_timestamp: Search timestamp (defaults to current time)
        """
        if not self.enabled:
            print(f"Search: {search_pattern} on {exchange} -> {len(symbols)} symbols")
            return
        
        try:
            self.log_system_event(
                'SEARCH',
                f"Symbol search completed: {search_pattern}",
                additional_data={
                    'search_pattern': search_pattern,
                    'exchange': exchange,
                    'symbols_found': symbols,
                    'result_count': len(symbols)
                }
            )
            logger.debug(f"🔍 Cached search results for {search_pattern}")
            
        except Exception as e:
            logger.error(f"❌ Failed to cache search results: {e}")


# Global instance for easy access across simple-demos scripts
_db_manager = None

def get_simple_database(enable_database: bool = None) -> SimpleDatabaseManager:
    """
    Get the global SimpleDatabaseManager instance.
    
    Args:
        enable_database: Whether to enable database functionality (None = auto-detect from env)
    
    Returns:
        SimpleDatabaseManager instance
    """
    global _db_manager
    
    if _db_manager is None:
        # Auto-detect database enablement if not specified
        if enable_database is None:
            enable_database = os.getenv('ENABLE_DATABASE', 'true').lower() == 'true'
        
        _db_manager = SimpleDatabaseManager(enable_database)
    
    return _db_manager

def reset_simple_database():
    """Reset the global database manager (useful for testing)."""
    global _db_manager
    _db_manager = None