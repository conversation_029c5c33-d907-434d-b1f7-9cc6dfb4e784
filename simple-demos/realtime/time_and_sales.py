#!/usr/bin/env python3
"""
Subscribe to Time & Sales data (trade execution data only).
Demonstrates RequestMarketDataUpdate with LAST_TRADE only.
"""

import argparse
import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
sys.path.append('..')
from shared import connect_and_authenticate, send_heartbeat, decode_unknown_message

# Add proto_generated directory for protobuf imports
sys.path.append('../../proto_generated')

import request_market_data_update_pb2
import response_market_data_update_pb2
import last_trade_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv('../.env.simple-demos')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Subscribe to Time & Sales data (trade execution data only)',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection settings
    parser.add_argument('--system',
                       default=os.getenv('RITHMIC_SYSTEM'),
                       help='Rithmic system to connect to')
    parser.add_argument('--gateway',
                       default=os.getenv('RITHMIC_GATEWAY'),
                       help='Gateway location for connection')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'),
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    
    # Market data settings
    parser.add_argument('--symbol',
                       default=os.getenv('SYMBOL'),
                       help='Symbol to subscribe to for market data')
    parser.add_argument('--exchange',
                       default=os.getenv('EXCHANGE'),
                       help='Exchange for the symbol')
    
    return parser.parse_args()

async def subscribe_time_and_sales():
    """Subscribe to Time & Sales (trade execution data only)."""
    args = parse_args()
    
    print(f"Subscribing to Time & Sales data for {args.symbol} on {args.exchange}")
    
    # Connect and authenticate with ticker plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create market data subscription request for trades only
    md_req = request_market_data_update_pb2.RequestMarketDataUpdate()
    md_req.template_id = 100
    md_req.user_msg.append("time_and_sales_subscription")
    
    # Set all available fields using command line arguments
    md_req.symbol = args.symbol
    md_req.exchange = args.exchange
    md_req.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.SUBSCRIBE
    md_req.update_bits = request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.LAST_TRADE
    
    print(f"Sending Time & Sales subscription request: {md_req}")
    await ws.send(md_req.SerializeToString())
    
    # Listen for trade updates
    print("Listening for Time & Sales trade data... (Ctrl+C to exit)")
    try:
        while True:
            try:
                msg_data = await asyncio.wait_for(ws.recv(), timeout=5)
                
                # Parse message type
                base = base_pb2.Base()
                base.ParseFromString(msg_data)
                
                if base.template_id == 101:  # ResponseMarketDataUpdate
                    response = response_market_data_update_pb2.ResponseMarketDataUpdate()
                    response.ParseFromString(msg_data)
                    print(f"Market Data Update Response: {response}")
                    
                elif base.template_id == 150:  # LastTrade
                    trade = last_trade_pb2.LastTrade()
                    trade.ParseFromString(msg_data)
                    print(f"Trade: {trade}")
                    
                else:
                    decode_unknown_message(base.template_id, msg_data)
                    
            except asyncio.TimeoutError:
                await send_heartbeat(ws)
                
    except KeyboardInterrupt:
        print("\nExiting...")
        await ws.close()

if __name__ == "__main__":
    asyncio.run(subscribe_time_and_sales())