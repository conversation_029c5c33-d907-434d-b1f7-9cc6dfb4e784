#!/usr/bin/env python3
"""
Subscribe to Level 1 market data: Best Bid/Offer and Last Trade.
Demonstrates RequestMarketDataUpdate with all available fields.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
from shared import (connect_and_authenticate, send_heartbeat, decode_unknown_message, search_contracts, TemplateIDs,
                    MarketDataParsers, safe_get_field, format_symbol_info,
                    ProtobufParsingError, RithmicAPIError, UnexpectedTemplateError, FieldValidationError)
from argument_parser import create_market_data_parser
from shared_database import get_simple_database

# Add proto_generated directory for protobuf imports
proto_dir = os.path.join(os.path.dirname(parent_dir), 'proto_generated')
sys.path.append(proto_dir)

import request_market_data_update_pb2
import response_market_data_update_pb2
import best_bid_offer_pb2
import last_trade_pb2
import request_login_pb2
import base_pb2
import request_search_symbols_pb2
import response_search_symbols_pb2

# Load configuration
load_dotenv(os.path.join(parent_dir, '.env.simple-demos'))

def parse_args():
    """Parse command line arguments using centralized argument parser."""
    parser = create_market_data_parser('Subscribe to Level 1 market data: Best Bid/Offer and Last Trade')
    return parser.parse_args()


async def subscribe_to_symbol(ws, symbol, exchange):
    """
    Subscribe to Level 1 market data for a single symbol.
    
    Requests both Last Trade (Template 150) and Best Bid/Offer (Template 151) updates
    for the specified symbol. This provides a complete Level 1 market picture.
    
    Args:
        ws: WebSocket connection to Rithmic
        symbol: Trading symbol (e.g., 'ESZ5') 
        exchange: Exchange name (e.g., 'CME')
    """
    md_req = request_market_data_update_pb2.RequestMarketDataUpdate()
    md_req.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
    md_req.user_msg.append(f"level1_subscription_{symbol}")
    md_req.symbol = symbol
    md_req.exchange = exchange
    md_req.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.SUBSCRIBE
    md_req.update_bits = (request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.LAST_TRADE | 
                         request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.BBO)
    
    print(f"Subscribing to {symbol} on {exchange}")
    await ws.send(md_req.SerializeToString())

async def subscribe_level1():
    """Subscribe to Level 1 market data."""
    args = parse_args()
    
    # Initialize database manager for data persistence
    db = get_simple_database()
    if db.is_enabled():
        print("📊 Database persistence enabled for Level 1 market data")
        db.log_system_event('STARTUP', 'Level 1 BBO/Trades subscription started')
    else:
        print("📝 Console output mode - database persistence disabled")
    
    # Connect and authenticate with ticker plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    symbols_to_subscribe = []
    
    if args.underlying:
        # Search for all contracts for the underlying
        print(f"\nSearching for all {args.underlying} contracts...")
        found_symbols = await search_contracts(ws, args.underlying, args.exchange)
        
        if found_symbols:
            print(f"Found {len(found_symbols)} contracts: {', '.join(found_symbols)}")
            symbols_to_subscribe = [(symbol, args.exchange) for symbol in found_symbols]
        else:
            print(f"No contracts found for underlying {args.underlying}")
            # Fall back to manual symbol entry if no contracts found
            if args.symbol:
                print(f"Falling back to manual symbol: {args.symbol}")
                symbols_to_subscribe = [(args.symbol, args.exchange)]
            else:
                print("No fallback symbol provided. Use --symbol to specify a manual symbol.")
    else:
        # Single symbol subscription
        symbols_to_subscribe = [(args.symbol, args.exchange)]
    
    if not symbols_to_subscribe:
        print("No symbols to subscribe to. Exiting.")
        await ws.close()
        return
    
    # Subscribe to all symbols
    print(f"\nSubscribing to {len(symbols_to_subscribe)} symbol(s)...")
    for symbol, exchange in symbols_to_subscribe:
        await subscribe_to_symbol(ws, symbol, exchange)
    
    # Listen for updates
    print("Listening for Level 1 market data updates... (Ctrl+C to exit)")
    try:
        while True:
            try:
                msg_data = await asyncio.wait_for(ws.recv(), timeout=5)
                
                # Parse message type with new comprehensive error handling
                try:
                    # First identify the template ID
                    base = base_pb2.Base()
                    base.ParseFromString(msg_data)
                    template_id = base.template_id
                    
                    if template_id == TemplateIDs.MARKET_DATA_UPDATE_RESPONSE:
                        response = MarketDataParsers.parse_market_data_response(msg_data, "Level1 Market Data Response")
                        print(f"Market Data Update Response: {response}")
                        
                    elif template_id == TemplateIDs.BEST_BID_OFFER:
                        bbo = MarketDataParsers.parse_best_bid_offer(msg_data, "Level1 BBO Subscription")
                        symbol_info = format_symbol_info(bbo)
                        symbol = symbol_info['symbol']
                        exchange = symbol_info['exchange']
                        
                        # Extract BBO data
                        bid_price = safe_get_field(bbo, 'bid_price', None)
                        bid_size = safe_get_field(bbo, 'bid_size', None)
                        ask_price = safe_get_field(bbo, 'ask_price', None)
                        ask_size = safe_get_field(bbo, 'ask_size', None)
                        
                        print(f"\n=== [{symbol}] Best Bid/Offer Update ===")
                        print(f"Exchange: {exchange}")
                        print(f"Bid: {bid_price or 'N/A'} @ {bid_size or 'N/A'}")
                        print(f"Ask: {ask_price or 'N/A'} @ {ask_size or 'N/A'}")
                        print(f"Timestamp: {safe_get_field(bbo, 'ssboe', 'N/A')}.{safe_get_field(bbo, 'usecs', 'N/A')}")
                        
                        # Persist to database if enabled
                        if db.is_enabled():
                            try:
                                # Create market timestamp from ssboe/usecs if available
                                ssboe = safe_get_field(bbo, 'ssboe', None)
                                usecs = safe_get_field(bbo, 'usecs', None)
                                market_timestamp = None
                                if ssboe is not None:
                                    # Convert SSBOE (seconds since beginning of epoch) to datetime
                                    from datetime import datetime, timezone
                                    market_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                                
                                # Collect additional fields for database
                                additional_fields = {
                                    'bid_orders': safe_get_field(bbo, 'bid_orders', None),
                                    'ask_orders': safe_get_field(bbo, 'ask_orders', None),
                                    'bid_implicit_size': safe_get_field(bbo, 'bid_implicit_size', None),
                                    'ask_implicit_size': safe_get_field(bbo, 'ask_implicit_size', None),
                                    'lean_price': safe_get_field(bbo, 'lean_price', None),
                                    'presence_bits': safe_get_field(bbo, 'presence_bits', None),
                                    'clear_bits': safe_get_field(bbo, 'clear_bits', None),
                                    'is_snapshot': safe_get_field(bbo, 'is_snapshot', False),
                                    'ssboe': ssboe,
                                    'usecs': usecs
                                }
                                
                                db.insert_best_bid_offer(
                                    symbol=symbol,
                                    exchange=exchange,
                                    bid_price=float(bid_price) if bid_price is not None else None,
                                    bid_size=int(bid_size) if bid_size is not None else None,
                                    ask_price=float(ask_price) if ask_price is not None else None,
                                    ask_size=int(ask_size) if ask_size is not None else None,
                                    market_timestamp=market_timestamp,
                                    additional_fields=additional_fields
                                )
                            except Exception as e:
                                print(f"⚠️ Database error for BBO: {e}")
                        
                    elif template_id == TemplateIDs.LAST_TRADE:
                        trade = MarketDataParsers.parse_last_trade(msg_data, "Level1 Last Trade Subscription")
                        symbol_info = format_symbol_info(trade)
                        symbol = symbol_info['symbol']
                        exchange = symbol_info['exchange']
                        
                        # Extract trade data
                        trade_price = safe_get_field(trade, 'trade_price', None)
                        trade_size = safe_get_field(trade, 'trade_size', None)
                        volume = safe_get_field(trade, 'volume', None)
                        aggressor = safe_get_field(trade, 'aggressor', None)
                        
                        print(f"\n=== [{symbol}] Last Trade Update ===")
                        print(f"Exchange: {exchange}")
                        print(f"Trade Price: {trade_price or 'N/A'}")
                        print(f"Trade Size: {trade_size or 'N/A'}")
                        print(f"Volume: {volume or 'N/A'}")
                        print(f"Timestamp: {safe_get_field(trade, 'ssboe', 'N/A')}.{safe_get_field(trade, 'usecs', 'N/A')}")
                        
                        # Persist to database if enabled
                        if db.is_enabled():
                            try:
                                # Create trade timestamp from ssboe/usecs if available
                                ssboe = safe_get_field(trade, 'ssboe', None)
                                usecs = safe_get_field(trade, 'usecs', None)
                                trade_timestamp = None
                                if ssboe is not None:
                                    # Convert SSBOE (seconds since beginning of epoch) to datetime
                                    from datetime import datetime, timezone
                                    trade_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                                
                                # Collect additional fields for database
                                additional_fields = {
                                    'exchange_order_id': safe_get_field(trade, 'exchange_order_id', None),
                                    'aggressor_exchange_order_id': safe_get_field(trade, 'aggressor_exchange_order_id', None),
                                    'net_change': safe_get_field(trade, 'net_change', None),
                                    'percent_change': safe_get_field(trade, 'percent_change', None),
                                    'vwap': safe_get_field(trade, 'vwap', None),
                                    'presence_bits': safe_get_field(trade, 'presence_bits', None),
                                    'clear_bits': safe_get_field(trade, 'clear_bits', None),
                                    'is_snapshot': safe_get_field(trade, 'is_snapshot', False),
                                    'ssboe': ssboe,
                                    'usecs': usecs,
                                    'source_ssboe': safe_get_field(trade, 'source_ssboe', None),
                                    'source_usecs': safe_get_field(trade, 'source_usecs', None),
                                    'source_nsecs': safe_get_field(trade, 'source_nsecs', None),
                                    'jop_ssboe': safe_get_field(trade, 'jop_ssboe', None),
                                    'jop_nsecs': safe_get_field(trade, 'jop_nsecs', None)
                                }
                                
                                db.insert_last_trade(
                                    symbol=symbol,
                                    exchange=exchange,
                                    trade_price=float(trade_price) if trade_price is not None else None,
                                    trade_size=int(trade_size) if trade_size is not None else None,
                                    volume=int(volume) if volume is not None else None,
                                    aggressor=str(aggressor) if aggressor is not None else None,
                                    trade_timestamp=trade_timestamp,
                                    additional_fields=additional_fields
                                )
                            except Exception as e:
                                print(f"⚠️ Database error for trade: {e}")
                        
                    else:
                        decode_unknown_message(template_id, msg_data)
                        
                except RithmicAPIError as e:
                    print(f"Rithmic API Error: {e}")
                    print(f"Error Code: {e.error_code}, Error Text: {e.error_text}")
                except ProtobufParsingError as e:
                    print(f"Protobuf Parsing Error: {e}")
                    print(f"Template ID: {e.template_id}")
                    # Fallback to original decoder for debugging
                    decode_unknown_message(e.template_id, msg_data)
                except UnexpectedTemplateError as e:
                    print(f"Unexpected Template Error: {e}")
                    print(f"Expected: {e.expected_id}, Received: {e.received_id}")
                    decode_unknown_message(e.received_id, msg_data)
                except FieldValidationError as e:
                    print(f"Field Validation Error: {e}")
                    print(f"Missing fields: {e.missing_fields}")
                except Exception as e:
                    print(f"Unexpected parsing error: {e}")
                    # Try to get template ID for fallback
                    try:
                        fallback_base = base_pb2.Base()
                        fallback_base.ParseFromString(msg_data)
                        decode_unknown_message(fallback_base.template_id, msg_data)
                    except:
                        print(f"Could not decode message for debugging")
                    
            except asyncio.TimeoutError:
                await send_heartbeat(ws)
                
    except KeyboardInterrupt:
        print("\nExiting...")
        if db.is_enabled():
            db.log_system_event('SHUTDOWN', 'Level 1 BBO/Trades subscription stopped by user')
        await ws.close()

if __name__ == "__main__":
    asyncio.run(subscribe_level1())