#!/usr/bin/env python3
"""
Subscribe to Level 2 market data: Market by Price (order book depth).
Demonstrates order book subscription with all available fields.
"""

import argparse
import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
sys.path.append('..')
from shared import connect_and_authenticate, send_heartbeat, decode_unknown_message

# Add proto_generated directory for protobuf imports
sys.path.append('../../proto_generated')

import request_market_data_update_pb2
import response_market_data_update_pb2
import order_book_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv('../.env.simple-demos')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Subscribe to Level 2 market data: Market by Price (order book depth)',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection settings
    parser.add_argument('--system',
                       default=os.getenv('RITHMIC_SYSTEM'),
                       help='Rithmic system to connect to')
    parser.add_argument('--gateway',
                       default=os.getenv('RITHMIC_GATEWAY'),
                       help='Gateway location for connection')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'),
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    
    # Market data settings
    parser.add_argument('--symbol',
                       default=os.getenv('SYMBOL'),
                       help='Symbol to subscribe to for market data')
    parser.add_argument('--exchange',
                       default=os.getenv('EXCHANGE'),
                       help='Exchange for the symbol')
    
    return parser.parse_args()

async def subscribe_level2():
    """Subscribe to Level 2 market data (Market by Price)."""
    args = parse_args()
    
    print(f"Subscribing to Level 2 market data (Market by Price) for {args.symbol} on {args.exchange}")
    
    # Connect and authenticate with ticker plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create market data subscription request for order book
    md_req = request_market_data_update_pb2.RequestMarketDataUpdate()
    md_req.template_id = 100
    md_req.user_msg.append("level2_subscription")
    
    # Set all available fields using command line arguments
    md_req.symbol = args.symbol
    md_req.exchange = args.exchange
    md_req.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.SUBSCRIBE
    md_req.update_bits = request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.ORDER_BOOK
    
    print(f"Sending Level 2 (Market by Price) subscription request: {md_req}")
    await ws.send(md_req.SerializeToString())
    
    # Listen for updates
    print("Listening for Level 2 market data updates... (Ctrl+C to exit)")
    try:
        while True:
            try:
                msg_data = await asyncio.wait_for(ws.recv(), timeout=5)
                
                # Parse message type
                base = base_pb2.Base()
                base.ParseFromString(msg_data)
                
                if base.template_id == 101:  # ResponseMarketDataUpdate
                    response = response_market_data_update_pb2.ResponseMarketDataUpdate()
                    response.ParseFromString(msg_data)
                    print(f"Market Data Update Response: {response}")
                    
                elif base.template_id == 140:  # OrderBook (Market by Price)
                    order_book = order_book_pb2.OrderBook()
                    order_book.ParseFromString(msg_data)
                    print(f"Order Book (Market by Price): {order_book}")
                    
                else:
                    decode_unknown_message(base.template_id, msg_data)
                    
            except asyncio.TimeoutError:
                await send_heartbeat(ws)
                
    except KeyboardInterrupt:
        print("\nExiting...")
        await ws.close()

if __name__ == "__main__":
    asyncio.run(subscribe_level2())