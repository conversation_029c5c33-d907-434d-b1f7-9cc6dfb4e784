#!/usr/bin/env python3
"""
Subscribe to Level 3 market data: Depth by Order (individual order tracking).
Demonstrates RequestDepthByOrderSnapshot and RequestDepthByOrderUpdates with all available fields.
"""

import argparse
import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
sys.path.append('..')
from shared import connect_and_authenticate, send_heartbeat, decode_unknown_message

# Add proto_generated directory for protobuf imports
sys.path.append('../../proto_generated')

import request_depth_by_order_snapshot_pb2
import response_depth_by_order_snapshot_pb2
import request_depth_by_order_updates_pb2
import response_depth_by_order_updates_pb2
import depth_by_order_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv('../.env.simple-demos')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Subscribe to Level 3 market data: Depth by Order (individual order tracking)',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection settings
    parser.add_argument('--system',
                       default=os.getenv('RITHMIC_SYSTEM'),
                       help='Rithmic system to connect to')
    parser.add_argument('--gateway',
                       default=os.getenv('RITHMIC_GATEWAY'),
                       help='Gateway location for connection')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'),
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    
    # Market data settings
    parser.add_argument('--symbol',
                       default=os.getenv('SYMBOL'),
                       help='Symbol to subscribe to for market data')
    parser.add_argument('--exchange',
                       default=os.getenv('EXCHANGE'),
                       help='Exchange for the symbol')
    
    return parser.parse_args()

async def subscribe_level3():
    """Subscribe to Level 3 market data (Depth by Order)."""
    args = parse_args()
    
    print(f"Subscribing to Level 3 market data (Depth by Order) for {args.symbol} on {args.exchange}")
    
    # Connect and authenticate with ticker plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # First, request depth by order snapshot
    snapshot_req = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
    snapshot_req.template_id = 115
    snapshot_req.user_msg.append("depth_snapshot_request")
    
    # Set all available fields using command line arguments
    snapshot_req.symbol = args.symbol
    snapshot_req.exchange = args.exchange
    # snapshot_req.depth_price = 0.0  # Optional: specific price level to request
    
    print(f"Sending Depth by Order snapshot request: {snapshot_req}")
    await ws.send(snapshot_req.SerializeToString())
    
    # Then, subscribe to depth by order updates
    updates_req = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates()
    updates_req.template_id = 117
    updates_req.user_msg.append("depth_updates_subscription")
    
    # Set all available fields using command line arguments
    updates_req.symbol = args.symbol
    updates_req.exchange = args.exchange
    updates_req.request = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates.Request.SUBSCRIBE
    # updates_req.depth_price = 0.0  # Optional: specific price level to request
    
    print(f"Sending Depth by Order updates subscription request: {updates_req}")
    await ws.send(updates_req.SerializeToString())
    
    # Listen for updates
    print("Listening for Level 3 depth by order data... (Ctrl+C to exit)")
    try:
        while True:
            try:
                msg_data = await asyncio.wait_for(ws.recv(), timeout=5)
                
                # Parse message type
                base = base_pb2.Base()
                base.ParseFromString(msg_data)
                
                if base.template_id == 116:  # ResponseDepthByOrderSnapshot
                    snapshot_response = response_depth_by_order_snapshot_pb2.ResponseDepthByOrderSnapshot()
                    snapshot_response.ParseFromString(msg_data)
                    print(f"Depth by Order Snapshot Response: {snapshot_response}")
                    
                elif base.template_id == 118:  # ResponseDepthByOrderUpdates
                    updates_response = response_depth_by_order_updates_pb2.ResponseDepthByOrderUpdates()
                    updates_response.ParseFromString(msg_data)
                    print(f"Depth by Order Updates Response: {updates_response}")
                    
                elif base.template_id == 160:  # DepthByOrder
                    depth_data = depth_by_order_pb2.DepthByOrder()
                    depth_data.ParseFromString(msg_data)
                    print(f"Depth by Order Data: {depth_data}")
                    
                else:
                    decode_unknown_message(base.template_id, msg_data)
                    
            except asyncio.TimeoutError:
                await send_heartbeat(ws)
                
    except KeyboardInterrupt:
        print("\nExiting...")
        await ws.close()

if __name__ == "__main__":
    asyncio.run(subscribe_level3())