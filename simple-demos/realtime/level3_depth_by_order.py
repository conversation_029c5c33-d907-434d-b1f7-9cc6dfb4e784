#!/usr/bin/env python3
"""
Subscribe to Level 3 market data: Depth by Order (individual order tracking).
Demonstrates RequestDepthByOrderSnapshot and RequestDepthByOrderUpdates with all available fields.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
from shared import (connect_and_authenticate, send_heartbeat, decode_unknown_message, search_contracts, TemplateIDs,
                    MarketDataParsers, safe_get_field, format_symbol_info,
                    ProtobufParsingError, RithmicAPIError, UnexpectedTemplateError, FieldValidationError)
from argument_parser import create_market_data_parser
from shared_database import get_simple_database

# Add proto_generated directory for protobuf imports
proto_dir = os.path.join(os.path.dirname(parent_dir), 'proto_generated')
sys.path.append(proto_dir)

import request_depth_by_order_snapshot_pb2
import response_depth_by_order_snapshot_pb2
import request_depth_by_order_updates_pb2
import response_depth_by_order_updates_pb2
import depth_by_order_pb2
import request_login_pb2
import base_pb2
import request_search_symbols_pb2
import response_search_symbols_pb2

# Load configuration
load_dotenv(os.path.join(parent_dir, '.env.simple-demos'))

def parse_args():
    """Parse command line arguments using centralized argument parser."""
    parser = create_market_data_parser('Subscribe to Level 3 market data: Depth by Order (individual order tracking)')
    return parser.parse_args()


async def subscribe_to_symbol_depth(ws, symbol, exchange):
    """
    Subscribe to depth by order for a single symbol with proper synchronization.
    
    This function implements the correct sequence for depth-by-order subscriptions:
    1. Subscribe to updates FIRST (Template 117) to ensure no data is missed
    2. Wait for subscription confirmation (Template 118) before proceeding  
    3. Request snapshot (Template 115) only after subscription is confirmed
    
    This prevents data loss that could occur with delay-based synchronization.
    
    Args:
        ws: WebSocket connection to Rithmic
        symbol: Trading symbol (e.g., 'ESZ5')
        exchange: Exchange name (e.g., 'CME')
    """
    # Subscribe to updates FIRST to ensure no data is missed
    updates_req = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates()
    updates_req.template_id = TemplateIDs.DEPTH_BY_ORDER_UPDATES_REQUEST
    updates_req.user_msg.append(f"depth_updates_{symbol}")
    updates_req.symbol = symbol
    updates_req.exchange = exchange
    updates_req.request = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates.Request.SUBSCRIBE
    
    print(f"Subscribing to depth updates for {symbol} on {exchange}")
    await ws.send(updates_req.SerializeToString())
    
    # Wait for subscription confirmation before requesting snapshot
    # This ensures updates subscription is active before snapshot request
    while True:
        response_data = await ws.recv()
        try:
            base = base_pb2.Base()
            base.ParseFromString(response_data)
            
            if base.template_id == TemplateIDs.DEPTH_BY_ORDER_UPDATES_RESPONSE:
                # Received confirmation that update subscription is active
                updates_response = response_depth_by_order_updates_pb2.ResponseDepthByOrderUpdates()
                updates_response.ParseFromString(response_data)
                print(f"Subscription confirmed for {symbol}")
                break
            else:
                # Handle other messages during subscription process (heartbeats, etc.)
                print(f"Received unexpected message during subscription (template {base.template_id}), continuing...")
        except Exception as e:
            print(f"Error parsing subscription response: {e}, continuing...")
    
    # Now request snapshot after subscription is confirmed
    snapshot_req = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
    snapshot_req.template_id = TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_REQUEST
    snapshot_req.user_msg.append(f"depth_snapshot_{symbol}")
    snapshot_req.symbol = symbol
    snapshot_req.exchange = exchange
    
    print(f"Requesting depth snapshot for {symbol} on {exchange}")
    await ws.send(snapshot_req.SerializeToString())

async def subscribe_level3():
    """Subscribe to Level 3 market data (Depth by Order)."""
    args = parse_args()
    
    # Initialize database manager for data persistence
    db = get_simple_database()
    if db.is_enabled():
        print("📊 Database persistence enabled for Level 3 depth by order data")
        db.log_system_event('STARTUP', 'Level 3 Depth by Order subscription started')
    else:
        print("📝 Console output mode - database persistence disabled")
    
    # Connect and authenticate with ticker plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    symbols_to_subscribe = []
    
    if args.underlying:
        # Search for all contracts for the underlying
        print(f"\nSearching for all {args.underlying} contracts...")
        found_symbols = await search_contracts(ws, args.underlying, args.exchange)
        
        if found_symbols:
            print(f"Found {len(found_symbols)} contracts: {', '.join(found_symbols)}")
            symbols_to_subscribe = [(symbol, args.exchange) for symbol in found_symbols]
        else:
            print(f"No contracts found for underlying {args.underlying}")
            # Fall back to manual symbol entry if no contracts found
            if args.symbol:
                print(f"Falling back to manual symbol: {args.symbol}")
                symbols_to_subscribe = [(args.symbol, args.exchange)]
            else:
                print("No fallback symbol provided. Use --symbol to specify a manual symbol.")
    else:
        # Single symbol subscription
        symbols_to_subscribe = [(args.symbol, args.exchange)]
    
    if not symbols_to_subscribe:
        print("No symbols to subscribe to. Exiting.")
        await ws.close()
        return
    
    # Subscribe to all symbols
    print(f"\nSubscribing to {len(symbols_to_subscribe)} symbol(s)...")
    for symbol, exchange in symbols_to_subscribe:
        await subscribe_to_symbol_depth(ws, symbol, exchange)
    
    
    # Buffer to store updates received before snapshot
    # This ensures no data is lost during the snapshot request process
    update_buffer = []
    snapshot_received = False
    
    # Listen for updates
    print("Listening for Level 3 depth by order data... (Ctrl+C to exit)")
    print("Note: Updates received before snapshot will be buffered and applied after snapshot processing")
    try:
        while True:
            try:
                msg_data = await asyncio.wait_for(ws.recv(), timeout=5)
                
                # Parse message type with comprehensive error handling
                try:
                    # First identify the template ID
                    base = base_pb2.Base()
                    base.ParseFromString(msg_data)
                    template_id = base.template_id
                    
                    if template_id == TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_RESPONSE:
                        snapshot_response = MarketDataParsers.parse_depth_snapshot_response(msg_data, "Level3 Depth Snapshot")
                        symbol_info = format_symbol_info(snapshot_response)
                        symbol = symbol_info['symbol']
                        exchange = symbol_info['exchange']
                        
                        print(f"\n=== [{symbol}] Depth by Order Snapshot Response ===")
                        print(f"Exchange: {exchange}")
                        print(f"Response Code: {safe_get_field(snapshot_response, 'rp_code', 'N/A')}")
                        print(f"Raw Response: {snapshot_response}")
                        
                        # Persist snapshot to database if enabled
                        if db.is_enabled():
                            try:
                                # Create market timestamp from ssboe/usecs if available
                                ssboe = safe_get_field(snapshot_response, 'ssboe', None)
                                usecs = safe_get_field(snapshot_response, 'usecs', None)
                                market_timestamp = None
                                if ssboe is not None:
                                    from datetime import datetime, timezone
                                    market_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                                
                                # Collect additional fields for database
                                additional_fields = {
                                    'sequence_number': safe_get_field(snapshot_response, 'sequence_number', None),
                                    'presence_bits': safe_get_field(snapshot_response, 'presence_bits', None),
                                    'clear_bits': safe_get_field(snapshot_response, 'clear_bits', None),
                                    'ssboe': ssboe,
                                    'usecs': usecs,
                                    'source_ssboe': safe_get_field(snapshot_response, 'source_ssboe', None),
                                    'source_usecs': safe_get_field(snapshot_response, 'source_usecs', None),
                                    'source_nsecs': safe_get_field(snapshot_response, 'source_nsecs', None),
                                    'jop_ssboe': safe_get_field(snapshot_response, 'jop_ssboe', None),
                                    'jop_nsecs': safe_get_field(snapshot_response, 'jop_nsecs', None)
                                }
                                
                                # For now, insert empty levels list - actual depth data comes in individual updates
                                # This is just the snapshot header/metadata
                                db.insert_depth_snapshot(
                                    symbol=symbol,
                                    exchange=exchange,
                                    levels=[],  # Individual depth levels come in separate updates
                                    sequence_number=additional_fields.get('sequence_number'),
                                    market_timestamp=market_timestamp,
                                    additional_fields=additional_fields
                                )
                            except Exception as e:
                                print(f"⚠️ Database error for snapshot: {e}")
                        
                        # Mark snapshot as received
                        snapshot_received = True
                        
                        # Process any buffered updates with new parsing
                        if update_buffer:
                            print(f"\n=== Processing {len(update_buffer)} buffered updates ===")
                            for buffered_data in update_buffer:
                                try:
                                    depth_data = MarketDataParsers.parse_depth_by_order(buffered_data, "Buffered Depth Update")
                                    symbol_info_buf = format_symbol_info(depth_data)
                                    symbol_buf = symbol_info_buf['symbol']
                                    exchange_buf = symbol_info_buf['exchange']
                                    
                                    print(f"\n=== [{symbol_buf}] Buffered Depth Update ===")
                                    print(f"Exchange: {exchange_buf}")
                                    print(f"Order ID: {safe_get_field(depth_data, 'order_id', 'N/A')}")
                                    print(f"Price: {safe_get_field(depth_data, 'price', 'N/A')}")
                                    print(f"Quantity: {safe_get_field(depth_data, 'quantity', 'N/A')}")
                                    print(f"Side: {safe_get_field(depth_data, 'side', 'N/A')}")
                                    
                                    # Persist buffered depth update to database if enabled
                                    if db.is_enabled():
                                        try:
                                            # Create market timestamp from ssboe/usecs if available
                                            ssboe = safe_get_field(depth_data, 'ssboe', None)
                                            usecs = safe_get_field(depth_data, 'usecs', None)
                                            market_timestamp = None
                                            if ssboe is not None:
                                                from datetime import datetime, timezone
                                                market_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                                            
                                            # Create a single level entry for this depth update
                                            level_data = {
                                                'update_type': safe_get_field(depth_data, 'action', 'NEW'),
                                                'transaction_type': safe_get_field(depth_data, 'side', None),
                                                'depth_price': safe_get_field(depth_data, 'price', None),
                                                'depth_size': safe_get_field(depth_data, 'quantity', None),
                                                'exchange_order_id': safe_get_field(depth_data, 'order_id', None)
                                            }
                                            
                                            # Insert as depth snapshot with single level
                                            db.insert_depth_snapshot(
                                                symbol=symbol_buf,
                                                exchange=exchange_buf,
                                                levels=[level_data] if any(level_data.values()) else [],
                                                market_timestamp=market_timestamp,
                                                additional_fields={
                                                    'ssboe': ssboe,
                                                    'usecs': usecs,
                                                    'is_buffered_update': True
                                                }
                                            )
                                        except Exception as e:
                                            print(f"⚠️ Database error for buffered update: {e}")
                                    
                                except (ProtobufParsingError, FieldValidationError) as e:
                                    print(f"Error parsing buffered update: {e}")
                            update_buffer.clear()
                        
                    elif template_id == TemplateIDs.DEPTH_BY_ORDER_UPDATES_RESPONSE:
                        updates_response = MarketDataParsers.parse_depth_updates_response(msg_data, "Level3 Depth Updates Response")
                        symbol_info = format_symbol_info(updates_response)
                        symbol = symbol_info['symbol']
                        
                        print(f"\n=== [{symbol}] Depth by Order Updates Response ===")
                        print(f"Exchange: {symbol_info['exchange']}")
                        print(f"Response Code: {safe_get_field(updates_response, 'rp_code', 'N/A')}")
                        print(f"Raw Response: {updates_response}")
                        
                    elif template_id == TemplateIDs.DEPTH_BY_ORDER:
                        if not snapshot_received:
                            # Buffer updates until snapshot arrives
                            update_buffer.append(msg_data)
                            print(f"Buffering update (snapshot pending)... Buffer size: {len(update_buffer)}")
                        else:
                            # Process update normally with new parsing
                            depth_data = MarketDataParsers.parse_depth_by_order(msg_data, "Level3 Depth Update")
                            symbol_info = format_symbol_info(depth_data)
                            symbol = symbol_info['symbol']
                            exchange = symbol_info['exchange']
                            
                            # Extract depth update data
                            order_id = safe_get_field(depth_data, 'order_id', None)
                            price = safe_get_field(depth_data, 'price', None)
                            quantity = safe_get_field(depth_data, 'quantity', None)
                            side = safe_get_field(depth_data, 'side', None)
                            action = safe_get_field(depth_data, 'action', None)
                            
                            print(f"\n=== [{symbol}] Depth by Order Update ===")
                            print(f"Exchange: {exchange}")
                            print(f"Order ID: {order_id or 'N/A'}")
                            print(f"Price: {price or 'N/A'}")
                            print(f"Quantity: {quantity or 'N/A'}")
                            print(f"Side: {side or 'N/A'}")
                            print(f"Action: {action or 'N/A'}")
                            print(f"Timestamp: {safe_get_field(depth_data, 'ssboe', 'N/A')}.{safe_get_field(depth_data, 'usecs', 'N/A')}")
                            
                            # Persist depth update to database if enabled
                            if db.is_enabled():
                                try:
                                    # Create market timestamp from ssboe/usecs if available
                                    ssboe = safe_get_field(depth_data, 'ssboe', None)
                                    usecs = safe_get_field(depth_data, 'usecs', None)
                                    market_timestamp = None
                                    if ssboe is not None:
                                        from datetime import datetime, timezone
                                        market_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                                    
                                    # Create a single level entry for this depth update
                                    level_data = {
                                        'update_type': action or 'NEW',
                                        'transaction_type': side,
                                        'depth_price': price,
                                        'depth_size': quantity,
                                        'exchange_order_id': order_id
                                    }
                                    
                                    # Insert as depth snapshot with single level
                                    db.insert_depth_snapshot(
                                        symbol=symbol,
                                        exchange=exchange,
                                        levels=[level_data] if any(level_data.values()) else [],
                                        market_timestamp=market_timestamp,
                                        additional_fields={
                                            'ssboe': ssboe,
                                            'usecs': usecs,
                                            'is_realtime_update': True
                                        }
                                    )
                                except Exception as e:
                                    print(f"⚠️ Database error for depth update: {e}")
                        
                    else:
                        decode_unknown_message(template_id, msg_data)
                        
                except RithmicAPIError as e:
                    print(f"Rithmic API Error: {e}")
                    print(f"Error Code: {e.error_code}, Error Text: {e.error_text}")
                except ProtobufParsingError as e:
                    print(f"Protobuf Parsing Error: {e}")
                    print(f"Template ID: {e.template_id}")
                    # Fallback to original decoder for debugging
                    decode_unknown_message(e.template_id, msg_data)
                except UnexpectedTemplateError as e:
                    print(f"Unexpected Template Error: {e}")
                    print(f"Expected: {e.expected_id}, Received: {e.received_id}")
                    decode_unknown_message(e.received_id, msg_data)
                except FieldValidationError as e:
                    print(f"Field Validation Error: {e}")
                    print(f"Missing fields: {e.missing_fields}")
                except Exception as e:
                    print(f"Unexpected parsing error: {e}")
                    # Try to get template ID for fallback
                    try:
                        fallback_base = base_pb2.Base()
                        fallback_base.ParseFromString(msg_data)
                        decode_unknown_message(fallback_base.template_id, msg_data)
                    except:
                        print(f"Could not decode message for debugging")
                    
            except asyncio.TimeoutError:
                await send_heartbeat(ws)
                
    except KeyboardInterrupt:
        print("\nExiting...")
        if db.is_enabled():
            db.log_system_event('SHUTDOWN', 'Level 3 Depth by Order subscription stopped by user')
        await ws.close()

if __name__ == "__main__":
    asyncio.run(subscribe_level3())