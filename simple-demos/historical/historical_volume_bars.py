#!/usr/bin/env python3
"""
Request historical volume bars.
Demonstrates RequestTickBarReplay with VOLUME_BAR type and all available fields.
"""

import asyncio
import sys
import os
import argparse
from dotenv import load_dotenv

# Add parent directory to path for shared imports
sys.path.append('..')
from shared import connect_and_authenticate

# Add proto_generated directory for protobuf imports
sys.path.append('../../proto_generated')

import request_tick_bar_replay_pb2
import response_tick_bar_replay_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv('../.env.simple-demos')

def parse_args():
    """Parse command line arguments with fallback to environment variables."""
    parser = argparse.ArgumentParser(
        description='Request historical volume bars from Rithmic API',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection parameters
    parser.add_argument('--system', 
                       default=os.getenv('RITHMIC_SYSTEM'),
                       help='Rithmic system to connect to')
    parser.add_argument('--gateway',
                       default=os.getenv('RITHMIC_GATEWAY'), 
                       help='Gateway location for connection')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'),
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    
    # Symbol parameters
    parser.add_argument('--symbol',
                       default=os.getenv('HIST_SYMBOL'),
                       help='Symbol for historical data request')
    parser.add_argument('--exchange',
                       default=os.getenv('HIST_EXCHANGE'),
                       help='Exchange for historical data request')
    
    # Bar parameters
    parser.add_argument('--volume-specifier',
                       type=int,
                       default=int(os.getenv('VOLUME_BAR_SPECIFIER', '500')),
                       help='Volume threshold for volume bars')
    
    # Date range parameters
    parser.add_argument('--start-index',
                       type=int,
                       default=int(os.getenv('HIST_START_INDEX', '1719532800')),
                       help='Start timestamp for historical data')
    parser.add_argument('--finish-index',
                       type=int,
                       default=int(os.getenv('HIST_FINISH_INDEX', '1719619199')),
                       help='End timestamp for historical data')
    
    return parser.parse_args()

async def try_volume_bar_request(ws, symbol, exchange, volume_specifier, start_index, finish_index, description):
    """Try a volume bar request with given parameters."""
    print(f"\n=== Attempting volume bar request: {description} ===")
    print(f"Parameters: {symbol} on {exchange}, volume threshold: {volume_specifier}, dates: {start_index}-{finish_index}")
    
    # Create volume bar replay request
    bar_req = request_tick_bar_replay_pb2.RequestTickBarReplay()
    bar_req.template_id = 206
    bar_req.user_msg.append("volume_bar_request")
    bar_req.symbol = symbol
    bar_req.exchange = exchange
    bar_req.bar_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarType.VOLUME_BAR
    bar_req.bar_sub_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarSubType.REGULAR
    bar_req.bar_type_specifier = str(volume_specifier)
    bar_req.start_index = start_index
    bar_req.finish_index = finish_index
    bar_req.direction = request_tick_bar_replay_pb2.RequestTickBarReplay.Direction.FIRST
    bar_req.time_order = request_tick_bar_replay_pb2.RequestTickBarReplay.TimeOrder.FORWARDS
    bar_req.user_max_count = 25
    bar_req.resume_bars = False
    
    print(f"Sending request...")
    await ws.send(bar_req.SerializeToString())
    
    # Listen for response
    bars_received = 0
    try:
        while True:
            msg_data = await ws.recv()
            base = base_pb2.Base()
            base.ParseFromString(msg_data)
            
            if base.template_id == 207:  # ResponseTickBarReplay
                bar_response = response_tick_bar_replay_pb2.ResponseTickBarReplay()
                bar_response.ParseFromString(msg_data)
                
                # Check for "no data" response
                rp_code = getattr(bar_response, 'rp_code', [])
                if len(rp_code) > 0 and ('no data' in str(rp_code).lower() or '7' in rp_code):
                    print(f"❌ No data found for {description}")
                    print(f"   Response codes: {rp_code}")
                    return False
                
                print(f"✅ Data found! Volume Bar Response: {bar_response}")
                bars_received += 1
                
                # Check if response is complete
                rq_handler_rp_code = getattr(bar_response, 'rq_handler_rp_code', [])
                if len(rq_handler_rp_code) == 0 and len(rp_code) > 0:
                    print(f"✅ Request complete! Total volume bars received: {bars_received}")
                    return True
                    
            else:
                print(f"Received message template_id {base.template_id}")
                
    except Exception as e:
        print(f"❌ Error during request: {e}")
        return False

async def request_historical_volume_bars(args):
    """Request historical volume bars with fallback strategies."""
    # Print configuration being used
    print(f"Historical Volume Bars Configuration:")
    print(f"  System: {args.system}")
    print(f"  Gateway: {args.gateway}")
    print(f"  User: {args.user}")
    print(f"  Symbol: {args.symbol}")
    print(f"  Exchange: {args.exchange}")
    print(f"  Volume Specifier: {args.volume_specifier} contracts per bar")
    print(f"  Start Index: {args.start_index}")
    print(f"  Finish Index: {args.finish_index}")
    print()
    
    # Connect and authenticate with history plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.HISTORY_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Get configuration
    symbol = args.symbol
    exchange = args.exchange
    volume_specifier = args.volume_specifier
    
    print(f"🔍 Historical Volume Bars Request")
    print(f"📊 Configuration: {symbol} on {exchange}")
    print(f"📈 Volume threshold: {volume_specifier} contracts per bar")
    
    # Define fallback strategies (preserve existing sophisticated fallback logic)
    strategies = [
        {
            'start': args.start_index,
            'finish': args.finish_index,
            'description': 'Primary date range (from command line/env)',
            'volume': volume_specifier
        },
        {
            'start': int(os.getenv('HIST_START_INDEX_ALT1', '1715731200')),
            'finish': int(os.getenv('HIST_FINISH_INDEX_ALT1', '1715817599')),
            'description': 'Fallback date range 1 (May 15, 2024)',
            'volume': volume_specifier
        },
        {
            'start': int(os.getenv('HIST_START_INDEX_ALT2', '1712707200')),
            'finish': int(os.getenv('HIST_FINISH_INDEX_ALT2', '1712793599')),
            'description': 'Fallback date range 2 (April 10, 2024)',
            'volume': volume_specifier
        },
        {
            'start': args.start_index,
            'finish': args.finish_index,
            'description': 'Lower volume threshold (250 contracts)',
            'volume': 250
        },
        {
            'start': args.start_index,
            'finish': args.finish_index,
            'description': 'Minimal volume threshold (100 contracts)',
            'volume': 100
        }
    ]
    
    # Try each strategy until one succeeds
    success = False
    for i, strategy in enumerate(strategies, 1):
        print(f"\n🎯 Strategy {i}/{len(strategies)}: {strategy['description']}")
        
        success = await try_volume_bar_request(
            ws, symbol, exchange, strategy['volume'],
            strategy['start'], strategy['finish'],
            strategy['description']
        )
        
        if success:
            print(f"\n🎉 SUCCESS! Volume bars retrieved using: {strategy['description']}")
            break
        else:
            print(f"❌ Strategy {i} failed, trying next approach...")
    
    if not success:
        print(f"\n💥 ALL STRATEGIES FAILED")
        print(f"📋 Troubleshooting suggestions:")
        print(f"   • Check if the symbol {symbol} exists and is actively traded")
        print(f"   • Verify the exchange {exchange} is correct")
        print(f"   • Try different date ranges (avoid weekends/holidays)")
        print(f"   • Consider using a front-month contract for better liquidity")
        print(f"   • Check if tick bars work with the same parameters (basic connectivity test)")
    
    await ws.close()

if __name__ == "__main__":
    args = parse_args()
    asyncio.run(request_historical_volume_bars(args))