#!/usr/bin/env python3
"""
Request historical tick bars.
Demonstrates RequestTickBarReplay with all available fields.
"""

import asyncio
import sys
import os
import argparse
from dotenv import load_dotenv

# Add parent directory to path for shared imports
sys.path.append('..')
from shared import connect_and_authenticate

# Add proto_generated directory for protobuf imports
sys.path.append('../../proto_generated')

import request_tick_bar_replay_pb2
import response_tick_bar_replay_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv('../.env.simple-demos')

def parse_args():
    """Parse command line arguments with fallback to environment variables."""
    parser = argparse.ArgumentParser(
        description='Request historical tick bars from Rithmic API',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection parameters
    parser.add_argument('--system', 
                       default=os.getenv('RITHMIC_SYSTEM'),
                       help='Rithmic system to connect to')
    parser.add_argument('--gateway',
                       default=os.getenv('RITHMIC_GATEWAY'), 
                       help='Gateway location for connection')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'),
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    
    # Symbol parameters
    parser.add_argument('--symbol',
                       default=os.getenv('HIST_SYMBOL'),
                       help='Symbol for historical data request')
    parser.add_argument('--exchange',
                       default=os.getenv('HIST_EXCHANGE'),
                       help='Exchange for historical data request')
    
    # Bar parameters
    parser.add_argument('--bar-specifier',
                       type=int,
                       default=int(os.getenv('BAR_TYPE_SPECIFIER', '1')),
                       help='Number of trades per tick bar')
    
    return parser.parse_args()

async def request_historical_tick_bars(args):
    """Request historical tick bars."""
    # Print configuration being used
    print(f"Historical Tick Bars Configuration:")
    print(f"  System: {args.system}")
    print(f"  Gateway: {args.gateway}")
    print(f"  User: {args.user}")
    print(f"  Symbol: {args.symbol}")
    print(f"  Exchange: {args.exchange}")
    print(f"  Bar Specifier: {args.bar_specifier} trades per bar")
    print()
    
    # Connect and authenticate with history plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.HISTORY_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create tick bar replay request with all available fields
    bar_req = request_tick_bar_replay_pb2.RequestTickBarReplay()
    bar_req.template_id = 206
    bar_req.user_msg.append("tick_bar_request")
    
    # Set all available fields for reference
    bar_req.symbol = args.symbol
    bar_req.exchange = args.exchange
    bar_req.bar_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarType.TICK_BAR
    bar_req.bar_sub_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarSubType.REGULAR
    bar_req.bar_type_specifier = str(args.bar_specifier)
    bar_req.start_index = 1703980800  # 2023-12-31 00:00:00 UTC
    bar_req.finish_index = 1704067200  # 2024-01-01 00:00:00 UTC (24 hours later)
    bar_req.direction = request_tick_bar_replay_pb2.RequestTickBarReplay.Direction.FIRST
    bar_req.time_order = request_tick_bar_replay_pb2.RequestTickBarReplay.TimeOrder.FORWARDS
    bar_req.user_max_count = 25  # Maximum bars per response message
    bar_req.resume_bars = False  # Don't resume from previous session
    
    print(f"Sending historical tick bars request: {bar_req}")
    await ws.send(bar_req.SerializeToString())
    
    # Listen for historical data
    print("Waiting for historical tick bar data...")
    try:
        bars_received = 0
        while True:
            msg_data = await ws.recv()
            
            # Parse message type
            base = base_pb2.Base()
            base.ParseFromString(msg_data)
            
            if base.template_id == 207:  # ResponseTickBarReplay
                bar_response = response_tick_bar_replay_pb2.ResponseTickBarReplay()
                bar_response.ParseFromString(msg_data)
                print(f"Tick Bar Response: {bar_response}")
                
                bars_received += 1
                
                # Check if response is complete using safe field access
                rq_handler_rp_code = getattr(bar_response, 'rq_handler_rp_code', [])
                rp_code = getattr(bar_response, 'rp_code', [])
                
                if len(rq_handler_rp_code) == 0 and len(rp_code) > 0:
                    print(f"Historical tick bar request complete. Total bars received: {bars_received}")
                    break
                    
            else:
                print(f"Received message template_id {base.template_id}")
                print(f"Raw data: {msg_data}")
                
    except Exception as e:
        print(f"Error receiving historical data: {e}")
    
    await ws.close()

if __name__ == "__main__":
    args = parse_args()
    asyncio.run(request_historical_tick_bars(args))