#!/usr/bin/env python3
"""
Request historical tick bars.
Demonstrates RequestTickBarReplay with all available fields.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
from shared import (connect_and_authenticate, send_heartbeat, decode_unknown_message, 
                    HistoricalDataParsers, safe_get_field, safe_get_list_field, format_symbol_info,
                    ProtobufParsingError, RithmicAPIError, UnexpectedTemplateError, FieldValidationError)
from argument_parser import create_historical_parser
from shared_database import get_simple_database

# Add proto_generated directory for protobuf imports
proto_dir = os.path.join(os.path.dirname(parent_dir), 'proto_generated')
sys.path.append(proto_dir)

import request_tick_bar_replay_pb2
import response_tick_bar_replay_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv(os.path.join(parent_dir, '.env.simple-demos'))

def parse_args():
    """Parse command line arguments using centralized argument parser."""
    parser = create_historical_parser('Request historical tick bars from Rithmic API')
    return parser.parse_args()

async def request_historical_tick_bars(args):
    """Request historical tick bars."""
    # Initialize database manager for data persistence
    db = get_simple_database()
    if db.is_enabled():
        print("📊 Database persistence enabled for historical tick bars")
        db.log_system_event('STARTUP', f'Historical tick bars request started for {args.symbol}')
    else:
        print("📝 Console output mode - database persistence disabled")
    
    # Print configuration being used
    print(f"Historical Tick Bars Configuration:")
    print(f"  System: {args.system}")
    print(f"  Gateway: {args.gateway}")
    print(f"  User: {args.user}")
    print(f"  Symbol: {args.symbol}")
    print(f"  Exchange: {args.exchange}")
    print(f"  Bar Specifier: {args.bar_specifier} trades per bar")
    print()
    
    # Connect and authenticate with history plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.HISTORY_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create tick bar replay request with all available fields
    bar_req = request_tick_bar_replay_pb2.RequestTickBarReplay()
    bar_req.template_id = 206
    bar_req.user_msg.append("tick_bar_request")
    
    # Set all available fields for reference
    bar_req.symbol = args.symbol
    bar_req.exchange = args.exchange
    bar_req.bar_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarType.TICK_BAR
    bar_req.bar_sub_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarSubType.REGULAR
    bar_req.bar_type_specifier = str(args.bar_specifier)
    bar_req.start_index = 1719532800  # June 28, 2024 00:00:00 UTC (recent active trading day)
    bar_req.finish_index = 1719619199  # June 28, 2024 23:59:59 UTC (end of same trading day)
    bar_req.direction = request_tick_bar_replay_pb2.RequestTickBarReplay.Direction.FIRST
    bar_req.time_order = request_tick_bar_replay_pb2.RequestTickBarReplay.TimeOrder.FORWARDS
    bar_req.user_max_count = 25  # Maximum bars per response message
    bar_req.resume_bars = False  # Don't resume from previous session
    
    print(f"Sending historical tick bars request: {bar_req}")
    await ws.send(bar_req.SerializeToString())
    
    # Listen for historical data
    print("Waiting for historical tick bar data...")
    print("💡 This operation may take several minutes for large date ranges...")
    try:
        bars_received = 0
        message_count = 0
        import time
        start_time = time.time()
        
        while True:
            msg_data = await ws.recv()
            message_count += 1
            elapsed = time.time() - start_time
            
            # Timeout safeguard with user-friendly messaging
            if elapsed > 180:  # 3 minute safeguard
                print(f"\n⏱️  Operation timed out after {elapsed:.1f} seconds")
                print(f"📊 Processed {message_count} messages, received {bars_received} bars")
                print(f"\n💡 This may indicate:")
                print(f"   • Large date range requiring more time to process")
                print(f"   • No tick data available for the specified date range") 
                print(f"   • Network latency or server processing delays")
                print(f"\n🔧 Suggestions:")
                print(f"   • Try a smaller date range (e.g., 1-2 hours instead of full day)")
                print(f"   • Use more recent dates during active trading hours")
                print(f"   • Check if the symbol {args.symbol} was actively traded on the specified dates")
                break
            
            # Progress indicator every 30 seconds
            if message_count == 1 or (elapsed > 0 and int(elapsed) % 30 == 0 and elapsed > 30):
                print(f"📈 Progress: {elapsed:.0f}s elapsed, {message_count} messages, {bars_received} bars received...")
            
            # Parse message type with comprehensive error handling
            try:
                # First identify the template ID
                base = base_pb2.Base()
                base.ParseFromString(msg_data)
                template_id = base.template_id
                
                if template_id == 207:  # ResponseTickBarReplay
                    bar_response = HistoricalDataParsers.parse_tick_bar_response(msg_data, "Historical Tick Bar Response")
                    symbol_info = format_symbol_info(bar_response)
                    symbol = symbol_info['symbol']
                    
                    print(f"\n=== [{symbol}] Tick Bar Response ===")
                    print(f"Exchange: {symbol_info['exchange']}")
                    print(f"Response Code: {safe_get_list_field(bar_response, 'rp_code', 0, 'N/A')}")
                    print(f"Request Handler Code: {safe_get_list_field(bar_response, 'rq_handler_rp_code', 0, 'N/A')}")
                    print(f"Raw Response: {bar_response}")
                    
                    bars_received += 1
                    
                    # Check if response is complete using safe field access
                    rq_handler_rp_code = safe_get_field(bar_response, 'rq_handler_rp_code', [])
                    rp_code = safe_get_field(bar_response, 'rp_code', [])
                    
                    if len(rq_handler_rp_code) == 0 and len(rp_code) > 0:
                        print(f"\n=== Historical tick bar request complete ===")
                        print(f"Total bars received: {bars_received}")
                        break
                
                elif template_id == 251:  # Tick Bar data
                    tick_bar = HistoricalDataParsers.parse_tick_bar(msg_data, "Historical Tick Bar Data")
                    symbol_info = format_symbol_info(tick_bar)
                    symbol = symbol_info['symbol']
                    exchange = symbol_info['exchange']
                    
                    # Extract tick bar data
                    open_price = safe_get_field(tick_bar, 'open_price', None)
                    high_price = safe_get_field(tick_bar, 'high_price', None)
                    low_price = safe_get_field(tick_bar, 'low_price', None)
                    close_price = safe_get_field(tick_bar, 'close_price', None)
                    volume = safe_get_field(tick_bar, 'volume', None)
                    tick_count = safe_get_field(tick_bar, 'tick_count', None)
                    
                    print(f"\n=== [{symbol}] Tick Bar Data ===")
                    print(f"Exchange: {exchange}")
                    print(f"Open: {open_price or 'N/A'}")
                    print(f"High: {high_price or 'N/A'}")
                    print(f"Low: {low_price or 'N/A'}")
                    print(f"Close: {close_price or 'N/A'}")
                    print(f"Volume: {volume or 'N/A'}")
                    print(f"Tick Count: {tick_count or 'N/A'}")
                    print(f"Timestamp: {safe_get_field(tick_bar, 'ssboe', 'N/A')}.{safe_get_field(tick_bar, 'usecs', 'N/A')}")
                    
                    # Persist to database if enabled
                    if db.is_enabled():
                        try:
                            # Create bar timestamp from ssboe/usecs if available
                            ssboe = safe_get_field(tick_bar, 'ssboe', None)
                            usecs = safe_get_field(tick_bar, 'usecs', None)
                            bar_timestamp = None
                            if ssboe is not None:
                                # Convert SSBOE (seconds since beginning of epoch) to datetime
                                from datetime import datetime, timezone
                                bar_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                            
                            # Collect additional fields for database
                            additional_fields = {
                                'num_trades': tick_count,  # For tick bars, tick_count represents number of trades
                                'vwap': safe_get_field(tick_bar, 'vwap', None),
                                'ssboe': ssboe,
                                'usecs': usecs,
                                'presence_bits': safe_get_field(tick_bar, 'presence_bits', None),
                                'clear_bits': safe_get_field(tick_bar, 'clear_bits', None)
                            }
                            
                            db.insert_time_bar(
                                symbol=symbol,
                                exchange=exchange,
                                bar_timestamp=bar_timestamp or datetime.now(timezone.utc),
                                open_price=float(open_price) if open_price is not None else None,
                                high_price=float(high_price) if high_price is not None else None,
                                low_price=float(low_price) if low_price is not None else None,
                                close_price=float(close_price) if close_price is not None else None,
                                volume=int(volume) if volume is not None else None,
                                num_trades=int(tick_count) if tick_count is not None else None,
                                bar_type='TICK',
                                bar_interval=args.bar_specifier,
                                additional_fields=additional_fields
                            )
                        except Exception as e:
                            print(f"⚠️ Database error for tick bar: {e}")
                    
                else:
                    decode_unknown_message(template_id, msg_data)
                    
            except RithmicAPIError as e:
                print(f"Rithmic API Error: {e}")
                print(f"Error Code: {e.error_code}, Error Text: {e.error_text}")
                break  # Stop on API errors for historical requests
            except ProtobufParsingError as e:
                print(f"Protobuf Parsing Error: {e}")
                print(f"Template ID: {e.template_id}")
                decode_unknown_message(e.template_id, msg_data)
            except UnexpectedTemplateError as e:
                print(f"Unexpected Template Error: {e}")
                print(f"Expected: {e.expected_id}, Received: {e.received_id}")
                decode_unknown_message(e.received_id, msg_data)
            except FieldValidationError as e:
                print(f"Field Validation Error: {e}")
                print(f"Missing fields: {e.missing_fields}")
            except Exception as e:
                print(f"Unexpected parsing error: {e}")
                # Try to get template ID for fallback
                try:
                    fallback_base = base_pb2.Base()
                    fallback_base.ParseFromString(msg_data)
                    decode_unknown_message(fallback_base.template_id, msg_data)
                except:
                    print(f"Could not decode message for debugging")
                
    except Exception as e:
        print(f"Error receiving historical data: {e}")
    
    await ws.close()

if __name__ == "__main__":
    print("🚀 Starting Historical Tick Bars script...")
    try:
        args = parse_args()
        print("📝 Configuration parsed successfully")
        import signal
        import sys
        
        def timeout_handler(signum, frame):
            print(f"\n⏱️  Script execution timed out")
            print(f"💡 This may indicate an issue with:")
            print(f"   • Network connectivity to Rithmic servers")
            print(f"   • Authentication process taking longer than expected")
            print(f"   • Large data request requiring more processing time")
            print(f"\n🔧 Suggestions:")
            print(f"   • Check network connection and try again")
            print(f"   • Verify credentials in .env.simple-demos file")
            print(f"   • Try with a smaller date range")
            sys.exit(1)
        
        # Set up a 5-minute timeout for the entire script
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(300)  # 5 minute timeout
        
        asyncio.run(request_historical_tick_bars(args))
        
        # Cancel the timeout if we complete successfully
        signal.alarm(0)
        
    except KeyboardInterrupt:
        print(f"\n🛑 Script interrupted by user")
    except Exception as e:
        print(f"\n❌ Script error: {e}")
        print(f"💡 Try running with different parameters or check configuration")