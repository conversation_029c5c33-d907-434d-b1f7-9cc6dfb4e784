#!/usr/bin/env python3
"""
Subscribe to real-time time bars (OHLCV data streaming).
Demonstrates RequestTimeBarUpdate with all available fields.
"""

import asyncio
import sys
import os
import argparse
from dotenv import load_dotenv

# Add parent directory to path for shared imports
sys.path.append('..')
from shared import connect_and_authenticate

# Add proto_generated directory for protobuf imports
sys.path.append('../../proto_generated')

import request_time_bar_update_pb2
import response_time_bar_update_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv('../.env.simple-demos')

def parse_args():
    """Parse command line arguments with fallback to environment variables."""
    parser = argparse.ArgumentParser(
        description='Request historical time bars from Rithmic API',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection parameters
    parser.add_argument('--system', 
                       default=os.getenv('RITHMIC_SYSTEM'),
                       help='Rithmic system to connect to')
    parser.add_argument('--gateway',
                       default=os.getenv('RITHMIC_GATEWAY'), 
                       help='Gateway location for connection')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'),
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    
    # Symbol parameters
    parser.add_argument('--symbol',
                       default=os.getenv('HIST_SYMBOL'),
                       help='Symbol for historical data request')
    parser.add_argument('--exchange',
                       default=os.getenv('HIST_EXCHANGE'),
                       help='Exchange for historical data request')
    
    # Bar parameters
    parser.add_argument('--bar-specifier',
                       type=int,
                       default=int(os.getenv('BAR_TYPE_SPECIFIER', '1')),
                       help='Time bar period in minutes')
    
    return parser.parse_args()

async def request_historical_time_bars(args):
    """Request historical time bars."""
    # Print configuration being used
    print(f"Historical Time Bars Configuration:")
    print(f"  System: {args.system}")
    print(f"  Gateway: {args.gateway}")
    print(f"  User: {args.user}")
    print(f"  Symbol: {args.symbol}")
    print(f"  Exchange: {args.exchange}")
    print(f"  Bar Period: {args.bar_specifier} minute(s)")
    print()
    
    # Connect and authenticate with history plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.HISTORY_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create time bar replay request with all available fields
    bar_req = request_time_bar_update_pb2.RequestTimeBarUpdate()
    bar_req.template_id = 200
    bar_req.user_msg.append("time_bar_request")
    
    # Set all available fields for reference
    bar_req.symbol = args.symbol
    bar_req.exchange = args.exchange
    bar_req.bar_type = request_time_bar_update_pb2.RequestTimeBarUpdate.BarType.MINUTE_BAR
    bar_req.request = request_time_bar_update_pb2.RequestTimeBarUpdate.Request.SUBSCRIBE
    bar_req.bar_type_period = args.bar_specifier
    # RequestTimeBarUpdate doesn't use start_index/finish_index - it's for real-time streaming
    # RequestTimeBarUpdate doesn't have direction, time_order, user_max_count, or resume_bars fields
    
    print(f"Sending historical time bars request: {bar_req}")
    await ws.send(bar_req.SerializeToString())
    
    # Listen for historical data
    print("Waiting for historical time bar data...")
    try:
        bars_received = 0
        while True:
            msg_data = await ws.recv()
            
            # Parse message type
            base = base_pb2.Base()
            base.ParseFromString(msg_data)
            
            if base.template_id == 201:  # ResponseTimeBarUpdate
                bar_response = response_time_bar_update_pb2.ResponseTimeBarUpdate()
                bar_response.ParseFromString(msg_data)
                print(f"Time Bar Response: {bar_response}")
                
                bars_received += 1
                
                # Check if response is complete using safe field access
                rq_handler_rp_code = getattr(bar_response, 'rq_handler_rp_code', [])
                rp_code = getattr(bar_response, 'rp_code', [])
                
                if len(rq_handler_rp_code) == 0 and len(rp_code) > 0:
                    print(f"Historical data request complete. Total bars received: {bars_received}")
                    break
                    
            else:
                print(f"Received message template_id {base.template_id}")
                print(f"Raw data: {msg_data}")
                
    except Exception as e:
        print(f"Error receiving historical data: {e}")
    
    await ws.close()

if __name__ == "__main__":
    args = parse_args()
    asyncio.run(request_historical_time_bars(args))