#!/usr/bin/env python3
"""
Subscribe to real-time time bars (OHLCV data streaming).
Demonstrates RequestTimeBarUpdate with all available fields.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
from shared import (connect_and_authenticate, send_heartbeat, decode_unknown_message, 
                    HistoricalDataParsers, safe_get_field, safe_get_list_field, format_symbol_info,
                    ProtobufParsingError, RithmicAPIError, UnexpectedTemplateError, FieldValidationError)
from argument_parser import create_historical_parser
from shared_database import get_simple_database

# Add proto_generated directory for protobuf imports
proto_dir = os.path.join(os.path.dirname(parent_dir), 'proto_generated')
sys.path.append(proto_dir)

import request_time_bar_update_pb2
import response_time_bar_update_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv(os.path.join(parent_dir, '.env.simple-demos'))

def parse_args():
    """Parse command line arguments using centralized argument parser."""
    parser = create_historical_parser('Request historical time bars from Rithmic API')
    
    # Add script-specific bar period argument
    parser.add_argument('--bar-specifier',
                       type=int,
                       default=int(os.getenv('BAR_TYPE_SPECIFIER', '1')),
                       help='Time bar period in minutes')
    
    return parser.parse_args()

async def request_historical_time_bars(args):
    """Request historical time bars."""
    # Initialize database manager for data persistence
    db = get_simple_database()
    if db.is_enabled():
        print("📊 Database persistence enabled for historical time bars")
        db.log_system_event('STARTUP', f'Historical time bars request started for {args.symbol}')
    else:
        print("📝 Console output mode - database persistence disabled")
    
    # Print configuration being used
    print(f"Historical Time Bars Configuration:")
    print(f"  System: {args.system}")
    print(f"  Gateway: {args.gateway}")
    print(f"  User: {args.user}")
    print(f"  Symbol: {args.symbol}")
    print(f"  Exchange: {args.exchange}")
    print(f"  Bar Period: {args.bar_specifier} minute(s)")
    print()
    
    # Connect and authenticate with history plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.HISTORY_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Create time bar replay request with all available fields
    bar_req = request_time_bar_update_pb2.RequestTimeBarUpdate()
    bar_req.template_id = 200
    bar_req.user_msg.append("time_bar_request")
    
    # Set all available fields for reference
    bar_req.symbol = args.symbol
    bar_req.exchange = args.exchange
    bar_req.bar_type = request_time_bar_update_pb2.RequestTimeBarUpdate.BarType.MINUTE_BAR
    bar_req.request = request_time_bar_update_pb2.RequestTimeBarUpdate.Request.SUBSCRIBE
    bar_req.bar_type_period = args.bar_specifier
    # RequestTimeBarUpdate doesn't use start_index/finish_index - it's for real-time streaming
    # RequestTimeBarUpdate doesn't have direction, time_order, user_max_count, or resume_bars fields
    
    print(f"Sending historical time bars request: {bar_req}")
    await ws.send(bar_req.SerializeToString())
    
    # Listen for historical data
    print("Waiting for historical time bar data...")
    try:
        bars_received = 0
        while True:
            msg_data = await ws.recv()
            
            # Parse message type with comprehensive error handling
            try:
                # First identify the template ID
                base = base_pb2.Base()
                base.ParseFromString(msg_data)
                template_id = base.template_id
                
                if template_id == 201:  # ResponseTimeBarUpdate
                    bar_response = HistoricalDataParsers.parse_time_bar_response(msg_data, "Historical Time Bar Response")
                    symbol_info = format_symbol_info(bar_response)
                    symbol = symbol_info['symbol']
                    
                    print(f"\n=== [{symbol}] Time Bar Response ===")
                    print(f"Exchange: {symbol_info['exchange']}")
                    print(f"Response Code: {safe_get_list_field(bar_response, 'rp_code', 0, 'N/A')}")
                    print(f"Request Handler Code: {safe_get_list_field(bar_response, 'rq_handler_rp_code', 0, 'N/A')}")
                    print(f"Raw Response: {bar_response}")
                    
                    bars_received += 1
                    
                    # Check if response is complete using safe field access
                    rq_handler_rp_code = safe_get_field(bar_response, 'rq_handler_rp_code', [])
                    rp_code = safe_get_field(bar_response, 'rp_code', [])
                    
                    if len(rq_handler_rp_code) == 0 and len(rp_code) > 0:
                        print(f"\n=== Historical data request complete ===")
                        print(f"Total bars received: {bars_received}")
                        break
                
                elif template_id == 250:  # Time Bar data
                    time_bar = HistoricalDataParsers.parse_time_bar(msg_data, "Historical Time Bar Data")
                    symbol_info = format_symbol_info(time_bar)
                    symbol = symbol_info['symbol']
                    exchange = symbol_info['exchange']
                    
                    # Extract time bar data
                    open_price = safe_get_field(time_bar, 'open_price', None)
                    high_price = safe_get_field(time_bar, 'high_price', None)
                    low_price = safe_get_field(time_bar, 'low_price', None)
                    close_price = safe_get_field(time_bar, 'close_price', None)
                    volume = safe_get_field(time_bar, 'volume', None)
                    
                    print(f"\n=== [{symbol}] Time Bar Data ===")
                    print(f"Exchange: {exchange}")
                    print(f"Open: {open_price or 'N/A'}")
                    print(f"High: {high_price or 'N/A'}")
                    print(f"Low: {low_price or 'N/A'}")
                    print(f"Close: {close_price or 'N/A'}")
                    print(f"Volume: {volume or 'N/A'}")
                    print(f"Timestamp: {safe_get_field(time_bar, 'ssboe', 'N/A')}.{safe_get_field(time_bar, 'usecs', 'N/A')}")
                    
                    # Persist to database if enabled
                    if db.is_enabled():
                        try:
                            # Create bar timestamp from ssboe/usecs if available
                            ssboe = safe_get_field(time_bar, 'ssboe', None)
                            usecs = safe_get_field(time_bar, 'usecs', None)
                            bar_timestamp = None
                            if ssboe is not None:
                                # Convert SSBOE (seconds since beginning of epoch) to datetime
                                from datetime import datetime, timezone
                                bar_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                            
                            # Collect additional fields for database
                            additional_fields = {
                                'num_trades': safe_get_field(time_bar, 'num_trades', None),
                                'vwap': safe_get_field(time_bar, 'vwap', None),
                                'ssboe': ssboe,
                                'usecs': usecs,
                                'presence_bits': safe_get_field(time_bar, 'presence_bits', None),
                                'clear_bits': safe_get_field(time_bar, 'clear_bits', None)
                            }
                            
                            db.insert_time_bar(
                                symbol=symbol,
                                exchange=exchange,
                                bar_timestamp=bar_timestamp or datetime.now(timezone.utc),
                                open_price=float(open_price) if open_price is not None else None,
                                high_price=float(high_price) if high_price is not None else None,
                                low_price=float(low_price) if low_price is not None else None,
                                close_price=float(close_price) if close_price is not None else None,
                                volume=int(volume) if volume is not None else None,
                                num_trades=additional_fields.get('num_trades'),
                                bar_type='MINUTE',
                                bar_interval=args.bar_specifier,
                                additional_fields=additional_fields
                            )
                        except Exception as e:
                            print(f"⚠️ Database error for time bar: {e}")
                    
                else:
                    decode_unknown_message(template_id, msg_data)
                    
            except RithmicAPIError as e:
                print(f"Rithmic API Error: {e}")
                print(f"Error Code: {e.error_code}, Error Text: {e.error_text}")
                break  # Stop on API errors for historical requests
            except ProtobufParsingError as e:
                print(f"Protobuf Parsing Error: {e}")
                print(f"Template ID: {e.template_id}")
                decode_unknown_message(e.template_id, msg_data)
            except UnexpectedTemplateError as e:
                print(f"Unexpected Template Error: {e}")
                print(f"Expected: {e.expected_id}, Received: {e.received_id}")
                decode_unknown_message(e.received_id, msg_data)
            except FieldValidationError as e:
                print(f"Field Validation Error: {e}")
                print(f"Missing fields: {e.missing_fields}")
            except Exception as e:
                print(f"Unexpected parsing error: {e}")
                # Try to get template ID for fallback
                try:
                    fallback_base = base_pb2.Base()
                    fallback_base.ParseFromString(msg_data)
                    decode_unknown_message(fallback_base.template_id, msg_data)
                except:
                    print(f"Could not decode message for debugging")
                
    except Exception as e:
        print(f"Error receiving historical data: {e}")
    
    await ws.close()

if __name__ == "__main__":
    try:
        args = parse_args()
        asyncio.run(request_historical_time_bars(args))
    except KeyboardInterrupt:
        print(f"\n🛑 Script interrupted by user")
        # Log shutdown event to database if available
        try:
            db = get_simple_database()
            if db.is_enabled():
                db.log_system_event('SHUTDOWN', 'Historical time bars script stopped by user')
        except:
            pass  # Don't fail if database logging fails during shutdown
    except Exception as e:
        print(f"\n❌ Script error: {e}")
        print(f"💡 Try running with different parameters or check configuration")