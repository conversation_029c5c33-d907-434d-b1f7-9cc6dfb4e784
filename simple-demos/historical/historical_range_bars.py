#!/usr/bin/env python3
"""
Request historical range bars.
Demonstrates RequestTickBarReplay with RANGE_BAR type and all available fields.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
from shared import (connect_and_authenticate, send_heartbeat, decode_unknown_message, 
                    HistoricalDataParsers, safe_get_field, safe_get_list_field, format_symbol_info,
                    ProtobufParsingError, RithmicAPIError, UnexpectedTemplateError, FieldValidationError)
from argument_parser import create_historical_parser
from shared_database import get_simple_database

# Add proto_generated directory for protobuf imports
proto_dir = os.path.join(os.path.dirname(parent_dir), 'proto_generated')
sys.path.append(proto_dir)

import request_tick_bar_replay_pb2
import response_tick_bar_replay_pb2
import request_login_pb2
import base_pb2

# Load configuration
load_dotenv(os.path.join(parent_dir, '.env.simple-demos'))

def parse_args():
    """Parse command line arguments using centralized argument parser."""
    parser = create_historical_parser('Request historical range bars from Rithmic API')
    
    # Add script-specific range bar arguments
    parser.add_argument('--range-specifier',
                       type=int,
                       default=int(os.getenv('RANGE_BAR_SPECIFIER', '4')),
                       help='Range threshold for range bars (in ticks)')
    parser.add_argument('--start-index',
                       type=int,
                       default=int(os.getenv('HIST_START_INDEX', '1719532800')),
                       help='Start timestamp for historical data')
    parser.add_argument('--finish-index',
                       type=int,
                       default=int(os.getenv('HIST_FINISH_INDEX', '1719619199')),
                       help='End timestamp for historical data')
    
    return parser.parse_args()

async def try_range_bar_request(ws, symbol, exchange, range_specifier, start_index, finish_index, description, db):
    """Try a range bar request with given parameters."""
    print(f"\n=== Attempting range bar request: {description} ===")
    print(f"Parameters: {symbol} on {exchange}, range threshold: {range_specifier} ticks, dates: {start_index}-{finish_index}")
    
    # Create range bar replay request
    bar_req = request_tick_bar_replay_pb2.RequestTickBarReplay()
    bar_req.template_id = 206
    bar_req.user_msg.append("range_bar_request")
    bar_req.symbol = symbol
    bar_req.exchange = exchange
    bar_req.bar_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarType.RANGE_BAR
    bar_req.bar_sub_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarSubType.REGULAR
    bar_req.bar_type_specifier = str(range_specifier)
    bar_req.start_index = start_index
    bar_req.finish_index = finish_index
    bar_req.direction = request_tick_bar_replay_pb2.RequestTickBarReplay.Direction.FIRST
    bar_req.time_order = request_tick_bar_replay_pb2.RequestTickBarReplay.TimeOrder.FORWARDS
    bar_req.user_max_count = 25
    bar_req.resume_bars = False
    
    print(f"Sending request...")
    await ws.send(bar_req.SerializeToString())
    
    # Listen for response
    bars_received = 0
    range_bars_processed = 0
    try:
        while True:
            msg_data = await ws.recv()
            base = base_pb2.Base()
            base.ParseFromString(msg_data)
            
            try:
                if base.template_id == 207:  # ResponseTickBarReplay
                    bar_response = HistoricalDataParsers.parse_tick_bar_replay_response(msg_data, "Historical Range Bar Response")
                    symbol_info = format_symbol_info(bar_response)
                    symbol_data = symbol_info['symbol']
                    
                    # Check for "no data" response
                    rp_code = safe_get_field(bar_response, 'rp_code', [])
                    if len(rp_code) > 0 and ('no data' in str(rp_code).lower() or '7' in rp_code):
                        print(f"❌ No data found for {description}")
                        print(f"   Response codes: {rp_code}")
                        return False
                    
                    print(f"✅ Data found! [{symbol_data}] Range Bar Response: {bar_response}")
                    bars_received += 1
                    
                    # Check if response is complete
                    rq_handler_rp_code = safe_get_field(bar_response, 'rq_handler_rp_code', [])
                    if len(rq_handler_rp_code) == 0 and len(rp_code) > 0:
                        print(f"✅ Request complete! Response messages: {bars_received}, Range bars processed: {range_bars_processed}")
                        return True
                        
                elif base.template_id == 253:  # Range Bar data
                    range_bar = HistoricalDataParsers.parse_range_bar(msg_data, "Historical Range Bar Data")
                    symbol_info = format_symbol_info(range_bar)
                    symbol_data = symbol_info['symbol']
                    exchange_data = symbol_info['exchange']
                    
                    # Extract range bar data
                    open_price = safe_get_field(range_bar, 'open_price', None)
                    high_price = safe_get_field(range_bar, 'high_price', None)
                    low_price = safe_get_field(range_bar, 'low_price', None)
                    close_price = safe_get_field(range_bar, 'close_price', None)
                    volume = safe_get_field(range_bar, 'volume', None)
                    
                    print(f"\n=== [{symbol_data}] Range Bar Data ===")
                    print(f"Exchange: {exchange_data}")
                    print(f"Open: {open_price or 'N/A'}")
                    print(f"High: {high_price or 'N/A'}")
                    print(f"Low: {low_price or 'N/A'}")
                    print(f"Close: {close_price or 'N/A'}")
                    print(f"Volume: {volume or 'N/A'}")
                    print(f"Timestamp: {safe_get_field(range_bar, 'ssboe', 'N/A')}.{safe_get_field(range_bar, 'usecs', 'N/A')}")
                    
                    range_bars_processed += 1
                    
                    # Persist to database if enabled
                    if db.is_enabled():
                        try:
                            # Create bar timestamp from ssboe/usecs if available
                            ssboe = safe_get_field(range_bar, 'ssboe', None)
                            usecs = safe_get_field(range_bar, 'usecs', None)
                            bar_timestamp = None
                            if ssboe is not None:
                                # Convert SSBOE (seconds since beginning of epoch) to datetime
                                from datetime import datetime, timezone
                                bar_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                            
                            # Collect additional fields for database
                            additional_fields = {
                                'num_trades': safe_get_field(range_bar, 'num_trades', None),
                                'vwap': safe_get_field(range_bar, 'vwap', None),
                                'ssboe': ssboe,
                                'usecs': usecs,
                                'presence_bits': safe_get_field(range_bar, 'presence_bits', None),
                                'clear_bits': safe_get_field(range_bar, 'clear_bits', None)
                            }
                            
                            db.insert_time_bar(
                                symbol=symbol_data,
                                exchange=exchange_data,
                                bar_timestamp=bar_timestamp or datetime.now(timezone.utc),
                                open_price=float(open_price) if open_price is not None else None,
                                high_price=float(high_price) if high_price is not None else None,
                                low_price=float(low_price) if low_price is not None else None,
                                close_price=float(close_price) if close_price is not None else None,
                                volume=int(volume) if volume is not None else None,
                                num_trades=additional_fields.get('num_trades'),
                                bar_type='RANGE',
                                bar_interval=range_specifier,
                                additional_fields=additional_fields
                            )
                        except Exception as e:
                            print(f"⚠️ Database error for range bar: {e}")
                        
            except (RithmicAPIError, ProtobufParsingError, UnexpectedTemplateError, FieldValidationError) as e:
                print(f"Parsing Error: {e}")
                decode_unknown_message(base.template_id, msg_data)
                return False
                    
            else:
                print(f"Received message template_id {base.template_id}")
                
    except Exception as e:
        print(f"❌ Error during request: {e}")
        return False

async def request_historical_range_bars(args):
    """Request historical range bars with fallback strategies."""
    # Initialize database manager for data persistence
    db = get_simple_database()
    if db.is_enabled():
        print("📊 Database persistence enabled for historical range bars")
        db.log_system_event('STARTUP', f'Historical range bars request started for {args.symbol}')
    else:
        print("📝 Console output mode - database persistence disabled")
    
    # Print configuration being used
    print(f"Historical Range Bars Configuration:")
    print(f"  System: {args.system}")
    print(f"  Gateway: {args.gateway}")
    print(f"  User: {args.user}")
    print(f"  Symbol: {args.symbol}")
    print(f"  Exchange: {args.exchange}")
    print(f"  Range Specifier: {args.range_specifier} ticks per bar")
    print(f"  Start Index: {args.start_index}")
    print(f"  Finish Index: {args.finish_index}")
    print()
    
    # Connect and authenticate with history plant
    ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.HISTORY_PLANT)
    if not ws:
        print("Authentication failed")
        return
    
    # Get configuration
    symbol = args.symbol
    exchange = args.exchange
    range_specifier = args.range_specifier
    
    print(f"🔍 Historical Range Bars Request")
    print(f"📊 Configuration: {symbol} on {exchange}")
    print(f"📈 Range threshold: {range_specifier} ticks per bar (ES: 1 tick = 0.25 points)")
    
    # Define fallback strategies (preserve existing sophisticated fallback logic)
    strategies = [
        {
            'start': args.start_index,
            'finish': args.finish_index,
            'description': 'Primary date range (from command line/env)',
            'range': range_specifier
        },
        {
            'start': int(os.getenv('HIST_START_INDEX_ALT1', '1715731200')),
            'finish': int(os.getenv('HIST_FINISH_INDEX_ALT1', '1715817599')),
            'description': 'Fallback date range 1 (May 15, 2024)',
            'range': range_specifier
        },
        {
            'start': int(os.getenv('HIST_START_INDEX_ALT2', '1712707200')),
            'finish': int(os.getenv('HIST_FINISH_INDEX_ALT2', '1712793599')),
            'description': 'Fallback date range 2 (April 10, 2024)',
            'range': range_specifier
        },
        {
            'start': args.start_index,
            'finish': args.finish_index,
            'description': 'Smaller range threshold (2 ticks = 0.5 points)',
            'range': 2
        },
        {
            'start': args.start_index,
            'finish': args.finish_index,
            'description': 'Minimal range threshold (1 tick = 0.25 points)',
            'range': 1
        }
    ]
    
    # Try each strategy until one succeeds
    success = False
    for i, strategy in enumerate(strategies, 1):
        print(f"\n🎯 Strategy {i}/{len(strategies)}: {strategy['description']}")
        
        success = await try_range_bar_request(
            ws, symbol, exchange, strategy['range'],
            strategy['start'], strategy['finish'],
            strategy['description'], db
        )
        
        if success:
            print(f"\n🎉 SUCCESS! Range bars retrieved using: {strategy['description']}")
            break
        else:
            print(f"❌ Strategy {i} failed, trying next approach...")
    
    if not success:
        print(f"\n💥 ALL STRATEGIES FAILED")
        print(f"📋 Troubleshooting suggestions:")
        print(f"   • Check if the symbol {symbol} exists and is actively traded")
        print(f"   • Verify the exchange {exchange} is correct")
        print(f"   • Try different date ranges (avoid weekends/holidays)")
        print(f"   • Consider using a front-month contract for better liquidity")
        print(f"   • Range bars need sufficient price movement - try during volatile market periods")
        print(f"   • Check if tick bars work with the same parameters (basic connectivity test)")
    
    await ws.close()

if __name__ == "__main__":
    args = parse_args()
    asyncio.run(request_historical_range_bars(args))