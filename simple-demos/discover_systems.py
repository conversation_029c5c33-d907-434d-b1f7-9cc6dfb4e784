#!/usr/bin/env python3
"""
Discover available Rithmic systems using shared authentication utilities.
"""

import argparse
import asyncio
import os
import sys
from dotenv import load_dotenv

# Add shared module to path
sys.path.append('.')
from shared import get_system_info

# Load configuration
load_dotenv('.env.simple-demos')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Discover available Rithmic systems',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Connection settings
    parser.add_argument('--uri', 
                       default=os.getenv('RITHMIC_URI'),
                       help='Rithmic WebSocket URI')
    parser.add_argument('--user',
                       default=os.getenv('RITHMIC_USER'), 
                       help='Rithmic username')
    parser.add_argument('--password',
                       default=os.getenv('RITHMIC_PASSWORD'),
                       help='Rithmic password')
    parser.add_argument('--app-name',
                       default=os.getenv('RITHMIC_APP_NAME'),
                       help='Application name')
    parser.add_argument('--app-version',
                       default=os.getenv('RITHMIC_APP_VERSION'),
                       help='Application version')
    parser.add_argument('--template-version',
                       default=os.getenv('RITHMIC_TEMPLATE_VERSION'),
                       help='Protocol template version')
    
    return parser.parse_args()

async def discover_systems():
    """Discover available Rithmic systems."""
    args = parse_args()
    
    print("Discovering available Rithmic systems...")
    print(f"Using URI: {args.uri}")
    print(f"Using User: {args.user}")
    
    # Use the robust system info function from shared.py
    systems = await get_system_info()
    
    if systems:
        print(f"\nDiscovered {len(systems)} available systems:")
        for i, system in enumerate(systems, 1):
            print(f"  {i}. {system}")
    else:
        print("No systems discovered or error occurred.")
    
    return systems

if __name__ == "__main__":
    asyncio.run(discover_systems())