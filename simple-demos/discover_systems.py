#!/usr/bin/env python3
"""
Discover available Rithmic systems using shared authentication utilities.
"""

import asyncio
import sys
from dotenv import load_dotenv

# Add shared module to path
sys.path.append('.')
from shared import get_system_info
from argument_parser import create_discovery_parser

# Load configuration
load_dotenv('.env.simple-demos')

def parse_args():
    """Parse command line arguments using centralized argument parser."""
    parser = create_discovery_parser('Discover available Rithmic systems')
    return parser.parse_args()

async def discover_systems():
    """Discover available Rithmic systems."""
    args = parse_args()
    
    print("Discovering available Rithmic systems...")
    print(f"Using URI: {args.uri}")
    
    # Use the robust system info function from shared.py
    systems = await get_system_info()
    
    if systems:
        print(f"\nDiscovered {len(systems)} available systems:")
        for i, system in enumerate(systems, 1):
            print(f"  {i}. {system}")
    else:
        print("No systems discovered or error occurred.")
    
    return systems

if __name__ == "__main__":
    asyncio.run(discover_systems())