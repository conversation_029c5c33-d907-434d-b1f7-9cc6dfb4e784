#!/usr/bin/env python3
"""
Centralized Argument Parser for Simple Demos

This module provides reusable argument parsing functions to eliminate
code duplication across simple-demos scripts. It defines common argument
patterns for connection, market data, and historical data operations.
"""

import argparse
import os


def add_connection_arguments(parser):
    """
    Add standard Rithmic connection arguments to an argument parser.
    
    These arguments are common across all scripts that connect to Rithmic:
    - system: Rithmic system name (e.g., 'Rithmic Paper Trading')
    - gateway: Gateway location (e.g., 'Chicago Area')
    - user: Rithmic username
    - password: Rithmic password
    
    All arguments default to corresponding environment variables.
    
    Args:
        parser (argparse.ArgumentParser): Parser to add arguments to
    """
    parser.add_argument(
        '--system', 
        default=os.getenv('RITHMIC_SYSTEM'),
        help='Rithmic system to connect to (default: RITHMIC_SYSTEM env var)'
    )
    parser.add_argument(
        '--gateway', 
        default=os.getenv('RITHMIC_GATEWAY'),
        help='Gateway location for connection (default: RITHMIC_GATEWAY env var)'
    )
    parser.add_argument(
        '--user', 
        default=os.getenv('RITHMIC_USER'),
        help='Rithmic username (default: RITHMIC_USER env var)'
    )
    parser.add_argument(
        '--password', 
        default=os.getenv('RITHMIC_PASSWORD'),
        help='Rithmic password (default: RITHMIC_PASSWORD env var)'
    )


def add_market_data_arguments(parser):
    """
    Add standard market data arguments to an argument parser.
    
    These arguments are common for realtime market data scripts:
    - symbol: Trading symbol to subscribe to
    - exchange: Exchange for the symbol
    - underlying: Subscribe to all contracts for an underlying
    
    Args:
        parser (argparse.ArgumentParser): Parser to add arguments to
    """
    parser.add_argument(
        '--symbol', 
        default=os.getenv('SYMBOL'),
        help='Symbol to subscribe to (default: SYMBOL env var)'
    )
    parser.add_argument(
        '--exchange', 
        default=os.getenv('EXCHANGE'),
        help='Exchange for the symbol (default: EXCHANGE env var)'
    )
    parser.add_argument(
        '--underlying',
        help='Subscribe to all contracts for this underlying (e.g., ES)'
    )


def add_historical_arguments(parser):
    """
    Add standard historical data arguments to an argument parser.
    
    These arguments are common for historical data scripts:
    - symbol: Symbol for historical data request
    - exchange: Exchange for historical data
    - start-date: Start date for historical data
    - end-date: End date for historical data
    
    Args:
        parser (argparse.ArgumentParser): Parser to add arguments to
    """
    parser.add_argument(
        '--symbol', 
        default=os.getenv('HIST_SYMBOL'),
        help='Symbol for historical data (default: HIST_SYMBOL env var)'
    )
    parser.add_argument(
        '--exchange', 
        default=os.getenv('HIST_EXCHANGE'),
        help='Exchange for historical data (default: HIST_EXCHANGE env var)'
    )
    parser.add_argument(
        '--start-date',
        help='Start date for historical data (YYYY-MM-DD format)'
    )
    parser.add_argument(
        '--end-date',
        help='End date for historical data (YYYY-MM-DD format)'
    )


def add_bar_type_arguments(parser):
    """
    Add bar type specifier arguments for historical data scripts.
    
    These arguments control the type and frequency of bars:
    - bar-type-specifier: Type of bars (time, tick, volume, range)
    - frequency: Frequency specification for the bars
    
    Args:
        parser (argparse.ArgumentParser): Parser to add arguments to
    """
    parser.add_argument(
        '--bar-type-specifier',
        default=os.getenv('BAR_TYPE_SPECIFIER'),
        help='Bar type specifier (default: BAR_TYPE_SPECIFIER env var)'
    )
    parser.add_argument(
        '--frequency',
        help='Frequency specification for bars (e.g., 1, 5, 15 for minutes)'
    )


def add_search_arguments(parser):
    """
    Add symbol search arguments to an argument parser.
    
    These arguments are used for symbol search operations:
    - exchange: Exchange to search in
    - pattern: Search pattern
    - product-code: Product code filter
    - instrument-type: Instrument type filter
    - pattern-type: Type of pattern matching
    
    Args:
        parser (argparse.ArgumentParser): Parser to add arguments to
    """
    parser.add_argument(
        '--exchange',
        default=os.getenv('SEARCH_EXCHANGE'),
        help='Exchange to search in (default: SEARCH_EXCHANGE env var)'
    )
    parser.add_argument(
        '--pattern',
        default=os.getenv('SEARCH_PATTERN', 'ES'),
        help='Search pattern (default: SEARCH_PATTERN env var or "ES")'
    )
    parser.add_argument(
        '--product-code',
        default=os.getenv('SEARCH_PRODUCT_CODE'),
        help='Product code filter (default: SEARCH_PRODUCT_CODE env var)'
    )
    parser.add_argument(
        '--instrument-type',
        type=int,
        default=int(os.getenv('SEARCH_INSTRUMENT_TYPE', '1')),
        help='Instrument type filter (default: SEARCH_INSTRUMENT_TYPE env var or 1)'
    )
    parser.add_argument(
        '--pattern-type',
        type=int,
        default=int(os.getenv('SEARCH_PATTERN_TYPE', '2')),
        help='Pattern type (1=EQUALS, 2=CONTAINS) (default: SEARCH_PATTERN_TYPE env var or 2)'
    )


def add_database_arguments(parser):
    """
    Add database configuration arguments to an argument parser.
    
    These arguments control database persistence and logging behavior:
    - enable-database: Enable/disable database persistence
    - mysql-host: MySQL database host
    - mysql-user: MySQL database user
    - mysql-password: MySQL database password
    - mysql-database: MySQL database name
    - mysql-port: MySQL database port
    - enable-file-fallback: Enable file-based logging fallback
    - enable-database-logging: Enable database logging
    
    Args:
        parser (argparse.ArgumentParser): Parser to add arguments to
    """
    # Database persistence control
    parser.add_argument(
        '--enable-database',
        action='store_true',
        default=os.getenv('ENABLE_DATABASE', 'true').lower() == 'true',
        help='Enable database persistence (default: ENABLE_DATABASE env var or true)'
    )
    parser.add_argument(
        '--disable-database',
        action='store_true',
        help='Disable database persistence (overrides --enable-database)'
    )
    
    # MySQL connection parameters
    parser.add_argument(
        '--mysql-host',
        default=os.getenv('MYSQL_HOST', 'localhost'),
        help='MySQL database host (default: MYSQL_HOST env var or localhost)'
    )
    parser.add_argument(
        '--mysql-user',
        default=os.getenv('MYSQL_USER', 'root'),
        help='MySQL database user (default: MYSQL_USER env var or root)'
    )
    parser.add_argument(
        '--mysql-password',
        default=os.getenv('MYSQL_PASSWORD'),
        help='MySQL database password (default: MYSQL_PASSWORD env var)'
    )
    parser.add_argument(
        '--mysql-database',
        default=os.getenv('MYSQL_DATABASE', 'rithmic_api'),
        help='MySQL database name (default: MYSQL_DATABASE env var or rithmic_api)'
    )
    parser.add_argument(
        '--mysql-port',
        type=int,
        default=int(os.getenv('MYSQL_PORT', '3306')),
        help='MySQL database port (default: MYSQL_PORT env var or 3306)'
    )
    
    # Database behavior options
    parser.add_argument(
        '--enable-file-fallback',
        action='store_true',
        default=os.getenv('ENABLE_FILE_LOGGING_FALLBACK', 'true').lower() == 'true',
        help='Enable file-based logging fallback (default: ENABLE_FILE_LOGGING_FALLBACK env var or true)'
    )
    parser.add_argument(
        '--disable-file-fallback',
        action='store_true',
        help='Disable file-based logging fallback (overrides --enable-file-fallback)'
    )
    parser.add_argument(
        '--enable-database-logging',
        action='store_true',
        default=os.getenv('ENABLE_DATABASE_LOGGING', 'true').lower() == 'true',
        help='Enable database logging (default: ENABLE_DATABASE_LOGGING env var or true)'
    )
    parser.add_argument(
        '--disable-database-logging',
        action='store_true',
        help='Disable database logging (overrides --enable-database-logging)'
    )


def add_discovery_arguments(parser):
    """
    Add system discovery arguments to an argument parser.
    
    These arguments are specific to system discovery operations:
    - uri: Rithmic URI to connect to
    - app-name: Application name
    - app-version: Application version
    - template-version: Template version
    
    Args:
        parser (argparse.ArgumentParser): Parser to add arguments to
    """
    parser.add_argument(
        '--uri',
        default=os.getenv('RITHMIC_URI'),
        help='Rithmic URI to connect to (default: RITHMIC_URI env var)'
    )
    parser.add_argument(
        '--app-name',
        default=os.getenv('RITHMIC_APP_NAME'),
        help='Application name (default: RITHMIC_APP_NAME env var)'
    )
    parser.add_argument(
        '--app-version',
        default=os.getenv('RITHMIC_APP_VERSION'),
        help='Application version (default: RITHMIC_APP_VERSION env var)'
    )
    parser.add_argument(
        '--template-version',
        default=os.getenv('RITHMIC_TEMPLATE_VERSION'),
        help='Template version (default: RITHMIC_TEMPLATE_VERSION env var)'
    )


def create_connection_parser(description="Rithmic connection script"):
    """
    Create a standard argument parser with connection arguments.
    
    This is a convenience function that creates a new ArgumentParser
    and adds the standard connection arguments.
    
    Args:
        description (str): Description for the argument parser
        
    Returns:
        argparse.ArgumentParser: Configured parser with connection arguments
    """
    parser = argparse.ArgumentParser(description=description)
    add_connection_arguments(parser)
    return parser


def create_market_data_parser(description="Rithmic market data script", include_database=True):
    """
    Create a standard argument parser for market data scripts.
    
    This includes connection, market data, and optionally database arguments.
    
    Args:
        description (str): Description for the argument parser
        include_database (bool): Whether to include database arguments (default: True)
        
    Returns:
        argparse.ArgumentParser: Configured parser with connection and market data arguments
    """
    parser = argparse.ArgumentParser(description=description)
    add_connection_arguments(parser)
    add_market_data_arguments(parser)
    if include_database:
        add_database_arguments(parser)
    return parser


def create_historical_parser(description="Rithmic historical data script", include_database=True):
    """
    Create a standard argument parser for historical data scripts.
    
    This includes connection, historical data, bar type, and optionally database arguments.
    
    Args:
        description (str): Description for the argument parser
        include_database (bool): Whether to include database arguments (default: True)
        
    Returns:
        argparse.ArgumentParser: Configured parser with connection and historical arguments
    """
    parser = argparse.ArgumentParser(description=description)
    add_connection_arguments(parser)
    add_historical_arguments(parser)
    add_bar_type_arguments(parser)
    if include_database:
        add_database_arguments(parser)
    return parser


def create_search_parser(description="Rithmic symbol search script", include_database=True):
    """
    Create a standard argument parser for symbol search scripts.
    
    This includes connection, search, and optionally database arguments.
    
    Args:
        description (str): Description for the argument parser
        include_database (bool): Whether to include database arguments (default: True)
        
    Returns:
        argparse.ArgumentParser: Configured parser with connection and search arguments
    """
    parser = argparse.ArgumentParser(description=description)
    add_connection_arguments(parser)
    add_search_arguments(parser)
    if include_database:
        add_database_arguments(parser)
    return parser


def create_discovery_parser(description="Rithmic system discovery script"):
    """
    Create a standard argument parser for system discovery scripts.
    
    This includes discovery-specific arguments.
    
    Args:
        description (str): Description for the argument parser
        
    Returns:
        argparse.ArgumentParser: Configured parser with discovery arguments
    """
    parser = argparse.ArgumentParser(description=description)
    add_discovery_arguments(parser)
    return parser


def create_database_parser(description="Database configuration script"):
    """
    Create a parser specifically for database configuration.
    
    This includes only database arguments.
    
    Args:
        description (str): Description for the argument parser
        
    Returns:
        argparse.ArgumentParser: Configured parser with database arguments
    """
    parser = argparse.ArgumentParser(description=description)
    add_database_arguments(parser)
    return parser


def process_database_arguments(args):
    """
    Process database arguments and set corresponding environment variables.
    
    This function takes parsed arguments and updates the environment
    to reflect the database configuration choices. This allows the
    database infrastructure to pick up the command-line overrides.
    
    Args:
        args: Parsed arguments from argparse
        
    Returns:
        dict: Dictionary of processed database configuration
    """
    # Process disable flags (they override enable flags)
    enable_database = args.enable_database and not getattr(args, 'disable_database', False)
    enable_file_fallback = args.enable_file_fallback and not getattr(args, 'disable_file_fallback', False)
    enable_database_logging = args.enable_database_logging and not getattr(args, 'disable_database_logging', False)
    
    # Update environment variables to reflect command-line arguments
    os.environ['ENABLE_DATABASE'] = 'true' if enable_database else 'false'
    os.environ['MYSQL_HOST'] = args.mysql_host
    os.environ['MYSQL_USER'] = args.mysql_user
    if args.mysql_password:
        os.environ['MYSQL_PASSWORD'] = args.mysql_password
    os.environ['MYSQL_DATABASE'] = args.mysql_database
    os.environ['MYSQL_PORT'] = str(args.mysql_port)
    os.environ['ENABLE_FILE_LOGGING_FALLBACK'] = 'true' if enable_file_fallback else 'false'
    os.environ['ENABLE_DATABASE_LOGGING'] = 'true' if enable_database_logging else 'false'
    
    # Return processed configuration for use by calling script
    config = {
        'enable_database': enable_database,
        'mysql_host': args.mysql_host,
        'mysql_user': args.mysql_user,
        'mysql_password': args.mysql_password,
        'mysql_database': args.mysql_database,
        'mysql_port': args.mysql_port,
        'enable_file_fallback': enable_file_fallback,
        'enable_database_logging': enable_database_logging
    }
    
    return config


if __name__ == "__main__":
    # Example usage demonstration
    print("Centralized Argument Parser for Simple Demos")
    print("=" * 50)
    print()
    
    # Show example of connection parser
    connection_parser = create_connection_parser("Example connection script")
    print("Connection Parser Help:")
    connection_parser.print_help()
    print()
    
    # Show example of market data parser
    market_parser = create_market_data_parser("Example market data script")
    print("Market Data Parser Help:")
    market_parser.print_help()
    print()
    
    print("This module provides centralized argument parsing for simple-demos scripts.")
    print("Import the appropriate functions to eliminate argument parsing duplication.")