# Configuration for Rithmic API Simple Demo Scripts
# ALL VALUES ARE REQUIRED - No default fallbacks are provided

# ==============================================================================
# CONNECTION SETTINGS (from main .env)
# ==============================================================================
RITHMIC_URI=wss://rprotocol.rithmic.com:443

# Rithmic system to connect to
# Available systems: "Rithmic 01", "Rithmic 04 Colo", "Rithmic Paper Trading", 
# "TopstepTrader", "Apex", "TradeFundrr", "MES Capital", "TheTradingPit", 
# "FundedFuturesNetwork", "PropShopTrader", "4PropTrader", "FastTrackTrading", 
# "DayTraders.com", "10XFutures", "LucidTrading", "ThriveTrading", 
# "LegendsTrading", "Earn2Trade", "P.T-t", "YPF-t"
RITHMIC_SYSTEM=Rithmic Paper Trading

# Gateway location for connection
# Available gateways: "Chicago Area", "New York Area", "London Area", etc.
RITHMIC_GATEWAY=Chicago Area
RITHMIC_USER=PP-013155
RITHMIC_PASSWORD=b7neA8k6JA

# ==============================================================================
# APPLICATION SETTINGS (from main .env)
# ==============================================================================
RITHMIC_APP_NAME=PythonRithmicClient
RITHMIC_APP_VERSION=1.0.0

# Protocol template version for API compatibility
# Current version: "3.9" (use latest stable version for best compatibility)
RITHMIC_TEMPLATE_VERSION=3.9

# ==============================================================================
# SYMBOL SEARCH CONFIGURATION
# ==============================================================================
# Pattern to search for symbols (supports wildcards)
# Examples: ES*, NQ*, RTY*, *Z5 (all CME E-mini futures)
SEARCH_PATTERN=ES*

# Exchange for symbol search
# Primary supported exchange: CME (Chicago Mercantile Exchange)
# Other exchanges (NYMEX, CBOT, COMEX) may require additional account permissions
# Leave empty for all exchanges
SEARCH_EXCHANGE=CME

# Instrument type for symbol search
# Available instrument types:
# 1=FUTURE, 2=FUTURE_OPTION, 3=FUTURE_STRATEGY, 4=EQUITY, 5=EQUITY_OPTION,
# 6=EQUITY_STRATEGY, 7=INDEX, 8=INDEX_OPTION, 9=SPREAD, 10=SYNTHETIC
SEARCH_INSTRUMENT_TYPE=1

# Product code for symbol search
# Available product codes for CME: ES, NQ, RTY (E-mini futures)
# Other CME products (YM) may have limited availability
# Energy/metals (CL, NG, GC, SI) require NYMEX/COMEX access
# Treasuries (ZN, ZB, ZF, ZT) require CBOT access  
# Equities (AAPL, MSFT, etc.) and crypto (BTC, ETH) are not supported
# Leave empty for all product codes
SEARCH_PRODUCT_CODE=ES

# Search pattern matching type
# Available patterns: 1=EQUALS (exact match), 2=CONTAINS (partial match, default for easier symbol discovery)
SEARCH_PATTERN_TYPE=2

# ==============================================================================
# REAL-TIME MARKET DATA CONFIGURATION  
# ==============================================================================
# Symbol for real-time market data subscriptions
# Examples: ESZ5, NQZ5, RTYZ5 (all CME E-mini futures)
SYMBOL=ESZ5

# Exchange for real-time market data
# Primary supported exchange: CME (Chicago Mercantile Exchange)
# Other exchanges (NYMEX, CBOT, COMEX) may require additional account permissions
EXCHANGE=CME

# ==============================================================================
# HISTORICAL DATA CONFIGURATION
# ==============================================================================
# Symbol for historical data requests
# Examples: ESZ5, NQZ5, RTYZ5 (all CME E-mini futures)
HIST_SYMBOL=ESZ5

# Exchange for historical data requests
# Primary supported exchange: CME (Chicago Mercantile Exchange)
# Other exchanges (NYMEX, CBOT, COMEX) may require additional account permissions
HIST_EXCHANGE=CME

# ==============================================================================
# EXCHANGE AND PRODUCT SUPPORT MATRIX (Based on Paper Trading Account Testing)
# ==============================================================================
# 
# ✅ FULLY SUPPORTED:
# - CME: ES (S&P 500), NQ (NASDAQ 100), RTY (Russell 2000) E-mini futures
#
# ⚠️  PERMISSION REQUIRED:
# - NYMEX: Energy futures (CL, NG, etc.) - requires additional account permissions
# - CBOT: Treasury futures (ZN, ZB, ZF, ZT) - requires additional account permissions  
# - COMEX: Metals futures (GC, SI, HG) - requires additional account permissions
#
# NOTE: Full production accounts may have access to additional exchanges and products.
# Paper trading accounts typically have access only to CME E-mini equity index futures.

# ==============================================================================
# BAR_TYPE_SPECIFIER - Defines aggregation period/threshold for historical bars
# ==============================================================================
# 
# TIME BARS: Aggregates data by time intervals
# Range: 1-10080 minutes, Default: 1, Purpose: time-based OHLCV aggregation
# Examples: 1=1min, 5=5min, 60=1hour, 1440=daily
#
# TICK BARS: Aggregates data by number of trades
# Range: 1-1000 trades, Default: 1, Purpose: trade-count based aggregation
# Examples: 1=every trade, 100=every 100 trades, 500=high activity
#
# VOLUME BARS: Aggregates data by contracts/shares traded (use VOLUME_BAR_SPECIFIER)
# Range: 100-25000 contracts, Default: 500, Purpose: volume-based aggregation
#
# RANGE BARS: Aggregates data by price movement (use RANGE_BAR_SPECIFIER)
# Range: 1-32 ticks, Default: 4, Purpose: price-movement based aggregation
# Note: ES futures move in 0.25 point increments (1 tick = 0.25 points)
#
BAR_TYPE_SPECIFIER=1

# ==============================================================================
# DEDICATED BAR TYPE SPECIFIERS (Optimized for specific bar types)
# ==============================================================================

# VOLUME BAR SPECIFIER - For historical_volume_bars.py
# Defines volume threshold needed to create one volume bar
# ES futures typical values: 100-1000 contracts per bar
# Higher values = fewer, more significant bars
# Lower values = more frequent bars (but may get "no data" during low volume periods)
VOLUME_BAR_SPECIFIER=500

# RANGE BAR SPECIFIER - For historical_range_bars.py  
# Defines price movement threshold needed to create one range bar
# ES futures move in 0.25 point increments (1 tick = 0.25 points)
# Common values: 4 ticks (1 point), 8 ticks (2 points), 16 ticks (4 points)
# Higher values = fewer, more significant price movement bars
# Lower values = more frequent bars (but may get "no data" during low volatility periods)
RANGE_BAR_SPECIFIER=4

# ==============================================================================
# HISTORICAL DATA DATE RANGES (Active Trading Periods)
# ==============================================================================

# Primary date range: Recent active trading day (avoid holidays/weekends)
# Using recent weekday with confirmed market activity
# Format: Unix timestamp (seconds since 1970-01-01 00:00:00 UTC)

# June 28, 2024 00:00:00 UTC (Friday) - Active trading day
HIST_START_INDEX=1719532800
# June 28, 2024 23:59:59 UTC (Friday) - End of same trading day  
HIST_FINISH_INDEX=1719619199

# Fallback date ranges for testing different periods
# May 15, 2024 (Wednesday) - Known active trading day
HIST_START_INDEX_ALT1=1715731200
HIST_FINISH_INDEX_ALT1=1715817599

# April 10, 2024 (Wednesday) - Another active trading day
HIST_START_INDEX_ALT2=1712707200  
HIST_FINISH_INDEX_ALT2=1712793599

# ==============================================================================
# TROUBLESHOOTING GUIDE FOR "NO DATA" RESPONSES
# ==============================================================================
# 
# QUICK FIXES:
# 1. Use active trading dates (avoid weekends/holidays)
# 2. Increase bar specifiers: VOLUME_BAR_SPECIFIER (500+), RANGE_BAR_SPECIFIER (4+)
# 3. Use CME E-mini futures (ES, NQ, RTY) with front-month contracts
# 4. Test with ALT1/ALT2 date ranges if primary dates fail
# 5. Start with tick bars to verify basic connectivity