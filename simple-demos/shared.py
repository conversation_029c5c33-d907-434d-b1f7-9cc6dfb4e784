#!/usr/bin/env python3
"""
Shared authentication and connection utilities for Rithmic API demo scripts.
"""

import asyncio
import pathlib
import ssl
import sys
import websockets
import os
from dotenv import load_dotenv

# Add proto_generated directory to path for protobuf imports
# Calculate path relative to this file's location
import os
script_dir = os.path.dirname(os.path.abspath(__file__))
proto_path = os.path.join(os.path.dirname(script_dir), 'proto_generated')
sys.path.append(proto_path)

import base_pb2
import request_rithmic_system_info_pb2
import response_rithmic_system_info_pb2
import request_login_pb2
import response_login_pb2
import request_heartbeat_pb2
import response_heartbeat_pb2
import request_logout_pb2

# Load configuration
load_dotenv('.env.simple-demos')

# Comprehensive Template ID mapping from Reference Guide
TEMPLATE_ID_MAP = {
    # 1.1 Templates Shared across Infrastructure Plants
    10: ("Login Request", "From Client"),
    11: ("Login Response", "From Server"),
    12: ("Logout Request", "From Client"), 
    13: ("Logout Response", "From Server"),
    14: ("Reference Data Request", "From Client"),
    15: ("Reference Data Response", "From Server"),
    16: ("Rithmic System Info Request", "From Client"),
    17: ("Rithmic System Info Response", "From Server"),
    18: ("Request Heartbeat", "From Client"),
    19: ("Response Heartbeat", "From Server"),
    20: ("Rithmic System Gateway Info Request", "From Client"),
    21: ("Rithmic System Gateway Info Response", "From Server"),
    75: ("Reject", "From Server"),
    76: ("User Account Update", "From Server"),
    77: ("Forced Logout", "From Server"),
    
    # 1.2 Templates Specific to Market Data Infrastructure
    100: ("Market Data Update Request", "From Client"),
    101: ("Market Data Update Response", "From Server"),
    102: ("Get Instrument by Underlying Request", "From Client"),
    103: ("Get Instrument by Underlying Response", "From Server"),
    104: ("Get Instrument by Underlying Keys Response", "From Server"),
    105: ("Market Data Update by Underlying Request", "From Client"),
    106: ("Market Data Update by Underlying Response", "From Server"),
    107: ("Give Tick Size Type Table Request", "From Client"),
    108: ("Give Tick Size Type Table Response", "From Server"),
    109: ("Search Symbols Request", "From Client"),
    110: ("Search Symbols Response", "From Server"),
    111: ("Product Codes Request", "From Client"),
    112: ("Product Codes Response", "From Server"),
    113: ("Front Month Contract Request", "From Client"),
    114: ("Front Month Contract Response", "From Server"),
    115: ("Depth By Order Snapshot Request", "From Client"),
    116: ("Depth By Order Snapshot Response", "From Server"),
    117: ("Depth By Order Updates Request", "From Client"),
    118: ("Depth By Order Updates Response", "From Server"),
    119: ("Get Volume At Price Request", "From Client"),
    120: ("Get Volume At Price Response", "From Server"),
    121: ("Auxilliary Reference Data Request", "From Client"),
    122: ("Auxilliary Reference Data Response", "From Server"),
    150: ("Last Trade", "From Server"),
    151: ("Best Bid Offer", "From Server"),
    152: ("Trade Statistics", "From Server"),
    153: ("Quote Statistics", "From Server"),
    154: ("Indicator Prices", "From Server"),
    155: ("End Of Day Prices", "From Server"),
    156: ("Order Book", "From Server"),
    157: ("Market Mode", "From Server"),
    158: ("Open Interest", "From Server"),
    159: ("Front Month Contract Update", "From Server"),
    160: ("Depth By Order", "From Server"),
    161: ("Depth By Order End Event", "From Server"),
    162: ("Symbol Margin Rate", "From Server"),
    163: ("Order Price Limits", "From Server"),
    
    # 1.4 Templates Specific to History Plant Infrastructure
    200: ("Time Bar Update Request", "From Client"),
    201: ("Time Bar Update Response", "From Server"),
    202: ("Time Bar Replay Request", "From Client"),
    203: ("Time Bar Replay Response", "From Server"),
    204: ("Tick Bar Update Request", "From Client"),
    205: ("Tick Bar Update Response", "From Server"),
    206: ("Tick Bar Replay Request", "From Client"),
    207: ("Tick Bar Replay Response", "From Server"),
    208: ("Volume Profile Minute Bars Request", "From Client"),
    209: ("Volume Profile Minute Bars Response", "From Server"),
    210: ("Resume Bars Request", "From Client"),
    211: ("Resume Bars Response", "From Server"),
    250: ("Time Bar", "From Server"),
    251: ("Tick Bar", "From Server"),
    
    # 1.3 Templates Specific to Order Plant Infrastructure
    300: ("Login Info Request", "From Client"),
    301: ("Login Info Response", "From Server"),
    302: ("Account List Request", "From Client"),
    303: ("Account List Response", "From Server"),
    304: ("Account RMS Info Request", "From Client"),
    305: ("Account RMS Info Response", "From Server"),
    306: ("Product RMS Info Request", "From Client"),
    307: ("Product RMS Info Response", "From Server"),
    308: ("Subscribe For Order Updates Request", "From Client"),
    309: ("Subscribe For Order Updates Response", "From Server"),
    310: ("Trade Routes Request", "From Client"),
    311: ("Trade Routes Response", "From Server"),
    312: ("New Order Request", "From Client"),
    313: ("New Order Response", "From Server"),
    314: ("Modify Order Request", "From Client"),
    315: ("Modify Order Response", "From Server"),
    316: ("Cancel Order Request", "From Client"),
    317: ("Cancel Order Response", "From Server"),
    318: ("Show Order History Dates Request", "From Client"),
    319: ("Show Order History Dates Response", "From Server"),
    320: ("Show Orders Request", "From Client"),
    321: ("Show Orders Response", "From Server"),
    322: ("Show Order History Request", "From Client"),
    323: ("Show Order History Response", "From Server"),
    324: ("Show Order History Summary Request", "From Client"),
    325: ("Show Order History Summary Response", "From Server"),
    326: ("Show Order History Detail Request", "From Client"),
    327: ("Show Order History Detail Response", "From Server"),
    328: ("OCO Order Request", "From Client"),
    329: ("OCO Order Response", "From Server"),
    330: ("Bracket Order Request", "From Client"),
    331: ("Bracket Order Response", "From Server"),
    350: ("Trade Route", "From Server"),
    351: ("Rithmic Order Notification", "From Server"),
    352: ("Exchange Order Notification", "From Server"),
    353: ("Bracket Updates", "From Server"),
    354: ("Account List Updates", "From Server"),
    355: ("Update Easy To Borrow List", "From Server"),
    356: ("Account RMS Updates", "From Server"),
    
    # 1.5 Templates Specific to PnL Plant
    400: ("PnL Position Updates Request", "From Client"),
    401: ("PnL Position Updates Response", "From Server"),
    402: ("PnL Position Snapshot Request", "From Client"),
    403: ("PnL Position Snapshot Response", "From Server"),
    450: ("Instrument PnL Position Update", "From Server"),
    451: ("Account PnL Position Update", "From Server"),
    
    # 1.6 Templates Specific to Repository Plant
    500: ("List Unaccepted Agreements Request", "From Client"),
    501: ("List Unaccepted Agreements Response", "From Server"),
    502: ("List Accepted Agreements Request", "From Client"),
    503: ("List Accepted Agreements Response", "From Server"),
    504: ("Accept Agreement Request", "From Client"),
    505: ("Accept Agreement Response", "From Server"),
    506: ("Show Agreement Request", "From Client"),
    507: ("Show Agreement Response", "From Server"),
    508: ("Set Rithmic MarketData Self Certification Status Request", "From Client"),
    509: ("Set Rithmic MarketData Self Certification Status Response", "From Server"),
}

def get_template_info(template_id):
    """Get template name and direction for a given template ID."""
    if template_id in TEMPLATE_ID_MAP:
        name, direction = TEMPLATE_ID_MAP[template_id]
        return name, direction
    else:
        return f"Unknown Template {template_id}", "Unknown Direction"

async def connect_to_rithmic():
    """Connect to Rithmic server with SSL (without authentication)."""
    uri = os.getenv('RITHMIC_URI')
    print(f"Attempting to connect to: {uri}")
    
    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    ssl_cert_path = pathlib.Path(os.path.dirname(script_dir)) / 'etc' / 'rithmic_ssl_cert_auth_params'
    
    # Verify SSL certificate exists
    if not ssl_cert_path.exists():
        print(f"ERROR: SSL certificate not found at {ssl_cert_path}")
        raise FileNotFoundError(f"SSL certificate missing: {ssl_cert_path}")
    
    print(f"Using SSL certificate: {ssl_cert_path}")
    ssl_context.load_verify_locations(ssl_cert_path)
    
    try:
        ws = await websockets.connect(uri, ssl=ssl_context, ping_interval=3)
        print(f"Connected successfully to {uri}")
        return ws
    except Exception as e:
        print(f"Connection failed: {type(e).__name__}: {e}")
        raise

async def connect_and_authenticate(infra_type=request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT):
    """Connect to Rithmic and authenticate using proper two-step process."""
    # Step A: Get system info  
    systems = await get_system_info()
    if not systems:
        print("Failed to get system information")
        return None
    
    # Verify our configured system is available
    configured_system = os.getenv('RITHMIC_SYSTEM')
    if configured_system not in systems:
        print(f"ERROR: Configured system '{configured_system}' not found in available systems")
        print("Available systems:", systems)
        return None
    
    print(f"System '{configured_system}' found in available systems")
    
    # Step B: Login with gateway  
    return await authenticate_with_gateway(infra_type)

async def get_system_info():
    """Get list of available Rithmic systems (Step A of authentication)."""
    uri = os.getenv('RITHMIC_URI')
    print(f"Step A: Getting system info from {uri}")
    
    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    ssl_cert_path = pathlib.Path(os.path.dirname(script_dir)) / 'etc' / 'rithmic_ssl_cert_auth_params'
    
    if not ssl_cert_path.exists():
        print(f"ERROR: SSL certificate not found at {ssl_cert_path}")
        raise FileNotFoundError(f"SSL certificate missing: {ssl_cert_path}")
    
    ssl_context.load_verify_locations(ssl_cert_path)
    
    try:
        # Step 1a: Open websocket
        ws = await websockets.connect(uri, ssl=ssl_context)
        print("Connected for system info")
        
        # Step 2a: Send RequestRithmicSystemInfo
        sys_req = request_rithmic_system_info_pb2.RequestRithmicSystemInfo()
        sys_req.template_id = 16
        sys_req.user_msg.append("system_info_request")
        
        print("Sending system info request...")
        await ws.send(sys_req.SerializeToString())
        
        # Step 3a: Parse response
        response_data = await asyncio.wait_for(ws.recv(), timeout=5.0)
        base = base_pb2.Base()
        base.ParseFromString(response_data)
        
        if base.template_id == 17:  # ResponseRithmicSystemInfo
            sys_response = response_rithmic_system_info_pb2.ResponseRithmicSystemInfo()
            sys_response.ParseFromString(response_data)
            
            print("Available systems:")
            systems = []
            for system in sys_response.system_name:
                print(f"  - {system}")
                systems.append(system)
            
            # Step 4a: Close websocket (server closes it anyway)
            try:
                await ws.close()
            except:
                pass
                
            return systems
        else:
            print(f"Unexpected response template_id: {base.template_id}")
            return []
            
    except asyncio.TimeoutError:
        print("System info request timeout")
        return []
    except Exception as e:
        print(f"System info error: {type(e).__name__}: {e}")
        return []

async def authenticate_with_gateway(infra_type):
    """Authenticate with Rithmic system including gateway (Step B)."""
    # Check required environment variables
    required_vars = ['RITHMIC_USER', 'RITHMIC_PASSWORD', 'RITHMIC_SYSTEM', 
                    'RITHMIC_GATEWAY', 'RITHMIC_APP_NAME', 'RITHMIC_APP_VERSION', 
                    'RITHMIC_TEMPLATE_VERSION']
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            print(f"ERROR: Missing required environment variable: {var}")
            return None
    
    uri = os.getenv('RITHMIC_URI')
    print(f"\nStep B: Logging in to {uri}")
    
    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    ssl_cert_path = pathlib.Path(os.path.dirname(script_dir)) / 'etc' / 'rithmic_ssl_cert_auth_params'
    ssl_context.load_verify_locations(ssl_cert_path)
    
    try:
        # Step 1b: Open new websocket
        ws = await websockets.connect(uri, ssl=ssl_context, ping_interval=3)
        print("Connected for login")
        
        # Step 2b: Send RequestLogin with credentials
        login_req = request_login_pb2.RequestLogin()
        login_req.template_id = 10
        login_req.template_version = os.getenv('RITHMIC_TEMPLATE_VERSION')
        login_req.user_msg.append("hello")
        login_req.user = os.getenv('RITHMIC_USER')
        login_req.password = os.getenv('RITHMIC_PASSWORD')
        login_req.app_name = os.getenv('RITHMIC_APP_NAME')
        login_req.app_version = os.getenv('RITHMIC_APP_VERSION')
        login_req.system_name = os.getenv('RITHMIC_SYSTEM')
        login_req.infra_type = infra_type
        
        # Note: gateway is specified in environment but not directly in login message
        gateway = os.getenv('RITHMIC_GATEWAY')
        print(f"Authenticating:")
        print(f"  System: {login_req.system_name}")
        print(f"  Gateway: {gateway}")
        print(f"  User: {login_req.user}")
        print(f"  Infrastructure: {infra_type}")
        
        print("\nSending login request...")
        await ws.send(login_req.SerializeToString())
        
        # Wait for response
        print("Waiting for login response...")
        response_data = await asyncio.wait_for(ws.recv(), timeout=30.0)
        
        # Parse response
        base = base_pb2.Base()
        base.ParseFromString(response_data)
        print(f"Received response with template_id: {base.template_id}")
        
        if base.template_id == 11:  # ResponseLogin
            login_response = response_login_pb2.ResponseLogin()
            login_response.ParseFromString(response_data)
            
            print(f"Login response:")
            print(f"  rp_code: {login_response.rp_code}")
            if hasattr(login_response, 'text') and login_response.text:
                print(f"  text: {login_response.text}")
            
            # Check authentication success
            if login_response.rp_code and login_response.rp_code[0] == "0":
                print("\nAuthentication successful!")
                return ws  # Return the connected websocket
            else:
                error_msg = login_response.rp_code[0] if login_response.rp_code else "Unknown error"
                error_text = ""
                if hasattr(login_response, 'text') and login_response.text:
                    error_text = login_response.text[0]
                print(f"\nAuthentication failed with code: {error_msg}")
                if error_text:
                    print(f"Error message: {error_text}")
                await ws.close()
                return None
        else:
            print(f"Unexpected response template_id: {base.template_id}")
            decode_unknown_message(base.template_id, response_data)
            await ws.close()
            return None
            
    except asyncio.TimeoutError:
        print("\nAuthentication timeout - no response received within 30 seconds")
        return None
    except Exception as e:
        print(f"\nAuthentication error: {type(e).__name__}: {e}")
        return None

async def authenticate_full():
    """Complete two-step authentication process."""
    print("Starting Rithmic authentication process...")
    
    # Step A: Get system info
    systems = await get_system_info()
    if not systems:
        print("Failed to get system information")
        return None
    
    # Verify our configured system is available
    configured_system = os.getenv('RITHMIC_SYSTEM')
    if configured_system not in systems:
        print(f"ERROR: Configured system '{configured_system}' not found in available systems")
        print("Available systems:", systems)
        return None
    
    print(f"\nSystem '{configured_system}' found in available systems")
    
    # Step B: Login with gateway
    return await authenticate_with_gateway(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)

async def authenticate(ws, infra_type):
    """Legacy authenticate function - now redirects to new process."""
    print("Note: Using legacy authenticate function. Consider using authenticate_full() directly.")
    
    # Close the provided websocket since we need fresh connections
    try:
        await ws.close()
    except:
        pass
    
    # Use the new authentication process
    new_ws = await authenticate_with_gateway(infra_type)
    return new_ws is not None

def hex_dump(data, bytes_per_line=16):
    """Display binary data in readable hex format."""
    lines = []
    for i in range(0, len(data), bytes_per_line):
        chunk = data[i:i + bytes_per_line]
        hex_part = ' '.join(f'{b:02x}' for b in chunk)
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        lines.append(f'{i:08x}: {hex_part:<{bytes_per_line*3}} {ascii_part}')
    return '\n'.join(lines)

def decode_protobuf_wire_format(data):
    """Decode protobuf wire format to extract field values generically."""
    fields = {}
    pos = 0
    
    while pos < len(data):
        try:
            # Read varint for field number and wire type
            tag, pos = read_varint(data, pos)
            field_number = tag >> 3
            wire_type = tag & 0x7
            
            if wire_type == 0:  # Varint
                value, pos = read_varint(data, pos)
                fields[field_number] = f"varint({value})"
            elif wire_type == 1:  # 64-bit
                if pos + 8 <= len(data):
                    value = int.from_bytes(data[pos:pos+8], 'little')
                    pos += 8
                    fields[field_number] = f"fixed64({value})"
                else:
                    break
            elif wire_type == 2:  # Length-delimited
                length, pos = read_varint(data, pos)
                if pos + length <= len(data):
                    value = data[pos:pos+length]
                    pos += length
                    # Try to decode as UTF-8 string
                    try:
                        string_value = value.decode('utf-8')
                        fields[field_number] = f"string(\"{string_value}\")"
                    except UnicodeDecodeError:
                        fields[field_number] = f"bytes({len(value)} bytes)"
                else:
                    break
            elif wire_type == 5:  # 32-bit
                if pos + 4 <= len(data):
                    value = int.from_bytes(data[pos:pos+4], 'little')
                    pos += 4
                    fields[field_number] = f"fixed32({value})"
                else:
                    break
            else:
                # Unknown wire type, skip
                break
        except Exception:
            break
    
    return fields

def read_varint(data, pos):
    """Read a varint from the data starting at pos."""
    result = 0
    shift = 0
    while pos < len(data):
        byte = data[pos]
        pos += 1
        result |= (byte & 0x7F) << shift
        if (byte & 0x80) == 0:
            break
        shift += 7
        if shift >= 64:
            raise ValueError("Varint too long")
    return result, pos

def decode_unknown_message(template_id, raw_data):
    """Decode and display protobuf messages with comprehensive template information."""
    # Get template information
    template_name, direction = get_template_info(template_id)
    
    print(f"Received message:")
    print(f"  Template ID: {template_id}")
    print(f"  Template Name: {template_name}")
    print(f"  Direction: {direction}")
    
    # Special handling for common messages
    if template_id == 19:  # Response Heartbeat
        print("  Message Type: Heartbeat response from server (connection keep-alive)")
    elif template_id == 151:  # Best Bid Offer
        print("  Message Type: Real-time best bid/offer market data")
    elif template_id == 150:  # Last Trade
        print("  Message Type: Real-time last trade market data")
    elif template_id == 101:  # Market Data Update Response
        print("  Message Type: Subscription confirmation/response")
    elif template_id in [201, 207]:  # Historical bar responses
        print("  Message Type: Historical bar data response")
    
    # Decode protobuf fields
    try:
        fields = decode_protobuf_wire_format(raw_data)
        if fields:
            print("  Decoded fields:")
            for field_num, value in sorted(fields.items()):
                print(f"    Field {field_num}: {value}")
        else:
            print("  No decodable protobuf fields found")
    except Exception as e:
        print(f"  Protobuf decode error: {e}")
    
    # Show hex dump for debugging (only for truly unknown messages)
    if template_id not in TEMPLATE_ID_MAP:
        print("  Hex dump (unknown template):")
        hex_output = hex_dump(raw_data)
        for line in hex_output.split('\n'):
            print(f"    {line}")
        print(f"  Raw bytes: {raw_data}")
    
    print()

async def send_heartbeat(ws):
    """Send heartbeat to maintain connection."""
    heartbeat_req = request_heartbeat_pb2.RequestHeartbeat()
    heartbeat_req.template_id = 18
    await ws.send(heartbeat_req.SerializeToString())

async def disconnect(ws):
    """Clean disconnect from Rithmic."""
    logout_req = request_logout_pb2.RequestLogout()
    logout_req.template_id = 12
    logout_req.user_msg.append("logout_request")
    
    await ws.send(logout_req.SerializeToString())
    await ws.close(1000, "goodbye")
    print("Disconnected from Rithmic")