# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **Rithmic R | Protocol API SDK** (v0.84.0.0) - a financial trading API for real-time market data, order management, and historical data access. The API uses WebSocket connections with Google Protocol Buffers for binary message serialization.

## Development Commands

### JavaScript/Node.js Development
```bash
# Navigate to samples directory
cd samples/samples.js/

# Install dependencies
npm install protobufjs ws

# Run sample applications
node SampleMD.js          # Market data subscription
node SampleBar.js         # Historical data bars  
node SampleOrder.js       # Order management
```

### Python Development
```bash
# Navigate to Python samples
cd samples/samples.py/

# Install dependencies (system-wide or in virtual environment)
pip install websockets protobuf

# Run sample applications
python3 SampleMD.py       # Market data subscription
python3 SampleBar.py      # Historical data
python3 SampleOrder.py    # Order management
```

### Protocol Buffer Compilation
```bash
# For JavaScript (runtime compilation used in samples)
# No compilation needed - uses protobufjs runtime loading

# For Python (pre-compiled files included, but to regenerate):
protoc --python_out=. proto/*.proto
```

## Architecture Overview

### Infrastructure Plants
The API is organized into separate infrastructure plants, each handling different aspects:

- **Ticker Plant**: Real-time market data (Level 1 & Level 3 depth-by-order)
- **Order Plant**: Order management, account info, position tracking
- **History Plant**: Historical time bars and tick bars
- **PnL Plant**: Profit/loss position updates
- **Repository Plant**: User agreements and permissions

### Authentication Flow
1. **System Discovery**: Send `RequestRithmicSystemInfo` (Template 16) to get available systems
2. **Login**: Send `RequestLogin` (Template 10) with credentials and selected system

### Core Components

**Protocol Definitions** (`proto/` directory):
- 130+ `.proto` files defining complete API surface
- Template-based messaging system (Templates 10-3509)
- Message types for requests, responses, and streaming updates

**Sample Applications** (`samples/` directory):
- Working examples in JavaScript and Python
- Demonstrate authentication, market data subscription, and order management
- Use these as reference implementations

**SSL Configuration** (`etc/rithmic_ssl_cert_auth_params`):
- Required SSL certificate for secure connections
- USERTrust RSA Certification Authority certificate

## Key Implementation Details

### Message Structure
- All messages use Google Protocol Buffers binary format
- Server uses Big Endian format
- Each message has a Template ID (10-3509) identifying message type
- Heartbeat mechanism required to maintain connection

### Connection Management
- WebSocket-based real-time communication
- Multiple concurrent connections supported (one per plant type)
- Heartbeat interval specified in login response
- Automatic reconnection logic recommended

### Market Data Templates
Key templates for market data operations:
- Template 100/101: Market Data Update Request/Response
- Template 113/114: Front Month Contract Request/Response
- Template 115/116: Depth By Order Snapshot Request/Response
- Template 117/118: Depth By Order Updates Request/Response
- Template 150: Last Trade updates
- Template 151: Best Bid/Offer updates
- Template 160: Depth By Order updates

### Error Handling
- Check `rp_code` field in responses: "0" = success, non-zero = error
- Responses may span multiple messages - check `rq_hndlr_rp_code` vs `rp_code`
- Implement retry logic for connection failures
- Handle heartbeat timeouts gracefully

## Development Notes

### Working with Protocol Buffers
- JavaScript: Uses `protobufjs` library with runtime loading
- Python: Uses pre-compiled `*_pb2.py` files
- All `.proto` files are located in `proto/` directory
- Message definitions include extensive field documentation

### Testing
- No formal test framework - use sample applications as functional tests
- Test against paper trading environment first
- Verify SSL certificate path and permissions

### File Organization
- Keep generated files separate from source
- Use timestamped filenames for data collection
- Log all API interactions for debugging
- Store raw responses for later analysis

## Web Dashboard Interface

The modern web-based dashboard provides complete functionality for managing the Rithmic API project. 

**Quick Start:**
```bash
cd web_dashboard/
pip install -r requirements.txt
python main.py
# Access at http://localhost:8000
```

See `web_dashboard/README.md` for complete documentation.

## Implementation Credentials (Paper Trading)
From `docs/archive/implementation-plan.md` - use for testing:
- **User**: "PP-013155"
- **Password**: "b7neA8k6JA"  
- **System**: "Rithmic Paper Trading"
- **Gateway**: "Chicago Area"

## Common Development Patterns

### Market Data Subscription
1. Login to Ticker Plant infrastructure
2. Subscribe to updates using Template 100
3. Handle streaming updates (Templates 150, 151, 160, etc.)
4. Maintain connection with heartbeats

### Order Management
1. Login to Order Plant infrastructure  
2. Get account list and RMS info
3. Subscribe to order updates
4. Send order requests (Templates 312, 314, 316)

### Historical Data
1. Login to History Plant infrastructure
2. Request time bars (Template 200) or tick bars (Template 204)
3. Handle bar updates and replay responses
4. Check for data truncation in large requests