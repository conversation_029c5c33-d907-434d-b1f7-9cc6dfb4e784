#!/usr/bin/env python3
"""
Unique Comprehensive System Test

Complete test of all 17 models with unique field names to avoid duplicate key errors.
"""

import sys
import time
import random
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
import os
env_file = project_root / '.env'
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()

from src.rithmic_api.config import config
from src.database.models import (
    Symbol, SymbolDAO,
    BestBidOffer, BestBidOfferDAO,
    LastTrade, LastTradeDAO,
    UserSession, UserSessionDAO,
    DepthByOrderSnapshot, DepthByOrderSnapshotDAO,
    DepthByOrderUpdate, DepthByOrderUpdateDAO,
    OrderBookLevel, OrderBookLevelDAO,
    TimeBar, TimeBarDAO,
    TickBar, TickBarDAO,
    OrderNotification, OrderNotificationDAO,
    OrderFill, OrderFillDAO,
    Position, PositionDAO,
    AccountInfo, AccountInfoDAO,
    SystemEvent, SystemEventDAO,
    TradeStatistics, TradeStatisticsDAO,
    QuoteStatistics, QuoteStatisticsDAO,
    Heartbeat, HeartbeatDAO,
    create_all_tables
)

def generate_unique_id():
    """Generate a unique identifier based on timestamp and random number."""
    return f"{int(time.time())}{random.randint(1000, 9999)}"

def test_unique_comprehensive():
    """Comprehensive test with unique data to avoid duplicate key errors."""
    print("🎯 Unique Comprehensive System Test")
    print("=" * 60)
    
    # Create all tables
    print("\n📊 Creating all tables...")
    try:
        create_all_tables()
        print("  ✅ All tables created successfully")
    except Exception as e:
        print(f"  ❌ Table creation failed: {e}")
        return 0

    # Generate unique identifiers for this test run
    unique_id = generate_unique_id()
    timestamp_now = datetime.now()
    
    # Test all 17 models with unique field names
    models_to_test = [
        # 1. Symbol
        (Symbol, SymbolDAO, "Symbol", {
            'symbol': f'UNIQUE_{unique_id}', 'exchange': 'CME', 'symbol_name': f'Unique Test Contract {unique_id}'
        }),
        
        # 2. BestBidOffer
        (BestBidOffer, BestBidOfferDAO, "BestBidOffer", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'bid_price': Decimal('4500.25'), 'bid_size': 10,
            'ask_price': Decimal('4500.50'), 'ask_size': 15,
            'ssboe': **********, 'usecs': 123456
        }),
        
        # 3. LastTrade
        (LastTrade, LastTradeDAO, "LastTrade", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'trade_price': Decimal('4500.25'), 'trade_size': 5,
            'volume': 1000, 'ssboe': **********, 'usecs': 123456
        }),
        
        # 4. UserSession
        (UserSession, UserSessionDAO, "UserSession", {
            'unique_user_id': f'TEST-USER-{unique_id}',
            'fcm_id': 'FCM001',
            'ib_id': 'IB001',
            'login_timestamp': timestamp_now,
            'heartbeat_interval': Decimal('5.0')
        }),
        
        # 5. DepthByOrderSnapshot
        (DepthByOrderSnapshot, DepthByOrderSnapshotDAO, "DepthByOrderSnapshot", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'depth_side': 'BUY', 'depth_price': Decimal('4500.25'),
            'depth_size': 10, 'sequence_number': 12345 + int(unique_id),
            'ssboe': **********, 'usecs': 123456
        }),
        
        # 6. DepthByOrderUpdate
        (DepthByOrderUpdate, DepthByOrderUpdateDAO, "DepthByOrderUpdate", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'depth_side': 'SELL', 'depth_price': Decimal('4500.50'),
            'depth_size': 8, 'update_type': 'ADD',
            'sequence_number': 12346 + int(unique_id), 'ssboe': **********, 'usecs': 123456
        }),
        
        # 7. OrderBookLevel
        (OrderBookLevel, OrderBookLevelDAO, "OrderBookLevel", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'side': 'BUY', 'price_level': Decimal(f'4500.{random.randint(10, 99)}'),
            'total_size': 25, 'order_count': 3,
            'ssboe': **********, 'usecs': 123456
        }),
        
        # 8. TimeBar
        (TimeBar, TimeBarDAO, "TimeBar", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'bar_type': '1min', 'period': '1',
            'open_price': Decimal('4500.00'), 'high_price': Decimal('4502.00'),
            'low_price': Decimal('4499.50'), 'close_price': Decimal('4501.25'),
            'volume': 1500, 'bar_timestamp': timestamp_now
        }),
        
        # 9. TickBar
        (TickBar, TickBarDAO, "TickBar", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'bar_type': 'TICK_BAR', 'bar_sub_type': 'REGULAR',
            'open_price': Decimal('4500.00'), 'high_price': Decimal('4502.00'),
            'low_price': Decimal('4499.50'), 'close_price': Decimal('4501.25'),
            'volume': 500, 'bar_timestamp': timestamp_now
        }),
        
        # 10. OrderNotification
        (OrderNotification, OrderNotificationDAO, "OrderNotification", {
            'notify_type': 'ORDER_FILL', 'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'account_id': f'TEST-ACCT-{unique_id}', 'quantity': 1,
            'price': Decimal('4500.25'), 'transaction_type': 'BUY',
            'ssboe': **********, 'usecs': 123456
        }),
        
        # 11. OrderFill
        (OrderFill, OrderFillDAO, "OrderFill", {
            'fill_id': f'FILL{unique_id}', 'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'account_id': f'TEST-ACCT-{unique_id}', 'fill_price': Decimal('4500.25'),
            'fill_quantity': 1, 'fill_side': 'BUY'
        }),
        
        # 12. Position
        (Position, PositionDAO, "Position", {
            'account_id': f'TEST-ACCT-{unique_id}', 'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'net_position': 5, 'avg_open_price': Decimal('4500.00'),
            'unrealized_pnl': Decimal('125.00'), 'realized_pnl': Decimal('0.00')
        }),
        
        # 13. AccountInfo
        (AccountInfo, AccountInfoDAO, "AccountInfo", {
            'account_id': f'TEST-ACCT-{unique_id}', 'account_name': f'Test Account {unique_id}',
            'fcm_id': 'FCM001', 'ib_id': 'IB001',
            'currency': 'USD', 'total_cash_balance': Decimal('50000.00')
        }),
        
        # 14. SystemEvent
        (SystemEvent, SystemEventDAO, "SystemEvent", {
            'event_type': 'LOGIN', 'user_id': f'TEST-USER-{unique_id}',
            'fcm_id': 'FCM001', 'ib_id': 'IB001',
            'system_name': f'Test System {unique_id}', 'gateway_name': f'Test Gateway {unique_id}',
            'infra_type': 'TICKER_PLANT',
            'event_status': 'SUCCESS', 'event_message': 'Login successful',
            'event_timestamp': timestamp_now
        }),
        
        # 15. TradeStatistics
        (TradeStatistics, TradeStatisticsDAO, "TradeStatistics", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'total_volume': 10000, 'trade_count': 250,
            'vwap': Decimal('4500.50'), 'high_trade_price': Decimal('4505.00'),
            'low_trade_price': Decimal('4495.00'), 'last_trade_price': Decimal('4500.00')
        }),
        
        # 16. QuoteStatistics
        (QuoteStatistics, QuoteStatisticsDAO, "QuoteStatistics", {
            'symbol': f'ES_{unique_id}', 'exchange': 'CME',
            'total_bid_size': 5000, 'total_ask_size': 4800,
            'bid_quote_count': 125, 'ask_quote_count': 120,
            'avg_bid_size': Decimal('40.0'), 'avg_ask_size': Decimal('40.0')
        }),
        
        # 17. Heartbeat
        (Heartbeat, HeartbeatDAO, "Heartbeat", {
            'user_id': f'TEST-USER-{unique_id}', 'session_id': f'sess_{unique_id}',
            'infra_type': 'TICKER_PLANT',
            'heartbeat_interval': Decimal('5.0'),
            'client_heartbeat_timestamp': timestamp_now,
            'server_heartbeat_timestamp': timestamp_now,
            'rtt_ms': Decimal('12.5')
        })
    ]
    
    successful = 0
    failed = 0
    
    print(f"\n🔍 Testing {len(models_to_test)} models with unique data...")
    
    for model_class, dao_class, model_name, test_data in models_to_test:
        try:
            print(f"\n  📋 Testing {model_name}...")
            
            # Create model instance
            model_instance = model_class(**test_data)
            print(f"    ✅ Model creation: Success")
            
            # Create DAO and test insert
            dao = dao_class()
            insert_id = dao.insert(model_instance)
            print(f"    ✅ Database insert: ID {insert_id}")
            
            # Test find by ID if available
            if hasattr(dao, 'find_by_id'):
                found = dao.find_by_id(insert_id)
                if found:
                    print(f"    ✅ Find by ID: Found")
                else:
                    print(f"    ⚠️ Find by ID: Not found")
            
            successful += 1
            
        except Exception as e:
            print(f"    ❌ Failed: {e}")
            failed += 1
    
    # Final results
    total = successful + failed
    coverage = (successful / total) * 100 if total > 0 else 0
    
    print(f"\n📊 Final Results:")
    print(f"  ✅ Successful: {successful}/{total} models")
    print(f"  ❌ Failed: {failed}/{total} models")
    print(f"  📈 Coverage: {coverage:.1f}%")
    
    if coverage == 100:
        print(f"\n🎉 PERFECT! All 17 models working!")
        print(f"🚀 System ready for production!")
    elif coverage >= 90:
        print(f"\n✅ Excellent! Almost all models working.")
    elif coverage >= 80:
        print(f"\n✅ Very good! Most models working.")
    else:
        print(f"\n⚠️ More work needed to reach full coverage.")
    
    return coverage

if __name__ == "__main__":
    coverage = test_unique_comprehensive()
    
    # Mark completion
    if coverage == 100:
        print(f"\n📝 SUCCESS! Achieved 100% model coverage!")
        print(f"All known issues have been resolved.")
    else:
        print(f"\n📝 Test completed: {coverage:.1f}% coverage achieved")