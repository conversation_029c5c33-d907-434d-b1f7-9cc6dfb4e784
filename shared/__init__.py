"""
Shared Components for Rithmic API Integration

This package contains reusable components extracted from simple-demos
and used across all Rithmic API scripts for unified functionality.

Modules:
- connection: WebSocket connection and authentication management
- contracts: Contract discovery and metadata handling  
- market_data: Market data subscription and processing
- database: Database persistence and caching
- config: Configuration management and environment loading

Key Features:
- Fixes contract discovery issue (finds actual monthly contracts like ESU24, ESZ24)
- Environment-based configuration (no hardcoded values)
- Server-provided heartbeat intervals
- Unified error handling and resource management
- Multiple persistence modes for different performance requirements
- Comprehensive contract search for ALL available ES futures
"""

from . import config
from . import connection
from . import contracts
from . import market_data
from . import database

__version__ = "2.0.0"
__all__ = ["config", "connection", "contracts", "market_data", "database"]