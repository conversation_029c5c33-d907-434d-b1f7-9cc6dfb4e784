#!/usr/bin/env python3

"""
PROFESSIONAL FUTURES DATA COLLECTION GUI CONTROLLER
====================================================

Comprehensive GUI for controlling multi-asset futures market data collection.
Provides professional trading interface for managing symbols, data types, and monitoring
real-time collection status with full process control capabilities.

Features:
- Multi-asset symbol hierarchy (Equity, Energy, Metals, Agriculture, Interest Rates)
- Parent/child contract selection with dynamic discovery
- Professional trading application color scheme
- Enhanced data type configuration with detailed descriptions
- Real-time log monitoring and process management
- Comprehensive statistics dashboard and configuration persistence
- Smart front month selection and continuous collection support
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import subprocess
import queue
import time
import os
import json
import signal
import sys
from datetime import datetime, timedelta
from pathlib import Path
import re

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not available. Environment variables will not be loaded from .env file.")
    print("Install with: pip install python-dotenv")
except Exception as e:
    print(f"Warning: Failed to load .env file: {e}")


class CodeGenerator:
    """
    Code generation framework for creating equivalent Python scripts from GUI operations.
    Generates standalone scripts with proper imports, authentication, and error handling.
    """
    
    def __init__(self, gui_controller):
        """Initialize code generator with reference to GUI controller."""
        self.gui = gui_controller
        
    def get_base_imports(self):
        """Generate base imports for all scripts."""
        return """#!/usr/bin/env python3
\"\"\"
Generated script from Professional Futures Data Collection GUI Controller
Auto-generated on: {timestamp}
\"\"\"

import os
import sys
import time
import json
import threading
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import mysql.connector
from mysql.connector import Error

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not available. Install with: pip install python-dotenv")

# Add src directory to path for local imports
script_dir = Path(__file__).parent
src_dir = script_dir / 'src'
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

try:
    from database.database_manager import DatabaseManager
except ImportError:
    print("Warning: DatabaseManager not available. Database operations may not work.")
""".format(timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    def _get_gui_value(self, env_key, default_value):
        """Safely get value from GUI environment variables."""
        try:
            if hasattr(self.gui, 'env_vars') and self.gui.env_vars and env_key in self.gui.env_vars:
                entry_widget = self.gui.env_vars[env_key].get('entry')
                if entry_widget and hasattr(entry_widget, 'get'):
                    value = entry_widget.get().strip()
                    return value if value else default_value
            return default_value
        except Exception:
            return default_value

    def get_database_config_code(self):
        """Generate database configuration code from current GUI settings."""
        if hasattr(self.gui, 'env_vars') and self.gui.env_vars:
            # Get values safely from GUI
            host = self._get_gui_value('MYSQL_HOST', 'localhost')
            port = self._get_gui_value('MYSQL_PORT', '3306')
            user = self._get_gui_value('MYSQL_USER', 'root')
            password = self._get_gui_value('MYSQL_PASSWORD', '')
            database = self._get_gui_value('MYSQL_DATABASE', 'rithmic_api')
            
            return f"""
# Database configuration from GUI settings
database_config = {{
    'host': '{host}',
    'port': {port},
    'user': '{user}',
    'password': '{password}',
    'database': '{database}'
}}

def get_database_manager():
    \"\"\"Create and return database manager instance.\"\"\"
    try:
        return DatabaseManager(database_config)
    except Exception as e:
        print(f"Error creating database manager: {{e}}")
        return None
"""
        else:
            return """
# Database configuration (update with your settings)
database_config = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'rithmic_api')
}

def get_database_manager():
    \"\"\"Create and return database manager instance.\"\"\"
    try:
        return DatabaseManager(database_config)
    except Exception as e:
        print(f"Error creating database manager: {e}")
        return None
"""

    def get_rithmic_config_code(self):
        """Generate Rithmic API configuration code from current GUI settings."""
        if hasattr(self.gui, 'env_vars') and self.gui.env_vars:
            # Get values safely from GUI
            uri = self._get_gui_value('RITHMIC_URI', 'wss://rituz00100.rithmic.com:443')
            user = self._get_gui_value('RITHMIC_USER', 'PP-013155')
            password = self._get_gui_value('RITHMIC_PASSWORD', 'b7neA8k6JA')
            system_name = self._get_gui_value('RITHMIC_SYSTEM_NAME', 'Rithmic Paper Trading')
            infra_type = self._get_gui_value('RITHMIC_INFRA_TYPE', 'Ticker Plant')
            
            return f"""
# Rithmic API configuration from GUI settings
rithmic_config = {{
    'uri': '{uri}',
    'user': '{user}',
    'password': '{password}',
    'system_name': '{system_name}',
    'infra_type': '{infra_type}'
}}
"""
        else:
            return """
# Rithmic API configuration (update with your settings)
rithmic_config = {
    'uri': os.getenv('RITHMIC_URI', 'wss://rituz00100.rithmic.com:443'),
    'user': os.getenv('RITHMIC_USER', 'PP-013155'),
    'password': os.getenv('RITHMIC_PASSWORD', 'b7neA8k6JA'),
    'system_name': os.getenv('RITHMIC_SYSTEM_NAME', 'Rithmic Paper Trading'),
    'infra_type': os.getenv('RITHMIC_INFRA_TYPE', 'Ticker Plant')
}
"""

    def generate_data_collection_script(self, contracts=None, data_types=None):
        """Generate complete data collection script."""
        contracts = contracts or self.get_selected_contracts()
        data_types = data_types or self.get_selected_data_types()
        
        script = self.get_base_imports()
        script += self.get_database_config_code()
        script += self.get_rithmic_config_code()
        
        script += f"""

def main():
    \"\"\"Main data collection function.\"\"\"
    print("🚀 Starting Professional Futures Data Collection")
    print("=" * 60)
    
    # Selected contracts: {contracts}
    # Selected data types: {data_types}
    
    # Initialize database manager
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Failed to initialize database manager")
        return False
    
    # Test database connection
    if not db_manager.test_connection():
        print("❌ Database connection test failed")
        return False
    
    print("✅ Database connection successful")
    
    processes = []
    
    try:
"""
        
        # Add process creation code for each data type
        for data_type in data_types:
            if data_type == 'level1':
                script += """
        # Start Level 1 market data collection
        print("📊 Starting Level 1 market data collection...")
        level1_cmd = [
            sys.executable, 
            'src/scripts/subscribe_level1_data.py',
            '--contracts', '{contracts}'
        ]
        level1_process = subprocess.Popen(level1_cmd, 
                                        stdout=subprocess.PIPE, 
                                        stderr=subprocess.PIPE,
                                        text=True)
        processes.append(('Level 1', level1_process))
""".format(contracts=','.join(contracts))
            
            elif data_type == 'depth_by_order':
                script += """
        # Start Depth by Order (Level 3) data collection
        print("🔍 Starting Depth by Order data collection...")
        dbo_cmd = [
            sys.executable,
            'src/scripts/subscribe_depth_by_order.py',
            '--contracts', '{contracts}'
        ]
        dbo_process = subprocess.Popen(dbo_cmd,
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     text=True)
        processes.append(('Depth by Order', dbo_process))
""".format(contracts=','.join(contracts))
            
            elif data_type == 'historical':
                script += """
        # Start Historical data collection
        print("📈 Starting Historical data collection...")
        hist_cmd = [
            sys.executable,
            'src/scripts/collect_historical_data.py',
            '--contracts', '{contracts}',
            '--bars', '1min,5min,15min'
        ]
        hist_process = subprocess.Popen(hist_cmd,
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      text=True)
        processes.append(('Historical', hist_process))
""".format(contracts=','.join(contracts))
        
        script += """
        
        # Monitor processes
        print(f"🏃‍♂️ Started {{len(processes)}} collection processes")
        print("📊 Monitoring data collection (Ctrl+C to stop)...")
        
        while True:
            try:
                # Check process health
                active_processes = []
                for name, process in processes:
                    if process.poll() is None:
                        active_processes.append((name, process))
                    else:
                        print(f"⚠️ Process {{name}} has stopped (return code: {{process.returncode}})")
                
                processes = active_processes
                
                if not processes:
                    print("❌ All processes have stopped")
                    break
                
                # Status update
                print(f"✅ {{len(processes)}} processes running - {{datetime.now().strftime('%H:%M:%S')}}")
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                print("\\n🛑 Stopping data collection...")
                break
    
    except Exception as e:
        print(f"❌ Error in data collection: {{e}}")
        
    finally:
        # Clean shutdown
        print("🧹 Cleaning up processes...")
        for name, process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ Stopped {{name}} process")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"⚠️ Force killed {{name}} process")
            except Exception as e:
                print(f"❌ Error stopping {{name}} process: {{e}}")
        
        if db_manager:
            db_manager.close_pool()
        
        print("✅ Data collection stopped successfully")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
        
        return script

    def generate_database_query_script(self, table_name, query_params=None):
        """Generate database query script."""
        script = self.get_base_imports()
        script += self.get_database_config_code()
        
        script += f"""

def main():
    \"\"\"Database query and analysis script.\"\"\"
    print("🗄️ Database Query and Analysis")
    print("=" * 50)
    
    # Initialize database manager
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Failed to initialize database manager")
        return False
    
    try:
        # Test connection
        if not db_manager.test_connection():
            print("❌ Database connection failed")
            return False
        
        print("✅ Database connection successful")
        
        # Query table: {table_name}
        table_name = "{table_name}"
        
        # Get table information
        print(f"📊 Analyzing table: {{table_name}}")
        
        # Get row count
        row_count = db_manager.get_table_row_count(table_name)
        print(f"   📈 Total rows: {{row_count:,}}")
        
        # Get table schema
        columns = db_manager.get_table_columns(table_name)
        print(f"   🏗️ Columns: {{len(columns)}}")
        for col in columns[:5]:  # Show first 5 columns
            print(f"      - {{col.get('column_name', col.get('COLUMN_NAME', 'unknown'))}}: {{col.get('data_type', col.get('DATA_TYPE', 'unknown'))}}")
        if len(columns) > 5:
            print(f"      ... and {{len(columns) - 5}} more columns")
        
        # Sample data query
        sample_query = f"SELECT * FROM `{{table_name}}` ORDER BY id DESC LIMIT 10"
        print(f"\\n📋 Sample data (latest 10 rows):")
        
        sample_data = db_manager.execute_query(sample_query)
        for i, row in enumerate(sample_data, 1):
            print(f"   {{i}}. {{dict(list(row.items())[:3])}}...")  # Show first 3 fields
        
        # Statistics queries
        print(f"\\n📊 Table Statistics:")
        
        # If timestamp column exists, show time range
        timestamp_cols = [col.get('column_name', col.get('COLUMN_NAME', '')) 
                         for col in columns 
                         if 'timestamp' in col.get('column_name', col.get('COLUMN_NAME', '')).lower()]
        
        if timestamp_cols:
            ts_col = timestamp_cols[0]
            try:
                time_query = f"SELECT MIN(`{{ts_col}}`) as min_time, MAX(`{{ts_col}}`) as max_time FROM `{{table_name}}`"
                time_result = db_manager.execute_query(time_query, fetch_all=False)
                if time_result:
                    print(f"   ⏰ Time range: {{time_result[0]['min_time']}} to {{time_result[0]['max_time']}}")
            except Exception as e:
                print(f"   ⚠️ Could not get time range: {{e}}")
        
        # If symbol column exists, show symbol count
        symbol_cols = [col.get('column_name', col.get('COLUMN_NAME', '')) 
                      for col in columns 
                      if 'symbol' in col.get('column_name', col.get('COLUMN_NAME', '')).lower()]
        
        if symbol_cols:
            sym_col = symbol_cols[0]
            try:
                symbol_query = f"SELECT COUNT(DISTINCT `{{sym_col}}`) as symbol_count FROM `{{table_name}}`"
                symbol_result = db_manager.execute_query(symbol_query, fetch_all=False)
                if symbol_result:
                    print(f"   🎯 Unique symbols: {{symbol_result[0]['symbol_count']}}")
            except Exception as e:
                print(f"   ⚠️ Could not get symbol count: {{e}}")
        
        print("\\n✅ Database analysis completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database query error: {{e}}")
        return False
        
    finally:
        if db_manager:
            db_manager.close_pool()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
        
        return script

    def get_selected_contracts(self):
        """Get currently selected contracts from GUI."""
        try:
            contracts = []
            if hasattr(self.gui, 'selected_listbox'):
                for i in range(self.gui.selected_listbox.size()):
                    contracts.append(self.gui.selected_listbox.get(i))
            return contracts if contracts else ['ESZ5']  # Default
        except:
            return ['ESZ5']  # Fallback

    def get_selected_data_types(self):
        """Get currently selected data types from GUI."""
        try:
            data_types = []
            if hasattr(self.gui, 'selected_data_types'):
                data_types = [dt for dt, var in self.gui.selected_data_types.items() if var.get()]
            return data_types if data_types else ['level1']  # Default
        except:
            return ['level1']  # Fallback

    def generate_multi_threaded_query_script(self, table_name, query_params=None, threading_strategy='ThreadPoolExecutor', max_workers=None):
        """Generate advanced multi-threaded database query script.
        
        Args:
            table_name: Target table name
            query_params: Query parameters and filters
            threading_strategy: 'ThreadPoolExecutor', 'ProcessPoolExecutor', or 'asyncio'
            max_workers: Maximum number of workers (default: CPU count)
        """
        query_params = query_params or {}
        max_workers = max_workers or 'os.cpu_count()'
        
        # Enhanced imports for multi-threading
        script = """#!/usr/bin/env python3
\"\"\"
Multi-Threaded Database Query Script
Generated from Professional Futures Data Collection GUI Controller
Auto-generated on: {timestamp}

Features:
- {threading_strategy} for parallel processing
- Connection pooling with load balancing
- Progress tracking and performance monitoring
- Memory-efficient chunked processing
- Comprehensive error handling and retry logic
\"\"\"

import os
import sys
import time
import json
import threading
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from queue import Queue, Empty
from typing import List, Dict, Any, Optional, Tuple
import mysql.connector
from mysql.connector import Error
from dataclasses import dataclass
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'query_{{table_name}}_{{datetime.now().strftime("%Y%m%d_%H%M%S")}}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logger.warning("python-dotenv not available. Install with: pip install python-dotenv")

# Add src directory to path for local imports
script_dir = Path(__file__).parent
src_dir = script_dir / 'src'
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

try:
    from database.database_manager import DatabaseManager
except ImportError:
    logger.warning("DatabaseManager not available. Database operations may not work.")

@dataclass
class QueryTask:
    \"\"\"Represents a database query task.\"\"\"
    query: str
    params: tuple
    chunk_id: int
    offset: int
    limit: int

@dataclass
class QueryResult:
    \"\"\"Represents query execution result.\"\"\"
    chunk_id: int
    data: List[Dict[str, Any]]
    execution_time: float
    error: Optional[str] = None

class ProgressTracker:
    \"\"\"Thread-safe progress tracking.\"\"\"
    
    def __init__(self, total_chunks: int):
        self.total_chunks = total_chunks
        self.completed_chunks = 0
        self.failed_chunks = 0
        self.start_time = time.time()
        self.lock = threading.Lock()
        
    def update(self, success: bool = True):
        with self.lock:
            if success:
                self.completed_chunks += 1
            else:
                self.failed_chunks += 1
                
    def get_progress(self) -> Dict[str, Any]:
        with self.lock:
            elapsed = time.time() - self.start_time
            progress_pct = (self.completed_chunks + self.failed_chunks) / self.total_chunks * 100
            
            return {{
                'completed': self.completed_chunks,
                'failed': self.failed_chunks,
                'total': self.total_chunks,
                'progress_pct': progress_pct,
                'elapsed_time': elapsed,
                'estimated_remaining': elapsed / max(1, progress_pct / 100) - elapsed if progress_pct > 0 else 0
            }}

class ConnectionPool:
    \"\"\"Thread-safe database connection pool.\"\"\"
    
    def __init__(self, config: Dict[str, Any], pool_size: int = 10):
        self.config = config
        self.pool_size = pool_size
        self.connections = Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self._initialize_pool()
        
    def _initialize_pool(self):
        \"\"\"Initialize connection pool.\"\"\"
        for _ in range(self.pool_size):
            try:
                conn = mysql.connector.connect(**self.config)
                self.connections.put(conn)
            except Error as e:
                logger.error(f"Failed to create connection: {{e}}")
                
    def get_connection(self, timeout: float = 30.0):
        \"\"\"Get connection from pool.\"\"\"
        try:
            return self.connections.get(timeout=timeout)
        except Empty:
            logger.warning("Connection pool exhausted, creating new connection")
            return mysql.connector.connect(**self.config)
            
    def return_connection(self, conn):
        \"\"\"Return connection to pool.\"\"\"
        if conn and conn.is_connected():
            try:
                self.connections.put_nowait(conn)
            except:
                # Pool is full, close connection
                conn.close()
        elif conn:
            conn.close()

""".format(
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            threading_strategy=threading_strategy,
            table_name=table_name
        )
        
        # Add database configuration
        script += self.get_database_config_code()
        
        # Add threading strategy specific implementation
        if threading_strategy == 'ThreadPoolExecutor':
            script += f"""
class ThreadedQueryExecutor:
    \"\"\"ThreadPoolExecutor-based query executor.\"\"\"
    
    def __init__(self, connection_pool: ConnectionPool, max_workers: int = {max_workers}):
        self.connection_pool = connection_pool
        self.max_workers = max_workers
        
    def execute_query_chunk(self, task: QueryTask) -> QueryResult:
        \"\"\"Execute a single query chunk.\"\"\"
        start_time = time.time()
        conn = None
        
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor(dictionary=True, buffered=True)
            
            cursor.execute(task.query, task.params)
            data = cursor.fetchall()
            cursor.close()
            
            execution_time = time.time() - start_time
            logger.debug(f"Chunk {{task.chunk_id}} completed: {{len(data)}} rows in {{execution_time:.2f}}s")
            
            return QueryResult(
                chunk_id=task.chunk_id,
                data=data,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Error executing chunk {{task.chunk_id}}: {{e}}")
            return QueryResult(
                chunk_id=task.chunk_id,
                data=[],
                execution_time=time.time() - start_time,
                error=str(e)
            )
        finally:
            if conn:
                self.connection_pool.return_connection(conn)
                
    def execute_parallel_query(self, table_name: str, total_rows: int, 
                             chunk_size: int = 10000, where_clause: str = "") -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        \"\"\"Execute parallel chunked query.\"\"\"
        
        # Calculate chunks
        num_chunks = (total_rows + chunk_size - 1) // chunk_size
        logger.info(f"Executing parallel query on {{table_name}}: {{total_rows}} rows in {{num_chunks}} chunks")
        
        # Create query tasks
        tasks = []
        base_query = f"SELECT * FROM `{{table_name}}`"
        if where_clause:
            base_query += f" WHERE {{where_clause}}"
            
        for i in range(num_chunks):
            offset = i * chunk_size
            query = f"{{base_query}} LIMIT {{chunk_size}} OFFSET {{offset}}"
            task = QueryTask(
                query=query,
                params=(),
                chunk_id=i,
                offset=offset,
                limit=chunk_size
            )
            tasks.append(task)
            
        # Execute in parallel
        progress_tracker = ProgressTracker(num_chunks)
        all_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_task = {{executor.submit(self.execute_query_chunk, task): task for task in tasks}}
            
            # Process completed tasks
            for future in as_completed(future_to_task):
                result = future.result()
                
                if result.error is None:
                    all_results.extend(result.data)
                    progress_tracker.update(success=True)
                    logger.info(f"Chunk {{result.chunk_id}} completed successfully")
                else:
                    progress_tracker.update(success=False)
                    logger.error(f"Chunk {{result.chunk_id}} failed: {{result.error}}")
                    
                # Log progress
                progress = progress_tracker.get_progress()
                logger.info(f"Progress: {{progress['progress_pct']:.1f}}% "
                          f"({{progress['completed']}}/{{progress['total']}} chunks, "
                          f"{{progress['failed']}} failed)")
        
        # Final statistics
        final_progress = progress_tracker.get_progress()
        stats = {{
            'total_rows_retrieved': len(all_results),
            'total_chunks': num_chunks,
            'successful_chunks': final_progress['completed'],
            'failed_chunks': final_progress['failed'],
            'total_execution_time': final_progress['elapsed_time'],
            'rows_per_second': len(all_results) / max(1, final_progress['elapsed_time'])
        }}
        
        return all_results, stats
"""
        
        elif threading_strategy == 'asyncio':
            script += f"""
class AsyncQueryExecutor:
    \"\"\"Asyncio-based query executor.\"\"\"
    
    def __init__(self, connection_config: Dict[str, Any], max_concurrent: int = {max_workers}):
        self.connection_config = connection_config
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
    async def execute_query_chunk(self, task: QueryTask) -> QueryResult:
        \"\"\"Execute a single query chunk asynchronously.\"\"\"
        async with self.semaphore:
            start_time = time.time()
            
            try:
                # Note: Using sync mysql connector in thread pool
                # For production, consider aiomysql or asyncio-compatible drivers
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None, 
                    self._execute_sync_query, 
                    task
                )
                
                execution_time = time.time() - start_time
                logger.debug(f"Async chunk {{task.chunk_id}} completed: {{len(result)}} rows in {{execution_time:.2f}}s")
                
                return QueryResult(
                    chunk_id=task.chunk_id,
                    data=result,
                    execution_time=execution_time
                )
                
            except Exception as e:
                logger.error(f"Error executing async chunk {{task.chunk_id}}: {{e}}")
                return QueryResult(
                    chunk_id=task.chunk_id,
                    data=[],
                    execution_time=time.time() - start_time,
                    error=str(e)
                )
                
    def _execute_sync_query(self, task: QueryTask) -> List[Dict[str, Any]]:
        \"\"\"Execute synchronous query (called from thread pool).\"\"\"
        conn = None
        try:
            conn = mysql.connector.connect(**self.connection_config)
            cursor = conn.cursor(dictionary=True, buffered=True)
            cursor.execute(task.query, task.params)
            data = cursor.fetchall()
            cursor.close()
            return data
        finally:
            if conn and conn.is_connected():
                conn.close()
                
    async def execute_parallel_query(self, table_name: str, total_rows: int,
                                   chunk_size: int = 10000, where_clause: str = "") -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        \"\"\"Execute parallel chunked query using asyncio.\"\"\"
        
        # Calculate chunks
        num_chunks = (total_rows + chunk_size - 1) // chunk_size
        logger.info(f"Executing async parallel query on {{table_name}}: {{total_rows}} rows in {{num_chunks}} chunks")
        
        # Create query tasks
        tasks = []
        base_query = f"SELECT * FROM `{{table_name}}`"
        if where_clause:
            base_query += f" WHERE {{where_clause}}"
            
        for i in range(num_chunks):
            offset = i * chunk_size
            query = f"{{base_query}} LIMIT {{chunk_size}} OFFSET {{offset}}"
            task = QueryTask(
                query=query,
                params=(),
                chunk_id=i,
                offset=offset,
                limit=chunk_size
            )
            tasks.append(task)
            
        # Execute all tasks concurrently
        progress_tracker = ProgressTracker(num_chunks)
        
        async_tasks = [self.execute_query_chunk(task) for task in tasks]
        results = await asyncio.gather(*async_tasks, return_exceptions=True)
        
        # Process results
        all_results = []
        for result in results:
            if isinstance(result, QueryResult):
                if result.error is None:
                    all_results.extend(result.data)
                    progress_tracker.update(success=True)
                else:
                    progress_tracker.update(success=False)
            else:
                progress_tracker.update(success=False)
                logger.error(f"Unexpected result type: {{type(result)}}")
                
        # Final statistics
        final_progress = progress_tracker.get_progress()
        stats = {{
            'total_rows_retrieved': len(all_results),
            'total_chunks': num_chunks,
            'successful_chunks': final_progress['completed'],
            'failed_chunks': final_progress['failed'],
            'total_execution_time': final_progress['elapsed_time'],
            'rows_per_second': len(all_results) / max(1, final_progress['elapsed_time'])
        }}
        
        return all_results, stats
"""
        
        # Add main execution function
        script += f"""

def get_table_row_count(table_name: str, db_config: Dict[str, Any], where_clause: str = "") -> int:
    \"\"\"Get total row count for table.\"\"\"
    conn = None
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        count_query = f"SELECT COUNT(*) FROM `{{table_name}}`"
        if where_clause:
            count_query += f" WHERE {{where_clause}}"
            
        cursor.execute(count_query)
        count = cursor.fetchone()[0]
        cursor.close()
        return count
        
    except Exception as e:
        logger.error(f"Error getting row count: {{e}}")
        return 0
    finally:
        if conn and conn.is_connected():
            conn.close()

def export_results_to_csv(results: List[Dict[str, Any]], filename: str):
    \"\"\"Export results to CSV file.\"\"\"
    if not results:
        logger.warning("No results to export")
        return
        
    import csv
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = list(results[0].keys())
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(results)
            
        logger.info(f"Exported {{len(results)}} rows to {{filename}}")
        
    except Exception as e:
        logger.error(f"Error exporting to CSV: {{e}}")

def main():
    \"\"\"Main multi-threaded query execution function.\"\"\"
    logger.info("🚀 Starting Multi-Threaded Database Query")
    logger.info("=" * 60)
    
    # Configuration
    table_name = "{table_name}"
    chunk_size = {query_params.get('chunk_size', 10000)}
    where_clause = "{query_params.get('where_clause', '')}"
    max_workers = {max_workers}
    export_csv = {str(query_params.get('export_csv', True)).lower()}
    
    logger.info(f"Table: {{table_name}}")
    logger.info(f"Chunk size: {{chunk_size:,}}")
    logger.info(f"Max workers: {{max_workers}}")
    logger.info(f"Threading strategy: {threading_strategy}")
    if where_clause:
        logger.info(f"Filter: {{where_clause}}")
    
    # Initialize database manager
    db_manager = get_database_manager()
    if not db_manager:
        logger.error("❌ Failed to initialize database manager")
        return False
        
    # Test database connection
    if not db_manager.test_connection():
        logger.error("❌ Database connection test failed")
        return False
        
    logger.info("✅ Database connection successful")
    
    try:
        # Get total row count
        logger.info("📊 Getting table row count...")
        total_rows = get_table_row_count(table_name, database_config, where_clause)
        logger.info(f"Total rows to process: {{total_rows:,}}")
        
        if total_rows == 0:
            logger.warning("No rows found to process")
            return True
            
        # Execute parallel query"""
        
        if threading_strategy == 'ThreadPoolExecutor':
            script += """
        # Create connection pool and executor
        connection_pool = ConnectionPool(database_config, pool_size=max_workers)
        executor = ThreadedQueryExecutor(connection_pool, max_workers)
        
        # Execute parallel query
        results, stats = executor.execute_parallel_query(
            table_name=table_name,
            total_rows=total_rows,
            chunk_size=chunk_size,
            where_clause=where_clause
        )"""
        
        elif threading_strategy == 'asyncio':
            script += """
        # Execute async parallel query
        executor = AsyncQueryExecutor(database_config, max_workers)
        results, stats = asyncio.run(executor.execute_parallel_query(
            table_name=table_name,
            total_rows=total_rows,
            chunk_size=chunk_size,
            where_clause=where_clause
        ))"""
        
        script += f"""
        
        # Display results
        logger.info("\\n" + "=" * 60)
        logger.info("📊 EXECUTION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total rows retrieved: {{stats['total_rows_retrieved']:,}}")
        logger.info(f"Total chunks processed: {{stats['total_chunks']}}")
        logger.info(f"Successful chunks: {{stats['successful_chunks']}}")
        logger.info(f"Failed chunks: {{stats['failed_chunks']}}")
        logger.info(f"Total execution time: {{stats['total_execution_time']:.2f}} seconds")
        logger.info(f"Processing rate: {{stats['rows_per_second']:,.0f}} rows/second")
        
        # Export results if requested
        if export_csv and results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"{{table_name}}_{{timestamp}}.csv"
            export_results_to_csv(results, csv_filename)
            
        logger.info("✅ Multi-threaded query completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during execution: {{e}}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
        
        return script

    def generate_symbol_search_script(self, exchange_filter=None, symbol_pattern=None):
        """Generate script for symbol search and static data collection."""
        
        script = self.get_base_imports()
        script += self.get_database_config_code()
        script += self.get_rithmic_config_code()
        
        script += f"""

# Symbol Search and Static Data Collection
# Exchange filter: {exchange_filter or 'All'}
# Symbol pattern: {symbol_pattern or 'All'}

import re
import concurrent.futures
from dataclasses import dataclass
from typing import Set, List, Dict, Optional

@dataclass
class SymbolInfo:
    \"\"\"Represents symbol metadata.\"\"\"
    symbol: str
    exchange: str
    product_code: str
    description: str
    tick_size: float
    contract_size: int
    expiration_date: Optional[str] = None
    last_trading_day: Optional[str] = None
    trading_hours: Optional[str] = None

class SymbolSearcher:
    \"\"\"Professional symbol search and metadata collection.\"\"\"
    
    def __init__(self, db_manager, rithmic_client=None):
        self.db_manager = db_manager
        self.rithmic_client = rithmic_client
        
    def search_database_symbols(self, pattern: str = None, exchange: str = None) -> List[SymbolInfo]:
        \"\"\"Search symbols from database.\"\"\"
        try:
            query = "SELECT DISTINCT symbol, exchange, product_code, symbol_name, tick_size, lot_size FROM symbols WHERE 1=1"
            params = []
            
            if pattern:
                query += " AND symbol LIKE %s"
                params.append(f"%{{pattern}}%")
                
            if exchange:
                query += " AND exchange = %s"
                params.append(exchange)
                
            query += " ORDER BY symbol"
            
            results = self.db_manager.execute_query(query, params)
            
            symbols = []
            for row in results:
                symbols.append(SymbolInfo(
                    symbol=row['symbol'],
                    exchange=row['exchange'],
                    product_code=row.get('product_code', ''),
                    description=row.get('symbol_name', ''),
                    tick_size=float(row.get('tick_size', 0.01)),
                    contract_size=int(row.get('lot_size', 1))
                ))
                
            return symbols
            
        except Exception as e:
            print(f"Error searching database symbols: {{e}}")
            return []
            
    def get_available_exchanges(self) -> List[str]:
        \"\"\"Get list of available exchanges.\"\"\"
        try:
            query = "SELECT DISTINCT exchange FROM symbols ORDER BY exchange"
            results = self.db_manager.execute_query(query)
            return [row['exchange'] for row in results]
        except Exception as e:
            print(f"Error getting exchanges: {{e}}")
            return []
            
    def get_product_codes(self, exchange: str = None) -> List[str]:
        \"\"\"Get list of product codes.\"\"\"
        try:
            query = "SELECT DISTINCT product_code FROM symbols WHERE product_code IS NOT NULL"
            params = []
            
            if exchange:
                query += " AND exchange = %s"
                params.append(exchange)
                
            query += " ORDER BY product_code"
            
            results = self.db_manager.execute_query(query, params)
            return [row['product_code'] for row in results]
        except Exception as e:
            print(f"Error getting product codes: {{e}}")
            return []
            
    def collect_static_data_parallel(self, symbols: List[str], max_workers: int = 10) -> Dict[str, SymbolInfo]:
        \"\"\"Collect static data for symbols in parallel.\"\"\"
        symbol_data = {{}}
        
        def collect_symbol_data(symbol: str) -> Optional[SymbolInfo]:
            try:
                # Simulate static data collection
                # In production, this would call Rithmic API or other data sources
                print(f"Collecting static data for {{symbol}}...")
                
                # Basic symbol info extraction
                exchange = "CME"  # Default or derive from symbol
                if symbol.startswith("ES"):
                    product_code = "ES"
                    description = "E-mini S&P 500"
                    tick_size = 0.25
                    contract_size = 50
                elif symbol.startswith("NQ"):
                    product_code = "NQ"  
                    description = "E-mini NASDAQ-100"
                    tick_size = 0.25
                    contract_size = 20
                elif symbol.startswith("YM"):
                    product_code = "YM"
                    description = "E-mini Dow Jones"
                    tick_size = 1.0
                    contract_size = 5
                else:
                    product_code = symbol[:2]
                    description = f"{{symbol}} Contract"
                    tick_size = 0.01
                    contract_size = 1
                    
                return SymbolInfo(
                    symbol=symbol,
                    exchange=exchange,
                    product_code=product_code,
                    description=description,
                    tick_size=tick_size,
                    contract_size=contract_size
                )
                
            except Exception as e:
                print(f"Error collecting data for {{symbol}}: {{e}}")
                return None
                
        # Execute in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_symbol = {{executor.submit(collect_symbol_data, symbol): symbol 
                               for symbol in symbols}}
            
            for future in concurrent.futures.as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    symbol_info = future.result()
                    if symbol_info:
                        symbol_data[symbol] = symbol_info
                except Exception as e:
                    print(f"Error processing {{symbol}}: {{e}}")
                    
        return symbol_data
        
    def store_symbol_data(self, symbol_data: Dict[str, SymbolInfo]):
        \"\"\"Store collected symbol data in database.\"\"\"
        try:
            # Create symbols table if not exists
            create_table_query = \"\"\"
                CREATE TABLE IF NOT EXISTS symbols (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL UNIQUE,
                    exchange VARCHAR(20),
                    product_code VARCHAR(10),
                    symbol_name TEXT,
                    tick_size DECIMAL(10,6),
                    lot_size INT,
                    expiration_date DATE,
                    last_trading_day DATE,
                    trading_hours TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_exchange (exchange),
                    INDEX idx_product_code (product_code)
                )
            \"\"\"
            
            self.db_manager.execute_query(create_table_query)
            
            # Insert or update symbol data
            for symbol, info in symbol_data.items():
                insert_query = \"\"\"
                    INSERT INTO symbols (symbol, exchange, product_code, symbol_name, 
                                       tick_size, lot_size)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        exchange = VALUES(exchange),
                        product_code = VALUES(product_code),
                        symbol_name = VALUES(symbol_name),
                        tick_size = VALUES(tick_size),
                        lot_size = VALUES(lot_size),
                        updated_at = CURRENT_TIMESTAMP
                \"\"\"
                
                params = (
                    info.symbol,
                    info.exchange,
                    info.product_code,
                    info.description,
                    info.tick_size,
                    info.contract_size
                )
                
                self.db_manager.execute_query(insert_query, params)
                
            print(f"✅ Stored {{len(symbol_data)}} symbols in database")
            
        except Exception as e:
            print(f"❌ Error storing symbol data: {{e}}")

def main():
    \"\"\"Main symbol search and data collection function.\"\"\"
    print("🔍 Starting Symbol Search and Static Data Collection")
    print("=" * 60)
    
    # Configuration
    exchange_filter = "{exchange_filter or ''}"
    symbol_pattern = "{symbol_pattern or ''}"
    max_workers = 10
    
    print(f"Exchange filter: {{exchange_filter or 'All'}}")
    print(f"Symbol pattern: {{symbol_pattern or 'All'}}")
    print(f"Max workers: {{max_workers}}")
    
    # Initialize database manager
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Failed to initialize database manager")
        return False
        
    # Test database connection
    if not db_manager.test_connection():
        print("❌ Database connection test failed")
        return False
        
    print("✅ Database connection successful")
    
    try:
        # Initialize symbol searcher
        searcher = SymbolSearcher(db_manager)
        
        # Get available exchanges
        print("\\n📋 Available exchanges:")
        exchanges = searcher.get_available_exchanges()
        for exchange in exchanges:
            print(f"  - {{exchange}}")
            
        # Search existing symbols
        print("\\n🔍 Searching existing symbols...")
        existing_symbols = searcher.search_database_symbols(
            pattern=symbol_pattern if symbol_pattern else None,
            exchange=exchange_filter if exchange_filter else None
        )
        
        print(f"Found {{len(existing_symbols)}} existing symbols")
        
        # Sample symbols for demonstration (in production, get from API)
        sample_symbols = ["ESZ24", "ESH25", "ESM25", "ESU25", "ESZ25",
                         "NQZ24", "NQH25", "NQM25", "NQU25", "NQZ25",
                         "YMZ24", "YMH25", "YMM25", "YMU25", "YMZ25"]
        
        if symbol_pattern:
            # Filter symbols by pattern
            pattern_regex = re.compile(symbol_pattern.replace('*', '.*'), re.IGNORECASE)
            sample_symbols = [s for s in sample_symbols if pattern_regex.match(s)]
            
        print(f"\\n📊 Collecting static data for {{len(sample_symbols)}} symbols...")
        
        # Collect static data in parallel
        symbol_data = searcher.collect_static_data_parallel(sample_symbols, max_workers)
        
        print(f"✅ Collected data for {{len(symbol_data)}} symbols")
        
        # Store in database
        print("\\n💾 Storing symbol data in database...")
        searcher.store_symbol_data(symbol_data)
        
        # Display summary
        print("\\n" + "=" * 60)
        print("📊 COLLECTION SUMMARY")
        print("=" * 60)
        print(f"Symbols processed: {{len(sample_symbols)}}")
        print(f"Data collected: {{len(symbol_data)}}")
        print(f"Success rate: {{len(symbol_data)/max(1,len(sample_symbols))*100:.1f}}%")
        
        print("\\n✅ Symbol search and data collection completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error during symbol collection: {{e}}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
        
        return script


class CodePreviewDialog:
    """Dialog for previewing and exporting generated code."""
    
    def __init__(self, parent, title, code, filename_suggestion="generated_script.py"):
        """Initialize code preview dialog."""
        self.parent = parent
        self.code = code
        self.filename_suggestion = filename_suggestion
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("900x700")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Configure dialog styling to match main application
        self.dialog.configure(bg='#f8f9fa')
        
        # Create main frame
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Title and description
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(title_frame, text=title, font=('Arial', 14, 'bold')).pack(side='left')
        ttk.Label(title_frame, text="Generated Python script with imports and configuration", 
                 foreground='#495057').pack(side='left', padx=(10, 0))
        
        # Code text area with syntax highlighting
        code_frame = ttk.LabelFrame(main_frame, text="Generated Code", padding=5)
        code_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # Create text widget with scrollbar
        text_frame = ttk.Frame(code_frame)
        text_frame.pack(fill='both', expand=True)
        
        self.code_text = scrolledtext.ScrolledText(
            text_frame, 
            wrap=tk.NONE,
            font=('Consolas', 10),
            bg='#ffffff',
            fg='#212529',
            insertbackground='#212529',
            selectbackground='#007bff',
            selectforeground='white'
        )
        self.code_text.pack(fill='both', expand=True)
        
        # Insert code
        self.code_text.insert('1.0', code)
        self.code_text.config(state='normal')  # Allow editing
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x')
        
        # Left side buttons
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side='left')
        
        ttk.Button(left_buttons, text="📋 Copy to Clipboard", 
                  command=self.copy_to_clipboard).pack(side='left', padx=(0, 5))
        ttk.Button(left_buttons, text="💾 Save to File", 
                  command=self.save_to_file).pack(side='left', padx=(0, 5))
        
        # Right side buttons
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side='right')
        
        ttk.Button(right_buttons, text="✅ Close", 
                  command=self.dialog.destroy).pack(side='right')
        
        # Focus and center
        self.center_dialog()
        self.code_text.focus_set()
        
    def center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Get dialog size
        dialog_width = self.dialog.winfo_reqwidth()
        dialog_height = self.dialog.winfo_reqheight()
        
        # Calculate position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
    def copy_to_clipboard(self):
        """Copy code to clipboard."""
        try:
            code = self.code_text.get('1.0', 'end-1c')
            self.dialog.clipboard_clear()
            self.dialog.clipboard_append(code)
            self.dialog.update()  # Ensure clipboard is updated
            
            # Show feedback
            messagebox.showinfo("Copied", "Code copied to clipboard!", parent=self.dialog)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy to clipboard: {e}", parent=self.dialog)
    
    def save_to_file(self):
        """Save code to file."""
        try:
            filename = filedialog.asksaveasfilename(
                parent=self.dialog,
                title="Save Generated Script",
                defaultextension=".py",
                filetypes=[("Python files", "*.py"), ("All files", "*.*")],
                initialvalue=self.filename_suggestion
            )
            
            if filename:
                code = self.code_text.get('1.0', 'end-1c')
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                
                messagebox.showinfo("Saved", f"Script saved to: {filename}", parent=self.dialog)
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save file: {e}", parent=self.dialog)


class ESFuturesGUIController:
    """Comprehensive GUI controller for ES futures data collection."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Professional Futures Data Collection Controller")
        self.root.geometry("1400x900")
        # Professional light mode trading color scheme
        self.colors = {
            'bg_dark': '#f8f9fa',        # Light background
            'bg_panel': '#ffffff',       # Panel background
            'bg_input': '#f1f3f4',       # Input field background
            'text_primary': '#212529',   # Primary dark text
            'text_secondary': '#495057', # Secondary dark gray text
            'accent_green': '#28a745',   # Positive/Buy/Success
            'accent_red': '#dc3545',     # Negative/Sell/Error
            'accent_blue': '#007bff',    # Neutral/Info
            'accent_yellow': '#ffc107',  # Warning/Caution
            'accent_purple': '#6f42c1',  # Special/Premium
            'border_light': '#dee2e6',   # Light borders
            'border_dark': '#adb5bd'     # Dark borders
        }
        self.root.configure(bg=self.colors['bg_dark'])
        
        # Application state
        self.collection_processes = {}
        self.is_collecting = False
        self.log_queue = queue.Queue()
        self.config_file = "es_collection_config.json"
        
        # Comprehensive futures contracts hierarchy
        self.futures_contracts = {
            "Equity Indexes": {
                "ES": {
                    "name": "S&P 500 E-mini",
                    "exchange": "CME",
                    "contracts": ["ESU5", "ESZ5", "ESH6", "ESM6", "ESU6", "ESZ6", "ESH7", "ESM7"],
                    "tick_size": 0.25,
                    "point_value": 50,
                    "margin_req": "$13,200",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                },
                "MES": {
                    "name": "Micro S&P 500 E-mini",
                    "exchange": "CME",
                    "contracts": ["MESU5", "MESZ5", "MESH6", "MESM6", "MESU6", "MESZ6", "MESH7", "MESM7"],
                    "tick_size": 0.25,
                    "point_value": 5,
                    "margin_req": "$1,320",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                },
                "NQ": {
                    "name": "NASDAQ-100 E-mini",
                    "exchange": "CME",
                    "contracts": ["NQU5", "NQZ5", "NQH6", "NQM6", "NQU6", "NQZ6", "NQH7", "NQM7"],
                    "tick_size": 0.25,
                    "point_value": 20,
                    "margin_req": "$17,600",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                },
                "MNQ": {
                    "name": "Micro NASDAQ-100 E-mini",
                    "exchange": "CME",
                    "contracts": ["MNQU5", "MNQZ5", "MNQH6", "MNQM6", "MNQU6", "MNQZ6", "MNQH7", "MNQM7"],
                    "tick_size": 0.25,
                    "point_value": 2,
                    "margin_req": "$1,760",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                },
                "YM": {
                    "name": "Dow Jones E-mini",
                    "exchange": "CBOT",
                    "contracts": ["YMU5", "YMZ5", "YMH6", "YMM6", "YMU6", "YMZ6", "YMH7", "YMM7"],
                    "tick_size": 1.0,
                    "point_value": 5,
                    "margin_req": "$11,000",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                },
                "MYM": {
                    "name": "Micro Dow Jones E-mini",
                    "exchange": "CBOT",
                    "contracts": ["MYMU5", "MYMZ5", "MYMH6", "MYMM6", "MYMU6", "MYMZ6", "MYMH7", "MYMM7"],
                    "tick_size": 1.0,
                    "point_value": 0.5,
                    "margin_req": "$1,100",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                },
                "RTY": {
                    "name": "Russell 2000 E-mini",
                    "exchange": "CME",
                    "contracts": ["RTYU5", "RTYZ5", "RTYH6", "RTYM6", "RTYU6", "RTYZ6", "RTYH7", "RTYM7"],
                    "tick_size": 0.1,
                    "point_value": 50,
                    "margin_req": "$9,900",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                },
                "M2K": {
                    "name": "Micro Russell 2000 E-mini",
                    "exchange": "CME",
                    "contracts": ["M2KU5", "M2KZ5", "M2KH6", "M2KM6", "M2KU6", "M2KZ6", "M2KH7", "M2KM7"],
                    "tick_size": 0.1,
                    "point_value": 5,
                    "margin_req": "$990",
                    "trading_hours": "17:00-16:00 CT",
                    "settlement": "Cash"
                }
            },
            "Interest Rates": {
                "ZB": {
                    "name": "30-Year Treasury Bond",
                    "exchange": "CBOT",
                    "contracts": ["ZBU5", "ZBZ5", "ZBH6", "ZBM6", "ZBU6", "ZBZ6"],
                    "tick_size": 0.03125,
                    "point_value": 1000
                },
                "ZN": {
                    "name": "10-Year Treasury Note",
                    "exchange": "CBOT",
                    "contracts": ["ZNU5", "ZNZ5", "ZNH6", "ZNM6", "ZNU6", "ZNZ6"],
                    "tick_size": 0.015625,
                    "point_value": 1000
                },
                "ZF": {
                    "name": "5-Year Treasury Note",
                    "exchange": "CBOT",
                    "contracts": ["ZFU5", "ZFZ5", "ZFH6", "ZFM6", "ZFU6", "ZFZ6"],
                    "tick_size": 0.0078125,
                    "point_value": 1000
                }
            },
            "Energy": {
                "CL": {
                    "name": "Crude Oil WTI",
                    "exchange": "NYMEX",
                    "contracts": ["CLU5", "CLV5", "CLX5", "CLZ5", "CLF6", "CLG6", "CLH6", "CLJ6"],
                    "tick_size": 0.01,
                    "point_value": 1000
                },
                "NG": {
                    "name": "Natural Gas",
                    "exchange": "NYMEX",
                    "contracts": ["NGU5", "NGV5", "NGX5", "NGZ5", "NGF6", "NGG6", "NGH6", "NGJ6"],
                    "tick_size": 0.001,
                    "point_value": 10000
                },
                "HO": {
                    "name": "Heating Oil",
                    "exchange": "NYMEX",
                    "contracts": ["HOU5", "HOV5", "HOX5", "HOZ5", "HOF6", "HOG6", "HOH6", "HOJ6"],
                    "tick_size": 0.0001,
                    "point_value": 42000
                }
            },
            "Metals": {
                "GC": {
                    "name": "Gold",
                    "exchange": "COMEX",
                    "contracts": ["GCZ5", "GCG6", "GCJ6", "GCM6", "GCQ6", "GCZ6"],
                    "tick_size": 0.10,
                    "point_value": 100
                },
                "SI": {
                    "name": "Silver",
                    "exchange": "COMEX",
                    "contracts": ["SIZ5", "SIH6", "SIK6", "SIN6", "SIU6", "SIZ6"],
                    "tick_size": 0.005,
                    "point_value": 5000
                },
                "HG": {
                    "name": "Copper",
                    "exchange": "COMEX",
                    "contracts": ["HGZ5", "HGH6", "HGK6", "HGN6", "HGU6", "HGZ6"],
                    "tick_size": 0.0005,
                    "point_value": 25000
                }
            },
            "Agriculture": {
                "ZC": {
                    "name": "Corn",
                    "exchange": "CBOT",
                    "contracts": ["ZCZ5", "ZCH6", "ZCK6", "ZCN6", "ZCU6", "ZCZ6"],
                    "tick_size": 0.25,
                    "point_value": 50
                },
                "ZS": {
                    "name": "Soybeans",
                    "exchange": "CBOT",
                    "contracts": ["ZSX5", "ZSF6", "ZSH6", "ZSK6", "ZSN6", "ZSQ6", "ZSU6", "ZSX6"],
                    "tick_size": 0.25,
                    "point_value": 50
                },
                "ZW": {
                    "name": "Wheat",
                    "exchange": "CBOT",
                    "contracts": ["ZWZ5", "ZWH6", "ZWK6", "ZWN6", "ZWU6", "ZWZ6"],
                    "tick_size": 0.25,
                    "point_value": 50
                },
                "KC": {
                    "name": "Coffee",
                    "exchange": "ICE",
                    "contracts": ["KCZ5", "KCH6", "KCK6", "KCN6", "KCU6", "KCZ6"],
                    "tick_size": 0.05,
                    "point_value": 375
                }
            }
        }
        
        # Enhanced data collection types with detailed descriptions
        self.data_types = {
            "level1": {
                "name": "Level 1 Market Data",
                "description": "Best Bid/Offer + Last Trade",
                "details": "Real-time BBO, trade prices/sizes, ~100-500 updates/sec",
                "use_case": "Basic market monitoring, spread analysis",
                "data_rate": "Low",
                "storage": "~50MB/day per contract"
            },
            "level2": {
                "name": "Level 2 - Market by Price (MBP)",
                "description": "Full order book depth aggregated by price",
                "details": "All price levels, market depth analysis, ~1000-5000 updates/sec",
                "use_case": "Market depth analysis, liquidity assessment",
                "data_rate": "High",
                "storage": "~500MB/day per contract"
            },
            "depth_by_order": {
                "name": "Level 3 - Depth by Order (DBO/MBO)",
                "description": "Individual order tracking and market microstructure",
                "details": "Order-by-order granularity, market microstructure, ~2000-10000 updates/sec",
                "use_case": "Order flow analysis, market microstructure",
                "data_rate": "Very High",
                "storage": "~2GB/day per contract"
            },
            "time_and_sales": {
                "name": "Time & Sales",
                "description": "Complete trade history",
                "details": "Every executed trade, volume analysis, real-time execution data",
                "use_case": "Trade analysis, volume profiling",
                "data_rate": "Medium",
                "storage": "~200MB/day per contract"
            },
            "historical": {
                "name": "Historical Data",
                "description": "Time bars and tick archives",
                "details": "OHLCV bars, tick replay, backtesting data",
                "use_case": "Backtesting, historical analysis",
                "data_rate": "On-demand",
                "storage": "Variable based on timeframe"
            },
        }
        
        # GUI state variables
        self.selected_contracts = tk.StringVar()
        self.selected_data_types = {}
        self.auto_restart = tk.BooleanVar(value=True)
        
        # Contract month mapping for front month calculation
        self.month_codes = {
            'F': 1, 'G': 2, 'H': 3, 'J': 4, 'K': 5, 'M': 6,
            'N': 7, 'Q': 8, 'U': 9, 'V': 10, 'X': 11, 'Z': 12
        }
        
        # Selected contracts tracking
        self.selected_parent_contracts = set()
        self.selected_specific_contracts = set()
        
        # Tab initialization tracking for lazy loading
        self.tabs_initialized = {
            'control': False,
            'monitoring': False, 
            'statistics': False,
            'configuration': False,
            'database': False,
            'logs': False,
            'symbol_search': False
        }
        
        # Initialize environment variables with defaults for cross-tab compatibility
        self.initialize_default_env_vars()
        
        # Initialize GUI components (fast startup)
        self.setup_gui()
        self.load_configuration()
        
        # Initialize code generator
        self.code_generator = CodeGenerator(self)
        
        # Start background initialization after GUI is shown
        self.root.after(100, self.start_background_initialization)
        
        # Setup cleanup on exit
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def initialize_default_env_vars(self):
        """Initialize env_vars with default values for cross-tab compatibility."""
        # Create a mock entry class that mimics tkinter Entry behavior
        class MockEntry:
            def __init__(self, default_value):
                self._value = str(default_value)
            
            def get(self):
                return self._value
            
            def delete(self, start, end):
                if start == 0 and end == tk.END:
                    self._value = ""
            
            def insert(self, index, value):
                if index == 0:
                    self._value = str(value)
        
        # Initialize env_vars with default database configuration
        self.env_vars = {
            'MYSQL_HOST': {
                'entry': MockEntry('**************'),
                'default': '**************',
                'label': 'Database Host'
            },
            'MYSQL_PORT': {
                'entry': MockEntry('3306'),
                'default': '3306',
                'label': 'Database Port'
            },
            'MYSQL_USER': {
                'entry': MockEntry('root'),
                'default': 'root',
                'label': 'Database User'
            },
            'MYSQL_PASSWORD': {
                'entry': MockEntry('debian'),
                'default': 'debian',
                'label': 'Database Password'
            },
            'MYSQL_DATABASE': {
                'entry': MockEntry('rithmic_api'),
                'default': 'rithmic_api',
                'label': 'Database Name'
            },
            'MYSQL_POOL_SIZE': {
                'entry': MockEntry('10'),
                'default': '10',
                'label': 'Connection Pool Size'
            },
            'MYSQL_MAX_OVERFLOW': {
                'entry': MockEntry('20'),
                'default': '20',
                'label': 'Max Pool Overflow'
            },
            'MYSQL_POOL_TIMEOUT': {
                'entry': MockEntry('30'),
                'default': '30',
                'label': 'Pool Timeout'
            },
            # Add other essential environment variables
            'RITHMIC_USER': {
                'entry': MockEntry('PP-013155'),
                'default': 'PP-013155',
                'label': 'Rithmic User'
            },
            'RITHMIC_PASSWORD': {
                'entry': MockEntry('b7neA8k6JA'),
                'default': 'b7neA8k6JA',
                'label': 'Rithmic Password'
            },
            'RITHMIC_SYSTEM': {
                'entry': MockEntry('Rithmic Paper Trading'),
                'default': 'Rithmic Paper Trading',
                'label': 'Rithmic System'
            },
            'CONTRACTS_TO_SUBSCRIBE': {
                'entry': MockEntry('@ES @NQ'),
                'default': '@ES @NQ',
                'label': 'Contracts to Subscribe'
            },
            'DATA_DIRECTORY': {
                'entry': MockEntry('data'),
                'default': 'data',
                'label': 'Data Directory'
            }
        }
        
        # Try to load actual values from .env file if it exists
        try:
            self.load_env_values_to_mock()
        except Exception as e:
            print(f"⚠️ Could not load .env file: {e}, using defaults")
    
    def load_env_values_to_mock(self):
        """Load values from .env file into mock env_vars."""
        env_file_path = '.env'
        if os.path.exists(env_file_path):
            env_values = {}
            with open(env_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_values[key.strip()] = value.strip()
            
            # Update mock entries with actual values
            for env_key, field_info in self.env_vars.items():
                if env_key in env_values:
                    field_info['entry']._value = env_values[env_key]
        
    def setup_gui(self):
        """Initialize the complete GUI interface with fast startup."""
        # Configure styles
        self.setup_styles()
        
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs with placeholders for fast startup
        self.create_tab_placeholders()
        
        # Bind tab change event for lazy loading
        self.notebook.bind('<<NotebookTabChanged>>', self.on_tab_changed)
        
        # Initialize the first tab (Control Panel) immediately
        self.initialize_control_tab()
        
        # Status bar
        self.create_status_bar()
        
    def create_tab_placeholders(self):
        """Create tab containers with placeholders for fast startup."""
        # Create tab frames
        self.control_frame = ttk.Frame(self.notebook)
        self.monitoring_frame = ttk.Frame(self.notebook)
        self.statistics_frame = ttk.Frame(self.notebook)
        self.configuration_frame = ttk.Frame(self.notebook)
        self.database_frame = ttk.Frame(self.notebook)
        self.symbol_search_frame = ttk.Frame(self.notebook)
        self.logs_frame = ttk.Frame(self.notebook)
        
        # Add tabs to notebook
        self.notebook.add(self.control_frame, text="🎯 Control Panel")
        self.notebook.add(self.monitoring_frame, text="📊 Monitoring")
        self.notebook.add(self.statistics_frame, text="📊 Statistics")
        self.notebook.add(self.configuration_frame, text="⚙️ Configuration")
        self.notebook.add(self.database_frame, text="🗄️ Database Viewer")
        self.notebook.add(self.symbol_search_frame, text="🔍 Symbol Search")
        self.notebook.add(self.logs_frame, text="📋 Live Logs")
        
        # Create loading placeholders for non-initialized tabs
        self.create_loading_placeholder(self.monitoring_frame, "Loading monitoring interface...")
        self.create_loading_placeholder(self.statistics_frame, "Loading statistics dashboard...")
        self.create_loading_placeholder(self.symbol_search_frame, "Loading symbol search interface...")
        self.create_loading_placeholder(self.logs_frame, "Loading live logs interface...")
        
        # Initialize database tab with new DatabaseViewer class
        from database_viewer import DatabaseViewer
        self.database_viewer = DatabaseViewer(self.database_frame, self)
        self.tabs_initialized['database'] = True
    
    def create_loading_placeholder(self, parent, message):
        """Create a loading placeholder for a tab."""
        placeholder_frame = ttk.Frame(parent)
        placeholder_frame.pack(expand=True, fill='both')
        
        # Center the loading message
        center_frame = ttk.Frame(placeholder_frame)
        center_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # Loading icon and message
        loading_label = ttk.Label(center_frame, text="⏳", font=('Arial', 24))
        loading_label.pack(pady=(0, 10))
        
        message_label = ttk.Label(center_frame, text=message, 
                                 font=('Arial', 12), 
                                 foreground=self.colors['text_secondary'])
        message_label.pack()
    
    def on_tab_changed(self, event):
        """Handle tab change events for lazy loading."""
        try:
            selected_tab = self.notebook.index(self.notebook.select())
            tab_names = ['control', 'monitoring', 'statistics', 'configuration', 'database', 'logs', 'symbol_search']
            
            if selected_tab < len(tab_names):
                tab_name = tab_names[selected_tab]
                if not self.tabs_initialized[tab_name]:
                    self.initialize_tab(tab_name)
        except Exception as e:
            self.log_message(f"⚠️ Error loading tab: {str(e)}")
    
    def initialize_tab(self, tab_name):
        """Initialize a specific tab's content."""
        if self.tabs_initialized[tab_name]:
            return
            
        try:
            if tab_name == 'control':
                self.initialize_control_tab()
            elif tab_name == 'monitoring':
                self.initialize_monitoring_tab()
            elif tab_name == 'statistics':
                self.initialize_statistics_tab()
            elif tab_name == 'configuration':
                self.initialize_configuration_tab()
            elif tab_name == 'database':
                self.initialize_database_tab()
            elif tab_name == 'logs':
                self.initialize_logs_tab()
            elif tab_name == 'symbol_search':
                self.initialize_symbol_search_tab()
                
            self.tabs_initialized[tab_name] = True
            
        except Exception as e:
            self.log_message(f"⚠️ Error initializing {tab_name} tab: {str(e)}")
    
    def start_background_initialization(self):
        """Start background initialization of non-critical components."""
        # Start monitoring thread in background
        threading.Thread(target=self.start_monitoring_thread, daemon=True).start()
    
    def initialize_control_tab(self):
        """Initialize the control panel tab immediately."""
        if self.tabs_initialized['control']:
            return
            
        # Clear placeholder if exists
        for widget in self.control_frame.winfo_children():
            widget.destroy()
            
        # Create the actual control panel content
        self.create_control_panel_content()
        self.tabs_initialized['control'] = True
    
    def initialize_configuration_tab(self):
        """Initialize configuration tab when first accessed."""
        if self.tabs_initialized['configuration']:
            return
            
        # Clear placeholder
        for widget in self.configuration_frame.winfo_children():
            widget.destroy()
            
        # Create the actual configuration content
        self.create_configuration_content()
        self.tabs_initialized['configuration'] = True
    
    def initialize_monitoring_tab(self):
        """Initialize monitoring tab when first accessed."""
        if self.tabs_initialized['monitoring']:
            return
            
        # Clear placeholder
        for widget in self.monitoring_frame.winfo_children():
            widget.destroy()
            
        # Create the actual monitoring content
        self.create_monitoring_content()
        self.tabs_initialized['monitoring'] = True
    
    def initialize_statistics_tab(self):
        """Initialize statistics tab when first accessed."""
        if self.tabs_initialized['statistics']:
            return
            
        # Clear placeholder
        for widget in self.statistics_frame.winfo_children():
            widget.destroy()
            
        # Create the actual statistics content
        self.create_statistics_content()
        self.tabs_initialized['statistics'] = True
    
    
    def initialize_logs_tab(self):
        """Initialize logs tab when first accessed."""
        if self.tabs_initialized['logs']:
            return
            
        # Clear placeholder
        for widget in self.logs_frame.winfo_children():
            widget.destroy()
            
        # Create the actual logs content
        self.create_logs_content()
        self.tabs_initialized['logs'] = True

    def initialize_symbol_search_tab(self):
        """Initialize symbol search tab when first accessed."""
        if self.tabs_initialized['symbol_search']:
            return
            
        # Clear placeholder
        for widget in self.symbol_search_frame.winfo_children():
            widget.destroy()
            
        # Create the actual symbol search content
        self.create_symbol_search_content()
        self.tabs_initialized['symbol_search'] = True
        
    def setup_styles(self):
        """Configure modern professional trading GUI styles and themes."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Enhanced professional trading color scheme  
        style.configure('TNotebook', 
                       background=self.colors['bg_panel'],
                       borderwidth=0,
                       tabmargins=[2, 5, 2, 0])
        style.configure('TNotebook.Tab', 
                       background=self.colors['bg_input'],
                       foreground=self.colors['text_primary'],
                       padding=[16, 10],
                       focuscolor='none',
                       borderwidth=1,
                       relief='solid')
        style.map('TNotebook.Tab',
                 background=[('selected', self.colors['accent_blue']),
                           ('active', self.colors['border_light']),
                           ('!active', self.colors['bg_input'])],
                 foreground=[('selected', 'white'),
                           ('active', self.colors['text_primary'])])
        
        style.configure('TFrame', background=self.colors['bg_panel'])
        style.configure('TLabel', 
                       background=self.colors['bg_panel'],
                       foreground=self.colors['text_primary'])
        style.configure('TLabelFrame', 
                       background=self.colors['bg_panel'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1,
                       relief='solid')
        style.configure('TLabelFrame.Label', 
                       background=self.colors['bg_panel'],
                       foreground=self.colors['accent_blue'])
        
        # Enhanced button styles with modern appearance
        style.configure('TButton', 
                       background=self.colors['bg_input'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1,
                       relief='solid',
                       focuscolor='none',
                       padding=[12, 6])
        style.map('TButton',
                 background=[('active', self.colors['border_light']),
                           ('pressed', self.colors['accent_blue']),
                           ('disabled', self.colors['bg_input'])],
                 foreground=[('active', self.colors['text_primary']),
                           ('pressed', 'white'),
                           ('disabled', self.colors['text_secondary'])],
                 relief=[('pressed', 'sunken'),
                        ('!pressed', 'solid')])
        
        # Enhanced special button styles
        style.configure('Green.TButton',
                       background=self.colors['accent_green'],
                       foreground='white',
                       borderwidth=1,
                       relief='solid',
                       padding=[12, 6])
        style.map('Green.TButton',
                 background=[('active', '#00b347'),
                           ('pressed', '#006633'),
                           ('disabled', '#cccccc')],
                 relief=[('pressed', 'sunken'),
                        ('!pressed', 'solid')])
        
        style.configure('Red.TButton',
                       background=self.colors['accent_red'],
                       foreground='white',
                       borderwidth=1,
                       relief='solid',
                       padding=[12, 6])
        style.map('Red.TButton',
                 background=[('active', '#e63939'),
                           ('pressed', '#c82333'),
                           ('disabled', '#cccccc')],
                 relief=[('pressed', 'sunken'),
                        ('!pressed', 'solid')])
        
        # Input controls
        style.configure('TCheckbutton', 
                       background=self.colors['bg_panel'],
                       foreground=self.colors['text_primary'],
                       focuscolor='none')
        style.configure('TCombobox',
                       fieldbackground=self.colors['bg_input'],
                       background=self.colors['bg_input'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1)
        style.configure('TSpinbox',
                       fieldbackground=self.colors['bg_input'],
                       background=self.colors['bg_input'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1)
        
        # Treeview for contract hierarchy
        style.configure('Treeview',
                       background=self.colors['bg_input'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_input'],
                       borderwidth=1)
        style.configure('Treeview.Heading',
                       background=self.colors['accent_blue'],
                       foreground='white')
        style.map('Treeview',
                 background=[('selected', self.colors['accent_blue'])])
        
    def create_control_panel_content(self):
        """Create the main control panel content."""
        # Create main sections with improved layout
        # Create a paned window for better space management
        main_paned = ttk.PanedWindow(self.control_frame, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left side: Symbol and data type selection
        left_section = ttk.Frame(main_paned)
        main_paned.add(left_section, weight=3)
        
        # Right side: Controls and actions
        right_section = ttk.Frame(main_paned)
        main_paned.add(right_section, weight=1)
        
        # Populate sections
        self.create_symbol_selection_section(left_section)
        self.create_data_type_selection_section(right_section)
        self.create_collection_controls_section(right_section)
        self.create_quick_actions_section(right_section)
        
    def create_symbol_selection_section(self, parent):
        """Create professional hierarchical symbol selection interface."""
        # Symbol selection frame
        symbol_frame = ttk.LabelFrame(parent, text="🎯 Multi-Asset Contract Selection", padding=10)
        symbol_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Selection controls frame
        controls_frame = ttk.Frame(symbol_frame)
        controls_frame.pack(fill='x', pady=(0, 10))
        
        # Quick selection buttons
        quick_frame = ttk.Frame(controls_frame)
        quick_frame.pack(side='left')
        
        ttk.Button(quick_frame, text="Select All Parents", 
                  command=self.select_all_parents).pack(side='left', padx=(0, 5))
        ttk.Button(quick_frame, text="Clear All", 
                  command=self.clear_all_selections).pack(side='left', padx=(0, 5))
        ttk.Button(quick_frame, text="Front Month Only", 
                  command=self.select_front_month_only).pack(side='left', padx=(0, 5))
        ttk.Button(quick_frame, text="Front 2 Months", 
                  command=self.select_front_two_months).pack(side='left', padx=(0, 5))
        
        # Contract hierarchy container
        hierarchy_container = ttk.Frame(symbol_frame)
        hierarchy_container.pack(fill='both', expand=True)
        
        # Left panel: Parent contract tree
        left_panel = ttk.LabelFrame(hierarchy_container, text="Contract Families", padding=5)
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 5))
        
        # Contract tree with checkboxes
        tree_frame = ttk.Frame(left_panel)
        tree_frame.pack(fill='both', expand=True)
        
        # Create Treeview for hierarchical display
        self.contract_tree = ttk.Treeview(tree_frame, 
                                         columns=('Exchange', 'Tick Size', 'Point Value'),
                                         height=15)
        self.contract_tree.pack(side='left', fill='both', expand=True)
        
        # Configure tree columns
        self.contract_tree.heading('#0', text='Contract', anchor='w')
        self.contract_tree.heading('Exchange', text='Exchange')
        self.contract_tree.heading('Tick Size', text='Tick Size')
        self.contract_tree.heading('Point Value', text='Point Value')
        
        self.contract_tree.column('#0', width=200, stretch=True)
        self.contract_tree.column('Exchange', width=80, stretch=False)
        self.contract_tree.column('Tick Size', width=80, stretch=False)
        self.contract_tree.column('Point Value', width=80, stretch=False)
        
        # Add scrollbar to tree
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.contract_tree.yview)
        tree_scrollbar.pack(side='right', fill='y')
        self.contract_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # Right panel: Selected contracts
        right_panel = ttk.LabelFrame(hierarchy_container, text="Selected Contracts", padding=5)
        right_panel.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # Selected contracts listbox
        self.selected_listbox = tk.Listbox(right_panel, 
                                          bg=self.colors['bg_input'],
                                          fg=self.colors['text_primary'],
                                          selectbackground=self.colors['accent_blue'],
                                          height=15)
        self.selected_listbox.pack(side='left', fill='both', expand=True)
        
        # Add scrollbar to selected list
        selected_scrollbar = ttk.Scrollbar(right_panel, orient='vertical', command=self.selected_listbox.yview)
        selected_scrollbar.pack(side='right', fill='y')
        self.selected_listbox.configure(yscrollcommand=selected_scrollbar.set)
        
        # Selection info frame
        info_frame = ttk.Frame(symbol_frame)
        info_frame.pack(fill='x', pady=(10, 0))
        
        self.selection_info = ttk.Label(info_frame, text="No contracts selected")
        self.selection_info.pack(side='left')
        
        # Initialize the contract tree
        self.populate_contract_tree()
        
        # Bind tree selection events
        self.contract_tree.bind('<Double-1>', self.on_tree_double_click)
        self.contract_tree.bind('<Button-3>', self.on_tree_right_click)  # Right click menu
        
        # Initialize contract tracking variables
        self.contract_vars = {}  # For backward compatibility
        self.parent_selections = {}  # Track parent contract selections
        
        # Create context menu for tree
        self.create_tree_context_menu()
            
    def create_data_type_selection_section(self, parent):
        """Create simplified data type selection interface."""
        datatype_frame = ttk.LabelFrame(parent, text="📈 Data Collection Types", padding=10)
        datatype_frame.pack(fill='x', padx=5, pady=5)
        
        # Create simple frame for data types (no scrolling needed)
        main_frame = ttk.Frame(datatype_frame)
        main_frame.pack(fill='both', expand=True)
        
        # Enhanced data type selection with simplified information
        self.selected_data_types = {}
        
        for data_type, info in self.data_types.items():
            # Create frame for each data type with reduced spacing
            type_frame = ttk.Frame(main_frame)
            type_frame.pack(fill='x', pady=2, padx=2)
            
            var = tk.BooleanVar(value=(data_type == 'level1'))  # Default selection Level 1
            self.selected_data_types[data_type] = var
            
            # Main checkbox
            cb = ttk.Checkbutton(type_frame, text=info['name'], variable=var,
                               command=self.update_data_type_selection)
            cb.pack(anchor='w')
            
            # Description label
            desc_label = ttk.Label(type_frame, text=info['description'], 
                                 foreground=self.colors['text_secondary'])
            desc_label.pack(anchor='w', padx=(20, 0))
            
            # Details label
            details_label = ttk.Label(type_frame, text=info['details'], 
                                    foreground=self.colors['text_secondary'])
            details_label.pack(anchor='w', padx=(20, 0))
            
    def create_collection_controls_section(self, parent):
        """Create collection control buttons and settings."""
        controls_frame = ttk.LabelFrame(parent, text="🎮 Collection Controls", padding=10)
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        # Control buttons
        button_frame = ttk.Frame(controls_frame)
        button_frame.pack(fill='x', pady=(0, 10))
        
        self.start_btn = ttk.Button(button_frame, text="🚀 Start", 
                                   command=self.start_collection, style='Green.TButton')
        self.start_btn.pack(side='left', padx=(0, 5))
        
        self.stop_btn = ttk.Button(button_frame, text="🛑 Stop", 
                                  command=self.stop_collection, state='disabled', 
                                  style='Red.TButton')
        self.stop_btn.pack(side='left', padx=(0, 5))
        
        self.restart_btn = ttk.Button(button_frame, text="🔄 Restart", 
                                     command=self.restart_collection, state='disabled')
        self.restart_btn.pack(side='left', padx=(0, 5))
        
        # Add some spacing between action buttons and controls
        spacer_frame = ttk.Frame(button_frame)
        spacer_frame.pack(side='left', padx=20)
        
        # Auto-restart checkbox in the same row as buttons
        self.auto_restart_cb = ttk.Checkbutton(button_frame, text="Auto-restart on failure", 
                                              variable=self.auto_restart)
        self.auto_restart_cb.pack(side='left', padx=(10, 0))
        
        # Show Code button
        self.show_code_btn = ttk.Button(button_frame, text="📄 Show Code", 
                                       command=self.show_data_collection_code)
        self.show_code_btn.pack(side='right', padx=(5, 0))
        
    def create_quick_actions_section(self, parent):
        """Create application shortcut buttons."""
        quick_frame = ttk.LabelFrame(parent, text="🔧 Application Shortcuts", padding=10)
        quick_frame.pack(fill='x', padx=10, pady=5)
        
        actions_frame = ttk.Frame(quick_frame)
        actions_frame.pack(fill='x')
        
        ttk.Button(actions_frame, text="💾 Save Configuration", 
                  command=self.save_configuration).pack(side='left', padx=(0, 5))
        ttk.Button(actions_frame, text="📂 Load Configuration", 
                  command=self.load_configuration_file).pack(side='left', padx=(0, 5))
        ttk.Button(actions_frame, text="📄 Export Configuration", 
                  command=self.export_configuration).pack(side='left', padx=(0, 5))
        ttk.Button(actions_frame, text="🔄 Reset to Defaults", 
                  command=self.reset_configuration).pack(side='right', padx=(5, 0))
        
    def create_monitoring_content(self):
        """Create the monitoring and status content."""
        
        # Process status section
        status_frame = ttk.LabelFrame(self.monitoring_frame, text="🔍 Process Status", padding=10)
        status_frame.pack(fill='x', padx=10, pady=5)
        
        # Status display with professional colors
        self.status_text = scrolledtext.ScrolledText(status_frame, height=8, wrap=tk.WORD,
                                                    bg=self.colors['bg_input'], 
                                                    fg=self.colors['accent_green'], 
                                                    font=('Consolas', 10),
                                                    insertbackground=self.colors['text_primary'])
        self.status_text.pack(fill='both', expand=True)
        
        # Data collection metrics
        metrics_frame = ttk.LabelFrame(self.monitoring_frame, text="📈 Collection Metrics", padding=10)
        metrics_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Metrics display with professional colors
        self.metrics_text = scrolledtext.ScrolledText(metrics_frame, height=15, wrap=tk.WORD,
                                                     bg=self.colors['bg_input'], 
                                                     fg=self.colors['text_primary'], 
                                                     font=('Consolas', 9),
                                                     insertbackground=self.colors['text_primary'])
        self.metrics_text.pack(fill='both', expand=True)
        
        # Refresh button
        ttk.Button(self.monitoring_frame, text="🔄 Refresh Status", 
                  command=self.refresh_monitoring).pack(pady=5)
        
    def create_statistics_content(self):
        """Create statistics and analytics content."""
        # File statistics
        file_stats_frame = ttk.LabelFrame(self.statistics_frame, text="📁 Data Files", padding=10)
        file_stats_frame.pack(fill='x', padx=10, pady=5)
        
        self.file_stats_text = scrolledtext.ScrolledText(file_stats_frame, height=10, wrap=tk.WORD,
                                                        bg=self.colors['bg_input'], 
                                                        fg=self.colors['text_primary'], 
                                                        font=('Consolas', 9),
                                                        insertbackground=self.colors['text_primary'])
        self.file_stats_text.pack(fill='both', expand=True)
        
        # Database statistics
        db_stats_frame = ttk.LabelFrame(self.statistics_frame, text="🗄️ Database Statistics", padding=10)
        db_stats_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.db_stats_text = scrolledtext.ScrolledText(db_stats_frame, height=10, wrap=tk.WORD,
                                                      bg=self.colors['bg_input'], 
                                                      fg=self.colors['text_primary'], 
                                                      font=('Consolas', 9),
                                                      insertbackground=self.colors['text_primary'])
        self.db_stats_text.pack(fill='both', expand=True)
        
        # Statistics controls
        stats_controls = ttk.Frame(self.statistics_frame)
        stats_controls.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(stats_controls, text="🔄 Refresh Statistics", 
                  command=self.refresh_statistics).pack(side='left', padx=(0, 5))
        ttk.Button(stats_controls, text="📊 Export Statistics", 
                  command=self.export_statistics).pack(side='left', padx=(0, 5))
        ttk.Button(stats_controls, text="📄 Show Code", 
                  command=self.show_statistics_code).pack(side='left', padx=(0, 5))
        ttk.Button(stats_controls, text="🧹 Clear Old Data", 
                  command=self.clear_old_data).pack(side='right')
    
        
    
    
    
        
    def create_logs_content(self):
        """Create real-time logs viewing content."""
        # Log display with professional terminal colors
        self.log_text = scrolledtext.ScrolledText(self.logs_frame, wrap=tk.WORD,
                                                 bg=self.colors['bg_dark'], 
                                                 fg=self.colors['accent_green'], 
                                                 font=('Consolas', 9),
                                                 insertbackground=self.colors['text_primary'])
        self.log_text.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Log controls
        log_controls = ttk.Frame(self.logs_frame)
        log_controls.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(log_controls, text="🧹 Clear Logs", 
                  command=self.clear_logs).pack(side='left', padx=(0, 5))
        ttk.Button(log_controls, text="💾 Save Logs", 
                  command=self.save_logs).pack(side='left', padx=(0, 5))
        
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_controls, text="Auto-scroll", 
                       variable=self.auto_scroll_var).pack(side='left', padx=(20, 0))
        
        # Log level filter
        ttk.Label(log_controls, text="Filter:").pack(side='right', padx=(0, 5))
        self.log_filter = ttk.Combobox(log_controls, values=["All", "INFO", "WARNING", "ERROR"], 
                                      width=10, state='readonly')
        self.log_filter.set("All")
        self.log_filter.pack(side='right')
        
    def create_configuration_content(self):
        """Create enhanced configuration management content with .env file integration."""
        # Create main notebook for configuration sections
        config_notebook = ttk.Notebook(self.configuration_frame)
        config_notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Environment Configuration Tab
        self.create_env_config_tab(config_notebook)
        
        # Configuration controls
        config_controls = ttk.Frame(self.configuration_frame)
        config_controls.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(config_controls, text="💾 Save All Configurations", 
                  command=self.save_all_configurations).pack(side='left', padx=(0, 5))
        ttk.Button(config_controls, text="📂 Load Configuration", 
                  command=self.load_configuration_file).pack(side='left', padx=(0, 5))
        ttk.Button(config_controls, text="🔄 Reset to Defaults", 
                  command=self.reset_configuration).pack(side='left', padx=(0, 5))
        ttk.Button(config_controls, text="📄 Show Code", 
                  command=self.show_configuration_code).pack(side='left', padx=(0, 5))
        ttk.Button(config_controls, text="🧪 Test Connections", 
                  command=self.test_all_connections).pack(side='right', padx=(5, 0))

    def create_symbol_search_content(self):
        """Create comprehensive symbol search and static data management interface."""
        # Main container with padding
        main_container = ttk.Frame(self.symbol_search_frame, padding=15)
        main_container.pack(fill='both', expand=True)
        
        # Title
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill='x', pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="🔍 Symbol Search & Static Data Management", 
                               font=('Arial', 16, 'bold'), foreground='#2c3e50')
        title_label.pack(side='left')
        
        # Status indicator
        self.symbol_search_status = ttk.Label(title_frame, text="", foreground='#27ae60')
        self.symbol_search_status.pack(side='right')
        
        # Create main paned window
        main_paned = ttk.PanedWindow(main_container, orient='horizontal')
        main_paned.pack(fill='both', expand=True)
        
        # Left panel - Search and filters
        left_panel = ttk.LabelFrame(main_paned, text="🔍 Search & Filters", padding=10)
        main_paned.add(left_panel, weight=1)
        
        # Search controls
        search_frame = ttk.Frame(left_panel)
        search_frame.pack(fill='x', pady=(0, 15))
        
        # Exchange filter
        ttk.Label(search_frame, text="Exchange:").grid(row=0, column=0, sticky='w', pady=2)
        self.exchange_filter_var = tk.StringVar()
        exchange_combo = ttk.Combobox(search_frame, textvariable=self.exchange_filter_var,
                                     values=['All', 'CME', 'CBOT', 'NYMEX', 'COMEX', 'ICE'], 
                                     width=15, state='readonly')
        exchange_combo.set('All')
        exchange_combo.grid(row=0, column=1, sticky='w', padx=(10, 0))
        
        # Symbol pattern
        ttk.Label(search_frame, text="Symbol Pattern:").grid(row=1, column=0, sticky='w', pady=2)
        self.symbol_pattern_var = tk.StringVar()
        pattern_entry = ttk.Entry(search_frame, textvariable=self.symbol_pattern_var, width=20)
        pattern_entry.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # Product code filter
        ttk.Label(search_frame, text="Product Code:").grid(row=2, column=0, sticky='w', pady=2)
        self.product_code_var = tk.StringVar()
        product_combo = ttk.Combobox(search_frame, textvariable=self.product_code_var,
                                    values=['All', 'ES', 'NQ', 'YM', 'RTY', 'ZN', 'ZB', 'ZF', 'ZT'], 
                                    width=15, state='readonly')
        product_combo.set('All')
        product_combo.grid(row=2, column=1, sticky='w', padx=(10, 0))
        
        # Search buttons
        button_frame = ttk.Frame(search_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(15, 0), sticky='w')
        
        ttk.Button(button_frame, text="🔍 Search Database", 
                  command=self.search_database_symbols).pack(side='left', padx=(0, 5))
        ttk.Button(button_frame, text="📡 Discover Symbols", 
                  command=self.discover_new_symbols).pack(side='left', padx=(0, 5))
        ttk.Button(button_frame, text="🧹 Clear", 
                  command=self.clear_symbol_search).pack(side='left', padx=(0, 5))
        
        # Examples
        examples_frame = ttk.LabelFrame(left_panel, text="Pattern Examples", padding=10)
        examples_frame.pack(fill='x', pady=(15, 0))
        
        examples_text = """ES* - All E-mini S&P 500 contracts
NQ* - All E-mini NASDAQ contracts
*Z25 - All December 2025 contracts
ES*24 - All E-mini S&P 500 2024 contracts
*H* - All March contracts"""
        ttk.Label(examples_frame, text=examples_text, justify='left', 
                 font=('Consolas', 9)).pack(anchor='w')
        
        # Code generation section
        code_frame = ttk.LabelFrame(left_panel, text="📄 Code Generation", padding=10)
        code_frame.pack(fill='x', pady=(15, 0))
        
        ttk.Button(code_frame, text="📄 Symbol Search Script", 
                  command=self.show_symbol_search_code).pack(fill='x', pady=2)
        ttk.Button(code_frame, text="🚀 Multi-threaded Query Script", 
                  command=self.show_multi_threaded_query_code).pack(fill='x', pady=2)
        
        # Right panel - Results
        right_panel = ttk.LabelFrame(main_paned, text="📊 Symbol Results", padding=10)
        main_paned.add(right_panel, weight=2)
        
        # Results controls
        results_controls = ttk.Frame(right_panel)
        results_controls.pack(fill='x', pady=(0, 10))
        
        self.results_count_label = ttk.Label(results_controls, text="Results: 0", 
                                           font=('Arial', 10, 'bold'))
        self.results_count_label.pack(side='left')
        
        # Results management buttons
        results_buttons = ttk.Frame(results_controls)
        results_buttons.pack(side='right')
        
        ttk.Button(results_buttons, text="📤 Export CSV", 
                  command=self.export_symbol_results).pack(side='left', padx=(0, 5))
        ttk.Button(results_buttons, text="💾 Save to Database", 
                  command=self.save_symbols_to_database).pack(side='left', padx=(0, 5))
        ttk.Button(results_buttons, text="📊 Show Details", 
                  command=self.show_symbol_details).pack(side='left', padx=(0, 5))
        
        # Results treeview
        results_tree_frame = ttk.Frame(right_panel)
        results_tree_frame.pack(fill='both', expand=True)
        
        # Column definitions
        columns = ('Symbol', 'Exchange', 'Product', 'Description', 'Tick Size', 'Contract Size', 'Status')
        
        self.symbols_tree = ttk.Treeview(results_tree_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.symbols_tree.heading('Symbol', text='Symbol', anchor='w')
        self.symbols_tree.heading('Exchange', text='Exchange', anchor='w')
        self.symbols_tree.heading('Product', text='Product', anchor='w')
        self.symbols_tree.heading('Description', text='Description', anchor='w')
        self.symbols_tree.heading('Tick Size', text='Tick Size', anchor='center')
        self.symbols_tree.heading('Contract Size', text='Contract Size', anchor='center')
        self.symbols_tree.heading('Status', text='Status', anchor='center')
        
        # Column widths
        self.symbols_tree.column('Symbol', width=80)
        self.symbols_tree.column('Exchange', width=80)
        self.symbols_tree.column('Product', width=60)
        self.symbols_tree.column('Description', width=200)
        self.symbols_tree.column('Tick Size', width=80)
        self.symbols_tree.column('Contract Size', width=100)
        self.symbols_tree.column('Status', width=80)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(results_tree_frame, orient="vertical", command=self.symbols_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_tree_frame, orient="horizontal", command=self.symbols_tree.xview)
        self.symbols_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.symbols_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        results_tree_frame.grid_rowconfigure(0, weight=1)
        results_tree_frame.grid_columnconfigure(0, weight=1)
        
        # Bind double-click for details
        self.symbols_tree.bind('<Double-1>', self.on_symbol_double_click)
        
        # Initialize with empty results
        self.symbol_search_results = []
        self.update_symbol_search_status("Ready for symbol search")

    def search_database_symbols(self):
        """Search for symbols in the database."""
        try:
            self.update_symbol_search_status("Searching database...")
            
            # Clear previous results
            for item in self.symbols_tree.get_children():
                self.symbols_tree.delete(item)
            
            # Get search parameters
            exchange = self.exchange_filter_var.get() if self.exchange_filter_var.get() != 'All' else None
            pattern = self.symbol_pattern_var.get().strip() if self.symbol_pattern_var.get().strip() else None
            product = self.product_code_var.get() if self.product_code_var.get() != 'All' else None
            
            # Build query
            query = "SELECT DISTINCT symbol, exchange, product_code, symbol_name, tick_size, lot_size FROM symbols WHERE 1=1"
            params = []
            
            if exchange:
                query += " AND exchange = %s"
                params.append(exchange)
            
            if pattern:
                # Convert wildcard pattern to SQL LIKE pattern
                sql_pattern = pattern.replace('*', '%')
                query += " AND symbol LIKE %s"
                params.append(sql_pattern)
                
            if product:
                query += " AND product_code = %s"
                params.append(product)
                
            query += " ORDER BY symbol"
            
            # Execute query through database viewer if available
            if hasattr(self, 'database_viewer') and self.database_viewer.db_manager:
                results = self.database_viewer.db_manager.execute_query(query, params)
            else:
                self.log_message("⚠️ Database connection not available")
                messagebox.showwarning("Database Not Available", 
                                     "Please connect to database in Database Viewer tab first.")
                return
                
            # Process results
            self.symbol_search_results = []
            for row in results:
                symbol_info = {
                    'symbol': row.get('symbol', ''),
                    'exchange': row.get('exchange', ''),
                    'product_code': row.get('product_code', ''),
                    'description': row.get('symbol_name', ''),
                    'tick_size': row.get('tick_size', 0.0),
                    'contract_size': row.get('lot_size', 0),
                    'status': 'In Database'
                }
                self.symbol_search_results.append(symbol_info)
                
                # Add to treeview
                self.symbols_tree.insert('', 'end', values=(
                    symbol_info['symbol'],
                    symbol_info['exchange'],
                    symbol_info['product_code'],
                    symbol_info['description'],
                    f"{symbol_info['tick_size']:.6f}".rstrip('0').rstrip('.'),
                    f"{symbol_info['contract_size']:,}",
                    symbol_info['status']
                ))
                
            # Update status
            self.results_count_label.config(text=f"Results: {len(results)}")
            self.update_symbol_search_status(f"Found {len(results)} symbols in database")
            self.log_message(f"🔍 Found {len(results)} symbols matching search criteria")
            
        except Exception as e:
            error_msg = f"Error searching database: {str(e)}"
            self.update_symbol_search_status(f"Error: {str(e)}")
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Search Error", error_msg)

    def discover_new_symbols(self):
        """Discover new symbols using API or predefined lists."""
        try:
            self.update_symbol_search_status("Discovering new symbols...")
            
            # Sample symbols for demonstration (in production, this would call APIs)
            sample_symbols = {
                'ES': ['ESZ24', 'ESH25', 'ESM25', 'ESU25', 'ESZ25', 'ESH26'],
                'NQ': ['NQZ24', 'NQH25', 'NQM25', 'NQU25', 'NQZ25', 'NQH26'],
                'YM': ['YMZ24', 'YMH25', 'YMM25', 'YMU25', 'YMZ25', 'YMH26'],
                'RTY': ['RTYZ24', 'RTYH25', 'RTYM25', 'RTYU25', 'RTYZ25'],
                'ZN': ['ZNZ24', 'ZNH25', 'ZNM25', 'ZNU25', 'ZNZ25'],
                'ZB': ['ZBZ24', 'ZBH25', 'ZBM25', 'ZBU25', 'ZBZ25'],
                'CL': ['CLZ24', 'CLF25', 'CLG25', 'CLH25', 'CLJ25'],
                'GC': ['GCZ24', 'GCG25', 'GCJ25', 'GCM25', 'GCQ25']
            }
            
            # Filter by product code if specified
            product_filter = self.product_code_var.get()
            if product_filter != 'All':
                sample_symbols = {product_filter: sample_symbols.get(product_filter, [])}
            
            # Filter by pattern if specified
            pattern = self.symbol_pattern_var.get().strip()
            if pattern:
                import re
                pattern_regex = re.compile(pattern.replace('*', '.*'), re.IGNORECASE)
                filtered_symbols = {}
                for product, symbols in sample_symbols.items():
                    filtered_symbols[product] = [s for s in symbols if pattern_regex.match(s)]
                sample_symbols = filtered_symbols
            
            # Clear previous results
            for item in self.symbols_tree.get_children():
                self.symbols_tree.delete(item)
                
            self.symbol_search_results = []
            total_discovered = 0
            
            # Process discovered symbols
            for product_code, symbols in sample_symbols.items():
                for symbol in symbols:
                    # Generate symbol metadata
                    symbol_info = self.generate_symbol_metadata(symbol, product_code)
                    self.symbol_search_results.append(symbol_info)
                    
                    # Add to treeview
                    self.symbols_tree.insert('', 'end', values=(
                        symbol_info['symbol'],
                        symbol_info['exchange'],
                        symbol_info['product_code'],
                        symbol_info['description'],
                        f"{symbol_info['tick_size']:.6f}".rstrip('0').rstrip('.'),
                        f"{symbol_info['contract_size']:,}",
                        symbol_info['status']
                    ))
                    total_discovered += 1
            
            # Update status
            self.results_count_label.config(text=f"Results: {total_discovered}")
            self.update_symbol_search_status(f"Discovered {total_discovered} new symbols")
            self.log_message(f"🔍 Discovered {total_discovered} symbols")
            
        except Exception as e:
            error_msg = f"Error discovering symbols: {str(e)}"
            self.update_symbol_search_status(f"Error: {str(e)}")
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Discovery Error", error_msg)

    def generate_symbol_metadata(self, symbol, product_code):
        """Generate metadata for a symbol based on product code patterns."""
        # Basic metadata mapping
        metadata_map = {
            'ES': {'exchange': 'CME', 'description': 'E-mini S&P 500', 'tick_size': 0.25, 'contract_size': 50},
            'NQ': {'exchange': 'CME', 'description': 'E-mini NASDAQ-100', 'tick_size': 0.25, 'contract_size': 20},
            'YM': {'exchange': 'CBOT', 'description': 'E-mini Dow Jones', 'tick_size': 1.0, 'contract_size': 5},
            'RTY': {'exchange': 'CME', 'description': 'E-mini Russell 2000', 'tick_size': 0.1, 'contract_size': 50},
            'ZN': {'exchange': 'CBOT', 'description': '10-Year Treasury Note', 'tick_size': 0.015625, 'contract_size': 100000},
            'ZB': {'exchange': 'CBOT', 'description': '30-Year Treasury Bond', 'tick_size': 0.03125, 'contract_size': 100000},
            'CL': {'exchange': 'NYMEX', 'description': 'Crude Oil', 'tick_size': 0.01, 'contract_size': 1000},
            'GC': {'exchange': 'COMEX', 'description': 'Gold', 'tick_size': 0.1, 'contract_size': 100}
        }
        
        base_info = metadata_map.get(product_code, {
            'exchange': 'CME', 
            'description': f'{product_code} Contract', 
            'tick_size': 0.01, 
            'contract_size': 1
        })
        
        return {
            'symbol': symbol,
            'exchange': base_info['exchange'],
            'product_code': product_code,
            'description': f"{base_info['description']} ({symbol})",
            'tick_size': base_info['tick_size'],
            'contract_size': base_info['contract_size'],
            'status': 'Discovered'
        }

    def clear_symbol_search(self):
        """Clear symbol search results and filters."""
        self.exchange_filter_var.set('All')
        self.symbol_pattern_var.set('')
        self.product_code_var.set('All')
        
        for item in self.symbols_tree.get_children():
            self.symbols_tree.delete(item)
            
        self.symbol_search_results = []
        self.results_count_label.config(text="Results: 0")
        self.update_symbol_search_status("Search cleared")
        self.log_message("🧹 Symbol search cleared")

    def export_symbol_results(self):
        """Export symbol search results to CSV."""
        if not self.symbol_search_results:
            messagebox.showwarning("No Results", "No symbol results to export.")
            return
            
        try:
            from tkinter import filedialog
            import csv
            from datetime import datetime
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialvalue=f"symbol_search_results_{timestamp}.csv"
            )
            
            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['symbol', 'exchange', 'product_code', 'description', 'tick_size', 'contract_size', 'status']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(self.symbol_search_results)
                    
                self.log_message(f"📤 Exported {len(self.symbol_search_results)} symbols to {filename}")
                messagebox.showinfo("Export Complete", f"Exported {len(self.symbol_search_results)} symbols to:\n{filename}")
                
        except Exception as e:
            error_msg = f"Error exporting results: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Export Error", error_msg)

    def save_symbols_to_database(self):
        """Save discovered symbols to database."""
        if not self.symbol_search_results:
            messagebox.showwarning("No Results", "No symbol results to save.")
            return
            
        try:
            # Check database connection
            if not hasattr(self, 'database_viewer') or not self.database_viewer.db_manager:
                messagebox.showwarning("Database Not Available", 
                                     "Please connect to database in Database Viewer tab first.")
                return
                
            # Create symbols table if not exists
            create_table_query = """
                CREATE TABLE IF NOT EXISTS symbols (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL UNIQUE,
                    exchange VARCHAR(20),
                    product_code VARCHAR(10),
                    symbol_name TEXT,
                    tick_size DECIMAL(10,6),
                    lot_size INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_exchange (exchange),
                    INDEX idx_product_code (product_code)
                )
            """
            
            self.database_viewer.db_manager.execute_query(create_table_query)
            
            # Insert symbols
            saved_count = 0
            for symbol_info in self.symbol_search_results:
                try:
                    insert_query = """
                        INSERT INTO symbols (symbol, exchange, product_code, symbol_name, tick_size, lot_size)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                            exchange = VALUES(exchange),
                            product_code = VALUES(product_code),
                            symbol_name = VALUES(symbol_name),
                            tick_size = VALUES(tick_size),
                            lot_size = VALUES(lot_size),
                            updated_at = CURRENT_TIMESTAMP
                    """
                    
                    params = (
                        symbol_info['symbol'],
                        symbol_info['exchange'],
                        symbol_info['product_code'],
                        symbol_info['description'],
                        symbol_info['tick_size'],
                        symbol_info['contract_size']
                    )
                    
                    self.database_viewer.db_manager.execute_query(insert_query, params)
                    saved_count += 1
                    
                except Exception as e:
                    self.log_message(f"⚠️ Error saving symbol {symbol_info['symbol']}: {str(e)}")
                    
            self.log_message(f"💾 Saved {saved_count} symbols to database")
            messagebox.showinfo("Save Complete", f"Saved {saved_count} symbols to database.")
            
        except Exception as e:
            error_msg = f"Error saving to database: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Save Error", error_msg)

    def show_symbol_details(self):
        """Show detailed information for selected symbol."""
        selection = self.symbols_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a symbol to view details.")
            return
            
        try:
            item = self.symbols_tree.item(selection[0])
            symbol = item['values'][0]
            
            # Find symbol in results
            symbol_info = None
            for info in self.symbol_search_results:
                if info['symbol'] == symbol:
                    symbol_info = info
                    break
                    
            if not symbol_info:
                messagebox.showerror("Error", "Symbol information not found.")
                return
                
            # Create details dialog
            dialog = tk.Toplevel(self.root)
            dialog.title(f"Symbol Details - {symbol}")
            dialog.geometry("500x400")
            dialog.transient(self.root)
            
            # Details frame
            details_frame = ttk.Frame(dialog, padding=20)
            details_frame.pack(fill='both', expand=True)
            
            # Title
            ttk.Label(details_frame, text=f"Symbol: {symbol}", 
                     font=('Arial', 14, 'bold')).pack(pady=(0, 20))
            
            # Details table
            details_tree = ttk.Treeview(details_frame, columns=('Value',), show='tree headings', height=10)
            details_tree.heading('#0', text='Property')
            details_tree.heading('Value', text='Value')
            details_tree.column('#0', width=200)
            details_tree.column('Value', width=250)
            
            # Add symbol details
            for key, value in symbol_info.items():
                display_key = key.replace('_', ' ').title()
                details_tree.insert('', 'end', text=display_key, values=(str(value),))
                
            details_tree.pack(fill='both', expand=True, pady=(0, 20))
            
            # Close button
            ttk.Button(details_frame, text="Close", command=dialog.destroy).pack()
            
        except Exception as e:
            error_msg = f"Error showing symbol details: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Details Error", error_msg)

    def on_symbol_double_click(self, event):
        """Handle double-click on symbol for details."""
        self.show_symbol_details()

    def update_symbol_search_status(self, message):
        """Update the symbol search status label."""
        self.symbol_search_status.config(text=message)
        
    def create_env_config_tab(self, parent_notebook):
        """Create environment configuration tab for .env file management."""
        env_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(env_frame, text="🔧 Environment Configuration")
        
        # Create scrollable frame
        canvas = tk.Canvas(env_frame, bg=self.colors['bg_panel'])
        scrollbar = ttk.Scrollbar(env_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Update configuration variables (preserve existing mock entries)
        # env_vars is already initialized with defaults in __init__
        # We'll update it with real tkinter Entry widgets as we create them
        
        # Rithmic API Configuration Section
        api_frame = ttk.LabelFrame(scrollable_frame, text="🔌 Rithmic API Settings", padding=15)
        api_frame.pack(fill='x', padx=10, pady=5)
        
        # API credentials
        self.create_config_field(api_frame, "User", "RITHMIC_USER", "PP-013155")
        self.create_config_field(api_frame, "Password", "RITHMIC_PASSWORD", "b7neA8k6JA", is_password=True)
        self.create_config_field(api_frame, "System", "RITHMIC_SYSTEM", "Rithmic Paper Trading")
        self.create_config_field(api_frame, "Gateway", "RITHMIC_GATEWAY", "Chicago Area")
        
        # API settings
        self.create_config_field(api_frame, "Application Name", "RITHMIC_APP_NAME", "PythonRithmicClient")
        self.create_config_field(api_frame, "Application Version", "RITHMIC_APP_VERSION", "1.0.0")
        self.create_config_field(api_frame, "URI", "RITHMIC_URI", "wss://rprotocol.rithmic.com:443")
        self.create_config_field(api_frame, "SSL Cert Path", "RITHMIC_SSL_CERT_PATH", "etc/rithmic_ssl_cert_auth_params")
        self.create_config_field(api_frame, "Template Version", "RITHMIC_TEMPLATE_VERSION", "3.9")
        
        # Database Configuration Section
        db_frame = ttk.LabelFrame(scrollable_frame, text="🗄️ Database Settings", padding=15)
        db_frame.pack(fill='x', padx=10, pady=5)
        
        self.create_config_field(db_frame, "Host", "MYSQL_HOST", "**************")
        self.create_config_field(db_frame, "Port", "MYSQL_PORT", "3306")
        self.create_config_field(db_frame, "User", "MYSQL_USER", "root")
        self.create_config_field(db_frame, "Password", "MYSQL_PASSWORD", "debian", is_password=True)
        self.create_config_field(db_frame, "Database", "MYSQL_DATABASE", "rithmic_api")
        self.create_config_field(db_frame, "Pool Size", "MYSQL_POOL_SIZE", "10")
        self.create_config_field(db_frame, "Max Overflow", "MYSQL_MAX_OVERFLOW", "20")
        self.create_config_field(db_frame, "Pool Timeout", "MYSQL_POOL_TIMEOUT", "30")
        
        # Contract Configuration Section
        contract_frame = ttk.LabelFrame(scrollable_frame, text="📊 Contract & Data Settings", padding=15)
        contract_frame.pack(fill='x', padx=10, pady=5)
        
        self.create_config_field(contract_frame, "Contracts to Subscribe", "CONTRACTS_TO_SUBSCRIBE", "@ES @NQ")
        self.create_config_field(contract_frame, "Max Concurrent Contracts", "MAX_CONCURRENT_CONTRACTS", "50")
        self.create_config_field(contract_frame, "Data Directory", "DATA_DIRECTORY", "data")
        
        # Load current environment values
        self.load_env_values()
        
    def create_config_field(self, parent, label_text, env_key, default_value, is_password=False):
        """Create a configuration field with label and entry."""
        field_frame = ttk.Frame(parent)
        field_frame.pack(fill='x', pady=2)
        
        # Label
        label = ttk.Label(field_frame, text=f"{label_text}:", width=20, anchor='w')
        label.pack(side='left', padx=(0, 10))
        
        # Entry
        if is_password:
            entry = ttk.Entry(field_frame, show="*", width=40)
        else:
            entry = ttk.Entry(field_frame, width=40)
        entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        # Preserve existing value from mock entry if it exists
        existing_value = default_value
        if env_key in self.env_vars and hasattr(self.env_vars[env_key]['entry'], 'get'):
            try:
                existing_value = self.env_vars[env_key]['entry'].get()
                # Use existing value if it's not empty, otherwise use default
                if existing_value:
                    entry.insert(0, existing_value)
                else:
                    entry.insert(0, default_value)
            except Exception:
                # If getting value fails, use default
                entry.insert(0, default_value)
        else:
            entry.insert(0, default_value)
        
        # Store reference (update existing entry info or create new)
        self.env_vars[env_key] = {
            'entry': entry,
            'default': default_value,
            'label': label_text
        }
        
        # Test connection button for database and API fields
        if env_key in ['MYSQL_HOST', 'RITHMIC_URI']:
            test_btn = ttk.Button(field_frame, text="🧪 Test", width=8,
                                 command=lambda k=env_key: self.test_connection(k))
            test_btn.pack(side='right', padx=(5, 0))
    
    def create_status_bar(self):
        """Create status bar at bottom of window."""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill='x', side='bottom')
        
        self.status_label = ttk.Label(self.status_bar, text="Ready - No collection active")
        self.status_label.pack(side='left', padx=10)
        
        self.process_count_label = ttk.Label(self.status_bar, text="Processes: 0")
        self.process_count_label.pack(side='right', padx=10)
        
    # Control methods
    def start_collection(self):
        """Start the selected data collection processes."""
        try:
            if self.is_collecting:
                messagebox.showwarning("Already Running", "Collection is already active!")
                return
                
            # Get selected contracts from new selection system
            selected_contracts = list(self.selected_specific_contracts)
            if not selected_contracts:
                # Fallback to old system for backward compatibility
                selected_contracts = [contract for contract, var in self.contract_vars.items() 
                                    if var.get()]
            
            selected_types = [dtype for dtype, var in self.selected_data_types.items() 
                            if var.get()]
            
            if not selected_contracts:
                messagebox.showerror("No Selection", "Please select at least one contract!")
                return
                
            if not selected_types:
                messagebox.showerror("No Selection", "Please select at least one data type!")
                return
                
            self.log_message("🚀 Starting comprehensive multi-asset futures data collection...")
            self.log_message(f"📊 Selected contracts: {', '.join(selected_contracts)}")
            self.log_message(f"📈 Selected data types: {', '.join(selected_types)}")
            
            # Start collection processes
            self.collection_processes = {}
            
            for contract in selected_contracts:
                for data_type in selected_types:
                    self.start_collection_process(contract, data_type)
                    
            self.is_collecting = True
            self.update_control_buttons()
            self.status_label.config(text="🚀 Collection Active")
            
            self.log_message("✅ Collection started successfully!")
            
        except Exception as e:
            self.log_message(f"❌ Failed to start collection: {e}")
            messagebox.showerror("Start Failed", f"Failed to start collection: {e}")
            
    def start_collection_process(self, contract, data_type):
        """Start a specific collection process."""
        try:
            process_key = f"{contract}_{data_type}"
            
            # Map data types to collection scripts
            if data_type == "level1":
                cmd = [sys.executable, "src/scripts/subscribe_level1_data.py", contract, "CME"]
            elif data_type == "level2" or data_type == "level3":  # Backward compatibility
                cmd = [sys.executable, "src/scripts/subscribe_depth_by_order.py", contract]
            elif data_type == "depth_by_order":
                cmd = [sys.executable, "src/scripts/subscribe_depth_by_order.py", contract]
            elif data_type == "time_and_sales":
                cmd = [sys.executable, "src/scripts/subscribe_level1_data.py", contract, "CME"]  # Use Level 1 for now
            elif data_type == "historical":
                cmd = [sys.executable, "src/scripts/collect_historical_data.py", contract]
            else:
                self.log_message(f"⚠️ Unknown data type: {data_type}")
                return
                
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.collection_processes[process_key] = {
                'process': process,
                'contract': contract,
                'data_type': data_type,
                'start_time': datetime.now(),
                'cmd': cmd
            }
            
            self.log_message(f"✅ Started {data_type} collection for {contract} (PID: {process.pid})")
            
            # Start monitoring thread for this process
            threading.Thread(target=self.monitor_process, args=(process_key,), daemon=True).start()
            
        except Exception as e:
            self.log_message(f"❌ Failed to start {data_type} for {contract}: {e}")
            
    def stop_collection(self):
        """Stop all collection processes."""
        try:
            if not self.is_collecting:
                messagebox.showinfo("Not Running", "No collection is currently active!")
                return
                
            self.log_message("🛑 Stopping all collection processes...")
            
            for process_key, proc_info in self.collection_processes.items():
                try:
                    process = proc_info['process']
                    if process.poll() is None:  # Process still running
                        self.log_message(f"🛑 Stopping {proc_info['data_type']} for {proc_info['contract']}...")
                        process.terminate()
                        
                        # Wait for graceful shutdown
                        try:
                            process.wait(timeout=10)
                        except subprocess.TimeoutExpired:
                            self.log_message(f"⚠️ Force killing {process_key}...")
                            process.kill()
                            process.wait()
                            
                        self.log_message(f"✅ Stopped {process_key}")
                except Exception as e:
                    self.log_message(f"❌ Error stopping {process_key}: {e}")
                    
            self.collection_processes.clear()
            self.is_collecting = False
            self.update_control_buttons()
            self.status_label.config(text="🛑 Collection Stopped")
            
            self.log_message("✅ All collection processes stopped")
            
        except Exception as e:
            self.log_message(f"❌ Failed to stop collection: {e}")
            messagebox.showerror("Stop Failed", f"Failed to stop collection: {e}")
            
    def restart_collection(self):
        """Restart all collection processes."""
        self.log_message("🔄 Restarting collection...")
        self.stop_collection()
        time.sleep(2)
        self.start_collection()
        
            
    # Quick action methods
        
    # Legacy selection methods for backward compatibility
    def select_all_contracts(self):
        """Select all contracts (legacy compatibility)."""
        self.select_all_parents()
        
    def select_no_contracts(self):
        """Deselect all contracts (legacy compatibility)."""
        self.clear_all_selections()
        
    def populate_contract_tree(self):
        """Populate the contract tree with hierarchical data."""
        # Clear existing items
        for item in self.contract_tree.get_children():
            self.contract_tree.delete(item)
        
        # Populate tree with contract hierarchy
        for category, contracts in self.futures_contracts.items():
            # Create category node
            category_id = self.contract_tree.insert('', 'end', text=category, 
                                                   values=('', '', ''), 
                                                   tags=('category',))
            
            for symbol, info in contracts.items():
                # Create parent contract node with enhanced details
                margin = info.get('margin_req', 'N/A')
                hours = info.get('trading_hours', 'N/A')
                settlement = info.get('settlement', 'N/A')
                details_text = f"{symbol} - {info['name']} | Margin: {margin} | Hours: {hours}"
                
                parent_id = self.contract_tree.insert(category_id, 'end', 
                                                     text=details_text, 
                                                     values=(info['exchange'], 
                                                           f"${info['tick_size']}", 
                                                           f"${info['point_value']}"),
                                                     tags=('parent',))
                
                # Add child contracts
                for contract in info['contracts']:
                    expiry_info = self.get_contract_expiry_info(contract)
                    self.contract_tree.insert(parent_id, 'end', 
                                            text=f"{contract} - {expiry_info}", 
                                            values=(info['exchange'], '', ''),
                                            tags=('child',))
        
        # Configure tag styles
        self.contract_tree.tag_configure('category', background=self.colors['accent_blue'], 
                                        foreground='white')
        self.contract_tree.tag_configure('parent', background=self.colors['bg_input'])
        self.contract_tree.tag_configure('child', background=self.colors['bg_panel'])
        
        # Expand all categories by default
        for item in self.contract_tree.get_children():
            self.contract_tree.item(item, open=True)
    
    def get_contract_expiry_info(self, contract):
        """Get human-readable expiry information for a contract."""
        if len(contract) >= 4:
            month_code = contract[-2]
            year_code = contract[-1]
            
            month_names = {
                'F': 'Jan', 'G': 'Feb', 'H': 'Mar', 'J': 'Apr', 'K': 'May', 'M': 'Jun',
                'N': 'Jul', 'Q': 'Aug', 'U': 'Sep', 'V': 'Oct', 'X': 'Nov', 'Z': 'Dec'
            }
            
            month_name = month_names.get(month_code, month_code)
            year = f"202{year_code}" if year_code.isdigit() else f"20{year_code}"
            
            return f"{month_name} {year}"
        return "Unknown Expiry"
    
    def get_front_month_contracts(self):
        """Calculate front month contracts based on current date."""
        from datetime import datetime
        current_date = datetime.now()
        current_month = current_date.month
        current_year = current_date.year
        
        front_month_contracts = []
        
        for category, contracts in self.futures_contracts.items():
            for symbol, info in contracts.items():
                # Find the nearest expiration contract
                nearest_contract = None
                nearest_months_ahead = float('inf')
                
                for contract in info['contracts']:
                    if len(contract) >= 4:
                        month_code = contract[-2]
                        year_code = contract[-1]
                        
                        if month_code in self.month_codes:
                            contract_month = self.month_codes[month_code]
                            contract_year = 2020 + int(year_code) if year_code.isdigit() else current_year
                            
                            # Calculate months ahead
                            months_ahead = (contract_year - current_year) * 12 + (contract_month - current_month)
                            
                            # Only consider future contracts
                            if months_ahead > 0 and months_ahead < nearest_months_ahead:
                                nearest_months_ahead = months_ahead
                                nearest_contract = contract
                
                if nearest_contract:
                    front_month_contracts.append(nearest_contract)
        
        return front_month_contracts
    
    def select_front_month_only(self):
        """Select only the front month contract for each parent."""
        self.clear_all_selections()
        front_month_contracts = self.get_front_month_contracts()
        
        for contract in front_month_contracts:
            self.add_contract_to_selection(contract)
        
        self.update_selection_display()
    
    def select_front_two_months(self):
        """Select the front two month contracts for each parent."""
        self.clear_all_selections()
        
        for category, contracts in self.futures_contracts.items():
            for symbol, info in contracts.items():
                # Get first two contracts (assuming they're sorted by expiry)
                for i, contract in enumerate(info['contracts'][:2]):
                    self.add_contract_to_selection(contract)
        
        self.update_selection_display()
    
    def select_all_parents(self):
        """Select all contracts for all parent contract families."""
        self.clear_all_selections()
        
        for category, contracts in self.futures_contracts.items():
            for symbol, info in contracts.items():
                for contract in info['contracts']:
                    self.add_contract_to_selection(contract)
        
        self.update_selection_display()
    
    def clear_all_selections(self):
        """Clear all contract selections."""
        self.selected_specific_contracts.clear()
        self.selected_parent_contracts.clear()
        self.update_selection_display()
    
    def add_contract_to_selection(self, contract):
        """Add a specific contract to the selection."""
        self.selected_specific_contracts.add(contract)
        
        # Update backward compatibility vars
        if contract not in self.contract_vars:
            self.contract_vars[contract] = tk.BooleanVar(value=True)
        else:
            self.contract_vars[contract].set(True)
    
    def remove_contract_from_selection(self, contract):
        """Remove a specific contract from the selection."""
        self.selected_specific_contracts.discard(contract)
        
        # Update backward compatibility vars
        if contract in self.contract_vars:
            self.contract_vars[contract].set(False)
    
    def update_selection_display(self):
        """Update the selected contracts display."""
        # Clear the listbox
        self.selected_listbox.delete(0, tk.END)
        
        # Add selected contracts with details
        for contract in sorted(self.selected_specific_contracts):
            contract_info = self.get_contract_display_info(contract)
            self.selected_listbox.insert(tk.END, contract_info)
        
        # Update info label
        count = len(self.selected_specific_contracts)
        self.selection_info.config(text=f"Selected: {count} contracts")
        
        # Update backward compatibility
        self.update_contract_selection()
    
    def get_contract_display_info(self, contract):
        """Get display information for a contract."""
        expiry_info = self.get_contract_expiry_info(contract)
        
        # Find parent info
        for category, contracts in self.futures_contracts.items():
            for symbol, info in contracts.items():
                if contract in info['contracts']:
                    return f"{contract} ({symbol} - {expiry_info}, {info['exchange']})"
        
        return contract
    
    def on_tree_double_click(self, event):
        """Handle tree double-click events."""
        item = self.contract_tree.selection()[0]
        tags = self.contract_tree.item(item, 'tags')
        
        if 'child' in tags:
            # Child contract - toggle selection
            contract_text = self.contract_tree.item(item, 'text')
            contract = contract_text.split(' - ')[0]  # Extract contract symbol
            
            if contract in self.selected_specific_contracts:
                self.remove_contract_from_selection(contract)
            else:
                self.add_contract_to_selection(contract)
            
            self.update_selection_display()
        
        elif 'parent' in tags:
            # Parent contract - select all children
            parent_text = self.contract_tree.item(item, 'text')
            symbol = parent_text.split(' - ')[0]
            
            # Find all contracts for this parent
            for category, contracts in self.futures_contracts.items():
                if symbol in contracts:
                    for contract in contracts[symbol]['contracts']:
                        self.add_contract_to_selection(contract)
            
            self.update_selection_display()
    
    def on_tree_right_click(self, event):
        """Handle tree right-click events for context menu."""
        item = self.contract_tree.identify('item', event.x, event.y)
        if item:
            self.contract_tree.selection_set(item)
            self.tree_context_menu.post(event.x_root, event.y_root)
    
    def create_tree_context_menu(self):
        """Create context menu for tree operations."""
        self.tree_context_menu = tk.Menu(self.root, tearoff=0,
                                        bg=self.colors['bg_panel'],
                                        fg=self.colors['text_primary'])
        
        self.tree_context_menu.add_command(label="Select This Contract", 
                                          command=self.context_select_contract)
        self.tree_context_menu.add_command(label="Select All Contracts (Parent)", 
                                          command=self.context_select_parent)
        self.tree_context_menu.add_separator()
        self.tree_context_menu.add_command(label="Remove from Selection", 
                                          command=self.context_remove_contract)
        self.tree_context_menu.add_separator()
        self.tree_context_menu.add_command(label="View Contract Details", 
                                          command=self.context_view_details)
    
    def context_select_contract(self):
        """Context menu: Select specific contract."""
        item = self.contract_tree.selection()[0]
        self.on_tree_double_click(None)  # Reuse double-click logic
    
    def context_select_parent(self):
        """Context menu: Select all contracts for parent."""
        item = self.contract_tree.selection()[0]
        tags = self.contract_tree.item(item, 'tags')
        
        if 'child' in tags:
            # Find parent
            parent = self.contract_tree.parent(item)
            self.contract_tree.selection_set(parent)
        
        self.on_tree_double_click(None)  # Reuse double-click logic
    
    def context_remove_contract(self):
        """Context menu: Remove contract from selection."""
        item = self.contract_tree.selection()[0]
        tags = self.contract_tree.item(item, 'tags')
        
        if 'child' in tags:
            contract_text = self.contract_tree.item(item, 'text')
            contract = contract_text.split(' - ')[0]
            self.remove_contract_from_selection(contract)
            self.update_selection_display()
    
    def context_view_details(self):
        """Context menu: View contract details."""
        item = self.contract_tree.selection()[0]
        contract_text = self.contract_tree.item(item, 'text')
        
        # Create details popup
        details_window = tk.Toplevel(self.root)
        details_window.title("Contract Details")
        details_window.configure(bg=self.colors['bg_dark'])
        details_window.geometry("600x500")
        details_window.resizable(True, True)
        
        # Get detailed contract information
        detailed_info = self.get_detailed_contract_info(contract_text, item)
        
        # Create scrolled text widget for comprehensive information
        text_widget = scrolledtext.ScrolledText(details_window, wrap=tk.WORD,
                                              bg=self.colors['bg_input'],
                                              fg=self.colors['text_primary'],
                                              font=('Consolas', 10))
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', detailed_info)
        text_widget.config(state='disabled')
        
        # Close button
        close_frame = ttk.Frame(details_window)
        close_frame.pack(fill='x', padx=10, pady=(0, 10))
        ttk.Button(close_frame, text="Close", 
                  command=details_window.destroy).pack(side='right')
    
    def get_detailed_contract_info(self, contract_text, tree_item):
        """Get comprehensive contract information for display."""
        try:
            tags = self.contract_tree.item(tree_item, 'tags')
            
            # Extract contract symbol from text
            if 'child' in tags:
                # Individual contract
                contract_symbol = contract_text.split(' - ')[0]
                parent_item = self.contract_tree.parent(tree_item)
                parent_text = self.contract_tree.item(parent_item, 'text')
                parent_symbol = parent_text.split(' - ')[0]
                
                # Find contract details
                contract_info = self.find_contract_info(parent_symbol)
                if contract_info:
                    return self.format_individual_contract_details(contract_symbol, parent_symbol, contract_info)
                    
            elif 'parent' in tags:
                # Parent contract family
                parent_symbol = contract_text.split(' - ')[0]
                contract_info = self.find_contract_info(parent_symbol)
                if contract_info:
                    return self.format_parent_contract_details(parent_symbol, contract_info)
                    
            elif 'category' in tags:
                # Category information
                return self.format_category_details(contract_text)
                
            return f"Contract Details\n\n{contract_text}\n\nNo detailed information available."
            
        except Exception as e:
            return f"Contract Details\n\nError retrieving information: {e}"
    
    def find_contract_info(self, symbol):
        """Find contract information in the futures_contracts data."""
        for category, contracts in self.futures_contracts.items():
            if symbol in contracts:
                return {
                    'category': category,
                    'symbol': symbol,
                    **contracts[symbol]
                }
        return None
    
    def format_individual_contract_details(self, contract_symbol, parent_symbol, contract_info):
        """Format details for individual contract."""
        expiry_info = self.get_contract_expiry_info(contract_symbol)
        
        details = []
        details.append("🔍 INDIVIDUAL CONTRACT DETAILS")
        details.append("=" * 50)
        details.append(f"Contract Symbol: {contract_symbol}")
        details.append(f"Contract Family: {parent_symbol}")
        details.append(f"Full Name: {contract_info['name']}")
        details.append(f"Expiry: {expiry_info}")
        details.append("")
        
        details.append("📊 TRADING SPECIFICATIONS")
        details.append("-" * 30)
        details.append(f"Exchange: {contract_info['exchange']}")
        details.append(f"Category: {contract_info['category']}")
        details.append(f"Tick Size: ${contract_info['tick_size']}")
        details.append(f"Point Value: ${contract_info['point_value']}")
        details.append("")
        
        if 'margin_req' in contract_info:
            details.append("💰 FINANCIAL REQUIREMENTS")
            details.append("-" * 30)
            details.append(f"Margin Requirement: {contract_info['margin_req']}")
            if 'trading_hours' in contract_info:
                details.append(f"Trading Hours: {contract_info['trading_hours']}")
            if 'settlement' in contract_info:
                details.append(f"Settlement: {contract_info['settlement']}")
            details.append("")
        
        details.append("📋 CONTRACT FAMILY MEMBERS")
        details.append("-" * 30)
        for contract in contract_info['contracts']:
            expiry = self.get_contract_expiry_info(contract)
            marker = "👉 " if contract == contract_symbol else "   "
            details.append(f"{marker}{contract} - {expiry}")
        
        return "\n".join(details)
    
    def format_parent_contract_details(self, parent_symbol, contract_info):
        """Format details for parent contract family."""
        details = []
        details.append("🏗️ CONTRACT FAMILY DETAILS")
        details.append("=" * 50)
        details.append(f"Symbol: {parent_symbol}")
        details.append(f"Full Name: {contract_info['name']}")
        details.append(f"Exchange: {contract_info['exchange']}")
        details.append(f"Category: {contract_info['category']}")
        details.append("")
        
        details.append("📊 TRADING SPECIFICATIONS")
        details.append("-" * 30)
        details.append(f"Tick Size: ${contract_info['tick_size']}")
        details.append(f"Point Value: ${contract_info['point_value']}")
        
        if 'margin_req' in contract_info:
            details.append("")
            details.append("💰 FINANCIAL REQUIREMENTS")
            details.append("-" * 30)
            details.append(f"Margin Requirement: {contract_info['margin_req']}")
            if 'trading_hours' in contract_info:
                details.append(f"Trading Hours: {contract_info['trading_hours']}")
            if 'settlement' in contract_info:
                details.append(f"Settlement: {contract_info['settlement']}")
        
        details.append("")
        details.append("📋 AVAILABLE CONTRACTS")
        details.append("-" * 30)
        for contract in contract_info['contracts']:
            expiry = self.get_contract_expiry_info(contract)
            details.append(f"   {contract} - {expiry}")
        
        details.append("")
        details.append("📈 ADDITIONAL INFORMATION")
        details.append("-" * 30)
        details.append(f"Total Contracts Available: {len(contract_info['contracts'])}")
        details.append("Contract months typically follow quarterly cycle")
        if parent_symbol.startswith('M'):
            details.append("This is a MICRO contract (1/10th size of standard)")
        
        return "\n".join(details)
    
    def format_category_details(self, category_name):
        """Format details for contract category."""
        details = []
        details.append(f"📁 {category_name.upper()} CATEGORY")
        details.append("=" * 50)
        
        if category_name in self.futures_contracts:
            category_contracts = self.futures_contracts[category_name]
            details.append(f"Number of Contract Families: {len(category_contracts)}")
            details.append("")
            
            for symbol, info in category_contracts.items():
                details.append(f"🔸 {symbol} - {info['name']}")
                details.append(f"   Exchange: {info['exchange']}")
                details.append(f"   Contracts: {len(info['contracts'])}")
                details.append("")
        
        return "\n".join(details)
    
    def update_data_type_selection(self):
        """Update data type selection display."""
        selected_types = [name for name, var in self.selected_data_types.items() if var.get()]
        print(f"Selected data types: {selected_types}")  # Debug output
    
        
    def update_contract_selection(self):
        """Update contract selection display."""
        selected = [contract for contract, var in self.contract_vars.items() if var.get()]
        self.selected_contracts.set(f"Selected: {len(selected)} contracts")
        
    def update_control_buttons(self):
        """Update control button states."""
        if self.is_collecting:
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            self.restart_btn.config(state='normal')
        else:
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            self.restart_btn.config(state='disabled')
            
    # Monitoring methods
    def start_monitoring_thread(self):
        """Start the monitoring thread."""
        self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
    def monitoring_loop(self):
        """Main monitoring loop."""
        while True:
            try:
                self.update_process_status()
                self.update_metrics()
                self.process_log_queue()
                time.sleep(5)  # Update every 5 seconds
            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(10)
                
    def monitor_process(self, process_key):
        """Monitor a specific process."""
        proc_info = self.collection_processes.get(process_key)
        if not proc_info:
            return
            
        process = proc_info['process']
        
        try:
            # Read output in real-time
            while process.poll() is None:
                output = process.stdout.readline()
                if output:
                    self.log_queue.put(f"[{process_key}] {output.strip()}")
                    
            # Process finished
            return_code = process.returncode
            if return_code == 0:
                self.log_message(f"✅ {process_key} completed successfully")
            else:
                self.log_message(f"❌ {process_key} exited with code {return_code}")
                
                if self.auto_restart.get() and self.is_collecting:
                    self.log_message(f"🔄 Auto-restarting {process_key}...")
                    self.restart_process(process_key)
                    
        except Exception as e:
            self.log_message(f"❌ Error monitoring {process_key}: {e}")
            
    def restart_process(self, process_key):
        """Restart a specific process."""
        try:
            proc_info = self.collection_processes.get(process_key)
            if not proc_info:
                return
                
            contract = proc_info['contract']
            data_type = proc_info['data_type']
            
            # Remove old process
            del self.collection_processes[process_key]
            
            # Start new process
            time.sleep(2)
            self.start_collection_process(contract, data_type)
            
        except Exception as e:
            self.log_message(f"❌ Failed to restart {process_key}: {e}")
            
    def update_process_status(self):
        """Update process status display."""
        if not hasattr(self, 'status_text'):
            return
            
        try:
            status_info = []
            active_processes = 0
            
            for process_key, proc_info in self.collection_processes.items():
                process = proc_info['process']
                if process.poll() is None:
                    active_processes += 1
                    uptime = datetime.now() - proc_info['start_time']
                    status_info.append(f"✅ {process_key}: PID {process.pid}, Uptime: {uptime}")
                else:
                    status_info.append(f"❌ {process_key}: Terminated")
                    
            self.root.after(0, self._update_status_display, status_info, active_processes)
            
        except Exception as e:
            print(f"Status update error: {e}")
            
    def _update_status_display(self, status_info, active_processes):
        """Update status display in main thread."""
        try:
            self.status_text.delete(1.0, tk.END)
            if status_info:
                self.status_text.insert(tk.END, "\n".join(status_info))
            else:
                self.status_text.insert(tk.END, "No active collection processes")
                
            self.process_count_label.config(text=f"Processes: {active_processes}")
            
        except Exception as e:
            print(f"Display update error: {e}")
            
    def update_metrics(self):
        """Update collection metrics."""
        try:
            metrics = self.collect_metrics()
            self.root.after(0, self._update_metrics_display, metrics)
        except Exception as e:
            print(f"Metrics update error: {e}")
            
    def _update_metrics_display(self, metrics):
        """Update metrics display in main thread."""
        try:
            if hasattr(self, 'metrics_text'):
                self.metrics_text.delete(1.0, tk.END)
                self.metrics_text.insert(tk.END, metrics)
        except Exception as e:
            print(f"Metrics display error: {e}")
            
    def collect_metrics(self):
        """Collect system and data metrics."""
        try:
            metrics = []
            metrics.append(f"📊 COLLECTION METRICS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            metrics.append("=" * 60)
            
            # Process metrics
            active_count = len([p for p in self.collection_processes.values() 
                              if p['process'].poll() is None])
            metrics.append(f"🔄 Active Processes: {active_count}")
            metrics.append(f"📋 Total Processes: {len(self.collection_processes)}")
            
            # Data file metrics
            data_dir = Path("data/logs/Rx")
            if data_dir.exists():
                files = list(data_dir.glob("*.jsonl")) + list(data_dir.glob("*.txt"))
                total_size = sum(f.stat().st_size for f in files if f.is_file())
                recent_files = [f for f in files if f.is_file() and 
                              (time.time() - f.stat().st_mtime) < 300]
                
                metrics.append(f"📁 Data Files: {len(files)}")
                metrics.append(f"💾 Total Size: {total_size / 1024 / 1024:.2f} MB")
                metrics.append(f"🆕 Recent Files: {len(recent_files)}")
                
            # Database metrics (if available)
            try:
                db_metrics = self.get_database_metrics()
                metrics.extend(db_metrics)
            except:
                metrics.append("🗄️ Database: Not available")
                
            return "\n".join(metrics)
            
        except Exception as e:
            return f"❌ Error collecting metrics: {e}"
            
    def get_database_metrics(self):
        """Get database metrics."""
        # This would connect to the database and get table counts, etc.
        # For now, return placeholder
        return [
            "🗄️ DATABASE METRICS:",
            "   - Connection: Active",
            "   - Tables: 17",
            "   - Recent Records: Updating..."
        ]
        
    def process_log_queue(self):
        """Process queued log messages."""
        try:
            while not self.log_queue.empty():
                message = self.log_queue.get_nowait()
                self.root.after(0, self._add_log_message, message)
        except queue.Empty:
            pass
        except Exception as e:
            print(f"Log queue error: {e}")
            
    def _add_log_message(self, message):
        """Add log message to display."""
        try:
            if hasattr(self, 'log_text'):
                self.log_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
                
                if self.auto_scroll_var.get():
                    self.log_text.see(tk.END)
                    
                # Limit log size
                lines = int(self.log_text.index('end-1c').split('.')[0])
                if lines > 1000:
                    self.log_text.delete(1.0, "100.0")
                    
        except Exception as e:
            print(f"Log display error: {e}")
            
    def log_message(self, message):
        """Add a message to the log."""
        self.log_queue.put(message)
        
    # Statistics methods
    def refresh_statistics(self):
        """Refresh statistics displays."""
        try:
            # File statistics
            file_stats = self.get_file_statistics()
            self.file_stats_text.delete(1.0, tk.END)
            self.file_stats_text.insert(tk.END, file_stats)
            
            # Database statistics
            db_stats = self.get_database_statistics()
            self.db_stats_text.delete(1.0, tk.END)
            self.db_stats_text.insert(tk.END, db_stats)
            
        except Exception as e:
            messagebox.showerror("Statistics Error", f"Failed to refresh statistics: {e}")
            
    def get_file_statistics(self):
        """Get file system statistics."""
        try:
            stats = []
            stats.append(f"📁 FILE STATISTICS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            stats.append("=" * 50)
            
            data_dir = Path("data/logs/Rx")
            if data_dir.exists():
                for file_pattern in ["*.jsonl", "*.txt"]:
                    files = list(data_dir.glob(file_pattern))
                    if files:
                        stats.append(f"\n📊 {file_pattern} Files:")
                        for file in sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[:10]:
                            size = file.stat().st_size
                            mtime = datetime.fromtimestamp(file.stat().st_mtime)
                            stats.append(f"   {file.name}: {size:,} bytes, {mtime.strftime('%H:%M:%S')}")
                            
            return "\n".join(stats)
            
        except Exception as e:
            return f"❌ Error getting file statistics: {e}"
            
    def get_database_statistics(self):
        """Get database statistics."""
        try:
            stats = []
            stats.append(f"🗄️ DATABASE STATISTICS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            stats.append("=" * 50)
            stats.append("📊 Table Information:")
            
            # Placeholder for actual database queries
            tables = [
                "best_bid_offer", "last_trades", "depth_by_order_snapshot",
                "depth_by_order_updates", "order_book_levels", "symbols"
            ]
            
            for table in tables:
                stats.append(f"   📋 {table}: Active")
                
            return "\n".join(stats)
            
        except Exception as e:
            return f"❌ Error getting database statistics: {e}"
            
    def export_statistics(self):
        """Export statistics to file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'w') as f:
                    f.write(self.get_file_statistics())
                    f.write("\n\n")
                    f.write(self.get_database_statistics())
                    
                messagebox.showinfo("Export Complete", f"Statistics exported to {filename}")
                
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export statistics: {e}")
            
    def clear_old_data(self):
        """Clear old data files."""
        if messagebox.askyesno("Clear Data", "Are you sure you want to clear old data files?"):
            try:
                # Implementation for clearing old data
                self.log_message("🧹 Clearing old data files...")
                # Add actual file cleanup logic here
                messagebox.showinfo("Clear Complete", "Old data files cleared successfully")
            except Exception as e:
                messagebox.showerror("Clear Error", f"Failed to clear data: {e}")
                
    # Log methods
    def clear_logs(self):
        """Clear the log display."""
        self.log_text.delete(1.0, tk.END)
        
    def save_logs(self):
        """Save logs to file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".log",
                filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'w') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                    
                messagebox.showinfo("Save Complete", f"Logs saved to {filename}")
                
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save logs: {e}")
            
    # Configuration methods
    def save_configuration(self):
        """Save current configuration."""
        try:
            config = {
                'contracts': {contract: var.get() for contract, var in self.contract_vars.items()},
                'data_types': {dtype: var.get() for dtype, var in self.selected_data_types.items()},
                'auto_restart': self.auto_restart.get(),
                'log_filter': self.log_filter.get() if hasattr(self, 'log_filter') else "All"
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
                
            self.update_config_display()
            self.log_message("💾 Configuration saved successfully")
            
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save configuration: {e}")
            
    def load_configuration(self):
        """Load configuration from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    
                # Apply configuration
                for contract, value in config.get('contracts', {}).items():
                    if contract in self.contract_vars:
                        self.contract_vars[contract].set(value)
                        
                for dtype, value in config.get('data_types', {}).items():
                    if dtype in self.selected_data_types:
                        self.selected_data_types[dtype].set(value)
                        
                self.auto_restart.set(config.get('auto_restart', True))
                
                self.update_contract_selection()
                self.update_config_display()
                self.log_message("📂 Configuration loaded successfully")
                
        except Exception as e:
            self.log_message(f"⚠️ Failed to load configuration: {e}")
            
    def load_configuration_file(self):
        """Load configuration from selected file."""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    config = json.load(f)
                    
                # Apply configuration (same as load_configuration)
                self.log_message(f"📂 Configuration loaded from {filename}")
                
            except Exception as e:
                messagebox.showerror("Load Error", f"Failed to load configuration: {e}")
                
    def export_configuration(self):
        """Export configuration to file."""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                config = {
                    'contracts': {contract: var.get() for contract, var in self.contract_vars.items()},
                    'data_types': {dtype: var.get() for dtype, var in self.selected_data_types.items()},
                    'auto_restart': self.auto_restart.get()
                }
                
                with open(filename, 'w') as f:
                    json.dump(config, f, indent=2)
                    
                messagebox.showinfo("Export Complete", f"Configuration exported to {filename}")
                
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export configuration: {e}")
                
    def reset_configuration(self):
        """Reset configuration to defaults."""
        if messagebox.askyesno("Reset Configuration", "Reset all settings to defaults?"):
            # Reset to defaults
            for var in self.contract_vars.values():
                var.set(True)
            for var in self.selected_data_types.values():
                var.set(True)
            self.auto_restart.set(True)
            
            self.update_contract_selection()
            self.update_config_display()
            self.log_message("🔄 Configuration reset to defaults")
            
    def update_config_display(self):
        """Update configuration display."""
        try:
            if hasattr(self, 'config_text'):
                config = {
                    'contracts': {contract: var.get() for contract, var in self.contract_vars.items()},
                    'data_types': {dtype: var.get() for dtype, var in self.selected_data_types.items()},
                    'auto_restart': self.auto_restart.get()
                }
                
                self.config_text.delete(1.0, tk.END)
                self.config_text.insert(tk.END, json.dumps(config, indent=2))
                
        except Exception as e:
            print(f"Config display error: {e}")
            
    def refresh_monitoring(self):
        """Refresh monitoring displays."""
        self.update_process_status()
        self.update_metrics()
        
    def on_closing(self):
        """Handle application closing."""
        if self.is_collecting:
            if messagebox.askyesno("Exit", "Collection is active. Stop all processes and exit?"):
                self.stop_collection()
                self.root.destroy()
        else:
            self.root.destroy()
            
    # Database viewer methods
    
    def on_table_select(self, event):
        """Handle table selection in the treeview."""
        selection = self.tables_tree.selection()
        if selection:
            item = self.tables_tree.item(selection[0])
            table_name = item['text']
            self.current_table = table_name
            self.current_page = 0
            self.load_table_data()
    
    def load_table_data(self):
        """Load data for the selected table with pagination."""
        if not self.current_table:
            return
            
        try:
            page_size = int(self.page_size_var.get())
            offset = self.current_page * page_size
            
            # Get total row count for pagination
            total_rows = self.db_manager.get_table_row_count(self.current_table)
            self.total_pages = (total_rows + page_size - 1) // page_size
            
            # Get table columns
            columns = self.db_manager.get_table_columns(self.current_table)
            column_names = []
            for col in columns:
                # Handle both uppercase and lowercase column names
                col_name = col.get('column_name') or col.get('COLUMN_NAME')
                if col_name:
                    column_names.append(col_name)
            
            # Create new data treeview if needed
            self.create_data_treeview(column_names)
            
            # Build query with filter if needed
            base_query = f"SELECT * FROM `{self.current_table}`"
            if self.current_filter:
                query = f"{base_query} WHERE {self.current_filter} LIMIT {page_size} OFFSET {offset}"
            else:
                query = f"{base_query} LIMIT {page_size} OFFSET {offset}"
            
            # Execute query
            data = self.db_manager.execute_query(query)
            
            # Clear existing data
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)
            
            # Insert new data
            for row in data:
                values = []
                for col_name in column_names:
                    value = row.get(col_name, '')
                    if value is None:
                        value = 'NULL'
                    elif isinstance(value, (dict, list)):
                        value = str(value)[:100] + '...' if len(str(value)) > 100 else str(value)
                    else:
                        value = str(value)[:100] + '...' if len(str(value)) > 100 else str(value)
                    values.append(value)
                self.data_tree.insert('', 'end', values=values)
            
            # Update page info
            self.page_info_label.config(text=f"Page {self.current_page + 1} of {self.total_pages} ({total_rows:,} total rows)")
            
        except Exception as e:
            self.log_message(f"⚠️ Error loading table data: {str(e)}")
    
    def create_data_treeview(self, column_names):
        """Create or recreate the data treeview with dynamic columns."""
        # Destroy existing treeview if it exists
        if self.data_tree:
            self.data_tree.destroy()
        if self.data_scrollbar_v:
            self.data_scrollbar_v.destroy()
        if self.data_scrollbar_h:
            self.data_scrollbar_h.destroy()
        
        # Create new treeview
        self.data_tree = ttk.Treeview(self.data_tree_frame, columns=column_names, show='headings')
        
        # Configure columns
        for col in column_names:
            self.data_tree.heading(col, text=col, anchor='w')
            # Set appropriate column width based on column name length
            width = max(100, min(200, len(col) * 10))
            self.data_tree.column(col, width=width, anchor='w')
        
        # Create scrollbars
        self.data_scrollbar_v = ttk.Scrollbar(self.data_tree_frame, orient="vertical", command=self.data_tree.yview)
        self.data_scrollbar_h = ttk.Scrollbar(self.data_tree_frame, orient="horizontal", command=self.data_tree.xview)
        
        self.data_tree.configure(yscrollcommand=self.data_scrollbar_v.set, 
                                xscrollcommand=self.data_scrollbar_h.set)
        
        # Pack elements
        self.data_tree.pack(side='left', fill='both', expand=True)
        self.data_scrollbar_v.pack(side='right', fill='y')
        self.data_scrollbar_h.pack(side='bottom', fill='x')
    
    def previous_page(self):
        """Go to previous page of data."""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_table_data()
    
    def next_page(self):
        """Go to next page of data."""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.load_table_data()
    
    def show_table_schema(self):
        """Show schema information for the selected table."""
        selection = self.tables_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a table first.")
            return
            
        item = self.tables_tree.item(selection[0])
        table_name = item['text']
        
        try:
            columns = self.db_manager.get_table_columns(table_name)
            
            # Create schema display window
            schema_window = tk.Toplevel(self.root)
            schema_window.title(f"Schema: {table_name}")
            schema_window.geometry("800x600")
            
            # Create treeview for schema
            schema_tree = ttk.Treeview(schema_window, columns=('type', 'nullable', 'default', 'key'), show='headings')
            schema_tree.heading('#0', text='Column')
            schema_tree.heading('type', text='Type')
            schema_tree.heading('nullable', text='Nullable')
            schema_tree.heading('default', text='Default')
            schema_tree.heading('key', text='Key')
            
            for col in columns:
                # Handle both uppercase and lowercase column attribute names
                col_name = col.get('column_name') or col.get('COLUMN_NAME')
                data_type = col.get('data_type') or col.get('DATA_TYPE')
                is_nullable = col.get('is_nullable') or col.get('IS_NULLABLE')
                column_default = col.get('column_default') or col.get('COLUMN_DEFAULT')
                column_key = col.get('column_key') or col.get('COLUMN_KEY')
                
                schema_tree.insert('', 'end', text=col_name, values=(
                    data_type,
                    is_nullable,
                    column_default or '',
                    column_key or ''
                ))
            
            schema_tree.pack(fill='both', expand=True, padx=10, pady=10)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load schema: {str(e)}")
    
    def show_data_filter(self):
        """Show dialog to set data filter."""
        filter_dialog = tk.Toplevel(self.root)
        filter_dialog.title("Filter Data")
        filter_dialog.geometry("400x200")
        
        ttk.Label(filter_dialog, text="WHERE clause (without WHERE keyword):").pack(pady=10)
        
        filter_entry = tk.Text(filter_dialog, height=3, width=50)
        filter_entry.pack(pady=10, padx=10, fill='x')
        filter_entry.insert('1.0', self.current_filter)
        
        def apply_filter():
            self.current_filter = filter_entry.get('1.0', 'end-1c').strip()
            self.current_page = 0
            self.load_table_data()
            filter_dialog.destroy()
        
        def clear_filter():
            self.current_filter = ""
            self.current_page = 0
            self.load_table_data()
            filter_dialog.destroy()
        
        button_frame = ttk.Frame(filter_dialog)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="Apply Filter", command=apply_filter).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Clear Filter", command=clear_filter).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel", command=filter_dialog.destroy).pack(side='left', padx=5)
    
    def export_table_data(self):
        """Export current table data to CSV."""
        if not self.current_table:
            messagebox.showwarning("No Table", "Please select a table first.")
            return
            
        try:
            from tkinter import filedialog
            import csv
            from datetime import datetime
            
            # Get file path
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialvalue=f"{self.current_table}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )
            
            if not filename:
                return
            
            # Export all data (not just current page)
            query = f"SELECT * FROM `{self.current_table}`"
            if self.current_filter:
                query += f" WHERE {self.current_filter}"
            
            data = self.db_manager.execute_query(query)
            
            if not data:
                messagebox.showinfo("No Data", "No data to export.")
                return
            
            # Write CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if data:
                    fieldnames = data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
            
            messagebox.showinfo("Export Complete", f"Data exported to {filename}")
            self.log_message(f"📤 Exported {len(data)} rows from {self.current_table} to CSV")
            
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export data: {str(e)}")
    
    def truncate_selected_table(self):
        """Truncate the selected table after confirmation."""
        selection = self.tables_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a table to truncate.")
            return
            
        item = self.tables_tree.item(selection[0])
        table_name = item['text']
        
        # Get current row count for display
        try:
            row_count = self.db_manager.get_table_row_count(table_name)
        except:
            row_count = "unknown"
        
        # Confirmation dialog
        if messagebox.askyesno("Confirm Truncate", 
                              f"⚠️ DANGER: This will permanently delete ALL data from table '{table_name}'!\n\n"
                              f"Current rows: {row_count:,}\n\n"
                              f"This action CANNOT be undone.\n\n"
                              f"Are you sure you want to truncate this table?"):
            try:
                # Execute truncate
                truncate_query = f"TRUNCATE TABLE `{table_name}`"
                self.db_manager.execute_update(truncate_query)
                
                # Refresh table list to update row counts
                self.refresh_database_tables()
                
                # Clear data view if this table was selected
                if self.current_table == table_name:
                    if hasattr(self, 'data_tree') and self.data_tree:
                        for item in self.data_tree.get_children():
                            self.data_tree.delete(item)
                
                messagebox.showinfo("Truncate Complete", f"Table '{table_name}' has been truncated successfully.")
                self.log_message(f"🗑️ Truncated table: {table_name}")
                
            except Exception as e:
                messagebox.showerror("Truncate Error", f"Failed to truncate table '{table_name}':\n{str(e)}")
                self.log_message(f"❌ Failed to truncate table {table_name}: {str(e)}")
    
    def truncate_all_tables(self):
        """Truncate all tables after confirmation."""
        try:
            # Get list of all tables
            query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                ORDER BY table_name
            """
            tables = self.db_manager.execute_query(query)
            
            if not tables:
                messagebox.showinfo("No Tables", "No tables found to truncate.")
                return
            
            table_names = []
            total_rows = 0
            for table_info in tables:
                table_name = table_info.get('table_name') or table_info.get('TABLE_NAME')
                if table_name:
                    table_names.append(table_name)
                    try:
                        row_count = self.db_manager.get_table_row_count(table_name)
                        total_rows += row_count
                    except:
                        pass
            
            # Confirmation dialog
            if messagebox.askyesno("Confirm Truncate All", 
                                  f"⚠️⚠️ EXTREME DANGER ⚠️⚠️\n\n"
                                  f"This will permanently delete ALL data from ALL {len(table_names)} tables!\n\n"
                                  f"Total estimated rows to delete: {total_rows:,}\n\n"
                                  f"Tables: {', '.join(table_names[:5])}{'...' if len(table_names) > 5 else ''}\n\n"
                                  f"THIS ACTION CANNOT BE UNDONE!\n\n"
                                  f"Are you absolutely sure?"):
                
                # Second confirmation for safety
                if messagebox.askyesno("Final Confirmation", 
                                      f"🚨 LAST WARNING 🚨\n\n"
                                      f"You are about to delete ALL data from {len(table_names)} tables.\n\n"
                                      f"Type 'YES' below to confirm:\n\n"
                                      f"This is your final chance to cancel!"):
                    
                    success_count = 0
                    failed_tables = []
                    
                    for table_name in table_names:
                        try:
                            truncate_query = f"TRUNCATE TABLE `{table_name}`"
                            self.db_manager.execute_update(truncate_query)
                            success_count += 1
                            self.log_message(f"🗑️ Truncated table: {table_name}")
                        except Exception as e:
                            failed_tables.append(f"{table_name}: {str(e)}")
                            self.log_message(f"❌ Failed to truncate {table_name}: {str(e)}")
                    
                    # Refresh table list
                    self.refresh_database_tables()
                    
                    # Clear data view
                    if hasattr(self, 'data_tree') and self.data_tree:
                        for item in self.data_tree.get_children():
                            self.data_tree.delete(item)
                    
                    # Show results
                    if failed_tables:
                        messagebox.showwarning("Partial Success", 
                                             f"Truncated {success_count} tables successfully.\n\n"
                                             f"Failed tables:\n" + "\n".join(failed_tables[:5]))
                    else:
                        messagebox.showinfo("Truncate Complete", 
                                          f"Successfully truncated all {success_count} tables.")
                        
        except Exception as e:
            messagebox.showerror("Truncate Error", f"Failed to truncate tables:\n{str(e)}")
            self.log_message(f"❌ Failed to truncate all tables: {str(e)}")

    # Environment configuration methods
    def load_env_values(self):
        """Load current values from .env file into the GUI fields."""
        try:
            env_file_path = '.env'
            env_values = {}
            
            # Read .env file
            if os.path.exists(env_file_path):
                with open(env_file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            env_values[key.strip()] = value.strip()
            
            # Set values in GUI fields
            for env_key, field_info in self.env_vars.items():
                value = env_values.get(env_key, field_info['default'])
                field_info['entry'].delete(0, tk.END)
                field_info['entry'].insert(0, value)
                
        except Exception as e:
            self.log_message(f"⚠️ Error loading .env values: {str(e)}")
    
    def save_env_file(self):
        """Save current GUI values to .env file."""
        try:
            env_file_path = '.env'
            backup_path = '.env.backup'
            
            # Create backup of existing .env file
            if os.path.exists(env_file_path):
                import shutil
                shutil.copy2(env_file_path, backup_path)
            
            # Read existing .env file to preserve structure and comments
            existing_lines = []
            if os.path.exists(env_file_path):
                with open(env_file_path, 'r', encoding='utf-8') as f:
                    existing_lines = f.readlines()
            
            # Build new content preserving structure
            new_lines = []
            processed_keys = set()
            
            for line in existing_lines:
                stripped = line.strip()
                if stripped and not stripped.startswith('#') and '=' in stripped:
                    key, _ = stripped.split('=', 1)
                    key = key.strip()
                    if key in self.env_vars:
                        # Replace with new value from GUI
                        new_value = self.env_vars[key]['entry'].get()
                        new_lines.append(f"{key}={new_value}\n")
                        processed_keys.add(key)
                    else:
                        # Keep existing line for keys not in GUI
                        new_lines.append(line)
                else:
                    # Keep comments and empty lines
                    new_lines.append(line)
            
            # Add any new keys that weren't in the original file
            for env_key in self.env_vars:
                if env_key not in processed_keys:
                    new_value = self.env_vars[env_key]['entry'].get()
                    new_lines.append(f"{env_key}={new_value}\n")
            
            # Write updated .env file
            with open(env_file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            self.log_message("💾 Environment configuration saved to .env file")
            messagebox.showinfo("Configuration Saved", 
                              "Environment configuration has been saved to .env file.\n"
                              "Backup created as .env.backup")
            
        except Exception as e:
            self.log_message(f"⚠️ Error saving .env file: {str(e)}")
            messagebox.showerror("Save Error", f"Failed to save .env file: {str(e)}")
    
    def test_connection(self, connection_type):
        """Test a specific connection type."""
        try:
            if connection_type == 'MYSQL_HOST':
                self.test_database_connection()
            elif connection_type == 'RITHMIC_URI':
                self.test_rithmic_connection()
            else:
                messagebox.showinfo("Test", f"Connection test for {connection_type} not implemented yet.")
                
        except Exception as e:
            messagebox.showerror("Test Error", f"Connection test failed: {str(e)}")
    
    def test_database_connection(self):
        """Test database connection using current GUI values."""
        try:
            # Get values from GUI
            config = {
                'host': self.env_vars['MYSQL_HOST']['entry'].get(),
                'port': int(self.env_vars['MYSQL_PORT']['entry'].get()),
                'user': self.env_vars['MYSQL_USER']['entry'].get(),
                'password': self.env_vars['MYSQL_PASSWORD']['entry'].get(),
                'database': self.env_vars['MYSQL_DATABASE']['entry'].get()
            }
            
            # Test connection
            import mysql.connector
            connection = mysql.connector.connect(**config, connect_timeout=5)
            
            if connection.is_connected():
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                cursor.close()
                connection.close()
                
                messagebox.showinfo("Database Test", 
                                  f"✅ Database connection successful!\n"
                                  f"MySQL Version: {version}\n"
                                  f"Host: {config['host']}:{config['port']}\n"
                                  f"Database: {config['database']}")
                self.log_message("✅ Database connection test successful")
            
        except Exception as e:
            messagebox.showerror("Database Test Failed", 
                               f"❌ Database connection failed:\n{str(e)}")
            self.log_message(f"❌ Database connection test failed: {str(e)}")
    
    def test_rithmic_connection(self):
        """Test Rithmic API connection using current GUI values."""
        try:
            # Get values from GUI
            uri = self.env_vars['RITHMIC_URI']['entry'].get()
            user = self.env_vars['RITHMIC_USER']['entry'].get()
            
            # Simple URI validation
            if not uri.startswith('wss://'):
                raise ValueError("URI must start with wss://")
            
            messagebox.showinfo("Rithmic Test", 
                              f"🔧 Rithmic connection test configured:\n"
                              f"URI: {uri}\n"
                              f"User: {user}\n"
                              f"Note: Full connection test requires WebSocket implementation.")
            self.log_message("🔧 Rithmic connection parameters validated")
            
        except Exception as e:
            messagebox.showerror("Rithmic Test Failed", 
                               f"❌ Rithmic connection test failed:\n{str(e)}")
            self.log_message(f"❌ Rithmic connection test failed: {str(e)}")
    
    def test_all_connections(self):
        """Test all configured connections."""
        try:
            self.log_message("🧪 Testing all connections...")
            
            # Test database
            try:
                self.test_database_connection()
            except:
                pass  # Error already logged
            
            # Test Rithmic (basic validation)
            try:
                self.test_rithmic_connection()
            except:
                pass  # Error already logged
                
        except Exception as e:
            self.log_message(f"⚠️ Error in connection tests: {str(e)}")
    
    def save_all_configurations(self):
        """Save both environment and legacy configurations."""
        try:
            # Save .env file
            self.save_env_file()
            
            # Save legacy configuration
            self.save_configuration()
            
            self.log_message("💾 All configurations saved successfully")
            
        except Exception as e:
            self.log_message(f"⚠️ Error saving configurations: {str(e)}")
            messagebox.showerror("Save Error", f"Failed to save configurations: {str(e)}")

    # =============================================================================
    # CODE GENERATION METHODS
    # =============================================================================
    
    def show_data_collection_code(self):
        """Show generated code for current data collection configuration."""
        try:
            # Generate data collection script
            script = self.code_generator.generate_data_collection_script()
            
            # Show in preview dialog
            CodePreviewDialog(
                self.root,
                "Data Collection Script",
                script,
                f"futures_data_collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            )
            
            self.log_message("📄 Data collection script generated")
            
        except Exception as e:
            self.log_message(f"⚠️ Error generating data collection code: {str(e)}")
            messagebox.showerror("Code Generation Error", f"Failed to generate code: {str(e)}")
    
    def show_database_query_code(self, table_name=None):
        """Show generated code for database queries."""
        try:
            # Use selected table or prompt for table name
            if not table_name:
                # Get table name from database viewer if available
                if hasattr(self, 'tables_tree') and self.tables_tree.selection():
                    selected = self.tables_tree.selection()[0]
                    table_name = self.tables_tree.item(selected)['text']
                else:
                    # Prompt user for table name
                    table_name = tk.simpledialog.askstring(
                        "Table Name", 
                        "Enter table name for query script:",
                        parent=self.root
                    )
            
            if not table_name:
                return
            
            # Generate database query script
            script = self.code_generator.generate_database_query_script(table_name)
            
            # Show in preview dialog
            CodePreviewDialog(
                self.root,
                f"Database Query Script - {table_name}",
                script,
                f"query_{table_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            )
            
            self.log_message(f"📄 Database query script generated for table: {table_name}")
            
        except Exception as e:
            self.log_message(f"⚠️ Error generating database query code: {str(e)}")
            messagebox.showerror("Code Generation Error", f"Failed to generate code: {str(e)}")
    
    def show_configuration_code(self):
        """Show generated code for configuration management."""
        try:
            # Generate configuration management script
            script = self.code_generator.get_base_imports()
            script += self.code_generator.get_database_config_code()
            script += self.code_generator.get_rithmic_config_code()
            
            script += """

def main():
    \"\"\"Configuration management and testing script.\"\"\"
    print("⚙️ Configuration Management and Testing")
    print("=" * 50)
    
    # Test database connection
    print("\\n🗄️ Testing Database Connection...")
    db_manager = get_database_manager()
    if db_manager and db_manager.test_connection():
        print("✅ Database connection successful")
        connection_info = db_manager.get_connection_info()
        for key, value in connection_info.items():
            print(f"   {key}: {value}")
        db_manager.close_pool()
    else:
        print("❌ Database connection failed")
    
    # Display Rithmic configuration
    print("\\n📡 Rithmic API Configuration:")
    for key, value in rithmic_config.items():
        if 'password' in key.lower():
            print(f"   {key}: {'*' * len(str(value))}")
        else:
            print(f"   {key}: {value}")
    
    print("\\n✅ Configuration review completed")

if __name__ == "__main__":
    main()
"""
            
            # Show in preview dialog
            CodePreviewDialog(
                self.root,
                "Configuration Management Script",
                script,
                f"configuration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            )
            
            self.log_message("📄 Configuration management script generated")
            
        except Exception as e:
            self.log_message(f"⚠️ Error generating configuration code: {str(e)}")
            messagebox.showerror("Code Generation Error", f"Failed to generate code: {str(e)}")
    
    def show_statistics_code(self):
        """Show generated code for statistics and file analysis."""
        try:
            # Generate statistics analysis script
            script = self.code_generator.get_base_imports()
            script += self.code_generator.get_database_config_code()
            
            script += """
import glob
from pathlib import Path

def analyze_data_files():
    \"\"\"Analyze data files in the collection directories.\"\"\"
    print("📁 Data Files Analysis")
    print("=" * 40)
    
    # Define data directories to analyze
    data_dirs = [
        Path("data"),
        Path("level1_data"),
        Path("depth_by_order_data"),
        Path("historical_data")
    ]
    
    total_files = 0
    total_size = 0
    
    for data_dir in data_dirs:
        if data_dir.exists():
            print(f"\\n📂 Directory: {data_dir}")
            
            # Find all data files
            json_files = list(data_dir.glob("*.json"))
            csv_files = list(data_dir.glob("*.csv"))
            all_files = json_files + csv_files
            
            dir_size = 0
            for file_path in all_files:
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    dir_size += file_size
                    
                    # Show recent files
                    if len(all_files) <= 5:
                        modified_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                        print(f"   📄 {file_path.name} ({file_size:,} bytes) - {modified_time.strftime('%Y-%m-%d %H:%M')}")
            
            print(f"   📊 Total files: {len(all_files)}")
            print(f"   💾 Directory size: {dir_size / (1024*1024):.2f} MB")
            
            total_files += len(all_files)
            total_size += dir_size
        else:
            print(f"\\n📂 Directory: {data_dir} - Not found")
    
    print(f"\\n📈 Summary:")
    print(f"   Total files: {total_files}")
    print(f"   Total size: {total_size / (1024*1024):.2f} MB")
    
    return total_files, total_size

def analyze_database_statistics():
    \"\"\"Analyze database tables and statistics.\"\"\"
    print("\\n🗄️ Database Statistics")
    print("=" * 40)
    
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Database connection failed")
        return
    
    try:
        # Get list of tables
        tables_query = \"\"\"
        SELECT table_name, table_rows, data_length, index_length
        FROM information_schema.tables 
        WHERE table_schema = %s
        ORDER BY table_rows DESC
        \"\"\"
        
        tables = db_manager.execute_query(tables_query, (db_manager.config['database'],))
        
        total_rows = 0
        total_data_size = 0
        total_index_size = 0
        
        print(f"📋 Tables in database '{db_manager.config['database']}':")
        
        for table in tables:
            table_name = table.get('table_name', table.get('TABLE_NAME', 'unknown'))
            rows = table.get('table_rows', table.get('TABLE_ROWS', 0)) or 0
            data_size = table.get('data_length', table.get('DATA_LENGTH', 0)) or 0
            index_size = table.get('index_length', table.get('INDEX_LENGTH', 0)) or 0
            
            print(f"   📊 {table_name}: {rows:,} rows, {data_size/(1024*1024):.2f} MB data, {index_size/(1024*1024):.2f} MB indexes")
            
            total_rows += rows
            total_data_size += data_size
            total_index_size += index_size
        
        print(f"\\n📈 Database Summary:")
        print(f"   Total tables: {len(tables)}")
        print(f"   Total rows: {total_rows:,}")
        print(f"   Total data size: {total_data_size/(1024*1024):.2f} MB")
        print(f"   Total index size: {total_index_size/(1024*1024):.2f} MB")
        
    except Exception as e:
        print(f"❌ Database analysis error: {e}")
    finally:
        db_manager.close_pool()

def main():
    \"\"\"Main statistics analysis function.\"\"\"
    print("📊 Comprehensive Data Collection Statistics")
    print("=" * 60)
    
    # Analyze data files
    total_files, total_file_size = analyze_data_files()
    
    # Analyze database
    analyze_database_statistics()
    
    print("\\n✅ Statistics analysis completed")
    
    # Export summary
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"statistics_summary_{timestamp}.txt"
    
    with open(summary_file, 'w') as f:
        f.write(f"Data Collection Statistics Summary\\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n")
        f.write(f"=" * 50 + "\\n")
        f.write(f"Total data files: {total_files}\\n")
        f.write(f"Total file size: {total_file_size / (1024*1024):.2f} MB\\n")
    
    print(f"📄 Summary exported to: {summary_file}")

if __name__ == "__main__":
    main()
"""
            
            # Show in preview dialog
            CodePreviewDialog(
                self.root,
                "Statistics Analysis Script",
                script,
                f"statistics_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            )
            
            self.log_message("📄 Statistics analysis script generated")
            
        except Exception as e:
            self.log_message(f"⚠️ Error generating statistics code: {str(e)}")
            messagebox.showerror("Code Generation Error", f"Failed to generate code: {str(e)}")

    def show_symbol_search_code(self, exchange_filter=None, symbol_pattern=None):
        """Show generated code for symbol search and static data collection."""
        try:
            # Create parameters dialog if no parameters provided
            if exchange_filter is None and symbol_pattern is None:
                dialog = tk.Toplevel(self.root)
                dialog.title("Symbol Search Script Parameters")
                dialog.geometry("400x300")
                dialog.transient(self.root)
                dialog.grab_set()
                
                # Main frame
                main_frame = ttk.Frame(dialog, padding=20)
                main_frame.pack(fill='both', expand=True)
                
                # Title
                ttk.Label(main_frame, text="Symbol Search Script Generator", 
                         font=('Arial', 12, 'bold')).pack(pady=(0, 20))
                
                # Exchange filter
                ttk.Label(main_frame, text="Exchange Filter (optional):").pack(anchor='w')
                exchange_var = tk.StringVar()
                exchange_combo = ttk.Combobox(main_frame, textvariable=exchange_var, 
                                            values=['CME', 'CBOT', 'NYMEX', 'COMEX', 'ICE'], 
                                            width=30)
                exchange_combo.pack(fill='x', pady=(5, 15))
                
                # Symbol pattern
                ttk.Label(main_frame, text="Symbol Pattern (optional, use * for wildcard):").pack(anchor='w')
                pattern_var = tk.StringVar()
                ttk.Entry(main_frame, textvariable=pattern_var, width=30).pack(fill='x', pady=(5, 15))
                
                # Examples
                examples_frame = ttk.LabelFrame(main_frame, text="Examples", padding=10)
                examples_frame.pack(fill='x', pady=(0, 15))
                examples_text = """ES* - All E-mini S&P 500 contracts
NQ* - All E-mini NASDAQ contracts  
*Z25 - All December 2025 contracts
ES*25 - All E-mini S&P 500 2025 contracts"""
                ttk.Label(examples_frame, text=examples_text, justify='left').pack(anchor='w')
                
                # Results
                result = {'exchange': None, 'pattern': None, 'cancelled': True}
                
                def generate():
                    result['exchange'] = exchange_var.get().strip() or None
                    result['pattern'] = pattern_var.get().strip() or None
                    result['cancelled'] = False
                    dialog.destroy()
                    
                def cancel():
                    dialog.destroy()
                
                # Buttons
                button_frame = ttk.Frame(main_frame)
                button_frame.pack(fill='x', pady=(10, 0))
                ttk.Button(button_frame, text="Generate Script", command=generate).pack(side='left')
                ttk.Button(button_frame, text="Cancel", command=cancel).pack(side='right')
                
                # Wait for dialog
                dialog.wait_window()
                
                if result['cancelled']:
                    return
                    
                exchange_filter = result['exchange']
                symbol_pattern = result['pattern']
            
            # Generate symbol search script
            script = self.code_generator.generate_symbol_search_script(
                exchange_filter=exchange_filter,
                symbol_pattern=symbol_pattern
            )
            
            # Create descriptive filename
            filename_parts = ["symbol_search"]
            if exchange_filter:
                filename_parts.append(exchange_filter.lower())
            if symbol_pattern:
                filename_parts.append(symbol_pattern.replace('*', 'wildcard'))
            filename_parts.append(datetime.now().strftime('%Y%m%d_%H%M%S'))
            filename = f"{'_'.join(filename_parts)}.py"
            
            # Show in preview dialog
            CodePreviewDialog(
                self.root,
                "Symbol Search & Static Data Collection Script",
                script,
                filename
            )
            
            filter_info = []
            if exchange_filter:
                filter_info.append(f"exchange={exchange_filter}")
            if symbol_pattern:
                filter_info.append(f"pattern={symbol_pattern}")
            filter_str = f" ({', '.join(filter_info)})" if filter_info else ""
            
            self.log_message(f"📄 Symbol search script generated{filter_str}")
            
        except Exception as e:
            self.log_message(f"⚠️ Error generating symbol search code: {str(e)}")
            messagebox.showerror("Code Generation Error", f"Failed to generate symbol search code: {str(e)}")

    def show_multi_threaded_query_code(self, table_name=None):
        """Show generated multi-threaded database query code."""
        try:
            # Get table name if not provided
            if not table_name:
                if hasattr(self, 'database_viewer') and self.database_viewer.current_table:
                    table_name = self.database_viewer.current_table
                else:
                    table_name = tk.simpledialog.askstring(
                        "Table Name", 
                        "Enter table name for multi-threaded query:"
                    )
                    
            if not table_name:
                return
                
            # Create parameters dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("Multi-Threaded Query Script Parameters")
            dialog.geometry("500x400")
            dialog.transient(self.root)
            dialog.grab_set()
            
            # Main frame
            main_frame = ttk.Frame(dialog, padding=20)
            main_frame.pack(fill='both', expand=True)
            
            # Title
            ttk.Label(main_frame, text=f"Generate Multi-Threaded Query Script", 
                     font=('Arial', 12, 'bold')).pack(pady=(0, 10))
            ttk.Label(main_frame, text=f"Table: {table_name}", 
                     font=('Arial', 10), foreground='blue').pack(pady=(0, 20))
            
            # Threading strategy
            ttk.Label(main_frame, text="Threading Strategy:", font=('Arial', 10, 'bold')).pack(anchor='w')
            threading_var = tk.StringVar(value="ThreadPoolExecutor")
            threading_frame = ttk.Frame(main_frame)
            threading_frame.pack(fill='x', pady=(5, 15))
            
            ttk.Radiobutton(threading_frame, text="ThreadPoolExecutor (Recommended)", 
                           variable=threading_var, value="ThreadPoolExecutor").pack(anchor='w')
            ttk.Radiobutton(threading_frame, text="ProcessPoolExecutor (CPU Intensive)", 
                           variable=threading_var, value="ProcessPoolExecutor").pack(anchor='w')
            ttk.Radiobutton(threading_frame, text="Asyncio (I/O Bound)", 
                           variable=threading_var, value="asyncio").pack(anchor='w')
            
            # Parameters
            params_frame = ttk.LabelFrame(main_frame, text="Parameters", padding=10)
            params_frame.pack(fill='x', pady=(0, 15))
            
            # Chunk size
            ttk.Label(params_frame, text="Chunk Size:").grid(row=0, column=0, sticky='w', pady=2)
            chunk_size_var = tk.StringVar(value="10000")
            ttk.Entry(params_frame, textvariable=chunk_size_var, width=15).grid(row=0, column=1, sticky='w', padx=(10, 0))
            
            # Max workers
            ttk.Label(params_frame, text="Max Workers:").grid(row=1, column=0, sticky='w', pady=2)
            max_workers_var = tk.StringVar(value="os.cpu_count()")
            ttk.Entry(params_frame, textvariable=max_workers_var, width=15).grid(row=1, column=1, sticky='w', padx=(10, 0))
            
            # Where clause
            ttk.Label(params_frame, text="WHERE Clause:").grid(row=2, column=0, sticky='w', pady=2)
            where_var = tk.StringVar()
            ttk.Entry(params_frame, textvariable=where_var, width=30).grid(row=2, column=1, sticky='w', padx=(10, 0))
            
            # Export CSV
            export_csv_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(params_frame, text="Export to CSV", 
                           variable=export_csv_var).grid(row=3, column=0, columnspan=2, sticky='w', pady=5)
            
            # Results
            result = {'params': None, 'cancelled': True}
            
            def generate():
                try:
                    chunk_size = int(chunk_size_var.get())
                    if chunk_size <= 0:
                        raise ValueError("Chunk size must be positive")
                except ValueError:
                    messagebox.showerror("Invalid Input", "Chunk size must be a positive integer")
                    return
                    
                result['params'] = {
                    'threading_strategy': threading_var.get(),
                    'chunk_size': chunk_size,
                    'max_workers': max_workers_var.get(),
                    'where_clause': where_var.get().strip(),
                    'export_csv': export_csv_var.get()
                }
                result['cancelled'] = False
                dialog.destroy()
                
            def cancel():
                dialog.destroy()
            
            # Buttons
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill='x', pady=(10, 0))
            ttk.Button(button_frame, text="Generate Script", command=generate).pack(side='left')
            ttk.Button(button_frame, text="Cancel", command=cancel).pack(side='right')
            
            # Wait for dialog
            dialog.wait_window()
            
            if result['cancelled']:
                return
                
            params = result['params']
            
            # Generate script
            query_params = {
                'chunk_size': params['chunk_size'],
                'where_clause': params['where_clause'],
                'export_csv': params['export_csv']
            }
            
            script = self.code_generator.generate_multi_threaded_query_script(
                table_name=table_name,
                query_params=query_params,
                threading_strategy=params['threading_strategy'],
                max_workers=params['max_workers']
            )
            
            # Create filename
            filename = f"multi_threaded_query_{table_name}_{params['threading_strategy'].lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            
            # Show in preview dialog
            CodePreviewDialog(
                self.root,
                f"Multi-Threaded Query Script - {table_name}",
                script,
                filename
            )
            
            self.log_message(f"📄 Multi-threaded query script generated for {table_name} ({params['threading_strategy']})")
            
        except Exception as e:
            self.log_message(f"⚠️ Error generating multi-threaded query code: {str(e)}")
            messagebox.showerror("Code Generation Error", f"Failed to generate multi-threaded query code: {str(e)}")

    def run(self):
        """Run the GUI application."""
        self.log_message("🎯 ES Futures GUI Controller started")
        self.log_message("📊 Ready for comprehensive market data collection")
        self.root.mainloop()

def check_dependencies():
    """Check if required dependencies are available."""
    try:
        import tkinter
        print("✅ tkinter available")
    except ImportError:
        print("❌ tkinter not available. Please install python3-tk")
        return False
        
    try:
        import threading
        import queue
        import json
        print("✅ Standard library modules available")
    except ImportError as e:
        print(f"❌ Missing standard library module: {e}")
        return False
        
    return True

def check_project_structure():
    """Check if required project files exist."""
    required_files = [
        "src/scripts/subscribe_level1_data.py",
        "continuous_es_data_collection.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            
    if missing_files:
        print("⚠️ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nSome GUI features may not work properly.")
        return False
        
    print("✅ Project structure verified")
    return True

def create_data_directories():
    """Create required data directories if they don't exist."""
    directories = [
        "data/logs/Rx",
        "data/logs/Tx",
        "data/exports",
        "data/config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        
    print("✅ Data directories created")

def main():
    """Main function to start the GUI with dependency checks and setup."""
    print("🎯 PROFESSIONAL FUTURES DATA COLLECTION GUI")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install required packages.")
        return 1
        
    # Check project structure
    structure_ok = check_project_structure()
    
    # Create data directories
    create_data_directories()
    
    # Launch GUI
    print("\n🚀 Starting GUI application...")
    try:
        app = ESFuturesGUIController()
        app.run()
        print("✅ GUI application completed successfully")
        return 0
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
        return 0
    except Exception as e:
        print(f"❌ Failed to start GUI: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())