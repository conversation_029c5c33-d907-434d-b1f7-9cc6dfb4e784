# Rithmic API Configuration

# Authentication (Paper Trading Credentials)
RITHMIC_USER=PP-013155
RITHMIC_PASSWORD=b7neA8k6JA
RITHMIC_SYSTEM=Rithmic Paper Trading
RITHMIC_GATEWAY=Chicago Area

# Application Settings
RITHMIC_APP_NAME=PythonRithmicClient
RITHMIC_APP_VERSION=1.0.0

# Connection Settings
RITHMIC_URI=wss://rprotocol.rithmic.com:443
RITHMIC_SSL_CERT_PATH=etc/rithmic_ssl_cert_auth_params
RITHMIC_TEMPLATE_VERSION=3.9

# Data Settings
DATA_DIRECTORY=data
CLEAR_DATA_ON_START=true

# ===== MULTI-CONTRACT SUBSCRIPTION SETTINGS =====

# Contract Subscription List (space-separated)
# Supports:
# - Specific contracts: ESU5.CME NQU5.CME 
# - Continuous contracts: @ES @NQ (resolves to front month)
# - Wildcards: * (all contracts), *.CME (all CME contracts), ES* (all ES contracts)
CONTRACTS_TO_SUBSCRIBE=@ES @NQ

# Alternative examples:
# CONTRACTS_TO_SUBSCRIBE=ESU5.CME NQU5.CME YMU5.CME          # Specific contracts
# CONTRACTS_TO_SUBSCRIBE=@ES @NQ @YM @RTY                    # Continuous contracts
# CONTRACTS_TO_SUBSCRIBE=ES* NQ*                             # Wildcard patterns
# CONTRACTS_TO_SUBSCRIBE=*.CME                               # All CME contracts
# CONTRACTS_TO_SUBSCRIBE=*                                   # All contracts (use with caution!)

# Maximum number of contracts to subscribe to simultaneously
# Leave empty for no limit (use with caution - may overwhelm the system)
MAX_CONCURRENT_CONTRACTS=10

# Contract resolution settings
CACHE_CONTRACTS=true
CACHE_TTL_HOURS=24
AUTO_REFRESH_CACHE=true

# Subscription behavior
SUBSCRIBE_ON_STARTUP=true
VALIDATE_CONTRACTS_BEFORE_SUBSCRIBE=true
CONTINUE_ON_INVALID_CONTRACT=true

# Performance settings
BATCH_SUBSCRIPTION_SIZE=5
SUBSCRIPTION_DELAY_MS=100