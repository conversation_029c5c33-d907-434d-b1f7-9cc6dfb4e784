# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: bracket_updates.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x62racket_updates.proto\x12\x03rti\"\x9d\x02\n\x0e\x42racketUpdates\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x13\n\tbasket_id\x18\xdc\xdd\x06 \x01(\t\x12\x14\n\nstop_ticks\x18\xda\xb6\t \x01(\x05\x12\x17\n\rstop_quantity\x18\xdb\xb6\t \x01(\x05\x12 \n\x16stop_quantity_released\x18\xe2\xb6\t \x01(\x05\x12\x16\n\x0ctarget_ticks\x18\xd8\xb6\t \x01(\x05\x12\x19\n\x0ftarget_quantity\x18\xd9\xb6\t \x01(\x05\x12\"\n\x18target_quantity_released\x18\xdc\xb6\t \x01(\x05')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'bracket_updates_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _BRACKETUPDATES._serialized_start=31
  _BRACKETUPDATES._serialized_end=316
# @@protoc_insertion_point(module_scope)
