# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_list_accepted_agreements.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'response_list_accepted_agreements.proto\x12\x03rti\"\xc7\x02\n\x1eResponseListAcceptedAgreements\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12$\n\x1a\x61greement_acceptance_ssboe\x18\xd3\xae\t \x01(\x05\x12%\n\x1b\x61greement_acceptance_status\x18\xd2\xae\t \x01(\t\x12&\n\x1c\x61greement_acceptance_request\x18\xd6\xae\t \x01(\t\x12\x19\n\x0f\x61greement_title\x18\xbe\xae\t \x01(\t\x12\x16\n\x0c\x61greement_id\x18\xbf\xae\t \x01(\t')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_list_accepted_agreements_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSELISTACCEPTEDAGREEMENTS._serialized_start=49
  _RESPONSELISTACCEPTEDAGREEMENTS._serialized_end=376
# @@protoc_insertion_point(module_scope)
