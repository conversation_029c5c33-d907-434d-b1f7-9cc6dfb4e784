# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: front_month_contract_update.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!front_month_contract_update.proto\x12\x03rti\"\xc5\x01\n\x18\x46rontMonthContractUpdate\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x1f\n\x15is_front_month_symbol\x18\xae\x8d\t \x01(\x08\x12\x15\n\x0bsymbol_name\x18\xa3\x8d\x06 \x01(\t\x12\x18\n\x0etrading_symbol\x18\xa7\xcb\t \x01(\t\x12\x1a\n\x10trading_exchange\x18\xa8\xcb\t \x01(\t')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'front_month_contract_update_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _FRONTMONTHCONTRACTUPDATE._serialized_start=43
  _FRONTMONTHCONTRACTUPDATE._serialized_end=240
# @@protoc_insertion_point(module_scope)
