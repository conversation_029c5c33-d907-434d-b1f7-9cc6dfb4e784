# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_product_rms_info.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fresponse_product_rms_info.proto\x12\x03rti\"\xb1\x04\n\x16ResponseProductRmsInfo\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x17\n\rpresence_bits\x18\x96\xb0\t \x01(\r\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x16\n\x0cproduct_code\x18\x8d\x93\x06 \x01(\t\x12\x14\n\nloss_limit\x18\xa3\xb3\t \x01(\x01\x12\x1e\n\x14\x63ommission_fill_rate\x18\xa9\xca\t \x01(\x01\x12\x19\n\x0f\x62uy_margin_rate\x18\xcb\xca\t \x01(\x01\x12\x1a\n\x10sell_margin_rate\x18\xcc\xca\t \x01(\x01\x12\x13\n\tbuy_limit\x18\x99\xb3\t \x01(\x05\x12\x1c\n\x12max_order_quantity\x18\x99\xdc\x06 \x01(\x05\x12\x14\n\nsell_limit\x18\xb3\xb3\t \x01(\x05\"\x9a\x01\n\x0cPresenceBits\x12\r\n\tBUY_LIMIT\x10\x01\x12\x0e\n\nSELL_LIMIT\x10\x02\x12\x0e\n\nLOSS_LIMIT\x10\x04\x12\x16\n\x12MAX_ORDER_QUANTITY\x10\x08\x12\x13\n\x0f\x42UY_MARGIN_RATE\x10\x10\x12\x14\n\x10SELL_MARGIN_RATE\x10 \x12\x18\n\x14\x43OMMISSION_FILL_RATE\x10@')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_product_rms_info_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSEPRODUCTRMSINFO._serialized_start=41
  _RESPONSEPRODUCTRMSINFO._serialized_end=602
  _RESPONSEPRODUCTRMSINFO_PRESENCEBITS._serialized_start=448
  _RESPONSEPRODUCTRMSINFO_PRESENCEBITS._serialized_end=602
# @@protoc_insertion_point(module_scope)
