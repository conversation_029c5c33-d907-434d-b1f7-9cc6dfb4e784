# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_depth_by_order_snapshot.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&response_depth_by_order_snapshot.proto\x12\x03rti\"\x94\x03\n\x1cResponseDepthByOrderSnapshot\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x19\n\x0fsequence_number\x18\x82\xeb\x06 \x01(\x04\x12G\n\ndepth_side\x18\x8c\xb0\t \x01(\x0e\x32\x31.rti.ResponseDepthByOrderSnapshot.TransactionType\x12\x15\n\x0b\x64\x65pth_price\x18\xa5\xb6\t \x01(\x01\x12\x14\n\ndepth_size\x18\xa6\xb6\t \x03(\x05\x12\x1e\n\x14\x64\x65pth_order_priority\x18\x8d\xb0\t \x03(\x04\x12\x1b\n\x11\x65xchange_order_id\x18\xf6\x8d\t \x03(\t\"$\n\x0fTransactionType\x12\x07\n\x03\x42UY\x10\x01\x12\x08\n\x04SELL\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_depth_by_order_snapshot_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSEDEPTHBYORDERSNAPSHOT._serialized_start=48
  _RESPONSEDEPTHBYORDERSNAPSHOT._serialized_end=452
  _RESPONSEDEPTHBYORDERSNAPSHOT_TRANSACTIONTYPE._serialized_start=416
  _RESPONSEDEPTHBYORDERSNAPSHOT_TRANSACTIONTYPE._serialized_end=452
# @@protoc_insertion_point(module_scope)
