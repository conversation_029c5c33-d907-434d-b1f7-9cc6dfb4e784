# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_bracket_order.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1brequest_bracket_order.proto\x12\x03rti\"\x9c\x11\n\x13RequestBracketOrder\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x12\n\x08user_tag\x18\x87\xb4\t \x01(\t\x12\x15\n\x0bwindow_name\x18\x85\xb8\t \x01(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x12\n\x08quantity\x18\x84\xeb\x06 \x01(\x05\x12\x0f\n\x05price\x18\xe2\xdd\x06 \x01(\x01\x12\x17\n\rtrigger_price\x18\xff\x8d\t \x01(\x01\x12\x44\n\x10transaction_type\x18\x83\xeb\x06 \x01(\x0e\x32(.rti.RequestBracketOrder.TransactionType\x12\x35\n\x08\x64uration\x18\x85\xeb\x06 \x01(\x0e\x32!.rti.RequestBracketOrder.Duration\x12\x38\n\nprice_type\x18\x88\xeb\x06 \x01(\x0e\x32\".rti.RequestBracketOrder.PriceType\x12\x15\n\x0btrade_route\x18\x90\xeb\x06 \x01(\t\x12\x41\n\x0emanual_or_auto\x18\xd6\xb8\t \x01(\x0e\x32\'.rti.RequestBracketOrder.OrderPlacement\x12\x36\n\tuser_type\x18\xb4\xb3\t \x01(\x0e\x32!.rti.RequestBracketOrder.UserType\x12<\n\x0c\x62racket_type\x18\x9f\xcb\t \x01(\x0e\x32$.rti.RequestBracketOrder.BracketType\x12\x1a\n\x10\x62reak_even_ticks\x18\xf2\xcb\t \x01(\x05\x12\"\n\x18\x62reak_even_trigger_ticks\x18\xf4\xcb\t \x01(\x05\x12\x19\n\x0ftarget_quantity\x18\xd9\xb6\t \x03(\x05\x12\x16\n\x0ctarget_ticks\x18\xd8\xb6\t \x03(\x05\x12\x17\n\rstop_quantity\x18\xdb\xb6\t \x03(\x05\x12\x14\n\nstop_ticks\x18\xda\xb6\t \x03(\x05\x12%\n\x1btrailing_stop_trigger_ticks\x18\xc4\xcb\t \x01(\x05\x12+\n!trailing_stop_by_last_trade_price\x18\x86\xcb\t \x01(\x08\x12(\n\x1etarget_market_order_if_touched\x18\xdf\xcb\t \x01(\x08\x12\x1f\n\x15stop_market_on_reject\x18\xe9\xb9\t \x01(\x08\x12 \n\x16target_market_at_ssboe\x18\xd9\xcb\t \x01(\x05\x12 \n\x16target_market_at_usecs\x18\xda\xcb\t \x01(\x05\x12\x1e\n\x14stop_market_at_ssboe\x18\xdb\xcb\t \x01(\x05\x12\x1e\n\x14stop_market_at_usecs\x18\xdc\xcb\t \x01(\x05\x12(\n\x1etarget_market_order_after_secs\x18\xdd\xcb\t \x01(\x05\x12\x1a\n\x10release_at_ssboe\x18\xf7\xb6\t \x01(\x05\x12\x1a\n\x10release_at_usecs\x18\xb5\xb7\t \x01(\x05\x12\x19\n\x0f\x63\x61ncel_at_ssboe\x18\x9d\xcb\t \x01(\x05\x12\x19\n\x0f\x63\x61ncel_at_usecs\x18\x9e\xcb\t \x01(\x05\x12\x1b\n\x11\x63\x61ncel_after_secs\x18\xf8\xb6\t \x01(\x05\x12\x1b\n\x11if_touched_symbol\x18\xd3\xb6\t \x01(\t\x12\x1d\n\x13if_touched_exchange\x18\xd4\xb6\t \x01(\t\x12\x42\n\x14if_touched_condition\x18\xd5\xb6\t \x01(\x0e\x32\".rti.RequestBracketOrder.Condition\x12\x45\n\x16if_touched_price_field\x18\xd6\xb6\t \x01(\x0e\x32#.rti.RequestBracketOrder.PriceField\x12\x1a\n\x10if_touched_price\x18\xa0\xb0\t \x01(\x01\"Z\n\x08UserType\x12\x13\n\x0fUSER_TYPE_ADMIN\x10\x00\x12\x11\n\rUSER_TYPE_FCM\x10\x01\x12\x10\n\x0cUSER_TYPE_IB\x10\x02\x12\x14\n\x10USER_TYPE_TRADER\x10\x03\"\x8c\x01\n\x0b\x42racketType\x12\r\n\tSTOP_ONLY\x10\x01\x12\x0f\n\x0bTARGET_ONLY\x10\x02\x12\x13\n\x0fTARGET_AND_STOP\x10\x03\x12\x14\n\x10STOP_ONLY_STATIC\x10\x04\x12\x16\n\x12TARGET_ONLY_STATIC\x10\x05\x12\x1a\n\x16TARGET_AND_STOP_STATIC\x10\x06\"$\n\x0fTransactionType\x12\x07\n\x03\x42UY\x10\x01\x12\x08\n\x04SELL\x10\x02\".\n\x08\x44uration\x12\x07\n\x03\x44\x41Y\x10\x01\x12\x07\n\x03GTC\x10\x02\x12\x07\n\x03IOC\x10\x03\x12\x07\n\x03\x46OK\x10\x04\"p\n\tPriceType\x12\t\n\x05LIMIT\x10\x01\x12\n\n\x06MARKET\x10\x02\x12\x0e\n\nSTOP_LIMIT\x10\x03\x12\x0f\n\x0bSTOP_MARKET\x10\x04\x12\x15\n\x11MARKET_IF_TOUCHED\x10\x05\x12\x14\n\x10LIMIT_IF_TOUCHED\x10\x06\"&\n\x0eOrderPlacement\x12\n\n\x06MANUAL\x10\x01\x12\x08\n\x04\x41UTO\x10\x02\"M\n\nPriceField\x12\r\n\tBID_PRICE\x10\x01\x12\x0f\n\x0bOFFER_PRICE\x10\x02\x12\x0f\n\x0bTRADE_PRICE\x10\x03\x12\x0e\n\nLEAN_PRICE\x10\x04\"\x83\x01\n\tCondition\x12\x0c\n\x08\x45QUAL_TO\x10\x01\x12\x10\n\x0cNOT_EQUAL_TO\x10\x02\x12\x10\n\x0cGREATER_THAN\x10\x03\x12\x19\n\x15GREATER_THAN_EQUAL_TO\x10\x04\x12\x0f\n\x0bLESSER_THAN\x10\x05\x12\x18\n\x14LESSER_THAN_EQUAL_TO\x10\x06')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_bracket_order_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTBRACKETORDER._serialized_start=37
  _REQUESTBRACKETORDER._serialized_end=2241
  _REQUESTBRACKETORDER_USERTYPE._serialized_start=1555
  _REQUESTBRACKETORDER_USERTYPE._serialized_end=1645
  _REQUESTBRACKETORDER_BRACKETTYPE._serialized_start=1648
  _REQUESTBRACKETORDER_BRACKETTYPE._serialized_end=1788
  _REQUESTBRACKETORDER_TRANSACTIONTYPE._serialized_start=1790
  _REQUESTBRACKETORDER_TRANSACTIONTYPE._serialized_end=1826
  _REQUESTBRACKETORDER_DURATION._serialized_start=1828
  _REQUESTBRACKETORDER_DURATION._serialized_end=1874
  _REQUESTBRACKETORDER_PRICETYPE._serialized_start=1876
  _REQUESTBRACKETORDER_PRICETYPE._serialized_end=1988
  _REQUESTBRACKETORDER_ORDERPLACEMENT._serialized_start=1990
  _REQUESTBRACKETORDER_ORDERPLACEMENT._serialized_end=2028
  _REQUESTBRACKETORDER_PRICEFIELD._serialized_start=2030
  _REQUESTBRACKETORDER_PRICEFIELD._serialized_end=2107
  _REQUESTBRACKETORDER_CONDITION._serialized_start=2110
  _REQUESTBRACKETORDER_CONDITION._serialized_end=2241
# @@protoc_insertion_point(module_scope)
