# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_tick_bar_replay.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1drequest_tick_bar_replay.proto\x12\x03rti\"\xc1\x05\n\x14RequestTickBarReplay\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x35\n\x08\x62\x61r_type\x18\xa0\xa3\x07 \x01(\x0e\x32!.rti.RequestTickBarReplay.BarType\x12<\n\x0c\x62\x61r_sub_type\x18\xa8\xa3\x07 \x01(\x0e\x32$.rti.RequestTickBarReplay.BarSubType\x12\x1c\n\x12\x62\x61r_type_specifier\x18\xc2\x85\t \x01(\t\x12\x15\n\x0bstart_index\x18\xaa\xab\t \x01(\x05\x12\x16\n\x0c\x66inish_index\x18\xab\xab\t \x01(\x05\x12\x18\n\x0euser_max_count\x18\xa4\xb3\t \x01(\x05\x12!\n\x17\x63ustom_session_open_ssm\x18\xa9\xa3\x07 \x01(\x05\x12\"\n\x18\x63ustom_session_close_ssm\x18\xaa\xa3\x07 \x01(\x05\x12\x38\n\tdirection\x18\x85\x8e\t \x01(\x0e\x32#.rti.RequestTickBarReplay.Direction\x12\x39\n\ntime_order\x18\xbb\x8e\t \x01(\x0e\x32#.rti.RequestTickBarReplay.TimeOrder\x12\x15\n\x0bresume_bars\x18\xaa\xb0\t \x01(\x08\"6\n\x07\x42\x61rType\x12\x0c\n\x08TICK_BAR\x10\x01\x12\r\n\tRANGE_BAR\x10\x02\x12\x0e\n\nVOLUME_BAR\x10\x03\"%\n\nBarSubType\x12\x0b\n\x07REGULAR\x10\x01\x12\n\n\x06\x43USTOM\x10\x02\" \n\tDirection\x12\t\n\x05\x46IRST\x10\x01\x12\x08\n\x04LAST\x10\x02\"(\n\tTimeOrder\x12\x0c\n\x08\x46ORWARDS\x10\x01\x12\r\n\tBACKWARDS\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_tick_bar_replay_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTTICKBARREPLAY._serialized_start=39
  _REQUESTTICKBARREPLAY._serialized_end=744
  _REQUESTTICKBARREPLAY_BARTYPE._serialized_start=575
  _REQUESTTICKBARREPLAY_BARTYPE._serialized_end=629
  _REQUESTTICKBARREPLAY_BARSUBTYPE._serialized_start=631
  _REQUESTTICKBARREPLAY_BARSUBTYPE._serialized_end=668
  _REQUESTTICKBARREPLAY_DIRECTION._serialized_start=670
  _REQUESTTICKBARREPLAY_DIRECTION._serialized_end=702
  _REQUESTTICKBARREPLAY_TIMEORDER._serialized_start=704
  _REQUESTTICKBARREPLAY_TIMEORDER._serialized_end=744
# @@protoc_insertion_point(module_scope)
