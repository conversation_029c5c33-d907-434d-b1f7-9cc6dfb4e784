# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_give_tick_size_type_table.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(response_give_tick_size_type_table.proto\x12\x03rti\"\xc9\x03\n\x1dResponseGiveTickSizeTypeTable\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x18\n\x0etick_size_type\x18\xb7\xb4\t \x01(\t\x12\x1f\n\x15tick_size_fp_operator\x18\xba\xb4\t \x01(\t\x12\x1f\n\x15tick_size_lp_operator\x18\xbb\xb4\t \x01(\t\x12\x1b\n\x11min_fprice_change\x18\x93\xb6\t \x01(\x01\x12\x1f\n\x15tick_size_first_price\x18\xb8\xb4\t \x01(\x01\x12\x1e\n\x14tick_size_last_price\x18\xb9\xb4\t \x01(\x01\"y\n\x0cPresenceBits\x12\x19\n\x15TICK_SIZE_FIRST_PRICE\x10\x01\x12\x18\n\x14TICK_SIZE_LAST_PRICE\x10\x02\x12\x19\n\x15TICK_SIZE_FP_OPERATOR\x10\x04\x12\x19\n\x15TICK_SIZE_LP_OPERATOR\x10\x08')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_give_tick_size_type_table_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSEGIVETICKSIZETYPETABLE._serialized_start=50
  _RESPONSEGIVETICKSIZETYPETABLE._serialized_end=507
  _RESPONSEGIVETICKSIZETYPETABLE_PRESENCEBITS._serialized_start=386
  _RESPONSEGIVETICKSIZETYPETABLE_PRESENCEBITS._serialized_end=507
# @@protoc_insertion_point(module_scope)
