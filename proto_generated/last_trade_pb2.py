# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: last_trade.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10last_trade.proto\x12\x03rti\"\xb0\x05\n\tLastTrade\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x14\n\nclear_bits\x18\xcb\xb7\t \x01(\r\x12\x15\n\x0bis_snapshot\x18\xa9\xdc\x06 \x01(\x08\x12\x15\n\x0btrade_price\x18\xa6\x8d\x06 \x01(\x01\x12\x14\n\ntrade_size\x18\xd2\x8e\x06 \x01(\x05\x12\x33\n\taggressor\x18\x83\xeb\x06 \x01(\x0e\x32\x1e.rti.LastTrade.TransactionType\x12\x1b\n\x11\x65xchange_order_id\x18\xf6\x8d\t \x01(\t\x12%\n\x1b\x61ggressor_exchange_order_id\x18\x91\xb8\t \x01(\t\x12\x14\n\nnet_change\x18\xab\x8d\x06 \x01(\x01\x12\x18\n\x0epercent_change\x18\xd8\x8d\x06 \x01(\x01\x12\x10\n\x06volume\x18\xc0\x8d\x06 \x01(\x04\x12\x0e\n\x04vwap\x18\x83\x98\x06 \x01(\x01\x12\x14\n\ntrade_time\x18\x9b\x90\x06 \x01(\t\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\x12\x16\n\x0csource_ssboe\x18\x80\x97\t \x01(\x05\x12\x16\n\x0csource_usecs\x18\x81\x97\t \x01(\x05\x12\x16\n\x0csource_nsecs\x18\x84\x97\t \x01(\x05\x12\x13\n\tjop_ssboe\x18\xc8\x98\t \x01(\x05\x12\x13\n\tjop_nsecs\x18\xcc\x98\t \x01(\x05\"X\n\x0cPresenceBits\x12\x0e\n\nLAST_TRADE\x10\x01\x12\x0e\n\nNET_CHANGE\x10\x02\x12\x12\n\x0ePRECENT_CHANGE\x10\x04\x12\n\n\x06VOLUME\x10\x08\x12\x08\n\x04VWAP\x10\x10\"$\n\x0fTransactionType\x12\x07\n\x03\x42UY\x10\x01\x12\x08\n\x04SELL\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'last_trade_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _LASTTRADE._serialized_start=26
  _LASTTRADE._serialized_end=714
  _LASTTRADE_PRESENCEBITS._serialized_start=588
  _LASTTRADE_PRESENCEBITS._serialized_end=676
  _LASTTRADE_TRANSACTIONTYPE._serialized_start=678
  _LASTTRADE_TRANSACTIONTYPE._serialized_end=714
# @@protoc_insertion_point(module_scope)
