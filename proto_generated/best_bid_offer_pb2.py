# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: best_bid_offer.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x62\x65st_bid_offer.proto\x12\x03rti\"\xdb\x03\n\x0c\x42\x65stBidOffer\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x14\n\nclear_bits\x18\xcb\xb7\t \x01(\r\x12\x15\n\x0bis_snapshot\x18\xa9\xdc\x06 \x01(\x08\x12\x13\n\tbid_price\x18\xb6\x8d\x06 \x01(\x01\x12\x12\n\x08\x62id_size\x18\xbe\x8d\x06 \x01(\x05\x12\x14\n\nbid_orders\x18\xa3\xb6\t \x01(\x05\x12\x1b\n\x11\x62id_implicit_size\x18\xf3\xb9\t \x01(\x05\x12\x12\n\x08\x62id_time\x18\xaa\x8f\x06 \x01(\t\x12\x13\n\task_price\x18\xb9\x8d\x06 \x01(\x01\x12\x12\n\x08\x61sk_size\x18\xbf\x8d\x06 \x01(\x05\x12\x14\n\nask_orders\x18\xa4\xb6\t \x01(\x05\x12\x1b\n\x11\x61sk_implicit_size\x18\xf4\xb9\t \x01(\x05\x12\x12\n\x08\x61sk_time\x18\xab\x8f\x06 \x01(\t\x12\x14\n\nlean_price\x18\x9d\xba\t \x01(\x01\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\"0\n\x0cPresenceBits\x12\x07\n\x03\x42ID\x10\x01\x12\x07\n\x03\x41SK\x10\x02\x12\x0e\n\nLEAN_PRICE\x10\x04')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'best_bid_offer_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _BESTBIDOFFER._serialized_start=30
  _BESTBIDOFFER._serialized_end=505
  _BESTBIDOFFER_PRESENCEBITS._serialized_start=457
  _BESTBIDOFFER_PRESENCEBITS._serialized_end=505
# @@protoc_insertion_point(module_scope)
