# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_set_rithmic_mrkt_data_self_cert_status.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4request_set_rithmic_mrkt_data_self_cert_status.proto\x12\x03rti\"\x92\x01\n\'RequestSetRithmicMrktDataSelfCertStatus\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x16\n\x0c\x61greement_id\x18\xbf\xae\t \x01(\t\x12$\n\x1amarket_data_usage_capacity\x18\xd7\xae\t \x01(\t')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_set_rithmic_mrkt_data_self_cert_status_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTSETRITHMICMRKTDATASELFCERTSTATUS._serialized_start=62
  _REQUESTSETRITHMICMRKTDATASELFCERTSTATUS._serialized_end=208
# @@protoc_insertion_point(module_scope)
