# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_tick_bar_update.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1drequest_tick_bar_update.proto\x12\x03rti\"\x81\x04\n\x14RequestTickBarUpdate\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x34\n\x07request\x18\xa0\x8d\x06 \x01(\x0e\x32!.rti.RequestTickBarUpdate.Request\x12\x35\n\x08\x62\x61r_type\x18\xa0\xa3\x07 \x01(\x0e\x32!.rti.RequestTickBarUpdate.BarType\x12<\n\x0c\x62\x61r_sub_type\x18\xa8\xa3\x07 \x01(\x0e\x32$.rti.RequestTickBarUpdate.BarSubType\x12\x1c\n\x12\x62\x61r_type_specifier\x18\xc2\x85\t \x01(\t\x12!\n\x17\x63ustom_session_open_ssm\x18\xa9\xa3\x07 \x01(\x05\x12\"\n\x18\x63ustom_session_close_ssm\x18\xaa\xa3\x07 \x01(\x05\"6\n\x07\x42\x61rType\x12\x0c\n\x08TICK_BAR\x10\x01\x12\r\n\tRANGE_BAR\x10\x02\x12\x0e\n\nVOLUME_BAR\x10\x03\"%\n\nBarSubType\x12\x0b\n\x07REGULAR\x10\x01\x12\n\n\x06\x43USTOM\x10\x02\")\n\x07Request\x12\r\n\tSUBSCRIBE\x10\x01\x12\x0f\n\x0bUNSUBSCRIBE\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_tick_bar_update_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTTICKBARUPDATE._serialized_start=39
  _REQUESTTICKBARUPDATE._serialized_end=552
  _REQUESTTICKBARUPDATE_BARTYPE._serialized_start=416
  _REQUESTTICKBARUPDATE_BARTYPE._serialized_end=470
  _REQUESTTICKBARUPDATE_BARSUBTYPE._serialized_start=472
  _REQUESTTICKBARUPDATE_BARSUBTYPE._serialized_end=509
  _REQUESTTICKBARUPDATE_REQUEST._serialized_start=511
  _REQUESTTICKBARUPDATE_REQUEST._serialized_end=552
# @@protoc_insertion_point(module_scope)
