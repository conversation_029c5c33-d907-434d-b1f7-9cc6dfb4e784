# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: rithmic_order_notification.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n rithmic_order_notification.proto\x12\x03rti\"\x8b\x12\n\x18RithmicOrderNotification\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_tag\x18\x87\xb4\t \x01(\t\x12?\n\x0bnotify_type\x18\x99\xb0\t \x01(\x0e\x32(.rti.RithmicOrderNotification.NotifyType\x12\x15\n\x0bis_snapshot\x18\xa9\xdc\x06 \x01(\x08\x12\x10\n\x06status\x18\xdf\xdd\x06 \x01(\t\x12\x13\n\tbasket_id\x18\xdc\xdd\x06 \x01(\t\x12\x1c\n\x12original_basket_id\x18\x81\xb7\t \x01(\t\x12\x1b\n\x11linked_basket_ids\x18\x96\xde\x06 \x01(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x11\n\x07user_id\x18\xbb\xff\x07 \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x18\n\x0etrade_exchange\x18\x95\xeb\x06 \x01(\t\x12\x15\n\x0btrade_route\x18\x90\xeb\x06 \x01(\t\x12\x1b\n\x11\x65xchange_order_id\x18\xf6\x8d\t \x01(\t\x12\x19\n\x0finstrument_type\x18\xa4\xdc\x06 \x01(\t\x12\x1b\n\x11\x63ompletion_reason\x18\x99\x8e\t \x01(\t\x12\x12\n\x08quantity\x18\x84\xeb\x06 \x01(\x05\x12\x1e\n\x14quan_release_pending\x18\x9b\xeb\x06 \x01(\x05\x12\x0f\n\x05price\x18\xe2\xdd\x06 \x01(\x01\x12\x17\n\rtrigger_price\x18\xff\x8d\t \x01(\x01\x12I\n\x10transaction_type\x18\x83\xeb\x06 \x01(\x0e\x32-.rti.RithmicOrderNotification.TransactionType\x12:\n\x08\x64uration\x18\x85\xeb\x06 \x01(\x0e\x32&.rti.RithmicOrderNotification.Duration\x12=\n\nprice_type\x18\x88\xeb\x06 \x01(\x0e\x32\'.rti.RithmicOrderNotification.PriceType\x12\x42\n\x0forig_price_type\x18\x92\xb9\t \x01(\x0e\x32\'.rti.RithmicOrderNotification.PriceType\x12\x46\n\x0emanual_or_auto\x18\xd6\xb8\t \x01(\x0e\x32,.rti.RithmicOrderNotification.OrderPlacement\x12\x41\n\x0c\x62racket_type\x18\x9f\xcb\t \x01(\x0e\x32).rti.RithmicOrderNotification.BracketType\x12\x18\n\x0e\x61vg_fill_price\x18\xf2\xdd\x06 \x01(\x01\x12\x19\n\x0ftotal_fill_size\x18\xff\xb3\t \x01(\x05\x12\x1d\n\x13total_unfilled_size\x18\x80\xb4\t \x01(\x05\x12\x18\n\x0etrail_by_ticks\x18\x88\xcb\t \x01(\x05\x12\x1b\n\x11trail_by_price_id\x18\x89\xcb\t \x01(\x05\x12\x19\n\x0fsequence_number\x18\x82\xeb\x06 \x01(\t\x12\x1e\n\x14orig_sequence_number\x18\x8f\x8e\t \x01(\t\x12\x1d\n\x13\x63or_sequence_number\x18\x90\x8e\t \x01(\t\x12\x12\n\x08\x63urrency\x18\x8e\xb6\t \x01(\t\x12\x16\n\x0c\x63ountry_code\x18\xbc\xb4\t \x01(\t\x12\x0e\n\x04text\x18\xc8\xa9\x07 \x01(\t\x12\x15\n\x0breport_text\x18\xdc\xa9\x07 \x01(\t\x12\x11\n\x07remarks\x18\xb6\xb9\t \x01(\t\x12\x15\n\x0bwindow_name\x18\x85\xb8\t \x01(\t\x12 \n\x16originator_window_name\x18\xaf\xb8\t \x01(\t\x12\x19\n\x0f\x63\x61ncel_at_ssboe\x18\x9d\xcb\t \x01(\x05\x12\x19\n\x0f\x63\x61ncel_at_usecs\x18\x9e\xcb\t \x01(\x05\x12\x1b\n\x11\x63\x61ncel_after_secs\x18\xf8\xb6\t \x01(\x05\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\"\xcb\x03\n\nNotifyType\x12\x18\n\x14ORDER_RCVD_FROM_CLNT\x10\x01\x12\x19\n\x15MODIFY_RCVD_FROM_CLNT\x10\x02\x12\x19\n\x15\x43\x41NCEL_RCVD_FROM_CLNT\x10\x03\x12\x10\n\x0cOPEN_PENDING\x10\x04\x12\x12\n\x0eMODIFY_PENDING\x10\x05\x12\x12\n\x0e\x43\x41NCEL_PENDING\x10\x06\x12\x1b\n\x17ORDER_RCVD_BY_EXCH_GTWY\x10\x07\x12\x1c\n\x18MODIFY_RCVD_BY_EXCH_GTWY\x10\x08\x12\x1c\n\x18\x43\x41NCEL_RCVD_BY_EXCH_GTWY\x10\t\x12\x16\n\x12ORDER_SENT_TO_EXCH\x10\n\x12\x17\n\x13MODIFY_SENT_TO_EXCH\x10\x0b\x12\x17\n\x13\x43\x41NCEL_SENT_TO_EXCH\x10\x0c\x12\x08\n\x04OPEN\x10\r\x12\x0c\n\x08MODIFIED\x10\x0e\x12\x0c\n\x08\x43OMPLETE\x10\x0f\x12\x17\n\x13MODIFICATION_FAILED\x10\x10\x12\x17\n\x13\x43\x41NCELLATION_FAILED\x10\x11\x12\x13\n\x0fTRIGGER_PENDING\x10\x12\x12\x0b\n\x07GENERIC\x10\x13\x12\x16\n\x12LINK_ORDERS_FAILED\x10\x14\",\n\x0fTransactionType\x12\x07\n\x03\x42UY\x10\x01\x12\x08\n\x04SELL\x10\x02\x12\x06\n\x02SS\x10\x03\".\n\x08\x44uration\x12\x07\n\x03\x44\x41Y\x10\x01\x12\x07\n\x03GTC\x10\x02\x12\x07\n\x03IOC\x10\x03\x12\x07\n\x03\x46OK\x10\x04\"C\n\tPriceType\x12\t\n\x05LIMIT\x10\x01\x12\n\n\x06MARKET\x10\x02\x12\x0e\n\nSTOP_LIMIT\x10\x03\x12\x0f\n\x0bSTOP_MARKET\x10\x04\"\x8c\x01\n\x0b\x42racketType\x12\r\n\tSTOP_ONLY\x10\x01\x12\x0f\n\x0bTARGET_ONLY\x10\x02\x12\x13\n\x0fTARGET_AND_STOP\x10\x03\x12\x14\n\x10STOP_ONLY_STATIC\x10\x04\x12\x16\n\x12TARGET_ONLY_STATIC\x10\x05\x12\x1a\n\x16TARGET_AND_STOP_STATIC\x10\x06\"&\n\x0eOrderPlacement\x12\n\n\x06MANUAL\x10\x01\x12\x08\n\x04\x41UTO\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rithmic_order_notification_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RITHMICORDERNOTIFICATION._serialized_start=42
  _RITHMICORDERNOTIFICATION._serialized_end=2357
  _RITHMICORDERNOTIFICATION_NOTIFYTYPE._serialized_start=1552
  _RITHMICORDERNOTIFICATION_NOTIFYTYPE._serialized_end=2011
  _RITHMICORDERNOTIFICATION_TRANSACTIONTYPE._serialized_start=2013
  _RITHMICORDERNOTIFICATION_TRANSACTIONTYPE._serialized_end=2057
  _RITHMICORDERNOTIFICATION_DURATION._serialized_start=2059
  _RITHMICORDERNOTIFICATION_DURATION._serialized_end=2105
  _RITHMICORDERNOTIFICATION_PRICETYPE._serialized_start=2107
  _RITHMICORDERNOTIFICATION_PRICETYPE._serialized_end=2174
  _RITHMICORDERNOTIFICATION_BRACKETTYPE._serialized_start=2177
  _RITHMICORDERNOTIFICATION_BRACKETTYPE._serialized_end=2317
  _RITHMICORDERNOTIFICATION_ORDERPLACEMENT._serialized_start=2319
  _RITHMICORDERNOTIFICATION_ORDERPLACEMENT._serialized_end=2357
# @@protoc_insertion_point(module_scope)
