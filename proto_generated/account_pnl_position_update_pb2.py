# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: account_pnl_position_update.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!account_pnl_position_update.proto\x12\x03rti\"\x94\t\n\x18\x41\x63\x63ountPnLPositionUpdate\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x15\n\x0bis_snapshot\x18\xa9\xdc\x06 \x01(\x08\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x16\n\x0c\x66ill_buy_qty\x18\xb9\xb3\t \x01(\x05\x12\x17\n\rfill_sell_qty\x18\xba\xb3\t \x01(\x05\x12\x17\n\rorder_buy_qty\x18\xb5\xb3\t \x01(\x05\x12\x18\n\x0eorder_sell_qty\x18\xb6\xb3\t \x01(\x05\x12\x11\n\x07\x62uy_qty\x18\x94\xb5\t \x01(\x05\x12\x12\n\x08sell_qty\x18\x95\xb5\t \x01(\x05\x12!\n\x17open_long_options_value\x18\xb1\xcb\t \x01(\t\x12\"\n\x18open_short_options_value\x18\xb2\xcb\t \x01(\t\x12\x1e\n\x14\x63losed_options_value\x18\xb3\xcb\t \x01(\t\x12\x1e\n\x14option_cash_reserved\x18\xb7\xcb\t \x01(\t\x12 \n\x16rms_account_commission\x18\xb9\xcb\t \x01(\t\x12\x1b\n\x11open_position_pnl\x18\xa1\xca\t \x01(\t\x12 \n\x16open_position_quantity\x18\xa2\xca\t \x01(\x05\x12\x1d\n\x13\x63losed_position_pnl\x18\xa3\xca\t \x01(\t\x12\"\n\x18\x63losed_position_quantity\x18\xa4\xca\t \x01(\x05\x12\x16\n\x0cnet_quantity\x18\xa7\xca\t \x01(\x05\x12\x1b\n\x11\x65xcess_buy_margin\x18\xbf\xca\t \x01(\t\x12\x18\n\x0emargin_balance\x18\xb1\xca\t \x01(\t\x12\x1c\n\x12min_margin_balance\x18\xb0\xca\t \x01(\t\x12\x1d\n\x13min_account_balance\x18\xa8\xca\t \x01(\t\x12\x19\n\x0f\x61\x63\x63ount_balance\x18\xaa\xca\t \x01(\t\x12\x16\n\x0c\x63\x61sh_on_hand\x18\xab\xca\t \x01(\t\x12\x1b\n\x11option_closed_pnl\x18\xbe\xcb\t \x01(\t\x12(\n\x1epercent_maximum_allowable_loss\x18\xa5\xca\t \x01(\t\x12\x19\n\x0foption_open_pnl\x18\xbd\xcb\t \x01(\t\x12\x15\n\x0bmtm_account\x18\x96\xb5\t \x01(\t\x12 \n\x16\x61vailable_buying_power\x18\xd7\xca\t \x01(\t\x12\x1b\n\x11used_buying_power\x18\xd6\xca\t \x01(\t\x12\x1f\n\x15reserved_buying_power\x18\xd5\xca\t \x01(\t\x12\x1c\n\x12\x65xcess_sell_margin\x18\xc0\xca\t \x01(\t\x12\x16\n\x0c\x64\x61y_open_pnl\x18\x82\xd2\t \x01(\t\x12\x18\n\x0e\x64\x61y_closed_pnl\x18\x83\xd2\t \x01(\t\x12\x11\n\x07\x64\x61y_pnl\x18\x84\xd2\t \x01(\t\x12\x1d\n\x13\x64\x61y_open_pnl_offset\x18\x85\xd2\t \x01(\t\x12\x1f\n\x15\x64\x61y_closed_pnl_offset\x18\x86\xd2\t \x01(\t\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'account_pnl_position_update_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _ACCOUNTPNLPOSITIONUPDATE._serialized_start=43
  _ACCOUNTPNLPOSITIONUPDATE._serialized_end=1215
# @@protoc_insertion_point(module_scope)
