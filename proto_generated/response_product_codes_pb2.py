# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_product_codes.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cresponse_product_codes.proto\x12\x03rti\"\xa4\x02\n\x14ResponseProductCodes\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x15\n\x0bsymbol_name\x18\xa3\x8d\x06 \x01(\t\x12\x16\n\x0cproduct_code\x18\x8d\x93\x06 \x01(\t\x12#\n\x19timezone_time_of_interest\x18\xba\xb8\t \x01(\t\x12$\n\x1a\x62\x65gin_time_of_interest_msm\x18\xb8\xb8\t \x01(\x05\x12\"\n\x18\x65nd_time_of_interest_msm\x18\xb9\xb8\t \x01(\x05')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_product_codes_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSEPRODUCTCODES._serialized_start=38
  _RESPONSEPRODUCTCODES._serialized_end=330
# @@protoc_insertion_point(module_scope)
