# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_show_agreement.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dresponse_show_agreement.proto\x12\x03rti\"\xbd\x02\n\x15ResponseShowAgreement\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x19\n\x0f\x61greement_title\x18\xbe\xae\t \x01(\t\x12\x16\n\x0c\x61greement_id\x18\xbf\xae\t \x01(\t\x12\x13\n\tagreement\x18\xbd\xae\t \x01(\x0c\x12\x18\n\x0e\x61greement_html\x18\xd8\xae\t \x01(\x0c\x12\"\n\x18\x61greement_mandatory_flag\x18\xc2\xae\t \x01(\t\x12\x1a\n\x10\x61greement_status\x18\xc7\xae\t \x01(\t\x12&\n\x1c\x61greement_acceptance_request\x18\xd6\xae\t \x01(\t')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_show_agreement_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSESHOWAGREEMENT._serialized_start=39
  _RESPONSESHOWAGREEMENT._serialized_end=356
# @@protoc_insertion_point(module_scope)
