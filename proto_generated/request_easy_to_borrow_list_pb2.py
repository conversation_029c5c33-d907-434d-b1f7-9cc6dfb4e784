# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_easy_to_borrow_list.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!request_easy_to_borrow_list.proto\x12\x03rti\"\xa8\x01\n\x17RequestEasyToBorrowList\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x37\n\x07request\x18\xa0\x8d\x06 \x01(\x0e\x32$.rti.RequestEasyToBorrowList.Request\")\n\x07Request\x12\r\n\tSUBSCRIBE\x10\x01\x12\x0f\n\x0bUNSUBSCRIBE\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_easy_to_borrow_list_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTEASYTOBORROWLIST._serialized_start=43
  _REQUESTEASYTOBORROWLIST._serialized_end=211
  _REQUESTEASYTOBORROWLIST_REQUEST._serialized_start=170
  _REQUESTEASYTOBORROWLIST_REQUEST._serialized_end=211
# @@protoc_insertion_point(module_scope)
