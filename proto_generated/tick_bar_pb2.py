# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tick_bar.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0etick_bar.proto\x12\x03rti\"\x95\x04\n\x07TickBar\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12$\n\x04type\x18\xa0\xa3\x07 \x01(\x0e\x32\x14.rti.TickBar.BarType\x12+\n\x08sub_type\x18\xa8\xa3\x07 \x01(\x0e\x32\x17.rti.TickBar.BarSubType\x12\x18\n\x0etype_specifier\x18\xc2\x85\t \x01(\t\x12\x14\n\nnum_trades\x18\xa4\xa3\x07 \x01(\x04\x12\x10\n\x06volume\x18\xa5\xa3\x07 \x01(\x04\x12\x14\n\nbid_volume\x18\xad\xa3\x07 \x01(\x04\x12\x14\n\nask_volume\x18\xae\xa3\x07 \x01(\x04\x12\x14\n\nopen_price\x18\xb3\x8d\x06 \x01(\x01\x12\x15\n\x0b\x63lose_price\x18\xb5\x8d\x06 \x01(\x01\x12\x14\n\nhigh_price\x18\xac\x8d\x06 \x01(\x01\x12\x13\n\tlow_price\x18\xad\x8d\x06 \x01(\x01\x12!\n\x17\x63ustom_session_open_ssm\x18\xa9\xa3\x07 \x01(\x05\x12\x18\n\x0e\x64\x61ta_bar_ssboe\x18\xa2\xa3\x07 \x03(\x05\x12\x18\n\x0e\x64\x61ta_bar_usecs\x18\xa3\xa3\x07 \x03(\x05\"6\n\x07\x42\x61rType\x12\x0c\n\x08TICK_BAR\x10\x01\x12\r\n\tRANGE_BAR\x10\x02\x12\x0e\n\nVOLUME_BAR\x10\x03\"%\n\nBarSubType\x12\x0b\n\x07REGULAR\x10\x01\x12\n\n\x06\x43USTOM\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tick_bar_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _TICKBAR._serialized_start=24
  _TICKBAR._serialized_end=557
  _TICKBAR_BARTYPE._serialized_start=464
  _TICKBAR_BARTYPE._serialized_end=518
  _TICKBAR_BARSUBTYPE._serialized_start=520
  _TICKBAR_BARSUBTYPE._serialized_end=557
# @@protoc_insertion_point(module_scope)
