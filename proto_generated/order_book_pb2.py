# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: order_book.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10order_book.proto\x12\x03rti\"\xf6\x03\n\tOrderBook\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x30\n\x0bupdate_type\x18\xa8\xcf\t \x01(\x0e\x32\x19.rti.OrderBook.UpdateType\x12\x13\n\tbid_price\x18\xaa\xb5\t \x03(\x01\x12\x12\n\x08\x62id_size\x18\xab\xb5\t \x03(\x05\x12\x14\n\nbid_orders\x18\xa1\xb6\t \x03(\x05\x12\x17\n\rimpl_bid_size\x18\xac\xb6\t \x03(\x05\x12\x13\n\task_price\x18\xac\xb5\t \x03(\x01\x12\x12\n\x08\x61sk_size\x18\xad\xb5\t \x03(\x05\x12\x14\n\nask_orders\x18\xa2\xb6\t \x03(\x05\x12\x17\n\rimpl_ask_size\x18\xaf\xb6\t \x03(\x05\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\" \n\x0cPresenceBits\x12\x07\n\x03\x42ID\x10\x01\x12\x07\n\x03\x41SK\x10\x02\"m\n\nUpdateType\x12\x14\n\x10\x43LEAR_ORDER_BOOK\x10\x01\x12\x0b\n\x07NO_BOOK\x10\x02\x12\x12\n\x0eSNAPSHOT_IMAGE\x10\x03\x12\t\n\x05\x42\x45GIN\x10\x04\x12\n\n\x06MIDDLE\x10\x05\x12\x07\n\x03\x45ND\x10\x06\x12\x08\n\x04SOLO\x10\x07')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'order_book_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _ORDERBOOK._serialized_start=26
  _ORDERBOOK._serialized_end=528
  _ORDERBOOK_PRESENCEBITS._serialized_start=385
  _ORDERBOOK_PRESENCEBITS._serialized_end=417
  _ORDERBOOK_UPDATETYPE._serialized_start=419
  _ORDERBOOK_UPDATETYPE._serialized_end=528
# @@protoc_insertion_point(module_scope)
