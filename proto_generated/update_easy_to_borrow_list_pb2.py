# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: update_easy_to_borrow_list.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n update_easy_to_borrow_list.proto\x12\x03rti\"\xb6\x01\n\x16UpdateEasyToBorrowList\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x17\n\rbroker_dealer\x18\xf4\xb7\t \x01(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x15\n\x0bsymbol_name\x18\xa3\x8d\x06 \x01(\t\x12\x17\n\rqty_available\x18\xf5\xb7\t \x01(\x05\x12\x14\n\nqty_needed\x18\xf6\xb7\t \x01(\x05\x12\x14\n\nborrowable\x18\x91\xde\x06 \x01(\x08')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'update_easy_to_borrow_list_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _UPDATEEASYTOBORROWLIST._serialized_start=42
  _UPDATEEASYTOBORROWLIST._serialized_end=224
# @@protoc_insertion_point(module_scope)
