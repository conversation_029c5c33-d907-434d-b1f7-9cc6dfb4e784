# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: depth_by_order_end_event.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1e\x64\x65pth_by_order_end_event.proto\x12\x03rti\"\x90\x01\n\x14\x44\x65pthByOrderEndEvent\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x03(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x03(\t\x12\x19\n\x0fsequence_number\x18\x82\xeb\x06 \x01(\x04\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'depth_by_order_end_event_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _DEPTHBYORDERENDEVENT._serialized_start=40
  _DEPTHBYORDERENDEVENT._serialized_end=184
# @@protoc_insertion_point(module_scope)
