# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_account_rms_info.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1erequest_account_rms_info.proto\x12\x03rti\"\xe6\x01\n\x15RequestAccountRmsInfo\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x38\n\tuser_type\x18\xb4\xb3\t \x01(\x0e\x32#.rti.RequestAccountRmsInfo.UserType\"E\n\x08UserType\x12\x11\n\rUSER_TYPE_FCM\x10\x01\x12\x10\n\x0cUSER_TYPE_IB\x10\x02\x12\x14\n\x10USER_TYPE_TRADER\x10\x03')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_account_rms_info_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTACCOUNTRMSINFO._serialized_start=40
  _REQUESTACCOUNTRMSINFO._serialized_end=270
  _REQUESTACCOUNTRMSINFO_USERTYPE._serialized_start=201
  _REQUESTACCOUNTRMSINFO_USERTYPE._serialized_end=270
# @@protoc_insertion_point(module_scope)
