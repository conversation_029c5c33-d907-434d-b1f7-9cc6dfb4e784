# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_volume_profile_minute_bars.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)response_volume_profile_minute_bars.proto\x12\x03rti\"\xfd\x04\n\x1fResponseVolumeProfileMinuteBars\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x15\n\x0brequest_key\x18\x96\x8d\x08 \x01(\t\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x10\n\x06period\x18\xaf\xa3\x07 \x01(\t\x12\x10\n\x06marker\x18\xbc\xa2\x07 \x01(\x05\x12\x14\n\nnum_trades\x18\xa4\xa3\x07 \x01(\x04\x12\x10\n\x06volume\x18\xa5\xa3\x07 \x01(\x04\x12\x14\n\nbid_volume\x18\xad\xa3\x07 \x01(\x04\x12\x14\n\nask_volume\x18\xae\xa3\x07 \x01(\x04\x12\x14\n\nopen_price\x18\xb3\x8d\x06 \x01(\x01\x12\x15\n\x0b\x63lose_price\x18\xb5\x8d\x06 \x01(\x01\x12\x14\n\nhigh_price\x18\xac\x8d\x06 \x01(\x01\x12\x13\n\tlow_price\x18\xad\x8d\x06 \x01(\x01\x12\x17\n\rprofile_price\x18\xb0\xa3\x07 \x03(\x01\x12%\n\x1bprofile_no_aggressor_volume\x18\xb1\xa3\x07 \x03(\x05\x12\x1c\n\x12profile_bid_volume\x18\xb2\xa3\x07 \x03(\x05\x12\x1c\n\x12profile_ask_volume\x18\xb3\xa3\x07 \x03(\x05\x12%\n\x1bprofile_no_aggressor_trades\x18\xb4\xa3\x07 \x03(\x05\x12&\n\x1cprofile_bid_aggressor_trades\x18\xb5\xa3\x07 \x03(\x05\x12&\n\x1cprofile_ask_aggressor_trades\x18\xb6\xa3\x07 \x03(\x05')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_volume_profile_minute_bars_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSEVOLUMEPROFILEMINUTEBARS._serialized_start=51
  _RESPONSEVOLUMEPROFILEMINUTEBARS._serialized_end=688
# @@protoc_insertion_point(module_scope)
