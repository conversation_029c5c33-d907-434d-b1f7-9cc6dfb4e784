# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_market_data_update.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n request_market_data_update.proto\x12\x03rti\"\xaf\x04\n\x17RequestMarketDataUpdate\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x37\n\x07request\x18\xa0\x8d\x06 \x01(\x0e\x32$.rti.RequestMarketDataUpdate.Request\x12\x15\n\x0bupdate_bits\x18\xe3\xb4\t \x01(\r\"\xc7\x02\n\nUpdateBits\x12\x0e\n\nLAST_TRADE\x10\x01\x12\x07\n\x03\x42\x42O\x10\x02\x12\x0e\n\nORDER_BOOK\x10\x04\x12\x08\n\x04OPEN\x10\x08\x12\x15\n\x11OPENING_INDICATOR\x10\x10\x12\x0c\n\x08HIGH_LOW\x10 \x12\x14\n\x10HIGH_BID_LOW_ASK\x10@\x12\n\n\x05\x43LOSE\x10\x80\x01\x12\x16\n\x11\x43LOSING_INDICATOR\x10\x80\x02\x12\x0f\n\nSETTLEMENT\x10\x80\x04\x12\x10\n\x0bMARKET_MODE\x10\x80\x08\x12\x12\n\rOPEN_INTEREST\x10\x80\x10\x12\x10\n\x0bMARGIN_RATE\x10\x80 \x12\x15\n\x10HIGH_PRICE_LIMIT\x10\x80@\x12\x15\n\x0fLOW_PRICE_LIMIT\x10\x80\x80\x01\x12\x1a\n\x14PROJECTED_SETTLEMENT\x10\x80\x80\x02\x12\x14\n\x0e\x41\x44JUSTED_CLOSE\x10\x80\x80\x04\")\n\x07Request\x12\r\n\tSUBSCRIBE\x10\x01\x12\x0f\n\x0bUNSUBSCRIBE\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_market_data_update_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTMARKETDATAUPDATE._serialized_start=42
  _REQUESTMARKETDATAUPDATE._serialized_end=601
  _REQUESTMARKETDATAUPDATE_UPDATEBITS._serialized_start=231
  _REQUESTMARKETDATAUPDATE_UPDATEBITS._serialized_end=558
  _REQUESTMARKETDATAUPDATE_REQUEST._serialized_start=560
  _REQUESTMARKETDATAUPDATE_REQUEST._serialized_end=601
# @@protoc_insertion_point(module_scope)
