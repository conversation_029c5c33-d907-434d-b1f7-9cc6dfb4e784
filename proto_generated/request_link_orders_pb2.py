# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_link_orders.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19request_link_orders.proto\x12\x03rti\"\x8c\x01\n\x11RequestLinkOrders\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x03(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x03(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x03(\t\x12\x13\n\tbasket_id\x18\xdc\xdd\x06 \x03(\t')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_link_orders_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTLINKORDERS._serialized_start=35
  _REQUESTLINKORDERS._serialized_end=175
# @@protoc_insertion_point(module_scope)
