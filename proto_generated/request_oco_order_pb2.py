# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_oco_order.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17request_oco_order.proto\x12\x03rti\"\xeb\x06\n\x0fRequestOCOOrder\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x12\n\x08user_tag\x18\x87\xb4\t \x03(\t\x12\x15\n\x0bwindow_name\x18\x85\xb8\t \x03(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x03(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x03(\t\x12\x12\n\x08quantity\x18\x84\xeb\x06 \x03(\x05\x12\x0f\n\x05price\x18\xe2\xdd\x06 \x03(\x01\x12\x17\n\rtrigger_price\x18\xff\x8d\t \x03(\x01\x12@\n\x10transaction_type\x18\x83\xeb\x06 \x03(\x0e\x32$.rti.RequestOCOOrder.TransactionType\x12\x31\n\x08\x64uration\x18\x85\xeb\x06 \x03(\x0e\x32\x1d.rti.RequestOCOOrder.Duration\x12\x34\n\nprice_type\x18\x88\xeb\x06 \x03(\x0e\x32\x1e.rti.RequestOCOOrder.PriceType\x12\x15\n\x0btrade_route\x18\x90\xeb\x06 \x03(\t\x12=\n\x0emanual_or_auto\x18\xd6\xb8\t \x03(\x0e\x32#.rti.RequestOCOOrder.OrderPlacement\x12\x17\n\rtrailing_stop\x18\x87\xcb\t \x03(\x08\x12\x18\n\x0etrail_by_ticks\x18\x88\xcb\t \x03(\x05\x12\x1b\n\x11trail_by_price_id\x18\x89\xcb\t \x03(\x05\x12\x19\n\x0f\x63\x61ncel_at_ssboe\x18\x9d\xcb\t \x01(\x05\x12\x19\n\x0f\x63\x61ncel_at_usecs\x18\x9e\xcb\t \x01(\x05\x12\x1b\n\x11\x63\x61ncel_after_secs\x18\xf8\xb6\t \x01(\x05\"$\n\x0fTransactionType\x12\x07\n\x03\x42UY\x10\x01\x12\x08\n\x04SELL\x10\x02\".\n\x08\x44uration\x12\x07\n\x03\x44\x41Y\x10\x01\x12\x07\n\x03GTC\x10\x02\x12\x07\n\x03IOC\x10\x03\x12\x07\n\x03\x46OK\x10\x04\"C\n\tPriceType\x12\t\n\x05LIMIT\x10\x01\x12\n\n\x06MARKET\x10\x02\x12\x0e\n\nSTOP_LIMIT\x10\x03\x12\x0f\n\x0bSTOP_MARKET\x10\x04\"&\n\x0eOrderPlacement\x12\n\n\x06MANUAL\x10\x01\x12\x08\n\x04\x41UTO\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_oco_order_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTOCOORDER._serialized_start=33
  _REQUESTOCOORDER._serialized_end=908
  _REQUESTOCOORDER_TRANSACTIONTYPE._serialized_start=715
  _REQUESTOCOORDER_TRANSACTIONTYPE._serialized_end=751
  _REQUESTOCOORDER_DURATION._serialized_start=753
  _REQUESTOCOORDER_DURATION._serialized_end=799
  _REQUESTOCOORDER_PRICETYPE._serialized_start=801
  _REQUESTOCOORDER_PRICETYPE._serialized_end=868
  _REQUESTOCOORDER_ORDERPLACEMENT._serialized_start=870
  _REQUESTOCOORDER_ORDERPLACEMENT._serialized_end=908
# @@protoc_insertion_point(module_scope)
