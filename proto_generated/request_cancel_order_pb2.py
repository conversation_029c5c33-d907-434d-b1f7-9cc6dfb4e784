# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_cancel_order.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1arequest_cancel_order.proto\x12\x03rti\"\x8e\x02\n\x12RequestCancelOrder\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x15\n\x0bwindow_name\x18\x85\xb8\t \x01(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x13\n\tbasket_id\x18\xdc\xdd\x06 \x01(\t\x12@\n\x0emanual_or_auto\x18\xd6\xb8\t \x01(\x0e\x32&.rti.RequestCancelOrder.OrderPlacement\"&\n\x0eOrderPlacement\x12\n\n\x06MANUAL\x10\x01\x12\x08\n\x04\x41UTO\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_cancel_order_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTCANCELORDER._serialized_start=36
  _REQUESTCANCELORDER._serialized_end=306
  _REQUESTCANCELORDER_ORDERPLACEMENT._serialized_start=268
  _REQUESTCANCELORDER_ORDERPLACEMENT._serialized_end=306
# @@protoc_insertion_point(module_scope)
