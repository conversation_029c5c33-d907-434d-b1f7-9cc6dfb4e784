# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: user_account_update.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19user_account_update.proto\x12\x03rti\"\x8b\x03\n\x11UserAccountUpdate\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x38\n\x0bupdate_type\x18\xb0\xb5\t \x01(\x0e\x32!.rti.UserAccountUpdate.UpdateType\x12\x38\n\x0b\x61\x63\x63\x65ss_type\x18\x90\xb3\t \x01(\x0e\x32!.rti.UserAccountUpdate.AccessType\x12\x18\n\x0esource_user_id\x18\x87\xb5\t \x01(\t\x12\x0e\n\x04user\x18\xbb\xff\x07 \x01(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x16\n\x0c\x61\x63\x63ount_name\x18\x92\xb3\t \x01(\t\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\"!\n\nUpdateType\x12\x07\n\x03\x41\x44\x44\x10\x01\x12\n\n\x06REMOVE\x10\x02\"+\n\nAccessType\x12\r\n\tREAD_ONLY\x10\x00\x12\x0e\n\nREAD_WRITE\x10\x01')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'user_account_update_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _USERACCOUNTUPDATE._serialized_start=35
  _USERACCOUNTUPDATE._serialized_end=430
  _USERACCOUNTUPDATE_UPDATETYPE._serialized_start=352
  _USERACCOUNTUPDATE_UPDATETYPE._serialized_end=385
  _USERACCOUNTUPDATE_ACCESSTYPE._serialized_start=387
  _USERACCOUNTUPDATE_ACCESSTYPE._serialized_end=430
# @@protoc_insertion_point(module_scope)
