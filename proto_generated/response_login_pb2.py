# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_login.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14response_login.proto\x12\x03rti\"\xf2\x01\n\rResponseLogin\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x1a\n\x10template_version\x18\xa2\xb0\t \x01(\t\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x16\n\x0c\x63ountry_code\x18\xd8\xb8\t \x01(\t\x12\x14\n\nstate_code\x18\xd9\xb8\t \x01(\t\x12\x18\n\x0eunique_user_id\x18\xd4\xae\t \x01(\t\x12\x1c\n\x12heartbeat_interval\x18\xa1\xb0\t \x01(\x01')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_login_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSELOGIN._serialized_start=30
  _RESPONSELOGIN._serialized_end=272
# @@protoc_insertion_point(module_scope)
