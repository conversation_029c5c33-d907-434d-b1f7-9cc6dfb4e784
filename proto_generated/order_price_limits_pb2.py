# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: order_price_limits.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18order_price_limits.proto\x12\x03rti\"\xa9\x02\n\x10OrderPriceLimits\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x14\n\nclear_bits\x18\xcb\xb7\t \x01(\r\x12\x15\n\x0bis_snapshot\x18\xa9\xdc\x06 \x01(\x08\x12\x1a\n\x10high_price_limit\x18\xdf\xb3\t \x01(\x01\x12\x19\n\x0flow_price_limit\x18\xf5\xb3\t \x01(\x01\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\"9\n\x0cPresenceBits\x12\x14\n\x10HIGH_PRICE_LIMIT\x10\x01\x12\x13\n\x0fLOW_PRICE_LIMIT\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'order_price_limits_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _ORDERPRICELIMITS._serialized_start=34
  _ORDERPRICELIMITS._serialized_end=331
  _ORDERPRICELIMITS_PRESENCEBITS._serialized_start=274
  _ORDERPRICELIMITS_PRESENCEBITS._serialized_end=331
# @@protoc_insertion_point(module_scope)
