# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_search_symbols.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1crequest_search_symbols.proto\x12\x03rti\"\xd7\x03\n\x14RequestSearchSymbols\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x15\n\x0bsearch_text\x18\xc8\xa9\x07 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x16\n\x0cproduct_code\x18\x8d\x93\x06 \x01(\t\x12\x43\n\x0finstrument_type\x18\xa4\xdc\x06 \x01(\x0e\x32(.rti.RequestSearchSymbols.InstrumentType\x12\x34\n\x07pattern\x18\x80\xbb\t \x01(\x0e\x32!.rti.RequestSearchSymbols.Pattern\"#\n\x07Pattern\x12\n\n\x06\x45QUALS\x10\x01\x12\x0c\n\x08\x43ONTAINS\x10\x02\"\xb0\x01\n\x0eInstrumentType\x12\n\n\x06\x46UTURE\x10\x01\x12\x11\n\rFUTURE_OPTION\x10\x02\x12\x13\n\x0f\x46UTURE_STRATEGY\x10\x03\x12\n\n\x06\x45QUITY\x10\x04\x12\x11\n\rEQUITY_OPTION\x10\x05\x12\x13\n\x0f\x45QUITY_STRATEGY\x10\x06\x12\t\n\x05INDEX\x10\x07\x12\x10\n\x0cINDEX_OPTION\x10\x08\x12\n\n\x06SPREAD\x10\t\x12\r\n\tSYNTHETIC\x10\n')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_search_symbols_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTSEARCHSYMBOLS._serialized_start=38
  _REQUESTSEARCHSYMBOLS._serialized_end=509
  _REQUESTSEARCHSYMBOLS_PATTERN._serialized_start=295
  _REQUESTSEARCHSYMBOLS_PATTERN._serialized_end=330
  _REQUESTSEARCHSYMBOLS_INSTRUMENTTYPE._serialized_start=333
  _REQUESTSEARCHSYMBOLS_INSTRUMENTTYPE._serialized_end=509
# @@protoc_insertion_point(module_scope)
