# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_volume_profile_minute_bars.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(request_volume_profile_minute_bars.proto\x12\x03rti\"\xec\x01\n\x1eRequestVolumeProfileMinuteBars\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x19\n\x0f\x62\x61r_type_period\x18\xaf\xa3\x07 \x01(\x05\x12\x15\n\x0bstart_index\x18\xaa\xab\t \x01(\x05\x12\x16\n\x0c\x66inish_index\x18\xab\xab\t \x01(\x05\x12\x18\n\x0euser_max_count\x18\xa4\xb3\t \x01(\x05\x12\x15\n\x0bresume_bars\x18\xaa\xb0\t \x01(\x08')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_volume_profile_minute_bars_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTVOLUMEPROFILEMINUTEBARS._serialized_start=50
  _REQUESTVOLUMEPROFILEMINUTEBARS._serialized_end=286
# @@protoc_insertion_point(module_scope)
