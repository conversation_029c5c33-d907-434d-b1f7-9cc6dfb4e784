# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: quote_statistics.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16quote_statistics.proto\x12\x03rti\"\xa0\x02\n\x0fQuoteStatistics\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x14\n\nclear_bits\x18\xcb\xb7\t \x01(\r\x12\x15\n\x0bis_snapshot\x18\xa9\xdc\x06 \x01(\x08\x12\x1b\n\x11highest_bid_price\x18\xd3\xb4\t \x01(\x01\x12\x1a\n\x10lowest_ask_price\x18\xd5\xb4\t \x01(\x01\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\"/\n\x0cPresenceBits\x12\x0f\n\x0bHIGHEST_BID\x10\x01\x12\x0e\n\nLOWEST_ASK\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'quote_statistics_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _QUOTESTATISTICS._serialized_start=32
  _QUOTESTATISTICS._serialized_end=320
  _QUOTESTATISTICS_PRESENCEBITS._serialized_start=273
  _QUOTESTATISTICS_PRESENCEBITS._serialized_end=320
# @@protoc_insertion_point(module_scope)
