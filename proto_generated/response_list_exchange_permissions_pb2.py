# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_list_exchange_permissions.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(response_list_exchange_permissions.proto\x12\x03rti\"\x91\x02\n\x1fResponseListExchangePermissions\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12P\n\x10\x65ntitlement_flag\x18\xb8\xae\t \x01(\x0e\x32\x34.rti.ResponseListExchangePermissions.EntitlementFlag\",\n\x0f\x45ntitlementFlag\x12\x0b\n\x07\x45NABLED\x10\x01\x12\x0c\n\x08\x44ISABLED\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_list_exchange_permissions_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSELISTEXCHANGEPERMISSIONS._serialized_start=50
  _RESPONSELISTEXCHANGEPERMISSIONS._serialized_end=323
  _RESPONSELISTEXCHANGEPERMISSIONS_ENTITLEMENTFLAG._serialized_start=279
  _RESPONSELISTEXCHANGEPERMISSIONS_ENTITLEMENTFLAG._serialized_end=323
# @@protoc_insertion_point(module_scope)
