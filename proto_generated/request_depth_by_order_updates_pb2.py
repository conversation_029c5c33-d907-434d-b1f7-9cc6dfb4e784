# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_depth_by_order_updates.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$request_depth_by_order_updates.proto\x12\x03rti\"\xeb\x01\n\x1aRequestDepthByOrderUpdates\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12:\n\x07request\x18\xa0\x8d\x06 \x01(\x0e\x32\'.rti.RequestDepthByOrderUpdates.Request\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x15\n\x0b\x64\x65pth_price\x18\xa5\xb6\t \x01(\x01\")\n\x07Request\x12\r\n\tSUBSCRIBE\x10\x01\x12\x0f\n\x0bUNSUBSCRIBE\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_depth_by_order_updates_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTDEPTHBYORDERUPDATES._serialized_start=46
  _REQUESTDEPTHBYORDERUPDATES._serialized_end=281
  _REQUESTDEPTHBYORDERUPDATES_REQUEST._serialized_start=240
  _REQUESTDEPTHBYORDERUPDATES_REQUEST._serialized_end=281
# @@protoc_insertion_point(module_scope)
