# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: account_rms_updates.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19\x61\x63\x63ount_rms_updates.proto\x12\x03rti\"\xb2\x02\n\x11\x41\x63\x63ountRmsUpdates\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x15\n\x0bupdate_bits\x18\xe3\xb4\t \x01(\x05\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12*\n auto_liq_threshold_current_value\x18\xe0\xff\x07 \x01(\t\x12\'\n\x1d\x61uto_liq_peak_account_balance\x18\xe9\xff\x07 \x01(\t\x12-\n#auto_liq_peak_account_balance_ssboe\x18\xea\xff\x07 \x01(\t\"2\n\nUpdateBits\x12$\n AUTO_LIQ_THRESHOLD_CURRENT_VALUE\x10\x01')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'account_rms_updates_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _ACCOUNTRMSUPDATES._serialized_start=35
  _ACCOUNTRMSUPDATES._serialized_end=341
  _ACCOUNTRMSUPDATES_UPDATEBITS._serialized_start=291
  _ACCOUNTRMSUPDATES_UPDATEBITS._serialized_end=341
# @@protoc_insertion_point(module_scope)
