# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_exit_position.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1brequest_exit_position.proto\x12\x03rti\"\xbe\x02\n\x13RequestExitPosition\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x15\n\x0bwindow_name\x18\x85\xb8\t \x01(\t\x12\x10\n\x06\x66\x63m_id\x18\x9d\xb3\t \x01(\t\x12\x0f\n\x05ib_id\x18\x9e\xb3\t \x01(\t\x12\x14\n\naccount_id\x18\x98\xb3\t \x01(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x1b\n\x11trading_algorithm\x18\xca\xb8\t \x01(\t\x12\x41\n\x0emanual_or_auto\x18\xd6\xb8\t \x01(\x0e\x32\'.rti.RequestExitPosition.OrderPlacement\"&\n\x0eOrderPlacement\x12\n\n\x06MANUAL\x10\x01\x12\x08\n\x04\x41UTO\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_exit_position_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTEXITPOSITION._serialized_start=37
  _REQUESTEXITPOSITION._serialized_end=355
  _REQUESTEXITPOSITION_ORDERPLACEMENT._serialized_start=317
  _REQUESTEXITPOSITION_ORDERPLACEMENT._serialized_end=355
# @@protoc_insertion_point(module_scope)
