# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: end_of_day_prices.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x65nd_of_day_prices.proto\x12\x03rti\"\xd9\x03\n\x0e\x45ndOfDayPrices\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x14\n\nclear_bits\x18\xcb\xb7\t \x01(\r\x12\x15\n\x0bis_snapshot\x18\xa9\xdc\x06 \x01(\x08\x12\x15\n\x0b\x63lose_price\x18\xb5\x8d\x06 \x01(\x01\x12\x14\n\nclose_date\x18\xef\x8d\x06 \x01(\t\x12\x1e\n\x14\x61\x64justed_close_price\x18\x8c\xb4\t \x01(\x01\x12\x1a\n\x10settlement_price\x18\xe6\x8d\x06 \x01(\x01\x12\x19\n\x0fsettlement_date\x18\x94\xb4\t \x01(\t\x12\x1f\n\x15settlement_price_type\x18\x8d\xb8\t \x01(\t\x12$\n\x1aprojected_settlement_price\x18\xfd\xba\t \x01(\x01\x12\x0f\n\x05ssboe\x18\xd4\x94\t \x01(\x05\x12\x0f\n\x05usecs\x18\xd5\x94\t \x01(\x05\"W\n\x0cPresenceBits\x12\t\n\x05\x43LOSE\x10\x01\x12\x0e\n\nSETTLEMENT\x10\x02\x12\x18\n\x14PROJECTED_SETTLEMENT\x10\x04\x12\x12\n\x0e\x41\x44JUSTED_CLOSE\x10\x08')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'end_of_day_prices_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _ENDOFDAYPRICES._serialized_start=33
  _ENDOFDAYPRICES._serialized_end=506
  _ENDOFDAYPRICES_PRESENCEBITS._serialized_start=419
  _ENDOFDAYPRICES_PRESENCEBITS._serialized_end=506
# @@protoc_insertion_point(module_scope)
