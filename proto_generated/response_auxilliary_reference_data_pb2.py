# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_auxilliary_reference_data.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(response_auxilliary_reference_data.proto\x12\x03rti\"\x91\x06\n\x1fResponseAuxilliaryReferenceData\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x17\n\rpresence_bits\x18\x92\x8d\t \x01(\r\x12\x14\n\nclear_bits\x18\xcb\xb7\t \x01(\r\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x1b\n\x11settlement_method\x18\xce\xad\t \x01(\t\x12\x1b\n\x11\x66irst_notice_date\x18\xb4\xba\t \x01(\t\x12\x1a\n\x10last_notice_date\x18\xb5\xba\t \x01(\t\x12\x1c\n\x12\x66irst_trading_date\x18\xf4\xba\t \x01(\t\x12\x1b\n\x11last_trading_date\x18\xfc\xb4\t \x01(\t\x12\x1d\n\x13\x66irst_delivery_date\x18\xf2\xba\t \x01(\t\x12\x1c\n\x12last_delivery_date\x18\xf3\xba\t \x01(\t\x12\x1d\n\x13\x66irst_position_date\x18\xf5\xba\t \x01(\t\x12\x1c\n\x12last_position_date\x18\xf6\xba\t \x01(\t\x12\x19\n\x0funit_of_measure\x18\xdf\xca\t \x01(\t\x12\x1d\n\x13unit_of_measure_qty\x18\xe0\xca\t \x01(\x01\"\x95\x02\n\x0cPresenceBits\x12\x15\n\x11SETTLEMENT_METHOD\x10\x01\x12\x15\n\x11\x46IRST_NOTICE_DATE\x10\x02\x12\x14\n\x10LAST_NOTICE_DATE\x10\x04\x12\x16\n\x12\x46IRST_TRADING_DATE\x10\x08\x12\x15\n\x11LAST_TRADING_DATE\x10\x10\x12\x17\n\x13\x46IRST_DELIVERY_DATE\x10 \x12\x16\n\x12LAST_DELIVERY_DATE\x10@\x12\x18\n\x13\x46IRST_POSITION_DATE\x10\x80\x01\x12\x17\n\x12LAST_POSITION_DATE\x10\x80\x02\x12\x14\n\x0fUNIT_OF_MEASURE\x10\x80\x04\x12\x18\n\x13UNIT_OF_MEASURE_QTY\x10\x80\x08')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_auxilliary_reference_data_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSEAUXILLIARYREFERENCEDATA._serialized_start=50
  _RESPONSEAUXILLIARYREFERENCEDATA._serialized_end=835
  _RESPONSEAUXILLIARYREFERENCEDATA_PRESENCEBITS._serialized_start=558
  _RESPONSEAUXILLIARYREFERENCEDATA_PRESENCEBITS._serialized_end=835
# @@protoc_insertion_point(module_scope)
