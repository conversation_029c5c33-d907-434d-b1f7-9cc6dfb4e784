# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: otps_proto_pool.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import request_login_pb2 as request__login__pb2
import response_login_pb2 as response__login__pb2
import request_logout_pb2 as request__logout__pb2
import response_logout_pb2 as response__logout__pb2
import request_reference_data_pb2 as request__reference__data__pb2
import response_reference_data_pb2 as response__reference__data__pb2
import request_rithmic_system_info_pb2 as request__rithmic__system__info__pb2
import response_rithmic_system_info_pb2 as response__rithmic__system__info__pb2
import request_rithmic_system_gateway_info_pb2 as request__rithmic__system__gateway__info__pb2
import response_rithmic_system_gateway_info_pb2 as response__rithmic__system__gateway__info__pb2
import request_heartbeat_pb2 as request__heartbeat__pb2
import response_heartbeat_pb2 as response__heartbeat__pb2
import reject_pb2 as reject__pb2
import forced_logout_pb2 as forced__logout__pb2
import user_account_update_pb2 as user__account__update__pb2
import request_market_data_update_pb2 as request__market__data__update__pb2
import response_market_data_update_pb2 as response__market__data__update__pb2
import request_auxilliary_reference_data_pb2 as request__auxilliary__reference__data__pb2
import response_auxilliary_reference_data_pb2 as response__auxilliary__reference__data__pb2
import request_give_tick_size_type_table_pb2 as request__give__tick__size__type__table__pb2
import response_give_tick_size_type_table_pb2 as response__give__tick__size__type__table__pb2
import request_get_instrument_by_underlying_pb2 as request__get__instrument__by__underlying__pb2
import response_get_instrument_by_underlying_pb2 as response__get__instrument__by__underlying__pb2
import response_get_instrument_by_underlying_keys_pb2 as response__get__instrument__by__underlying__keys__pb2
import request_market_data_update_by_underlying_pb2 as request__market__data__update__by__underlying__pb2
import response_market_data_update_by_underlying_pb2 as response__market__data__update__by__underlying__pb2
import request_search_symbols_pb2 as request__search__symbols__pb2
import response_search_symbols_pb2 as response__search__symbols__pb2
import request_product_codes_pb2 as request__product__codes__pb2
import response_product_codes_pb2 as response__product__codes__pb2
import request_front_month_contract_pb2 as request__front__month__contract__pb2
import response_front_month_contract_pb2 as response__front__month__contract__pb2
import request_depth_by_order_snapshot_pb2 as request__depth__by__order__snapshot__pb2
import response_depth_by_order_snapshot_pb2 as response__depth__by__order__snapshot__pb2
import request_depth_by_order_updates_pb2 as request__depth__by__order__updates__pb2
import response_depth_by_order_updates_pb2 as response__depth__by__order__updates__pb2
import request_get_volume_at_price_pb2 as request__get__volume__at__price__pb2
import response_get_volume_at_price_pb2 as response__get__volume__at__price__pb2
import best_bid_offer_pb2 as best__bid__offer__pb2
import order_book_pb2 as order__book__pb2
import last_trade_pb2 as last__trade__pb2
import trade_statistics_pb2 as trade__statistics__pb2
import quote_statistics_pb2 as quote__statistics__pb2
import indicator_prices_pb2 as indicator__prices__pb2
import open_interest_pb2 as open__interest__pb2
import end_of_day_prices_pb2 as end__of__day__prices__pb2
import market_mode_pb2 as market__mode__pb2
import front_month_contract_update_pb2 as front__month__contract__update__pb2
import depth_by_order_pb2 as depth__by__order__pb2
import depth_by_order_end_event_pb2 as depth__by__order__end__event__pb2
import symbol_margin_rate_pb2 as symbol__margin__rate__pb2
import order_price_limits_pb2 as order__price__limits__pb2
import request_login_info_pb2 as request__login__info__pb2
import response_login_info_pb2 as response__login__info__pb2
import request_account_list_pb2 as request__account__list__pb2
import response_account_list_pb2 as response__account__list__pb2
import request_account_rms_info_pb2 as request__account__rms__info__pb2
import response_account_rms_info_pb2 as response__account__rms__info__pb2
import request_account_rms_updates_pb2 as request__account__rms__updates__pb2
import response_account_rms_updates_pb2 as response__account__rms__updates__pb2
import request_product_rms_info_pb2 as request__product__rms__info__pb2
import response_product_rms_info_pb2 as response__product__rms__info__pb2
import request_subscribe_for_order_updates_pb2 as request__subscribe__for__order__updates__pb2
import response_subscribe_for_order_updates_pb2 as response__subscribe__for__order__updates__pb2
import request_trade_routes_pb2 as request__trade__routes__pb2
import response_trade_routes_pb2 as response__trade__routes__pb2
import request_new_order_pb2 as request__new__order__pb2
import response_new_order_pb2 as response__new__order__pb2
import request_modify_order_pb2 as request__modify__order__pb2
import response_modify_order_pb2 as response__modify__order__pb2
import request_modify_order_reference_data_pb2 as request__modify__order__reference__data__pb2
import response_modify_order_reference_data_pb2 as response__modify__order__reference__data__pb2
import request_cancel_order_pb2 as request__cancel__order__pb2
import response_cancel_order_pb2 as response__cancel__order__pb2
import request_cancel_all_orders_pb2 as request__cancel__all__orders__pb2
import response_cancel_all_orders_pb2 as response__cancel__all__orders__pb2
import request_show_orders_pb2 as request__show__orders__pb2
import response_show_orders_pb2 as response__show__orders__pb2
import request_show_order_history_pb2 as request__show__order__history__pb2
import response_show_order_history_pb2 as response__show__order__history__pb2
import request_show_order_history_summary_pb2 as request__show__order__history__summary__pb2
import response_show_order_history_summary_pb2 as response__show__order__history__summary__pb2
import request_show_order_history_detail_pb2 as request__show__order__history__detail__pb2
import response_show_order_history_detail_pb2 as response__show__order__history__detail__pb2
import request_show_order_history_dates_pb2 as request__show__order__history__dates__pb2
import response_show_order_history_dates_pb2 as response__show__order__history__dates__pb2
import request_oco_order_pb2 as request__oco__order__pb2
import response_oco_order_pb2 as response__oco__order__pb2
import request_bracket_order_pb2 as request__bracket__order__pb2
import response_bracket_order_pb2 as response__bracket__order__pb2
import request_show_brackets_pb2 as request__show__brackets__pb2
import response_show_brackets_pb2 as response__show__brackets__pb2
import request_show_bracket_stops_pb2 as request__show__bracket__stops__pb2
import response_show_bracket_stops_pb2 as response__show__bracket__stops__pb2
import request_update_target_bracket_level_pb2 as request__update__target__bracket__level__pb2
import response_update_target_bracket_level_pb2 as response__update__target__bracket__level__pb2
import request_update_stop_bracket_level_pb2 as request__update__stop__bracket__level__pb2
import response_update_stop_bracket_level_pb2 as response__update__stop__bracket__level__pb2
import request_subscribe_to_bracket_updates_pb2 as request__subscribe__to__bracket__updates__pb2
import response_subscribe_to_bracket_updates_pb2 as response__subscribe__to__bracket__updates__pb2
import request_list_exchange_permissions_pb2 as request__list__exchange__permissions__pb2
import response_list_exchange_permissions_pb2 as response__list__exchange__permissions__pb2
import request_link_orders_pb2 as request__link__orders__pb2
import response_link_orders_pb2 as response__link__orders__pb2
import request_easy_to_borrow_list_pb2 as request__easy__to__borrow__list__pb2
import response_easy_to_borrow_list_pb2 as response__easy__to__borrow__list__pb2
import request_order_session_config_pb2 as request__order__session__config__pb2
import response_order_session_config_pb2 as response__order__session__config__pb2
import request_exit_position_pb2 as request__exit__position__pb2
import response_exit_position_pb2 as response__exit__position__pb2
import request_replay_executions_pb2 as request__replay__executions__pb2
import response_replay_executions_pb2 as response__replay__executions__pb2
import trade_route_pb2 as trade__route__pb2
import bracket_updates_pb2 as bracket__updates__pb2
import rithmic_order_notification_pb2 as rithmic__order__notification__pb2
import exchange_order_notification_pb2 as exchange__order__notification__pb2
import account_list_updates_pb2 as account__list__updates__pb2
import update_easy_to_borrow_list_pb2 as update__easy__to__borrow__list__pb2
import account_rms_updates_pb2 as account__rms__updates__pb2
import request_pnl_position_updates_pb2 as request__pnl__position__updates__pb2
import response_pnl_position_updates_pb2 as response__pnl__position__updates__pb2
import request_pnl_position_snapshot_pb2 as request__pnl__position__snapshot__pb2
import response_pnl_position_snapshot_pb2 as response__pnl__position__snapshot__pb2
import account_pnl_position_update_pb2 as account__pnl__position__update__pb2
import instrument_pnl_position_update_pb2 as instrument__pnl__position__update__pb2
import request_tick_bar_replay_pb2 as request__tick__bar__replay__pb2
import response_tick_bar_replay_pb2 as response__tick__bar__replay__pb2
import request_tick_bar_update_pb2 as request__tick__bar__update__pb2
import response_tick_bar_update_pb2 as response__tick__bar__update__pb2
import request_time_bar_replay_pb2 as request__time__bar__replay__pb2
import response_time_bar_replay_pb2 as response__time__bar__replay__pb2
import request_time_bar_update_pb2 as request__time__bar__update__pb2
import response_time_bar_update_pb2 as response__time__bar__update__pb2
import request_volume_profile_minute_bars_pb2 as request__volume__profile__minute__bars__pb2
import response_volume_profile_minute_bars_pb2 as response__volume__profile__minute__bars__pb2
import request_resume_bars_pb2 as request__resume__bars__pb2
import response_resume_bars_pb2 as response__resume__bars__pb2
import tick_bar_pb2 as tick__bar__pb2
import time_bar_pb2 as time__bar__pb2
import request_list_unaccepted_agreements_pb2 as request__list__unaccepted__agreements__pb2
import response_list_unaccepted_agreements_pb2 as response__list__unaccepted__agreements__pb2
import request_list_accepted_agreements_pb2 as request__list__accepted__agreements__pb2
import response_list_accepted_agreements_pb2 as response__list__accepted__agreements__pb2
import request_accept_agreement_pb2 as request__accept__agreement__pb2
import response_accept_agreement_pb2 as response__accept__agreement__pb2
import request_set_rithmic_mrkt_data_self_cert_status_pb2 as request__set__rithmic__mrkt__data__self__cert__status__pb2
import response_set_rithmic_mrkt_data_self_cert_status_pb2 as response__set__rithmic__mrkt__data__self__cert__status__pb2
import request_show_agreement_pb2 as request__show__agreement__pb2
import response_show_agreement_pb2 as response__show__agreement__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15otps_proto_pool.proto\x1a\x13request_login.proto\x1a\x14response_login.proto\x1a\x14request_logout.proto\x1a\x15response_logout.proto\x1a\x1crequest_reference_data.proto\x1a\x1dresponse_reference_data.proto\x1a!request_rithmic_system_info.proto\x1a\"response_rithmic_system_info.proto\x1a)request_rithmic_system_gateway_info.proto\x1a*response_rithmic_system_gateway_info.proto\x1a\x17request_heartbeat.proto\x1a\x18response_heartbeat.proto\x1a\x0creject.proto\x1a\x13\x66orced_logout.proto\x1a\x19user_account_update.proto\x1a request_market_data_update.proto\x1a!response_market_data_update.proto\x1a\'request_auxilliary_reference_data.proto\x1a(response_auxilliary_reference_data.proto\x1a\'request_give_tick_size_type_table.proto\x1a(response_give_tick_size_type_table.proto\x1a*request_get_instrument_by_underlying.proto\x1a+response_get_instrument_by_underlying.proto\x1a\x30response_get_instrument_by_underlying_keys.proto\x1a.request_market_data_update_by_underlying.proto\x1a/response_market_data_update_by_underlying.proto\x1a\x1crequest_search_symbols.proto\x1a\x1dresponse_search_symbols.proto\x1a\x1brequest_product_codes.proto\x1a\x1cresponse_product_codes.proto\x1a\"request_front_month_contract.proto\x1a#response_front_month_contract.proto\x1a%request_depth_by_order_snapshot.proto\x1a&response_depth_by_order_snapshot.proto\x1a$request_depth_by_order_updates.proto\x1a%response_depth_by_order_updates.proto\x1a!request_get_volume_at_price.proto\x1a\"response_get_volume_at_price.proto\x1a\x14\x62\x65st_bid_offer.proto\x1a\x10order_book.proto\x1a\x10last_trade.proto\x1a\x16trade_statistics.proto\x1a\x16quote_statistics.proto\x1a\x16indicator_prices.proto\x1a\x13open_interest.proto\x1a\x17\x65nd_of_day_prices.proto\x1a\x11market_mode.proto\x1a!front_month_contract_update.proto\x1a\x14\x64\x65pth_by_order.proto\x1a\x1e\x64\x65pth_by_order_end_event.proto\x1a\x18symbol_margin_rate.proto\x1a\x18order_price_limits.proto\x1a\x18request_login_info.proto\x1a\x19response_login_info.proto\x1a\x1arequest_account_list.proto\x1a\x1bresponse_account_list.proto\x1a\x1erequest_account_rms_info.proto\x1a\x1fresponse_account_rms_info.proto\x1a!request_account_rms_updates.proto\x1a\"response_account_rms_updates.proto\x1a\x1erequest_product_rms_info.proto\x1a\x1fresponse_product_rms_info.proto\x1a)request_subscribe_for_order_updates.proto\x1a*response_subscribe_for_order_updates.proto\x1a\x1arequest_trade_routes.proto\x1a\x1bresponse_trade_routes.proto\x1a\x17request_new_order.proto\x1a\x18response_new_order.proto\x1a\x1arequest_modify_order.proto\x1a\x1bresponse_modify_order.proto\x1a)request_modify_order_reference_data.proto\x1a*response_modify_order_reference_data.proto\x1a\x1arequest_cancel_order.proto\x1a\x1bresponse_cancel_order.proto\x1a\x1frequest_cancel_all_orders.proto\x1a response_cancel_all_orders.proto\x1a\x19request_show_orders.proto\x1a\x1aresponse_show_orders.proto\x1a request_show_order_history.proto\x1a!response_show_order_history.proto\x1a(request_show_order_history_summary.proto\x1a)response_show_order_history_summary.proto\x1a\'request_show_order_history_detail.proto\x1a(response_show_order_history_detail.proto\x1a&request_show_order_history_dates.proto\x1a\'response_show_order_history_dates.proto\x1a\x17request_oco_order.proto\x1a\x18response_oco_order.proto\x1a\x1brequest_bracket_order.proto\x1a\x1cresponse_bracket_order.proto\x1a\x1brequest_show_brackets.proto\x1a\x1cresponse_show_brackets.proto\x1a request_show_bracket_stops.proto\x1a!response_show_bracket_stops.proto\x1a)request_update_target_bracket_level.proto\x1a*response_update_target_bracket_level.proto\x1a\'request_update_stop_bracket_level.proto\x1a(response_update_stop_bracket_level.proto\x1a*request_subscribe_to_bracket_updates.proto\x1a+response_subscribe_to_bracket_updates.proto\x1a\'request_list_exchange_permissions.proto\x1a(response_list_exchange_permissions.proto\x1a\x19request_link_orders.proto\x1a\x1aresponse_link_orders.proto\x1a!request_easy_to_borrow_list.proto\x1a\"response_easy_to_borrow_list.proto\x1a\"request_order_session_config.proto\x1a#response_order_session_config.proto\x1a\x1brequest_exit_position.proto\x1a\x1cresponse_exit_position.proto\x1a\x1frequest_replay_executions.proto\x1a response_replay_executions.proto\x1a\x11trade_route.proto\x1a\x15\x62racket_updates.proto\x1a rithmic_order_notification.proto\x1a!exchange_order_notification.proto\x1a\x1a\x61\x63\x63ount_list_updates.proto\x1a update_easy_to_borrow_list.proto\x1a\x19\x61\x63\x63ount_rms_updates.proto\x1a\"request_pnl_position_updates.proto\x1a#response_pnl_position_updates.proto\x1a#request_pnl_position_snapshot.proto\x1a$response_pnl_position_snapshot.proto\x1a!account_pnl_position_update.proto\x1a$instrument_pnl_position_update.proto\x1a\x1drequest_tick_bar_replay.proto\x1a\x1eresponse_tick_bar_replay.proto\x1a\x1drequest_tick_bar_update.proto\x1a\x1eresponse_tick_bar_update.proto\x1a\x1drequest_time_bar_replay.proto\x1a\x1eresponse_time_bar_replay.proto\x1a\x1drequest_time_bar_update.proto\x1a\x1eresponse_time_bar_update.proto\x1a(request_volume_profile_minute_bars.proto\x1a)response_volume_profile_minute_bars.proto\x1a\x19request_resume_bars.proto\x1a\x1aresponse_resume_bars.proto\x1a\x0etick_bar.proto\x1a\x0etime_bar.proto\x1a(request_list_unaccepted_agreements.proto\x1a)response_list_unaccepted_agreements.proto\x1a&request_list_accepted_agreements.proto\x1a\'response_list_accepted_agreements.proto\x1a\x1erequest_accept_agreement.proto\x1a\x1fresponse_accept_agreement.proto\x1a\x34request_set_rithmic_mrkt_data_self_cert_status.proto\x1a\x35response_set_rithmic_mrkt_data_self_cert_status.proto\x1a\x1crequest_show_agreement.proto\x1a\x1dresponse_show_agreement.proto')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'otps_proto_pool_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
