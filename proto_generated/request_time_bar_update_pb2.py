# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: request_time_bar_update.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1drequest_time_bar_update.proto\x12\x03rti\"\xe4\x02\n\x14RequestTimeBarUpdate\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x10\n\x06symbol\x18\x94\xdc\x06 \x01(\t\x12\x12\n\x08\x65xchange\x18\x95\xdc\x06 \x01(\t\x12\x34\n\x07request\x18\xa0\x8d\x06 \x01(\x0e\x32!.rti.RequestTimeBarUpdate.Request\x12\x35\n\x08\x62\x61r_type\x18\xa0\xa3\x07 \x01(\x0e\x32!.rti.RequestTimeBarUpdate.BarType\x12\x19\n\x0f\x62\x61r_type_period\x18\xc8\xa2\x07 \x01(\x05\"H\n\x07\x42\x61rType\x12\x0e\n\nSECOND_BAR\x10\x01\x12\x0e\n\nMINUTE_BAR\x10\x02\x12\r\n\tDAILY_BAR\x10\x03\x12\x0e\n\nWEEKLY_BAR\x10\x04\")\n\x07Request\x12\r\n\tSUBSCRIBE\x10\x01\x12\x0f\n\x0bUNSUBSCRIBE\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'request_time_bar_update_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REQUESTTIMEBARUPDATE._serialized_start=39
  _REQUESTTIMEBARUPDATE._serialized_end=395
  _REQUESTTIMEBARUPDATE_BARTYPE._serialized_start=280
  _REQUESTTIMEBARUPDATE_BARTYPE._serialized_end=352
  _REQUESTTIMEBARUPDATE_REQUEST._serialized_start=354
  _REQUESTTIMEBARUPDATE_REQUEST._serialized_end=395
# @@protoc_insertion_point(module_scope)
