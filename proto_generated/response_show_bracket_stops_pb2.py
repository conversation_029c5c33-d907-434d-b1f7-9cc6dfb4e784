# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: response_show_bracket_stops.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!response_show_bracket_stops.proto\x12\x03rti\"\xa8\x02\n\x18ResponseShowBracketStops\x12\x15\n\x0btemplate_id\x18\xe3\xb6\t \x02(\x05\x12\x12\n\x08user_msg\x18\x98\x8d\x08 \x03(\t\x12\x1c\n\x12rq_handler_rp_code\x18\x9c\x8d\x08 \x03(\t\x12\x11\n\x07rp_code\x18\x9e\x8d\x08 \x03(\t\x12\x13\n\tbasket_id\x18\xdc\xdd\x06 \x01(\t\x12\x17\n\rstop_quantity\x18\xdb\xb6\t \x01(\t\x12 \n\x16stop_quantity_released\x18\xe2\xb6\t \x01(\t\x12\x14\n\nstop_ticks\x18\xda\xb6\t \x01(\t\x12#\n\x19\x62racket_trailing_field_id\x18\x86\xcb\t \x01(\t\x12%\n\x1btrailing_stop_trigger_ticks\x18\xc4\xcb\t \x01(\t')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'response_show_bracket_stops_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSESHOWBRACKETSTOPS._serialized_start=43
  _RESPONSESHOWBRACKETSTOPS._serialized_end=339
# @@protoc_insertion_point(module_scope)
