#!/usr/bin/env python3
"""
Basic Connectivity Test for ES Futures Data Collection

Simple test to validate core connectivity and system readiness.
Tests the most critical components needed for automated data collection.

Usage:
    python tests/test_basic_connectivity.py
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime, timezone
from typing import Dict, Any
import json

# Add project paths
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'simple-demos'))
sys.path.insert(0, os.path.join(project_root, 'automated_es_collection'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BasicConnectivityTest:
    """Basic connectivity and system readiness test."""
    
    def __init__(self):
        """Initialize basic connectivity test."""
        self.results = {
            'test_name': 'Basic Connectivity Test',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'tests': {},
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0
            }
        }
    
    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """Log test result."""
        self.results['tests'][test_name] = {
            'passed': passed,
            'details': details,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        self.results['summary']['total'] += 1
        if passed:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}: PASSED")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: FAILED")
        
        if details:
            print(f"   {details}")
    
    def test_python_environment(self) -> bool:
        """Test Python environment and imports."""
        print("\n🔍 Testing Python environment...")
        
        try:
            # Test basic Python version
            if sys.version_info < (3, 8):
                self.log_test_result('python_version', False, 
                                   f"Python 3.8+ required, found {sys.version}")
                return False
            
            # Test required packages
            required_packages = ['asyncio', 'json', 'os', 'sys', 'time', 'datetime']
            missing_packages = []
            
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    missing_packages.append(package)
            
            if missing_packages:
                self.log_test_result('python_packages', False,
                                   f"Missing packages: {', '.join(missing_packages)}")
                return False
            
            self.log_test_result('python_environment', True,
                               f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
            return True
            
        except Exception as e:
            self.log_test_result('python_environment', False, str(e))
            return False
    
    def test_project_structure(self) -> bool:
        """Test project directory structure."""
        print("\n🔍 Testing project structure...")
        
        required_paths = [
            'simple-demos',
            'automated_es_collection',
            'proto_generated',
            'src/database'
        ]
        
        missing_paths = []
        for path in required_paths:
            full_path = os.path.join(project_root, path)
            if not os.path.exists(full_path):
                missing_paths.append(path)
        
        if missing_paths:
            self.log_test_result('project_structure', False,
                               f"Missing directories: {', '.join(missing_paths)}")
            return False
        
        self.log_test_result('project_structure', True,
                           f"All required directories found")
        return True
    
    def test_configuration_files(self) -> bool:
        """Test configuration file availability."""
        print("\n🔍 Testing configuration files...")
        
        config_files = [
            '.env',
            'simple-demos/.env.simple-demos',
            'src/database/schema.sql',
            'automated_es_collection/market_hours.py',
            'automated_es_collection/es_futures_collector.py'
        ]
        
        missing_files = []
        for config_file in config_files:
            full_path = os.path.join(project_root, config_file)
            if not os.path.exists(full_path):
                missing_files.append(config_file)
        
        if missing_files:
            self.log_test_result('configuration_files', False,
                               f"Missing files: {', '.join(missing_files)}")
            return False
        
        self.log_test_result('configuration_files', True,
                           "All configuration files found")
        return True
    
    def test_market_hours_module(self) -> bool:
        """Test market hours calculation module."""
        print("\n🔍 Testing market hours module...")
        
        try:
            from market_hours import CMEGlobexHours, MarketState
            
            market_hours = CMEGlobexHours()
            current_state = market_hours.get_market_state()
            
            # Test basic functionality
            current_et = market_hours.get_current_et_time()
            is_collection_time = market_hours.is_collection_time()
            time_diff, next_event = market_hours.time_until_next_event()
            
            details = f"Market state: {current_state.value}, Next: {next_event}"
            self.log_test_result('market_hours_module', True, details)
            return True
            
        except Exception as e:
            self.log_test_result('market_hours_module', False, str(e))
            return False
    
    def test_database_config(self) -> bool:
        """Test database configuration."""
        print("\n🔍 Testing database configuration...")
        
        try:
            # Check if we can load configuration
            from config import get_config
            
            config = get_config()
            db_config = config.database
            
            # Validate required database settings
            required_fields = ['host', 'user', 'password', 'database']
            missing_fields = []
            
            for field in required_fields:
                if not hasattr(db_config, field) or not getattr(db_config, field):
                    missing_fields.append(field)
            
            if missing_fields:
                self.log_test_result('database_config', False,
                                   f"Missing DB config: {', '.join(missing_fields)}")
                return False
            
            details = f"Host: {db_config.host}, Database: {db_config.database}"
            self.log_test_result('database_config', True, details)
            return True
            
        except Exception as e:
            self.log_test_result('database_config', False, str(e))
            return False
    
    def test_websocket_imports(self) -> bool:
        """Test WebSocket and protobuf imports."""
        print("\n🔍 Testing WebSocket and protobuf imports...")
        
        try:
            import websockets
            import protobuf
            
            self.log_test_result('websocket_imports', True,
                               "WebSocket and protobuf modules available")
            return True
            
        except ImportError as e:
            self.log_test_result('websocket_imports', False,
                               f"Missing dependency: {e}")
            return False
        except Exception as e:
            self.log_test_result('websocket_imports', False, str(e))
            return False
    
    def test_rithmic_credentials(self) -> bool:
        """Test Rithmic credentials availability."""
        print("\n🔍 Testing Rithmic credentials...")
        
        try:
            from config import get_config
            
            config = get_config()
            rithmic_config = config.rithmic
            
            # Check required credentials
            required_fields = ['user', 'password', 'system', 'gateway']
            missing_fields = []
            
            for field in required_fields:
                if not hasattr(rithmic_config, field) or not getattr(rithmic_config, field):
                    missing_fields.append(field)
            
            if missing_fields:
                self.log_test_result('rithmic_credentials', False,
                                   f"Missing credentials: {', '.join(missing_fields)}")
                return False
            
            # Don't log actual credentials for security
            details = f"User: {rithmic_config.user}, System: {rithmic_config.system}"
            self.log_test_result('rithmic_credentials', True, details)
            return True
            
        except Exception as e:
            self.log_test_result('rithmic_credentials', False, str(e))
            return False
    
    def test_cron_installation(self) -> bool:
        """Test cron job installation status."""
        print("\n🔍 Testing cron installation...")
        
        try:
            import subprocess
            
            # Check if cron jobs are installed
            result = subprocess.run(['crontab', '-l'], 
                                  capture_output=True, text=True, check=False)
            
            if result.returncode == 0:
                crontab_content = result.stdout
                if 'ES Collection' in crontab_content:
                    # Count installed jobs
                    job_count = crontab_content.count('ES Collection')
                    details = f"Found {job_count} ES Collection cron jobs"
                    self.log_test_result('cron_installation', True, details)
                    return True
                else:
                    self.log_test_result('cron_installation', False,
                                       "No ES Collection cron jobs found")
                    return False
            else:
                self.log_test_result('cron_installation', False,
                                   "Cannot access crontab")
                return False
                
        except Exception as e:
            self.log_test_result('cron_installation', False, str(e))
            return False
    
    def test_network_connectivity(self) -> bool:
        """Test basic network connectivity."""
        print("\n🔍 Testing network connectivity...")
        
        try:
            import socket
            
            # Test DNS resolution and connectivity to a reliable server
            test_hosts = [
                ('google.com', 80),
                ('*******', 53)
            ]
            
            connected = False
            for host, port in test_hosts:
                try:
                    sock = socket.create_connection((host, port), timeout=5)
                    sock.close()
                    connected = True
                    break
                except Exception:
                    continue
            
            if connected:
                self.log_test_result('network_connectivity', True,
                                   "Internet connectivity confirmed")
                return True
            else:
                self.log_test_result('network_connectivity', False,
                                   "No internet connectivity detected")
                return False
                
        except Exception as e:
            self.log_test_result('network_connectivity', False, str(e))
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all basic connectivity tests."""
        print("🚀 Starting Basic Connectivity Tests")
        print("=" * 50)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run all tests
        tests = [
            self.test_python_environment,
            self.test_project_structure,
            self.test_configuration_files,
            self.test_market_hours_module,
            self.test_database_config,
            self.test_websocket_imports,
            self.test_rithmic_credentials,
            self.test_cron_installation,
            self.test_network_connectivity
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                self.log_test_result(test.__name__, False, f"Test error: {e}")
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        summary = self.results['summary']
        total = summary['total']
        passed = summary['passed']
        failed = summary['failed']
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {(passed/total*100) if total > 0 else 0:.1f}%")
        
        # Show failed tests
        if failed > 0:
            print(f"\n❌ FAILED TESTS:")
            for test_name, result in self.results['tests'].items():
                if not result['passed']:
                    print(f"  • {test_name}: {result['details']}")
        
        # System readiness assessment
        print(f"\n🎯 SYSTEM READINESS:")
        if failed == 0:
            print("✅ System is fully ready for automated data collection")
        elif failed <= 2:
            print("⚠️ System is mostly ready with minor issues")
        else:
            print("❌ System needs attention before deployment")
        
        # Time until market open
        try:
            from market_hours import CMEGlobexHours
            market_hours = CMEGlobexHours()
            time_diff, next_event = market_hours.time_until_next_event()
            print(f"⏰ {next_event}")
            print(f"🕐 Time remaining: {time_diff}")
        except Exception:
            pass
        
        return self.results


def main():
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Basic Connectivity Test')
    parser.add_argument('--json', action='store_true', help='Output results as JSON')
    args = parser.parse_args()
    
    # Run tests
    test_runner = BasicConnectivityTest()
    results = test_runner.run_all_tests()
    
    # Output results
    if args.json:
        print("\n" + "=" * 50)
        print("JSON RESULTS:")
        print(json.dumps(results, indent=2))
    
    # Exit with appropriate code
    failed_count = results['summary']['failed']
    sys.exit(0 if failed_count == 0 else 1)


if __name__ == "__main__":
    main()