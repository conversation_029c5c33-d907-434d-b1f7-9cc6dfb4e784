#!/usr/bin/env python3
"""
Test Runner for ES Futures Data Collection System

Comprehensive test suite runner that executes all tests and provides
a consolidated report of system readiness and connectivity status.

Usage:
    python tests/run_all_tests.py
    python tests/run_all_tests.py --verbose
    python tests/run_all_tests.py --json
    python tests/run_all_tests.py --include-streaming
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List
import json
import subprocess

# Add project paths
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Quiet by default
logger = logging.getLogger(__name__)


class TestSuiteRunner:
    """Comprehensive test suite runner."""
    
    def __init__(self, verbose: bool = False):
        """Initialize test suite runner."""
        self.verbose = verbose
        self.results = {
            'test_suite': 'ES Futures Data Collection System',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'test_results': {},
            'summary': {
                'total_test_suites': 0,
                'passed_test_suites': 0,
                'failed_test_suites': 0,
                'total_individual_tests': 0,
                'passed_individual_tests': 0,
                'failed_individual_tests': 0
            },
            'recommendations': []
        }
    
    def log_suite_result(self, suite_name: str, result: Dict[str, Any]):
        """Log results from a test suite."""
        self.results['test_results'][suite_name] = result
        self.results['summary']['total_test_suites'] += 1
        
        # Count individual tests
        if 'summary' in result:
            suite_summary = result['summary']
            self.results['summary']['total_individual_tests'] += suite_summary.get('total', 0)
            self.results['summary']['passed_individual_tests'] += suite_summary.get('passed', 0)
            self.results['summary']['failed_individual_tests'] += suite_summary.get('failed', 0)
            
            # Suite passes if it has more passed than failed tests
            if suite_summary.get('failed', 1) == 0 or suite_summary.get('passed', 0) > suite_summary.get('failed', 0):
                self.results['summary']['passed_test_suites'] += 1
                print(f"✅ {suite_name}: PASSED ({suite_summary.get('passed', 0)}/{suite_summary.get('total', 0)} tests)")
            else:
                self.results['summary']['failed_test_suites'] += 1
                print(f"❌ {suite_name}: FAILED ({suite_summary.get('failed', 0)}/{suite_summary.get('total', 0)} tests failed)")
        else:
            # Assume failed if no summary
            self.results['summary']['failed_test_suites'] += 1
            print(f"❌ {suite_name}: FAILED (no results)")
    
    async def run_api_connection_test(self) -> Dict[str, Any]:
        """Run API connection test suite."""
        print("\n🔍 Running API Connection Tests...")
        print("-" * 50)
        
        try:
            # Import and run the test
            from test_api_connection import APIConnectionTest
            
            test_runner = APIConnectionTest(verbose=self.verbose)
            results = await test_runner.run_all_tests()
            
            return results
            
        except Exception as e:
            logger.error(f"API connection test failed: {e}")
            return {
                'test_name': 'API Connection Test',
                'error': str(e),
                'summary': {'total': 1, 'passed': 0, 'failed': 1}
            }
    
    async def run_level3_data_test(self, include_streaming: bool = False) -> Dict[str, Any]:
        """Run Level 3 data test suite."""
        print("\n🔍 Running Level 3 Data Tests...")
        print("-" * 50)
        
        try:
            # Import and run the test
            from test_level3_data import Level3DataTest
            
            test_runner = Level3DataTest(verbose=self.verbose)
            results = await test_runner.run_all_tests(test_streaming=include_streaming)
            
            return results
            
        except Exception as e:
            logger.error(f"Level 3 data test failed: {e}")
            return {
                'test_name': 'Level 3 Data Test',
                'error': str(e),
                'summary': {'total': 1, 'passed': 0, 'failed': 1}
            }
    
    async def run_pre_market_connectivity_test(self) -> Dict[str, Any]:
        """Run pre-market connectivity test."""
        print("\n🔍 Running Pre-Market Connectivity Test...")
        print("-" * 50)
        
        try:
            # Import and run the test
            from test_pre_market_connectivity import PreMarketConnectivityTest
            
            test_runner = PreMarketConnectivityTest(verbose=self.verbose)
            await test_runner.run_single_connectivity_test()
            results = test_runner.get_results()
            
            # Convert to expected format
            summary = results['summary']
            test_summary = {
                'total': 1,
                'passed': 1 if summary['successful_connections'] > 0 else 0,
                'failed': 0 if summary['successful_connections'] > 0 else 1
            }
            results['summary'] = test_summary
            
            return results
            
        except Exception as e:
            logger.error(f"Pre-market connectivity test failed: {e}")
            return {
                'test_name': 'Pre-Market Connectivity Test',
                'error': str(e),
                'summary': {'total': 1, 'passed': 0, 'failed': 1}
            }
    
    def generate_recommendations(self):
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Check API connection results
        api_results = self.results['test_results'].get('API Connection Test', {})
        if api_results.get('summary', {}).get('failed', 0) > 0:
            recommendations.append({
                'category': 'API Connection',
                'priority': 'HIGH',
                'message': 'API connection tests failed. Check credentials and network connectivity.',
                'action': 'Verify RITHMIC_USER, RITHMIC_PASSWORD in environment configuration.'
            })
        
        # Check Level 3 results
        level3_results = self.results['test_results'].get('Level 3 Data Test', {})
        if level3_results.get('summary', {}).get('failed', 0) > 0:
            recommendations.append({
                'category': 'Level 3 Data',
                'priority': 'MEDIUM',
                'message': 'Level 3 data tests had issues. May affect depth by order data collection.',
                'action': 'Check if account has Level 3 data permissions and market is open for data.'
            })
        
        # Check pre-market connectivity
        premarket_results = self.results['test_results'].get('Pre-Market Connectivity Test', {})
        if premarket_results.get('summary', {}).get('successful_connections', 0) == 0:
            recommendations.append({
                'category': 'Pre-Market Connectivity',
                'priority': 'LOW',
                'message': 'Pre-market connectivity test failed. May need to adjust startup timing.',
                'action': 'Try testing closer to market open time or check if servers accept early connections.'
            })
        
        # Overall system readiness
        total_failed = self.results['summary']['failed_test_suites']
        if total_failed == 0:
            recommendations.append({
                'category': 'System Status',
                'priority': 'INFO',
                'message': 'All test suites passed. System is ready for automated data collection.',
                'action': 'System can be deployed to production.'
            })
        elif total_failed <= 1:
            recommendations.append({
                'category': 'System Status',
                'priority': 'MEDIUM',
                'message': 'Most tests passed. System is mostly ready with minor issues.',
                'action': 'Review failed tests and consider deploying with monitoring.'
            })
        else:
            recommendations.append({
                'category': 'System Status',
                'priority': 'HIGH',
                'message': 'Multiple test suites failed. System needs attention before deployment.',
                'action': 'Resolve failed tests before proceeding with automated data collection.'
            })
        
        self.results['recommendations'] = recommendations
    
    def print_summary(self):
        """Print comprehensive test summary."""
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 70)
        
        summary = self.results['summary']
        
        print(f"Test Suites:")
        print(f"  Total: {summary['total_test_suites']}")
        print(f"  Passed: {summary['passed_test_suites']}")
        print(f"  Failed: {summary['failed_test_suites']}")
        
        print(f"\nIndividual Tests:")
        print(f"  Total: {summary['total_individual_tests']}")
        print(f"  Passed: {summary['passed_individual_tests']}")
        print(f"  Failed: {summary['failed_individual_tests']}")
        
        if summary['total_test_suites'] > 0:
            suite_success_rate = (summary['passed_test_suites'] / summary['total_test_suites']) * 100
            print(f"\nTest Suite Success Rate: {suite_success_rate:.1f}%")
        
        if summary['total_individual_tests'] > 0:
            test_success_rate = (summary['passed_individual_tests'] / summary['total_individual_tests']) * 100
            print(f"Individual Test Success Rate: {test_success_rate:.1f}%")
        
        # Print detailed results
        print(f"\n📋 DETAILED RESULTS:")
        for suite_name, results in self.results['test_results'].items():
            if 'summary' in results:
                suite_summary = results['summary']
                status = "✅" if suite_summary.get('failed', 1) == 0 else "❌"
                print(f"  {status} {suite_name}: {suite_summary.get('passed', 0)}/{suite_summary.get('total', 0)} passed")
                
                # Show failed tests
                if 'tests' in results:
                    for test_name, test_result in results['tests'].items():
                        if not test_result.get('passed', True):
                            print(f"    ❌ {test_name}")
                            if 'details' in test_result and 'error' in test_result['details']:
                                print(f"       Error: {test_result['details']['error']}")
        
        # Print recommendations
        if self.results['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in self.results['recommendations']:
                priority_symbol = {
                    'INFO': 'ℹ️',
                    'LOW': '🟡',
                    'MEDIUM': '🟠', 
                    'HIGH': '🔴'
                }.get(rec['priority'], '•')
                
                print(f"  {priority_symbol} {rec['category']} ({rec['priority']})")
                print(f"     {rec['message']}")
                print(f"     Action: {rec['action']}")
    
    async def run_all_tests(self, include_streaming: bool = False) -> Dict[str, Any]:
        """Run all test suites."""
        print("🚀 Starting Comprehensive Test Suite")
        print("=" * 70)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Test Mode: {'Full (with streaming)' if include_streaming else 'Standard'}")
        
        # Run all test suites
        try:
            # API Connection Tests
            api_results = await self.run_api_connection_test()
            self.log_suite_result('API Connection Test', api_results)
            
            # Level 3 Data Tests
            level3_results = await self.run_level3_data_test(include_streaming=include_streaming)
            self.log_suite_result('Level 3 Data Test', level3_results)
            
            # Pre-Market Connectivity Tests
            premarket_results = await self.run_pre_market_connectivity_test()
            self.log_suite_result('Pre-Market Connectivity Test', premarket_results)
            
        except KeyboardInterrupt:
            print("\n⚠️ Tests interrupted by user")
        except Exception as e:
            print(f"\n❌ Test suite execution error: {e}")
        
        # Generate recommendations
        self.generate_recommendations()
        
        # Print summary
        self.print_summary()
        
        return self.results


async def main():
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Comprehensive Test Suite for ES Futures Data Collection')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--json', action='store_true', help='Output results as JSON')
    parser.add_argument('--include-streaming', action='store_true', help='Include streaming data tests')
    args = parser.parse_args()
    
    # Run all tests
    test_runner = TestSuiteRunner(verbose=args.verbose)
    results = await test_runner.run_all_tests(include_streaming=args.include_streaming)
    
    # Output JSON results if requested
    if args.json:
        print("\n" + "=" * 70)
        print("JSON RESULTS:")
        print(json.dumps(results, indent=2))
    
    # Exit with appropriate code
    failed_suites = results['summary']['failed_test_suites']
    sys.exit(0 if failed_suites == 0 else 1)


if __name__ == "__main__":
    asyncio.run(main())