#!/usr/bin/env python3
"""
Quick Connectivity Test - Market Opening Soon!

Simple test to validate API connectivity before market opens.
"""

import asyncio
import sys
import os
import time
from datetime import datetime, timezone

# Add paths correctly
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'simple-demos'))
sys.path.insert(0, os.path.join(project_root, 'automated_es_collection'))
sys.path.insert(0, os.path.join(project_root, 'proto_generated'))

async def test_basic_connectivity():
    """Test basic connectivity before market opens."""
    print("🚀 Quick Connectivity Test")
    print("=" * 40)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Test 1: Market hours module
        print("\n🔍 Testing market hours...")
        from market_hours import CMEGlobexHours
        market_hours = CMEGlobexHours()
        current_state = market_hours.get_market_state()
        time_diff, next_event = market_hours.time_until_next_event()
        print(f"✅ Market state: {current_state.value}")
        print(f"✅ {next_event}")
        print(f"✅ Time remaining: {time_diff}")
        
        # Test 2: Can we import basic shared modules?
        print("\n🔍 Testing shared module import...")
        import shared
        print("✅ Shared module imported successfully")
        
        # Test 3: Can we import protobuf modules?
        print("\n🔍 Testing protobuf imports...")
        import request_login_pb2
        import base_pb2
        print("✅ Protobuf modules imported successfully")
        
        # Test 4: Basic websockets import
        print("\n🔍 Testing websockets...")
        import websockets
        print("✅ WebSockets module available")
        
        print("\n🎉 CONNECTIVITY TEST PASSED!")
        print("System appears ready for data collection.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ CONNECTIVITY TEST FAILED!")
        print(f"Error: {e}")
        return False

async def main():
    """Main test runner."""
    success = await test_basic_connectivity()
    
    if success:
        print("\n💡 NEXT STEPS:")
        print("1. Market should be opening very soon")
        print("2. The automated collection system is ready")
        print("3. Cron jobs are installed and will start automatically")
        print("4. Monitor logs in automated_es_collection/logs/")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())