#!/usr/bin/env python3
"""
Live Connection Test - Test API connection as market opens!
"""

import asyncio
import sys
import os
import time
from datetime import datetime, timezone

# Add paths correctly
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'simple-demos'))
sys.path.insert(0, os.path.join(project_root, 'automated_es_collection'))
sys.path.insert(0, os.path.join(project_root, 'proto_generated'))

async def test_live_connection():
    """Test live API connection."""
    print("🚀 Live API Connection Test")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Import required modules
        from shared import connect_and_authenticate, disconnect, search_contracts
        import request_login_pb2
        
        print("\n🔍 Testing WebSocket connection...")
        start_time = time.time()
        
        # Attempt connection to ticker plant
        ws = await connect_and_authenticate(
            request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT
        )
        
        connection_time = time.time() - start_time
        
        if ws:
            print(f"✅ Connected successfully in {connection_time:.2f}s")
            
            # Test symbol search
            print("\n🔍 Testing ES contract search...")
            es_symbols = await search_contracts(ws, "ES", "CME")
            
            if es_symbols:
                print(f"✅ Found {len(es_symbols)} ES contracts")
                print(f"✅ Front month contract: {es_symbols[0]}")
                
                # Test depth by order snapshot
                print(f"\n🔍 Testing Level 3 data for {es_symbols[0]}...")
                
                import request_depth_by_order_snapshot_pb2
                from shared import TemplateIDs
                
                snapshot_req = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
                snapshot_req.template_id = TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_REQUEST
                snapshot_req.user_msg.append("live_test")
                snapshot_req.symbol = es_symbols[0]
                snapshot_req.exchange = "CME"
                
                await ws.send(snapshot_req.SerializeToString())
                
                # Wait for response
                try:
                    response_data = await asyncio.wait_for(ws.recv(), timeout=10)
                    print("✅ Level 3 snapshot request successful")
                except asyncio.TimeoutError:
                    print("⚠️ Level 3 snapshot timeout (normal if market closed)")
            
            else:
                print("❌ No ES contracts found")
            
            # Clean up
            await disconnect(ws)
            print("✅ Connection closed cleanly")
            
            return True
        else:
            print("❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

async def main():
    """Main test runner."""
    # Check market status first
    try:
        from market_hours import CMEGlobexHours
        market_hours = CMEGlobexHours()
        current_state = market_hours.get_market_state()
        time_diff, next_event = market_hours.time_until_next_event()
        
        print(f"Market state: {current_state.value}")
        print(f"{next_event}")
        print(f"Time remaining: {time_diff}")
    except Exception as e:
        print(f"Market hours check failed: {e}")
    
    # Run connection test
    success = await test_live_connection()
    
    if success:
        print("\n🎉 LIVE CONNECTION TEST PASSED!")
        print("The automated ES data collection system is ready.")
    else:
        print("\n❌ LIVE CONNECTION TEST FAILED!")
        print("Check credentials and try again.")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())