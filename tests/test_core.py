#!/usr/bin/env python3
"""
Consolidated Core Functionality Tests

Tests authentication, basic operations, protocol buffer handling, and core client functionality
for the Rithmic API.

Consolidates:
- Basic functionality tests from various test files
- Authentication-related tests
- Protocol buffer parsing tests
- Core client operation tests
"""

import sys
import logging
import unittest
import asyncio
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
    from src.rithmic_api.config import config
    CORE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Core components not available: {e}")
    CORE_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce log noise during tests
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestClientInitialization(unittest.TestCase):
    """Test client initialization and basic setup."""
    
    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
    
    def test_client_creation(self):
        """Test client can be created properly."""
        print("\n=== Testing Client Creation ===")
        
        # Test client creation without connection
        client = RithmicWebSocketClient()
        
        # Verify initial state
        self.assertIsNotNone(client, "Client should be created")
        self.assertFalse(client.is_connected, "Client should not be connected initially")
        self.assertFalse(client.is_authenticated, "Client should not be authenticated initially")
        
        # Verify essential attributes
        self.assertIsNotNone(client.TEMPLATE_IDS, "Client should have template IDs")
        self.assertIsNotNone(client.message_router, "Client should have message router")
        
        print("✅ Client creation successful")
    
    def test_configuration_loading(self):
        """Test configuration loading."""
        print("\n=== Testing Configuration Loading ===")
        
        # Test config object exists and has required fields
        self.assertIsNotNone(config, "Config should be available")
        
        # Check for essential config fields (don't validate values as they may be test values)
        config_dict = vars(config) if hasattr(config, '__dict__') else config
        
        if isinstance(config_dict, dict):
            # Basic structure test
            self.assertIsInstance(config_dict, dict, "Config should be dict-like")
        
        print("✅ Configuration loading verified")
    
    def test_template_id_definitions(self):
        """Test template ID definitions are complete."""
        print("\n=== Testing Template ID Definitions ===")
        
        client = RithmicWebSocketClient()
        
        # Verify essential template IDs are defined
        essential_templates = [
            'REQUEST_RITHMIC_SYSTEM_INFO',
            'RESPONSE_RITHMIC_SYSTEM_INFO',
            'REQUEST_LOGIN', 
            'RESPONSE_LOGIN',
            'REQUEST_HEARTBEAT',
            'RESPONSE_HEARTBEAT',
            'REQUEST_SEARCH_SYMBOLS',
            'RESPONSE_SEARCH_SYMBOLS'
        ]
        
        for template in essential_templates:
            self.assertIn(template, client.TEMPLATE_IDS, f"Template {template} should be defined")
            template_id = client.TEMPLATE_IDS[template]
            self.assertIsInstance(template_id, int, f"Template {template} should have integer ID")
            self.assertGreater(template_id, 0, f"Template {template} should have positive ID")
        
        print(f"✅ {len(essential_templates)} essential template IDs verified")


class TestProtocolBufferHandling(unittest.TestCase):
    """Test protocol buffer message handling."""
    
    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
    
    def test_message_parsing_infrastructure(self):
        """Test message parsing infrastructure exists."""
        print("\n=== Testing Message Parsing Infrastructure ===")
        
        client = RithmicWebSocketClient()
        
        # Verify parse method exists
        self.assertTrue(hasattr(client, 'parse_message_by_template_id'),
                       "Client should have message parsing method")
        
        # Test that method can handle basic parameters
        parse_method = client.parse_message_by_template_id
        self.assertIsNotNone(parse_method, "Parse method should be callable")
        
        print("✅ Message parsing infrastructure verified")
    
    def test_protobuf_imports(self):
        """Test protocol buffer imports are working."""
        print("\n=== Testing Protocol Buffer Imports ===")
        
        # Test that essential protobuf modules can be imported
        try:
            from proto_generated import (
                base_pb2,
                request_rithmic_system_info_pb2,
                response_rithmic_system_info_pb2,
                request_login_pb2,
                response_login_pb2
            )
            
            # Verify modules have expected classes
            self.assertTrue(hasattr(request_rithmic_system_info_pb2, 'RequestRithmicSystemInfo'),
                           "System info request should be available")
            self.assertTrue(hasattr(response_rithmic_system_info_pb2, 'ResponseRithmicSystemInfo'), 
                           "System info response should be available")
            self.assertTrue(hasattr(request_login_pb2, 'RequestLogin'),
                           "Login request should be available")
            self.assertTrue(hasattr(response_login_pb2, 'ResponseLogin'),
                           "Login response should be available")
            
            print("✅ Protocol buffer imports successful")
            
        except ImportError as e:
            self.skipTest(f"Protocol buffer modules not available: {e}")


class TestAuthenticationInfrastructure(unittest.TestCase):
    """Test authentication infrastructure."""
    
    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
    
    def test_authentication_methods_exist(self):
        """Test authentication methods are defined."""
        print("\n=== Testing Authentication Methods ===")
        
        client = RithmicWebSocketClient()
        
        # Verify essential authentication methods exist
        auth_methods = [
            'discover_systems',
            'login',
            'heartbeat_loop'
        ]
        
        for method in auth_methods:
            self.assertTrue(hasattr(client, method), f"Client should have {method} method")
            method_obj = getattr(client, method)
            self.assertTrue(callable(method_obj), f"{method} should be callable")
        
        print("✅ Authentication methods verified")
    
    def test_authentication_state_management(self):
        """Test authentication state management."""
        print("\n=== Testing Authentication State ===")
        
        client = RithmicWebSocketClient()
        
        # Test initial authentication state
        self.assertFalse(client.is_authenticated, "Client should start unauthenticated")
        
        # Verify authentication state attributes exist
        auth_attributes = ['is_authenticated', 'is_connected']
        for attr in auth_attributes:
            self.assertTrue(hasattr(client, attr), f"Client should have {attr} attribute")
        
        print("✅ Authentication state management verified")


class TestBasicOperations(unittest.TestCase):
    """Test basic client operations."""
    
    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
    
    def test_message_sending_infrastructure(self):
        """Test message sending infrastructure."""
        print("\n=== Testing Message Sending Infrastructure ===")
        
        client = RithmicWebSocketClient()
        
        # Verify essential message sending methods exist
        messaging_methods = [
            'send_message',
            'send_request',
            'send_request_multi_response'
        ]
        
        for method in messaging_methods:
            self.assertTrue(hasattr(client, method), f"Client should have {method} method")
            method_obj = getattr(client, method)
            self.assertTrue(callable(method_obj), f"{method} should be callable")
        
        print("✅ Message sending infrastructure verified")
    
    def test_symbol_search_infrastructure(self):
        """Test symbol search infrastructure."""
        print("\n=== Testing Symbol Search Infrastructure ===")
        
        client = RithmicWebSocketClient()
        
        # Verify symbol search methods exist
        search_methods = [
            'search_symbols',
            'find_front_month_contract'
        ]
        
        for method in search_methods:
            self.assertTrue(hasattr(client, method), f"Client should have {method} method")
            method_obj = getattr(client, method)
            self.assertTrue(callable(method_obj), f"{method} should be callable")
        
        print("✅ Symbol search infrastructure verified")
    
    def test_market_data_infrastructure(self):
        """Test market data infrastructure."""
        print("\n=== Testing Market Data Infrastructure ===")
        
        client = RithmicWebSocketClient()
        
        # Verify market data methods exist
        market_data_methods = [
            'subscribe_to_market_data',
            'listen_for_updates'
        ]
        
        for method in market_data_methods:
            self.assertTrue(hasattr(client, method), f"Client should have {method} method")
            method_obj = getattr(client, method)
            self.assertTrue(callable(method_obj), f"{method} should be callable")
        
        print("✅ Market data infrastructure verified")


class TestErrorHandling(unittest.TestCase):
    """Test error handling infrastructure."""
    
    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
    
    def test_exception_handling_structure(self):
        """Test exception handling structure."""
        print("\n=== Testing Exception Handling ===")
        
        client = RithmicWebSocketClient()
        
        # Test that client methods are structured to handle exceptions
        # (This is a structural test, not a functional test)
        
        # Verify client has proper logging setup
        self.assertTrue(hasattr(client, 'logger'), "Client should have logger")
        
        # Verify essential error handling components exist
        if hasattr(client, 'message_router'):
            router = client.message_router
            self.assertTrue(hasattr(router, 'stats'), "Router should have stats for error tracking")
        
        print("✅ Exception handling structure verified")
    
    def test_timeout_handling_infrastructure(self):
        """Test timeout handling infrastructure."""
        print("\n=== Testing Timeout Handling ===")
        
        client = RithmicWebSocketClient()
        
        # Verify timeout-related attributes exist
        timeout_attributes = ['default_timeout']
        for attr in timeout_attributes:
            if hasattr(client, attr):
                timeout_value = getattr(client, attr)
                self.assertIsInstance(timeout_value, (int, float), f"{attr} should be numeric")
                self.assertGreater(timeout_value, 0, f"{attr} should be positive")
        
        print("✅ Timeout handling infrastructure verified")


def run_core_tests():
    """Run all core functionality tests."""
    print("🧪 Running Consolidated Core Functionality Tests")
    print("=" * 60)
    
    if not CORE_AVAILABLE:
        print("❌ Core components not available. Skipping core tests.")
        return False
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestClientInitialization))
    suite.addTest(unittest.makeSuite(TestProtocolBufferHandling))
    suite.addTest(unittest.makeSuite(TestAuthenticationInfrastructure))
    suite.addTest(unittest.makeSuite(TestBasicOperations))
    suite.addTest(unittest.makeSuite(TestErrorHandling))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 Tests run: {result.testsRun}")
    print(f"✅ Successes: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"💥 Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n🔍 Failures:")
        for test, error in result.failures:
            print(f"  - {test}: {error}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    return len(result.failures) == 0 and len(result.errors) == 0


if __name__ == "__main__":
    success = run_core_tests()
    sys.exit(0 if success else 1)