#!/usr/bin/env python3
"""
API Connection Test for ES Futures Data Collection

Tests the basic API connectivity to Rithmic's ticker plant infrastructure.
Validates authentication, system discovery, and WebSocket connection establishment.

Usage:
    python tests/test_api_connection.py
    python tests/test_api_connection.py --verbose
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any
import json

# Add project paths
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'simple-demos'))

from simple_demos.shared import (
    connect_and_authenticate, disconnect, TemplateIDs, 
    MarketDataParsers, safe_get_field
)
from simple_demos.shared_database import get_simple_database

# Add proto_generated directory
proto_dir = os.path.join(project_root, 'proto_generated')
sys.path.append(proto_dir)

import request_login_pb2
import request_search_symbols_pb2
import base_pb2

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class APIConnectionTest:
    """Test suite for API connection functionality."""
    
    def __init__(self, verbose: bool = False):
        """
        Initialize API connection test.
        
        Args:
            verbose: Enable verbose logging
        """
        self.verbose = verbose
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        self.results = {
            'test_name': 'API Connection Test',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'tests': {},
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0
            }
        }
        
        self.ws = None
    
    def log_test_result(self, test_name: str, passed: bool, details: Dict[str, Any] = None):
        """Log test result."""
        self.results['tests'][test_name] = {
            'passed': passed,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'details': details or {}
        }
        
        self.results['summary']['total'] += 1
        if passed:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}: PASSED")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: FAILED")
            if details:
                print(f"   Details: {details}")
    
    async def test_database_connection(self) -> bool:
        """Test database connectivity."""
        print("\n🔍 Testing database connection...")
        
        try:
            db = get_simple_database()
            
            if not db.is_enabled():
                self.log_test_result('database_connection', False, 
                                   {'error': 'Database not enabled'})
                return False
            
            # Test basic database operation
            db.log_system_event('TEST', 'API connection test started')
            
            self.log_test_result('database_connection', True, 
                               {'status': 'Database connection successful'})
            return True
            
        except Exception as e:
            self.log_test_result('database_connection', False, 
                               {'error': str(e)})
            return False
    
    async def test_websocket_connection(self) -> bool:
        """Test WebSocket connection to Rithmic ticker plant."""
        print("\n🔍 Testing WebSocket connection...")
        
        try:
            start_time = time.time()
            
            # Attempt connection and authentication
            self.ws = await connect_and_authenticate(
                request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT
            )
            
            connection_time = time.time() - start_time
            
            if self.ws:
                self.log_test_result('websocket_connection', True, {
                    'connection_time_ms': round(connection_time * 1000, 2),
                    'infrastructure': 'TICKER_PLANT'
                })
                return True
            else:
                self.log_test_result('websocket_connection', False, 
                                   {'error': 'Authentication failed'})
                return False
                
        except Exception as e:
            self.log_test_result('websocket_connection', False, 
                               {'error': str(e)})
            return False
    
    async def test_system_info_request(self) -> bool:
        """Test system information request."""
        print("\n🔍 Testing system info request...")
        
        if not self.ws:
            self.log_test_result('system_info_request', False, 
                               {'error': 'No WebSocket connection'})
            return False
        
        try:
            # System info should be available after authentication
            # This test just verifies we can communicate with the server
            
            # Send a simple heartbeat to verify communication
            from simple_demos.shared import send_heartbeat
            await send_heartbeat(self.ws)
            
            self.log_test_result('system_info_request', True, {
                'status': 'Server communication successful'
            })
            return True
            
        except Exception as e:
            self.log_test_result('system_info_request', False, 
                               {'error': str(e)})
            return False
    
    async def test_symbol_search(self) -> bool:
        """Test symbol search functionality."""
        print("\n🔍 Testing symbol search...")
        
        if not self.ws:
            self.log_test_result('symbol_search', False, 
                               {'error': 'No WebSocket connection'})
            return False
        
        try:
            # Create search request for ES contracts
            search_req = request_search_symbols_pb2.RequestSearchSymbols()
            search_req.template_id = TemplateIDs.SEARCH_SYMBOLS_REQUEST
            search_req.user_msg.append("test_search")
            search_req.search_text = "ES"
            search_req.exchange = "CME"
            search_req.instrument_type = request_search_symbols_pb2.RequestSearchSymbols.InstrumentType.FUTURE
            search_req.pattern = request_search_symbols_pb2.RequestSearchSymbols.Pattern.CONTAINS
            
            print(f"   Sending search request for ES contracts...")
            await self.ws.send(search_req.SerializeToString())
            
            # Wait for response with timeout
            response_data = await asyncio.wait_for(self.ws.recv(), timeout=10)
            
            # Parse response
            search_response = MarketDataParsers.parse_search_symbols_response(
                response_data, "API Test Symbol Search"
            )
            
            # Extract symbols
            from simple_demos.shared import safe_get_repeated_string_field
            symbols = safe_get_repeated_string_field(search_response, 'symbol', [])
            
            if symbols:
                # Filter for ES contracts
                es_symbols = [s for s in symbols if s.startswith('ES') and len(s) >= 3]
                
                self.log_test_result('symbol_search', True, {
                    'total_symbols': len(symbols),
                    'es_symbols': len(es_symbols),
                    'sample_symbols': es_symbols[:5] if es_symbols else []
                })
                return True
            else:
                self.log_test_result('symbol_search', False, 
                                   {'error': 'No symbols returned'})
                return False
                
        except asyncio.TimeoutError:
            self.log_test_result('symbol_search', False, 
                               {'error': 'Search request timeout'})
            return False
        except Exception as e:
            self.log_test_result('symbol_search', False, 
                               {'error': str(e)})
            return False
    
    async def test_connection_stability(self) -> bool:
        """Test connection stability over time."""
        print("\n🔍 Testing connection stability...")
        
        if not self.ws:
            self.log_test_result('connection_stability', False, 
                               {'error': 'No WebSocket connection'})
            return False
        
        try:
            # Test connection over 30 seconds with periodic heartbeats
            test_duration = 30  # seconds
            heartbeat_interval = 5  # seconds
            start_time = time.time()
            heartbeat_count = 0
            
            print(f"   Testing connection stability for {test_duration} seconds...")
            
            while time.time() - start_time < test_duration:
                try:
                    from simple_demos.shared import send_heartbeat
                    await send_heartbeat(self.ws)
                    heartbeat_count += 1
                    
                    if self.verbose:
                        elapsed = time.time() - start_time
                        print(f"   Heartbeat {heartbeat_count} sent ({elapsed:.1f}s)")
                    
                    await asyncio.sleep(heartbeat_interval)
                    
                except Exception as e:
                    self.log_test_result('connection_stability', False, {
                        'error': f'Connection lost after {heartbeat_count} heartbeats',
                        'exception': str(e)
                    })
                    return False
            
            elapsed_time = time.time() - start_time
            self.log_test_result('connection_stability', True, {
                'test_duration_seconds': round(elapsed_time, 2),
                'heartbeats_sent': heartbeat_count,
                'connection_stable': True
            })
            return True
            
        except Exception as e:
            self.log_test_result('connection_stability', False, 
                               {'error': str(e)})
            return False
    
    async def cleanup(self):
        """Clean up connections."""
        if self.ws:
            try:
                await disconnect(self.ws)
                print("\n🔌 Connection closed")
            except Exception as e:
                print(f"\n⚠️ Error closing connection: {e}")
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all API connection tests."""
        print("🚀 Starting API Connection Tests")
        print("=" * 50)
        
        try:
            # Run tests in order
            await self.test_database_connection()
            await self.test_websocket_connection()
            
            if self.ws:  # Only run remaining tests if connection successful
                await self.test_system_info_request()
                await self.test_symbol_search()
                await self.test_connection_stability()
        
        finally:
            await self.cleanup()
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        summary = self.results['summary']
        total = summary['total']
        passed = summary['passed']
        failed = summary['failed']
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {(passed/total*100) if total > 0 else 0:.1f}%")
        
        if failed > 0:
            print(f"\n❌ FAILED TESTS:")
            for test_name, result in self.results['tests'].items():
                if not result['passed']:
                    print(f"  • {test_name}: {result['details']}")
        
        return self.results


async def main():
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description='API Connection Test Suite')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--json', action='store_true', help='Output results as JSON')
    args = parser.parse_args()
    
    # Run tests
    test_runner = APIConnectionTest(verbose=args.verbose)
    results = await test_runner.run_all_tests()
    
    # Output results
    if args.json:
        print("\n" + "=" * 50)
        print("JSON RESULTS:")
        print(json.dumps(results, indent=2))
    
    # Exit with appropriate code
    failed_count = results['summary']['failed']
    sys.exit(0 if failed_count == 0 else 1)


if __name__ == "__main__":
    asyncio.run(main())