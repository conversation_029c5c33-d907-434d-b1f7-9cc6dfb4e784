#!/usr/bin/env python3
"""
Consolidated Discovery and Cache Tests

Tests symbol discovery, contract caching, search variations, and exhaustive discovery
for the Rithmic API.

Consolidates:
- test_exhaustive_discovery.py
- test_cache_improvements.py
- test_search_variations.py
- test_multi_contract_basic.py
- Discovery-related functionality from other tests
"""

import sys
import logging
import unittest
import asyncio
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.contract_cache import ContractCache
    from src.utils.contract_resolver import ContractResolver
    from src.utils.multi_contract_manager import MultiContractManager
    from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
    DISCOVERY_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Discovery components not available: {e}")
    DISCOVERY_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce log noise during tests
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestContractCache(unittest.TestCase):
    """Test contract caching functionality."""
    
    def setUp(self):
        """Set up test environment."""
        if not DISCOVERY_AVAILABLE:
            self.skipTest("Discovery components not available")
    
    def test_cache_initialization(self):
        """Test cache can be initialized properly."""
        print("\n=== Testing Cache Initialization ===")
        
        # Test cache initialization
        cache = ContractCache(cache_dir="test_cache", use_database=False)
        
        # Verify initial state
        self.assertIsNotNone(cache._contracts, "Cache should have contracts dict")
        self.assertIsNotNone(cache.cache_dir, "Cache should have cache directory")
        
        print("✅ Cache initialization successful")
    
    def test_cache_operations(self):
        """Test basic cache operations."""
        print("\n=== Testing Cache Operations ===")
        
        cache = ContractCache(cache_dir="test_cache", use_database=False)
        
        # Test adding contract
        test_contract = {
            'symbol': 'TEST',
            'exchange': 'TEST_EXCHANGE',
            'product': 'TEST_PRODUCT',
            'type': 'FUTURE'
        }
        
        cache.add_contract('TEST@TEST_EXCHANGE', test_contract)
        
        # Test retrieving contract
        retrieved = cache.get_contract('TEST@TEST_EXCHANGE')
        self.assertIsNotNone(retrieved, "Contract should be retrievable")
        self.assertEqual(retrieved['symbol'], 'TEST')
        
        # Test cache status
        status = cache.get_cache_status()
        self.assertIn('total_contracts', status)
        self.assertGreater(status['total_contracts'], 0)
        
        print("✅ Cache operations verified")
    
    def test_cache_search_functionality(self):
        """Test cache search and filtering."""
        print("\n=== Testing Cache Search ===")
        
        cache = ContractCache(cache_dir="test_cache", use_database=False)
        
        # Add multiple test contracts
        test_contracts = [
            {'symbol': 'ES', 'exchange': 'CME', 'product': 'ES', 'type': 'FUTURE'},
            {'symbol': 'NQ', 'exchange': 'CME', 'product': 'NQ', 'type': 'FUTURE'},
            {'symbol': 'YM', 'exchange': 'CME', 'product': 'YM', 'type': 'FUTURE'},
        ]
        
        for contract in test_contracts:
            key = f"{contract['symbol']}@{contract['exchange']}"
            cache.add_contract(key, contract)
        
        # Test search by exchange
        cme_contracts = cache.get_contracts_by_exchange('CME')
        self.assertGreaterEqual(len(cme_contracts), 3, "Should find CME contracts")
        
        # Test search by product
        es_contracts = cache.get_contracts_by_product('ES')
        self.assertGreaterEqual(len(es_contracts), 1, "Should find ES contracts")
        
        print("✅ Cache search functionality verified")


class TestContractResolver(unittest.TestCase):
    """Test contract resolution functionality."""
    
    def setUp(self):
        """Set up test environment."""
        if not DISCOVERY_AVAILABLE:
            self.skipTest("Discovery components not available")
    
    def test_resolver_initialization(self):
        """Test resolver can be initialized properly."""
        print("\n=== Testing Resolver Initialization ===")
        
        # Test resolver initialization with mock cache
        cache = ContractCache(cache_dir="test_cache", use_database=False)
        resolver = ContractResolver(cache)
        
        # Verify initial state
        self.assertIsNotNone(resolver.cache, "Resolver should have cache reference")
        
        print("✅ Resolver initialization successful")
    
    def test_symbol_resolution(self):
        """Test symbol resolution logic."""
        print("\n=== Testing Symbol Resolution ===")
        
        cache = ContractCache(cache_dir="test_cache", use_database=False)
        resolver = ContractResolver(cache)
        
        # Add test contract to cache
        test_contract = {
            'symbol': 'ESH25',
            'exchange': 'CME',
            'product': 'ES',
            'type': 'FUTURE'
        }
        cache.add_contract('ESH25@CME', test_contract)
        
        # Test exact symbol resolution
        resolved = resolver.resolve_symbol('ESH25', 'CME')
        self.assertIsNotNone(resolved, "Should resolve exact symbol match")
        self.assertEqual(resolved['symbol'], 'ESH25')
        
        print("✅ Symbol resolution verified")


class TestMultiContractManager(unittest.TestCase):
    """Test multi-contract management functionality."""
    
    def setUp(self):
        """Set up test environment."""
        if not DISCOVERY_AVAILABLE:
            self.skipTest("Discovery components not available")
    
    def test_manager_initialization(self):
        """Test manager can be initialized properly."""
        print("\n=== Testing Manager Initialization ===")
        
        # Create mock client
        mock_client = MagicMock()
        manager = MultiContractManager(mock_client)
        
        # Verify initial state
        self.assertIsNotNone(manager.client, "Manager should have client reference")
        self.assertIsNotNone(manager.subscriptions, "Manager should have subscriptions dict")
        
        print("✅ Manager initialization successful")
    
    def test_subscription_management(self):
        """Test subscription management."""
        print("\n=== Testing Subscription Management ===")
        
        mock_client = MagicMock()
        manager = MultiContractManager(mock_client)
        
        # Test adding subscription
        manager.add_subscription('ES@CME', {'symbol': 'ES', 'exchange': 'CME'})
        self.assertIn('ES@CME', manager.subscriptions)
        
        # Test removing subscription
        manager.remove_subscription('ES@CME')
        self.assertNotIn('ES@CME', manager.subscriptions)
        
        print("✅ Subscription management verified")


class TestSearchVariations(unittest.TestCase):
    """Test search pattern variations."""
    
    def setUp(self):
        """Set up test environment."""
        if not DISCOVERY_AVAILABLE:
            self.skipTest("Discovery components not available")
    
    def test_search_pattern_generation(self):
        """Test search pattern generation."""
        print("\n=== Testing Search Patterns ===")
        
        # Test various search patterns
        patterns = [
            ('ES', ['ES*', 'ES', '*ES*']),
            ('NQ', ['NQ*', 'NQ', '*NQ*']),
            ('YM', ['YM*', 'YM', '*YM*']),
        ]
        
        for base_symbol, expected_patterns in patterns:
            # Test that we can generate multiple search patterns
            # (This would normally be done by the search functions)
            generated_patterns = [
                f"{base_symbol}*",
                base_symbol,
                f"*{base_symbol}*"
            ]
            
            self.assertEqual(generated_patterns, expected_patterns,
                           f"Search patterns for {base_symbol} should match expected")
        
        print("✅ Search pattern generation verified")
    
    def test_search_parameter_variations(self):
        """Test search parameter variations."""
        print("\n=== Testing Search Parameters ===")
        
        # Test various search parameter combinations
        search_params = [
            {'exchange': 'CME', 'type': 'FUTURE'},
            {'exchange': 'NYMEX', 'type': 'FUTURE'},
            {'product': 'ES', 'type': 'FUTURE'},
            {'type': 'OPTION'},
        ]
        
        for params in search_params:
            # Verify parameters are properly structured
            self.assertIsInstance(params, dict, "Search params should be dict")
            if 'exchange' in params:
                self.assertIsInstance(params['exchange'], str, "Exchange should be string")
            if 'type' in params:
                self.assertIn(params['type'], ['FUTURE', 'OPTION'], "Type should be valid")
        
        print("✅ Search parameter variations verified")


class TestExhaustiveDiscovery(unittest.TestCase):
    """Test exhaustive discovery functionality."""
    
    def setUp(self):
        """Set up test environment."""
        if not DISCOVERY_AVAILABLE:
            self.skipTest("Discovery components not available")
    
    def test_discovery_patterns(self):
        """Test discovery pattern generation."""
        print("\n=== Testing Discovery Patterns ===")
        
        # Test pattern generation for exhaustive discovery
        exchanges = ['CME', 'NYMEX', 'ICE']
        product_codes = ['ES', 'NQ', 'YM', 'CL', 'NG']
        
        # Generate patterns for each combination
        patterns = []
        for exchange in exchanges:
            for product in product_codes:
                patterns.append({
                    'exchange': exchange,
                    'product': product,
                    'pattern': f"{product}*"
                })
        
        self.assertEqual(len(patterns), len(exchanges) * len(product_codes),
                        "Should generate pattern for each exchange/product combination")
        
        # Verify pattern structure
        for pattern in patterns[:5]:  # Check first 5
            self.assertIn('exchange', pattern)
            self.assertIn('product', pattern)
            self.assertIn('pattern', pattern)
        
        print(f"✅ Generated {len(patterns)} discovery patterns")
    
    def test_discovery_result_processing(self):
        """Test discovery result processing."""
        print("\n=== Testing Discovery Results ===")
        
        # Mock discovery results
        mock_results = [
            {'symbol': 'ESH25', 'exchange': 'CME', 'product': 'ES'},
            {'symbol': 'ESM25', 'exchange': 'CME', 'product': 'ES'},
            {'symbol': 'NQH25', 'exchange': 'CME', 'product': 'NQ'},
        ]
        
        # Process results (group by exchange, product, etc.)
        exchanges = set(result['exchange'] for result in mock_results)
        products = set(result['product'] for result in mock_results)
        
        self.assertIn('CME', exchanges, "Should identify CME exchange")
        self.assertIn('ES', products, "Should identify ES product")
        self.assertIn('NQ', products, "Should identify NQ product")
        
        # Count by product
        es_count = sum(1 for r in mock_results if r['product'] == 'ES')
        nq_count = sum(1 for r in mock_results if r['product'] == 'NQ')
        
        self.assertEqual(es_count, 2, "Should find 2 ES contracts")
        self.assertEqual(nq_count, 1, "Should find 1 NQ contract")
        
        print("✅ Discovery result processing verified")


def run_discovery_tests():
    """Run all discovery and cache tests."""
    print("🧪 Running Consolidated Discovery & Cache Tests")
    print("=" * 60)
    
    if not DISCOVERY_AVAILABLE:
        print("❌ Discovery components not available. Skipping discovery tests.")
        return False
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestContractCache))
    suite.addTest(unittest.makeSuite(TestContractResolver))
    suite.addTest(unittest.makeSuite(TestMultiContractManager))
    suite.addTest(unittest.makeSuite(TestSearchVariations))
    suite.addTest(unittest.makeSuite(TestExhaustiveDiscovery))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 Tests run: {result.testsRun}")
    print(f"✅ Successes: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"💥 Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n🔍 Failures:")
        for test, error in result.failures:
            print(f"  - {test}: {error}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    return len(result.failures) == 0 and len(result.errors) == 0


if __name__ == "__main__":
    success = run_discovery_tests()
    sys.exit(0 if success else 1)