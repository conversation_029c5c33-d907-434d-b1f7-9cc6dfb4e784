#!/usr/bin/env python3
"""
Level 3 Data Test for ES Futures Data Collection

Tests Level 3 (depth by order) data availability and streaming connectivity.
Focuses on front-month ES contract data collection and validates data integrity.

Usage:
    python tests/test_level3_data.py
    python tests/test_level3_data.py --verbose
    python tests/test_level3_data.py --test-streaming
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List
import json

# Add project paths
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'simple-demos'))

from simple_demos.shared import (
    connect_and_authenticate, disconnect, search_contracts, TemplateIDs,
    MarketDataParsers, safe_get_field, format_symbol_info,
    ProtobufParsingError, RithmicAPIError, UnexpectedTemplateError
)

# Add proto_generated directory
proto_dir = os.path.join(project_root, 'proto_generated')
sys.path.append(proto_dir)

import request_login_pb2
import request_depth_by_order_snapshot_pb2
import request_depth_by_order_updates_pb2
import response_depth_by_order_snapshot_pb2
import response_depth_by_order_updates_pb2
import depth_by_order_pb2
import base_pb2

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Level3DataTest:
    """Test suite for Level 3 depth by order data functionality."""
    
    def __init__(self, verbose: bool = False):
        """
        Initialize Level 3 data test.
        
        Args:
            verbose: Enable verbose logging
        """
        self.verbose = verbose
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        self.results = {
            'test_name': 'Level 3 Data Test',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'tests': {},
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0
            }
        }
        
        self.ws = None
        self.test_symbol = None
        self.received_messages = []
        self.snapshot_received = False
        self.updates_received = 0
    
    def log_test_result(self, test_name: str, passed: bool, details: Dict[str, Any] = None):
        """Log test result."""
        self.results['tests'][test_name] = {
            'passed': passed,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'details': details or {}
        }
        
        self.results['summary']['total'] += 1
        if passed:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}: PASSED")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: FAILED")
        
        if details and (not passed or self.verbose):
            for key, value in details.items():
                print(f"   {key}: {value}")
    
    async def test_connection_setup(self) -> bool:
        """Test WebSocket connection setup."""
        print("\n🔍 Testing connection setup...")
        
        try:
            start_time = time.time()
            self.ws = await connect_and_authenticate(
                request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT
            )
            connection_time = time.time() - start_time
            
            if self.ws:
                self.log_test_result('connection_setup', True, {
                    'connection_time_ms': round(connection_time * 1000, 2),
                    'infrastructure': 'TICKER_PLANT'
                })
                return True
            else:
                self.log_test_result('connection_setup', False, 
                                   {'error': 'Failed to authenticate'})
                return False
                
        except Exception as e:
            self.log_test_result('connection_setup', False, 
                               {'error': str(e)})
            return False
    
    async def test_front_month_discovery(self) -> bool:
        """Discover and select front-month ES contract."""
        print("\n🔍 Testing front-month ES contract discovery...")
        
        if not self.ws:
            self.log_test_result('front_month_discovery', False,
                               {'error': 'No WebSocket connection'})
            return False
        
        try:
            # Search for ES contracts
            es_symbols = await search_contracts(self.ws, "ES", "CME")
            
            if not es_symbols:
                self.log_test_result('front_month_discovery', False,
                                   {'error': 'No ES contracts found'})
                return False
            
            # Sort symbols to find front month (earliest expiration)
            # ES contracts follow format like ESZ5, ESH6, etc.
            # Sort by month code and year
            month_order = {'H': 3, 'M': 6, 'U': 9, 'Z': 12}  # Mar, Jun, Sep, Dec
            
            def get_sort_key(symbol):
                if len(symbol) >= 4:
                    month_code = symbol[2]
                    year_code = symbol[3]
                    if month_code in month_order:
                        # Convert year code to full year (5=2025, 6=2026, etc.)
                        year = 2020 + int(year_code) if int(year_code) <= 9 else 2010 + int(year_code)
                        month = month_order[month_code]
                        return (year, month)
                return (9999, 12)  # Invalid symbols sort last
            
            sorted_contracts = sorted(es_symbols, key=get_sort_key)
            
            # Select front month (first in sorted list)
            self.test_symbol = sorted_contracts[0] if sorted_contracts else None
            
            if self.test_symbol:
                self.log_test_result('front_month_discovery', True, {
                    'total_contracts': len(es_symbols),
                    'front_month_contract': self.test_symbol,
                    'all_contracts': sorted_contracts[:10]  # Show first 10
                })
                return True
            else:
                self.log_test_result('front_month_discovery', False,
                                   {'error': 'Could not determine front month'})
                return False
                
        except Exception as e:
            self.log_test_result('front_month_discovery', False,
                               {'error': str(e)})
            return False
    
    async def test_depth_snapshot_request(self) -> bool:
        """Test depth by order snapshot request."""
        print(f"\n🔍 Testing depth snapshot request for {self.test_symbol}...")
        
        if not self.ws or not self.test_symbol:
            self.log_test_result('depth_snapshot_request', False,
                               {'error': 'Missing connection or test symbol'})
            return False
        
        try:
            # Create snapshot request
            snapshot_req = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
            snapshot_req.template_id = TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_REQUEST
            snapshot_req.user_msg.append(f"test_snapshot_{self.test_symbol}")
            snapshot_req.symbol = self.test_symbol
            snapshot_req.exchange = "CME"
            
            print(f"   Requesting depth snapshot for {self.test_symbol}...")
            start_time = time.time()
            await self.ws.send(snapshot_req.SerializeToString())
            
            # Wait for response with timeout
            response_data = await asyncio.wait_for(self.ws.recv(), timeout=10)
            response_time = time.time() - start_time
            
            # Parse response
            base = base_pb2.Base()
            base.ParseFromString(response_data)
            template_id = base.template_id
            
            if template_id == TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_RESPONSE:
                snapshot_response = MarketDataParsers.parse_depth_snapshot_response(
                    response_data, "Level3 Test Depth Snapshot"
                )
                
                # Check response status
                rp_code = safe_get_field(snapshot_response, 'rp_code', None)
                
                if rp_code == "0":  # Success
                    self.snapshot_received = True
                    self.log_test_result('depth_snapshot_request', True, {
                        'symbol': self.test_symbol,
                        'response_time_ms': round(response_time * 1000, 2),
                        'template_id': template_id,
                        'rp_code': rp_code
                    })
                    return True
                else:
                    self.log_test_result('depth_snapshot_request', False, {
                        'error': f'Snapshot request failed with code: {rp_code}',
                        'symbol': self.test_symbol
                    })
                    return False
            else:
                # Might be an error response or different message
                self.log_test_result('depth_snapshot_request', False, {
                    'error': f'Unexpected response template: {template_id}',
                    'expected': TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_RESPONSE
                })
                return False
                
        except asyncio.TimeoutError:
            self.log_test_result('depth_snapshot_request', False,
                               {'error': 'Snapshot request timeout'})
            return False
        except Exception as e:
            self.log_test_result('depth_snapshot_request', False,
                               {'error': str(e)})
            return False
    
    async def test_depth_updates_subscription(self) -> bool:
        """Test depth by order updates subscription."""
        print(f"\n🔍 Testing depth updates subscription for {self.test_symbol}...")
        
        if not self.ws or not self.test_symbol:
            self.log_test_result('depth_updates_subscription', False,
                               {'error': 'Missing connection or test symbol'})
            return False
        
        try:
            # Create updates subscription request
            updates_req = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates()
            updates_req.template_id = TemplateIDs.DEPTH_BY_ORDER_UPDATES_REQUEST
            updates_req.user_msg.append(f"test_updates_{self.test_symbol}")
            updates_req.symbol = self.test_symbol
            updates_req.exchange = "CME"
            updates_req.request = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates.Request.SUBSCRIBE
            
            print(f"   Subscribing to depth updates for {self.test_symbol}...")
            start_time = time.time()
            await self.ws.send(updates_req.SerializeToString())
            
            # Wait for subscription confirmation
            response_data = await asyncio.wait_for(self.ws.recv(), timeout=10)
            response_time = time.time() - start_time
            
            # Parse response
            base = base_pb2.Base()
            base.ParseFromString(response_data)
            template_id = base.template_id
            
            if template_id == TemplateIDs.DEPTH_BY_ORDER_UPDATES_RESPONSE:
                updates_response = MarketDataParsers.parse_depth_updates_response(
                    response_data, "Level3 Test Depth Updates"
                )
                
                # Check response status
                rp_code = safe_get_field(updates_response, 'rp_code', None)
                
                if rp_code == "0":  # Success
                    self.log_test_result('depth_updates_subscription', True, {
                        'symbol': self.test_symbol,
                        'response_time_ms': round(response_time * 1000, 2),
                        'template_id': template_id,
                        'rp_code': rp_code
                    })
                    return True
                else:
                    self.log_test_result('depth_updates_subscription', False, {
                        'error': f'Subscription failed with code: {rp_code}',
                        'symbol': self.test_symbol
                    })
                    return False
            else:
                self.log_test_result('depth_updates_subscription', False, {
                    'error': f'Unexpected response template: {template_id}',
                    'expected': TemplateIDs.DEPTH_BY_ORDER_UPDATES_RESPONSE
                })
                return False
                
        except asyncio.TimeoutError:
            self.log_test_result('depth_updates_subscription', False,
                               {'error': 'Subscription request timeout'})
            return False
        except Exception as e:
            self.log_test_result('depth_updates_subscription', False,
                               {'error': str(e)})
            return False
    
    async def test_streaming_data_availability(self, duration: int = 30) -> bool:
        """Test streaming depth data availability."""
        print(f"\n🔍 Testing streaming data for {duration} seconds...")
        
        if not self.ws or not self.test_symbol:
            self.log_test_result('streaming_data_availability', False,
                               {'error': 'Missing connection or test symbol'})
            return False
        
        try:
            start_time = time.time()
            messages_received = 0
            depth_updates = 0
            other_messages = 0
            errors = 0
            
            print(f"   Listening for depth data on {self.test_symbol}...")
            
            while time.time() - start_time < duration:
                try:
                    # Check for messages with short timeout
                    msg_data = await asyncio.wait_for(self.ws.recv(), timeout=1)
                    messages_received += 1
                    
                    # Parse message type
                    base = base_pb2.Base()
                    base.ParseFromString(msg_data)
                    template_id = base.template_id
                    
                    if template_id == TemplateIDs.DEPTH_BY_ORDER:
                        depth_updates += 1
                        
                        if self.verbose:
                            depth_data = MarketDataParsers.parse_depth_by_order(
                                msg_data, "Level3 Test Stream"
                            )
                            symbol_info = format_symbol_info(depth_data)
                            symbol = symbol_info['symbol']
                            
                            order_id = safe_get_field(depth_data, 'order_id', 'N/A')
                            price = safe_get_field(depth_data, 'price', 'N/A')
                            quantity = safe_get_field(depth_data, 'quantity', 'N/A')
                            side = safe_get_field(depth_data, 'side', 'N/A')
                            
                            print(f"   Depth Update: {symbol} {side} {price}@{quantity} [{order_id}]")
                    
                    else:
                        other_messages += 1
                        if self.verbose:
                            print(f"   Other message: Template {template_id}")
                
                except asyncio.TimeoutError:
                    # No message received in timeout period - this is normal
                    continue
                except Exception as e:
                    errors += 1
                    if self.verbose:
                        print(f"   Error processing message: {e}")
            
            elapsed_time = time.time() - start_time
            
            # Determine success based on whether we received any data
            # During market closed hours, we might not receive any depth updates
            # but the connection should still be stable
            
            success = errors == 0  # Success if no errors occurred
            
            details = {
                'test_duration_seconds': round(elapsed_time, 2),
                'total_messages': messages_received,
                'depth_updates': depth_updates,
                'other_messages': other_messages,
                'errors': errors,
                'messages_per_second': round(messages_received / elapsed_time, 2) if elapsed_time > 0 else 0
            }
            
            if depth_updates > 0:
                details['status'] = 'Active market data received'
            elif messages_received > 0:
                details['status'] = 'Connection stable, no depth data (market may be closed)'
            else:
                details['status'] = 'No messages received (market closed or connection issue)'
            
            self.log_test_result('streaming_data_availability', success, details)
            self.updates_received = depth_updates
            return success
            
        except Exception as e:
            self.log_test_result('streaming_data_availability', False,
                               {'error': str(e)})
            return False
    
    async def test_data_quality(self) -> bool:
        """Test data quality and integrity."""
        print(f"\n🔍 Testing data quality...")
        
        # This test checks if the data we received makes sense
        quality_score = 0
        total_checks = 4
        issues = []
        
        # Check 1: Connection was established
        if self.ws:
            quality_score += 1
        else:
            issues.append("No connection established")
        
        # Check 2: Test symbol was found
        if self.test_symbol:
            quality_score += 1
        else:
            issues.append("No test symbol identified")
        
        # Check 3: Snapshot functionality works
        if self.snapshot_received:
            quality_score += 1
        else:
            issues.append("Snapshot request failed or not supported")
        
        # Check 4: Data structure integrity
        try:
            # Verify we can create the protobuf messages without errors
            test_req = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
            test_req.symbol = "TEST"
            test_req.exchange = "CME"
            test_req.SerializeToString()  # This will fail if structure is bad
            quality_score += 1
        except Exception:
            issues.append("Protobuf message structure errors")
        
        success = quality_score >= total_checks * 0.75  # 75% or better
        
        self.log_test_result('data_quality', success, {
            'quality_score': f"{quality_score}/{total_checks}",
            'quality_percentage': round((quality_score / total_checks) * 100, 1),
            'issues': issues,
            'updates_received': self.updates_received
        })
        
        return success
    
    async def cleanup(self):
        """Clean up connections."""
        if self.ws:
            try:
                await disconnect(self.ws)
                print("\n🔌 Connection closed")
            except Exception as e:
                print(f"\n⚠️ Error closing connection: {e}")
    
    async def run_all_tests(self, test_streaming: bool = False) -> Dict[str, Any]:
        """Run all Level 3 data tests."""
        print("🚀 Starting Level 3 Data Tests")
        print("=" * 50)
        
        try:
            # Core tests
            await self.test_connection_setup()
            
            if self.ws:
                await self.test_front_month_discovery()
                
                if self.test_symbol:
                    await self.test_depth_snapshot_request()
                    await self.test_depth_updates_subscription()
                    
                    if test_streaming:
                        await self.test_streaming_data_availability(30)
                    
                    await self.test_data_quality()
        
        finally:
            await self.cleanup()
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        summary = self.results['summary']
        total = summary['total']
        passed = summary['passed']
        failed = summary['failed']
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {(passed/total*100) if total > 0 else 0:.1f}%")
        
        if self.test_symbol:
            print(f"Test Symbol: {self.test_symbol}")
        
        if failed > 0:
            print(f"\n❌ FAILED TESTS:")
            for test_name, result in self.results['tests'].items():
                if not result['passed']:
                    print(f"  • {test_name}")
                    if 'error' in result['details']:
                        print(f"    Error: {result['details']['error']}")
        
        return self.results


async def main():
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Level 3 Data Test Suite')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--json', action='store_true', help='Output results as JSON')
    parser.add_argument('--test-streaming', action='store_true', help='Include streaming data test')
    args = parser.parse_args()
    
    # Run tests
    test_runner = Level3DataTest(verbose=args.verbose)
    results = await test_runner.run_all_tests(test_streaming=args.test_streaming)
    
    # Output results
    if args.json:
        print("\n" + "=" * 50)
        print("JSON RESULTS:")
        print(json.dumps(results, indent=2))
    
    # Exit with appropriate code
    failed_count = results['summary']['failed']
    sys.exit(0 if failed_count == 0 else 1)


if __name__ == "__main__":
    asyncio.run(main())