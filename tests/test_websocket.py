#!/usr/bin/env python3
"""
Consolidated WebSocket and Concurrency Tests

Tests WebSocket connectivity, concurrency fixes, message routing, and streaming data
for the Rithmic API.

Consolidates:
- test_websocket_concurrency.py
- test_concurrency_fixes.py
- test_optimized_websockets.py
- WebSocket-related functionality from other tests
"""

import asyncio
import sys
import logging
import unittest
import time
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
    from src.rithmic_api.config import config
    WEBSOCKET_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ WebSocket components not available: {e}")
    WEBSOCKET_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce log noise during tests
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestWebSocketConcurrency(unittest.TestCase):
    """Test WebSocket concurrency improvements."""
    
    def setUp(self):
        """Set up test environment."""
        if not WEBSOCKET_AVAILABLE:
            self.skipTest("WebSocket components not available")
    
    def test_no_legacy_receive_calls(self):
        """Verify no legacy receive_message() calls in critical methods."""
        print("\n=== Testing Legacy Receive Elimination ===")
        
        # Import and inspect the client class
        client_source = Path(project_root / 'src' / 'rithmic_api' / 'rithmic_websocket_client.py').read_text()
        
        # Check that send_request_multi_response doesn't call _raw_receive_message directly
        self.assertNotIn('await self._raw_receive_message()', 
                        client_source.split('async def send_request_multi_response')[1].split('async def')[0],
                        "send_request_multi_response should not call _raw_receive_message directly")
        
        print("✅ Legacy direct receive calls eliminated")
    
    def test_message_router_architecture(self):
        """Test message router architecture."""
        print("\n=== Testing Message Router Architecture ===")
        
        # Verify MessageRouter class exists and has required methods
        from src.rithmic_api.rithmic_websocket_client import MessageRouter
        
        # Check required methods exist
        required_methods = [
            '_handle_request_response',
            '_handle_streaming_data', 
            'add_pending_request',
            '_cleanup_expired_requests'
        ]
        
        for method in required_methods:
            self.assertTrue(hasattr(MessageRouter, method), f"MessageRouter should have {method} method")
        
        print("✅ Message router architecture verified")
    
    def test_pending_request_multi_response_support(self):
        """Test PendingRequest supports multi-response patterns."""
        print("\n=== Testing Multi-Response Support ===")
        
        from src.rithmic_api.rithmic_websocket_client import PendingRequest
        
        # Create a multi-response pending request
        future = asyncio.Future()
        request = PendingRequest(
            request_id="test-123",
            template_id=100,
            response_template_ids=[101, 102],
            future=future,
            is_multi_response=True,
            max_responses=5,
            max_timeouts=3
        )
        
        # Test multi-response attributes
        self.assertTrue(request.is_multi_response, "Request should be marked as multi-response")
        self.assertEqual(request.max_responses, 5)
        self.assertEqual(request.max_timeouts, 3)
        self.assertEqual(len(request.collected_responses), 0)
        
        # Test response collection
        request.add_response(101, MagicMock(), b'test_data')
        self.assertEqual(len(request.collected_responses), 1)
        self.assertEqual(request.timeout_count, 0)  # Should reset on response
        
        print("✅ Multi-response support verified")


class TestWebSocketConnection(unittest.TestCase):
    """Test WebSocket connection functionality."""
    
    def setUp(self):
        """Set up test environment."""
        if not WEBSOCKET_AVAILABLE:
            self.skipTest("WebSocket components not available")
    
    def test_client_initialization(self):
        """Test client can be initialized properly."""
        print("\n=== Testing Client Initialization ===")
        
        # Test client initialization without connecting
        client = RithmicWebSocketClient()
        
        # Verify initial state
        self.assertFalse(client.is_connected, "Client should not be connected initially")
        self.assertFalse(client.is_authenticated, "Client should not be authenticated initially")
        self.assertIsNotNone(client.message_router, "Client should have message router")
        
        print("✅ Client initialization successful")
    
    def test_template_id_mapping(self):
        """Test template ID mapping is complete."""
        print("\n=== Testing Template ID Mapping ===")
        
        client = RithmicWebSocketClient()
        
        # Verify essential template IDs are defined
        essential_templates = [
            'REQUEST_RITHMIC_SYSTEM_INFO',
            'RESPONSE_RITHMIC_SYSTEM_INFO',
            'REQUEST_LOGIN',
            'RESPONSE_LOGIN',
            'REQUEST_SEARCH_SYMBOLS',
            'RESPONSE_SEARCH_SYMBOLS',
            'REQUEST_HEARTBEAT',
            'RESPONSE_HEARTBEAT'
        ]
        
        for template in essential_templates:
            self.assertIn(template, client.TEMPLATE_IDS, f"Template {template} should be defined")
            self.assertIsNotNone(client.TEMPLATE_IDS[template], f"Template {template} should have valid ID")
        
        print(f"✅ {len(essential_templates)} essential template IDs verified")


class TestMessageRouting(unittest.TestCase):
    """Test message routing functionality."""
    
    def setUp(self):
        """Set up test environment."""
        if not WEBSOCKET_AVAILABLE:
            self.skipTest("WebSocket components not available")
    
    async def test_message_router_lifecycle(self):
        """Test message router start/stop lifecycle."""
        print("\n=== Testing Message Router Lifecycle ===")
        
        client = RithmicWebSocketClient()
        router = client.message_router
        
        # Test initial state
        self.assertFalse(router.is_running, "Router should not be running initially")
        
        # Note: We can't actually start the router without a connection,
        # but we can verify the infrastructure is in place
        self.assertIsNotNone(router.pending_requests, "Router should have pending requests dict")
        self.assertIsNotNone(router.template_to_requests, "Router should have template mapping")
        
        print("✅ Message router lifecycle infrastructure verified")
    
    def test_pending_request_management(self):
        """Test pending request management."""
        print("\n=== Testing Pending Request Management ===")
        
        client = RithmicWebSocketClient()
        router = client.message_router
        
        # Create test request
        from src.rithmic_api.rithmic_websocket_client import PendingRequest
        future = asyncio.Future()
        request = PendingRequest(
            request_id="test-pending-123",
            template_id=100,
            response_template_ids=[101],
            future=future
        )
        
        # Add request
        request_id = router.add_pending_request(request)
        self.assertEqual(request_id, "test-pending-123")
        self.assertIn("test-pending-123", router.pending_requests)
        self.assertIn("test-pending-123", router.template_to_requests[101])
        
        # Remove request
        router._remove_pending_request("test-pending-123")
        self.assertNotIn("test-pending-123", router.pending_requests)
        self.assertNotIn("test-pending-123", router.template_to_requests[101])
        
        print("✅ Pending request management verified")


class TestConcurrencyFixes(unittest.TestCase):
    """Test specific concurrency fixes."""
    
    def setUp(self):
        """Set up test environment."""
        if not WEBSOCKET_AVAILABLE:
            self.skipTest("WebSocket components not available")
    
    def test_no_concurrent_recv_calls(self):
        """Test that concurrent recv calls are prevented."""
        print("\n=== Testing Concurrent Recv Prevention ===")
        
        # This is a structural test - verify the architecture prevents concurrent calls
        client = RithmicWebSocketClient()
        
        # Verify that only MessageRouter calls _raw_receive_message in the main loop
        # and other methods use the router instead
        
        # Check that send_request_multi_response uses router
        method_source = Path(project_root / 'src' / 'rithmic_api' / 'rithmic_websocket_client.py').read_text()
        multi_response_method = method_source.split('async def send_request_multi_response')[1].split('async def')[0]
        
        # Should NOT contain direct _raw_receive_message calls
        self.assertNotIn('await self._raw_receive_message()', multi_response_method,
                        "send_request_multi_response should not call _raw_receive_message directly")
        
        # Should contain router interaction
        self.assertIn('message_router', multi_response_method,
                     "send_request_multi_response should use message router")
        
        print("✅ Concurrent recv call prevention verified")
    
    def test_request_timeout_handling(self):
        """Test request timeout handling."""
        print("\n=== Testing Request Timeout Handling ===")
        
        from src.rithmic_api.rithmic_websocket_client import PendingRequest
        
        # Create request with short timeout
        future = asyncio.Future()
        request = PendingRequest(
            request_id="timeout-test",
            template_id=100,
            response_template_ids=[101],
            future=future,
            timeout=0.1  # 100ms timeout
        )
        
        # Wait for timeout period
        time.sleep(0.2)
        
        # Check if request is expired
        self.assertTrue(request.is_expired(), "Request should be expired after timeout")
        
        print("✅ Request timeout handling verified")


async def run_async_websocket_tests():
    """Run async WebSocket tests."""
    print("\n🔄 Running Async WebSocket Tests")
    
    # Create a simple test case for async functionality
    test_case = TestMessageRouting()
    test_case.setUp()
    
    try:
        await test_case.test_message_router_lifecycle()
        print("✅ Async WebSocket tests completed")
        return True
    except Exception as e:
        print(f"❌ Async WebSocket tests failed: {e}")
        return False


def run_websocket_tests():
    """Run all WebSocket and concurrency tests."""
    print("🧪 Running Consolidated WebSocket & Concurrency Tests")
    print("=" * 60)
    
    if not WEBSOCKET_AVAILABLE:
        print("❌ WebSocket components not available. Skipping WebSocket tests.")
        return False
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestWebSocketConcurrency))
    suite.addTest(unittest.makeSuite(TestWebSocketConnection))
    suite.addTest(unittest.makeSuite(TestMessageRouting))
    suite.addTest(unittest.makeSuite(TestConcurrencyFixes))
    
    # Run synchronous tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Run async tests
    print("\n" + "=" * 40)
    async_success = asyncio.run(run_async_websocket_tests())
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 Sync Tests run: {result.testsRun}")
    print(f"✅ Sync Successes: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Sync Failures: {len(result.failures)}")
    print(f"💥 Sync Errors: {len(result.errors)}")
    print(f"🔄 Async Tests: {'✅ Passed' if async_success else '❌ Failed'}")
    
    if result.failures:
        print("\n🔍 Failures:")
        for test, error in result.failures:
            print(f"  - {test}: {error}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    return len(result.failures) == 0 and len(result.errors) == 0 and async_success


if __name__ == "__main__":
    success = run_websocket_tests()
    sys.exit(0 if success else 1)