#!/usr/bin/env python3
"""
Pre-Market Connectivity Test for ES Futures Data Collection

Tests the earliest time prior to market open that socket connections can be established
and determines optimal connection timing for data collection startup.

This test helps determine:
1. When Rithmic servers accept connections before market open
2. When market data becomes available
3. Optimal timing for automated startup scripts

Usage:
    python tests/test_pre_market_connectivity.py
    python tests/test_pre_market_connectivity.py --continuous
    python tests/test_pre_market_connectivity.py --check-interval 30
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List
import json

# Add project paths
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'simple-demos'))
sys.path.insert(0, os.path.join(project_root, 'automated_es_collection'))

from simple_demos.shared import (
    connect_and_authenticate, disconnect, search_contracts, TemplateIDs,
    MarketDataParsers, safe_get_field
)

from market_hours import CMEGlobexHours, MarketState

# Add proto_generated directory
proto_dir = os.path.join(project_root, 'proto_generated')
sys.path.append(proto_dir)

import request_login_pb2
import request_depth_by_order_snapshot_pb2
import request_market_data_update_pb2
import base_pb2

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PreMarketConnectivityTest:
    """Test suite for pre-market connectivity timing."""
    
    def __init__(self, verbose: bool = False):
        """
        Initialize pre-market connectivity test.
        
        Args:
            verbose: Enable verbose logging
        """
        self.verbose = verbose
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        self.market_hours = CMEGlobexHours()
        self.results = {
            'test_name': 'Pre-Market Connectivity Test',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'market_info': {},
            'connectivity_tests': [],
            'summary': {
                'total_attempts': 0,
                'successful_connections': 0,
                'failed_connections': 0,
                'earliest_connection_time': None,
                'data_availability_time': None
            }
        }
        
        # Get current market status
        current_et = self.market_hours.get_current_et_time()
        market_state = self.market_hours.get_market_state()
        time_diff, next_event = self.market_hours.time_until_next_event()
        
        self.results['market_info'] = {
            'current_time_et': current_et.isoformat(),
            'current_time_utc': datetime.now(timezone.utc).isoformat(),
            'market_state': market_state.value,
            'next_event': next_event,
            'time_until_next_event': str(time_diff),
            'dst_active': self.market_hours.is_dst_active()
        }
    
    def log_connectivity_attempt(self, attempt_result: Dict[str, Any]):
        """Log a connectivity attempt result."""
        self.results['connectivity_tests'].append(attempt_result)
        self.results['summary']['total_attempts'] += 1
        
        if attempt_result['connection_successful']:
            self.results['summary']['successful_connections'] += 1
            
            # Track earliest successful connection
            if not self.results['summary']['earliest_connection_time']:
                self.results['summary']['earliest_connection_time'] = attempt_result['timestamp']
            
            # Track when data becomes available
            if attempt_result.get('data_available') and not self.results['summary']['data_availability_time']:
                self.results['summary']['data_availability_time'] = attempt_result['timestamp']
        else:
            self.results['summary']['failed_connections'] += 1
        
        # Print real-time results
        status = "✅" if attempt_result['connection_successful'] else "❌"
        data_status = ""
        if attempt_result['connection_successful']:
            if attempt_result.get('data_available'):
                data_status = " [DATA AVAILABLE]"
            elif attempt_result.get('subscriptions_successful'):
                data_status = " [SUBSCRIPTIONS OK]"
        
        current_et = self.market_hours.get_current_et_time()
        print(f"{status} {current_et.strftime('%H:%M:%S ET')} - Connection: {attempt_result['connection_successful']}{data_status}")
        
        if self.verbose and 'error' in attempt_result:
            print(f"   Error: {attempt_result['error']}")
    
    async def test_single_connection_attempt(self, test_symbol: str = None) -> Dict[str, Any]:
        """
        Test a single connection attempt with comprehensive checks.
        
        Args:
            test_symbol: Symbol to test data availability (optional)
            
        Returns:
            Dict[str, Any]: Results of the connection attempt
        """
        attempt_start = time.time()
        current_et = self.market_hours.get_current_et_time()
        market_state = self.market_hours.get_market_state()
        
        result = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'time_et': current_et.isoformat(),
            'market_state': market_state.value,
            'connection_successful': False,
            'connection_time_ms': None,
            'authentication_successful': False,
            'symbol_search_successful': False,
            'subscriptions_successful': False,
            'data_available': False,
            'error': None,
            'test_symbol': test_symbol
        }
        
        ws = None
        
        try:
            # Test 1: Basic connection and authentication
            start_time = time.time()
            ws = await connect_and_authenticate(
                request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT
            )
            connection_time = time.time() - start_time
            
            if ws:
                result['connection_successful'] = True
                result['authentication_successful'] = True
                result['connection_time_ms'] = round(connection_time * 1000, 2)
                
                # Test 2: Symbol search capability
                try:
                    es_symbols = await search_contracts(ws, "ES", "CME")
                    if es_symbols:
                        result['symbol_search_successful'] = True
                        if not test_symbol:
                            # Use first available ES symbol
                            test_symbol = es_symbols[0]
                            result['test_symbol'] = test_symbol
                except Exception as e:
                    result['symbol_search_error'] = str(e)
                
                # Test 3: Market data subscription capability
                if test_symbol:
                    try:
                        # Test Level 1 subscription
                        md_req = request_market_data_update_pb2.RequestMarketDataUpdate()
                        md_req.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
                        md_req.user_msg.append("pre_market_test")
                        md_req.symbol = test_symbol
                        md_req.exchange = "CME"
                        md_req.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.SUBSCRIBE
                        md_req.update_bits = (
                            request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.LAST_TRADE |
                            request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.BBO
                        )
                        
                        await ws.send(md_req.SerializeToString())
                        
                        # Test Level 3 subscription
                        depth_req = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
                        depth_req.template_id = TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_REQUEST
                        depth_req.user_msg.append("pre_market_depth_test")
                        depth_req.symbol = test_symbol
                        depth_req.exchange = "CME"
                        
                        await ws.send(depth_req.SerializeToString())
                        
                        result['subscriptions_successful'] = True
                        
                        # Test 4: Check for actual data availability
                        # Listen for a short time to see if any data comes through
                        data_check_duration = 5  # seconds
                        data_messages = 0
                        
                        start_listen = time.time()
                        while time.time() - start_listen < data_check_duration:
                            try:
                                msg_data = await asyncio.wait_for(ws.recv(), timeout=1)
                                
                                # Parse message to see if it's market data
                                base = base_pb2.Base()
                                base.ParseFromString(msg_data)
                                template_id = base.template_id
                                
                                if template_id in [
                                    TemplateIDs.BEST_BID_OFFER,
                                    TemplateIDs.LAST_TRADE,
                                    TemplateIDs.DEPTH_BY_ORDER
                                ]:
                                    data_messages += 1
                                    
                            except asyncio.TimeoutError:
                                continue
                        
                        if data_messages > 0:
                            result['data_available'] = True
                            result['data_messages_received'] = data_messages
                        
                    except Exception as e:
                        result['subscription_error'] = str(e)
            
        except Exception as e:
            result['error'] = str(e)
        
        finally:
            # Clean up connection
            if ws:
                try:
                    await disconnect(ws)
                except Exception:
                    pass
        
        result['total_test_time_ms'] = round((time.time() - attempt_start) * 1000, 2)
        return result
    
    async def run_continuous_connectivity_test(self, check_interval: int = 60, max_duration: int = 3600):
        """
        Run continuous connectivity tests until market opens or max duration reached.
        
        Args:
            check_interval: Seconds between connection attempts
            max_duration: Maximum test duration in seconds
        """
        print("🚀 Starting Continuous Pre-Market Connectivity Test")
        print("=" * 60)
        
        # Display current market status
        print(f"Current Time (ET): {self.results['market_info']['current_time_et']}")
        print(f"Market State: {self.results['market_info']['market_state']}")
        print(f"Next Event: {self.results['market_info']['next_event']}")
        print(f"Time Until: {self.results['market_info']['time_until_next_event']}")
        print(f"DST Active: {self.results['market_info']['dst_active']}")
        print()
        
        print(f"Testing connectivity every {check_interval} seconds...")
        print("Time (ET)     Status")
        print("-" * 40)
        
        start_time = time.time()
        test_symbol = None
        
        while time.time() - start_time < max_duration:
            # Perform connectivity test
            result = await self.test_single_connection_attempt(test_symbol)
            self.log_connectivity_attempt(result)
            
            # Use discovered symbol for subsequent tests
            if result.get('test_symbol') and not test_symbol:
                test_symbol = result['test_symbol']
            
            # Check if market has opened
            current_state = self.market_hours.get_market_state()
            if current_state == MarketState.OPEN:
                print(f"\n🎉 Market is now OPEN!")
                
                # Do one final test to confirm data availability
                print("Performing final data availability test...")
                final_result = await self.test_single_connection_attempt(test_symbol)
                self.log_connectivity_attempt(final_result)
                break
            
            # Check if we should continue based on time until market open
            time_diff, next_event = self.market_hours.time_until_next_event()
            
            # Stop if market opening is more than 2 hours away
            if time_diff.total_seconds() > 7200:  # 2 hours
                print(f"\n⏰ Market opening is more than 2 hours away. Stopping test.")
                break
            
            # Wait for next check
            await asyncio.sleep(check_interval)
        
        print("\n" + "=" * 60)
        print("📊 CONNECTIVITY TEST SUMMARY")
        print("=" * 60)
        
        self._print_summary()
    
    async def run_single_connectivity_test(self):
        """Run a single connectivity test."""
        print("🚀 Starting Single Pre-Market Connectivity Test")
        print("=" * 60)
        
        # Display current market status
        for key, value in self.results['market_info'].items():
            print(f"{key}: {value}")
        print()
        
        # Perform single test
        result = await self.test_single_connection_attempt()
        self.log_connectivity_attempt(result)
        
        print("\n" + "=" * 60)
        print("📊 CONNECTIVITY TEST RESULTS")
        print("=" * 60)
        
        self._print_summary()
        
        return result
    
    def _print_summary(self):
        """Print test summary."""
        summary = self.results['summary']
        
        print(f"Total Attempts: {summary['total_attempts']}")
        print(f"Successful Connections: {summary['successful_connections']}")
        print(f"Failed Connections: {summary['failed_connections']}")
        
        if summary['successful_connections'] > 0:
            success_rate = (summary['successful_connections'] / summary['total_attempts']) * 100
            print(f"Success Rate: {success_rate:.1f}%")
            
            if summary['earliest_connection_time']:
                earliest = datetime.fromisoformat(summary['earliest_connection_time'].replace('Z', '+00:00'))
                earliest_et = earliest.astimezone(self.market_hours.et_tz)
                print(f"Earliest Successful Connection: {earliest_et.strftime('%Y-%m-%d %H:%M:%S ET')}")
            
            if summary['data_availability_time']:
                data_time = datetime.fromisoformat(summary['data_availability_time'].replace('Z', '+00:00'))
                data_time_et = data_time.astimezone(self.market_hours.et_tz)
                print(f"Data Available From: {data_time_et.strftime('%Y-%m-%d %H:%M:%S ET')}")
        
        # Show recent results
        if self.results['connectivity_tests']:
            print(f"\n📋 Recent Test Results:")
            for test in self.results['connectivity_tests'][-5:]:  # Last 5 tests
                et_time = datetime.fromisoformat(test['time_et'].replace('Z', '+00:00'))
                status = "✅" if test['connection_successful'] else "❌"
                conn_time = f" ({test['connection_time_ms']}ms)" if test['connection_time_ms'] else ""
                data_status = " [DATA]" if test.get('data_available') else ""
                print(f"  {status} {et_time.strftime('%H:%M:%S ET')}{conn_time}{data_status}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if summary['successful_connections'] > 0:
            print("✅ Connections are possible before market open")
            
            if summary['earliest_connection_time']:
                earliest = datetime.fromisoformat(summary['earliest_connection_time'].replace('Z', '+00:00'))
                earliest_et = earliest.astimezone(self.market_hours.et_tz)
                
                # Calculate how far before market open this was
                next_open = self.market_hours.get_next_market_open(earliest_et)
                time_before_open = next_open - earliest_et
                
                if time_before_open.total_seconds() > 0:
                    minutes_before = int(time_before_open.total_seconds() / 60)
                    print(f"✅ Connections work at least {minutes_before} minutes before market open")
                    print(f"📅 Recommended startup time: {(earliest_et - timedelta(minutes=5)).strftime('%H:%M:%S ET')} (5 min buffer)")
                
            if summary['data_availability_time']:
                print("✅ Market data is available before official market open")
            else:
                print("⚠️  Connection works but no live data yet (normal before market open)")
        else:
            print("❌ No successful connections - check credentials or try closer to market open")
            
            # Check if we're too far from market open
            time_diff, next_event = self.market_hours.time_until_next_event()
            if time_diff.total_seconds() > 3600:  # More than 1 hour
                print("💡 Try testing closer to market open time (within 1 hour)")
    
    def get_results(self) -> Dict[str, Any]:
        """Get test results."""
        return self.results


async def main():
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Pre-Market Connectivity Test Suite')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--json', action='store_true', help='Output results as JSON')
    parser.add_argument('--continuous', action='store_true', help='Run continuous testing until market open')
    parser.add_argument('--check-interval', type=int, default=60, help='Seconds between checks (default: 60)')
    parser.add_argument('--max-duration', type=int, default=3600, help='Maximum test duration in seconds (default: 3600)')
    args = parser.parse_args()
    
    # Create test runner
    test_runner = PreMarketConnectivityTest(verbose=args.verbose)
    
    # Run appropriate test
    if args.continuous:
        await test_runner.run_continuous_connectivity_test(
            check_interval=args.check_interval,
            max_duration=args.max_duration
        )
    else:
        await test_runner.run_single_connectivity_test()
    
    # Output results
    results = test_runner.get_results()
    
    if args.json:
        print("\n" + "=" * 60)
        print("JSON RESULTS:")
        print(json.dumps(results, indent=2))
    
    # Exit with appropriate code based on whether any connections succeeded
    failed_count = results['summary']['failed_connections']
    success_count = results['summary']['successful_connections']
    
    sys.exit(0 if success_count > 0 else 1)


if __name__ == "__main__":
    asyncio.run(main())