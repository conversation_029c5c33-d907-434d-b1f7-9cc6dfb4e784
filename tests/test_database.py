#!/usr/bin/env python3
"""
Consolidated Database Integration Tests

Tests database connectivity, table creation, CRUD operations, and data persistence
for the Rithmic API MySQL integration.

Consolidates:
- test_database_integration.py
- test_database_population.py  
- Database-related functionality from other tests
"""

import os
import sys
import asyncio
import logging
import unittest
from datetime import datetime, date
from decimal import Decimal
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    # Load .env file first by importing config
    from src.rithmic_api.config import config  # This loads .env file
    from src.database.database_manager import DatabaseManager, init_database
    from src.database.models import (
        Symbol, SymbolDAO, BestBidOffer, BestBidOfferDAO,
        UserSession, UserSessionDAO, create_all_tables, get_database_stats
    )
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Database components not available: {e}")
    DATABASE_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestDatabaseConnection(unittest.TestCase):
    """Test basic database connectivity and setup."""
    
    def setUp(self):
        """Set up test environment."""
        if not DATABASE_AVAILABLE:
            self.skipTest("Database components not available")
    
    def test_database_connection(self):
        """Test basic database connectivity."""
        print("\n=== Testing Database Connection ===")
        
        db = DatabaseManager()
        
        # Test connection
        self.assertTrue(db.test_connection(), "Database connection should succeed")
        print("✅ Database connection successful")
        
        # Get connection info
        info = db.get_connection_info()
        self.assertIsNotNone(info.get('database'))
        self.assertIsNotNone(info.get('host'))
        print(f"📊 Database: {info.get('database')} on {info.get('host')}:{info.get('port')}")
        print(f"📊 MySQL Version: {info.get('version')}")
        print(f"📊 Pool Size: {info.get('pool_size')}")
    
    def test_table_creation(self):
        """Test table creation functionality."""
        print("\n=== Testing Table Creation ===")
        
        # This should create all required tables
        result = create_all_tables()
        self.assertIsNotNone(result, "Table creation should return result")
        print("✅ All tables created successfully")


class TestDatabaseCRUD(unittest.TestCase):
    """Test database CRUD operations."""
    
    def setUp(self):
        """Set up test environment."""
        if not DATABASE_AVAILABLE:
            self.skipTest("Database components not available")
        
        # Ensure tables exist
        create_all_tables()
        self.symbol_dao = SymbolDAO()
        self.bbo_dao = BestBidOfferDAO()
        self.session_dao = UserSessionDAO()
    
    def test_symbol_operations(self):
        """Test symbol CRUD operations."""
        print("\n=== Testing Symbol CRUD Operations ===")
        
        # Create test symbol
        test_symbol = Symbol(
            symbol="TEST_SYMBOL",
            exchange="TEST_EXCHANGE",
            symbol_name="Test Symbol",
            product_code="TEST",
            instrument_type="FUTURE"
        )
        
        # Insert symbol
        symbol_id = self.symbol_dao.upsert(test_symbol)
        self.assertIsNotNone(symbol_id, "Symbol insertion should return ID")
        print(f"✅ Symbol inserted with ID: {symbol_id}")
        
        # Retrieve symbol
        retrieved_symbol = self.symbol_dao.find_by_symbol_exchange("TEST_SYMBOL", "TEST_EXCHANGE")
        self.assertIsNotNone(retrieved_symbol, "Symbol should be retrievable")
        self.assertEqual(retrieved_symbol.symbol, "TEST_SYMBOL")
        print("✅ Symbol retrieved successfully")
        
        # Update symbol
        test_symbol.symbol_name = "Updated Test Symbol"
        updated_id = self.symbol_dao.upsert(test_symbol)
        self.assertEqual(symbol_id, updated_id, "Update should return same ID")
        print("✅ Symbol updated successfully")
        
        # Clean up would require custom delete query
        print("✅ Symbol test completed (cleanup skipped)")
    
    def test_best_bid_offer_operations(self):
        """Test best bid offer CRUD operations."""
        print("\n=== Testing Best Bid Offer Operations ===")
        
        # Create test BBO
        test_bbo = BestBidOffer(
            symbol="TEST_BBO",
            exchange="TEST_EXCHANGE",
            bid_price=Decimal('100.50'),
            bid_size=10,
            ask_price=Decimal('100.60'),
            ask_size=15,
            market_timestamp=datetime.now()
        )
        
        # Insert BBO
        bbo_id = self.bbo_dao.insert(test_bbo)
        self.assertIsNotNone(bbo_id, "BBO insertion should return ID")
        print(f"✅ BBO inserted with ID: {bbo_id}")
        
        # Retrieve BBO
        retrieved_bbos = self.bbo_dao.find_by_symbol("TEST_BBO")
        self.assertTrue(len(retrieved_bbos) > 0, "BBO should be retrievable")
        retrieved_bbo = retrieved_bbos[0]
        self.assertEqual(retrieved_bbo.symbol, "TEST_BBO")
        self.assertEqual(retrieved_bbo.bid_price, Decimal('100.50'))
        print("✅ BBO retrieved successfully")
        
        # Clean up would require custom delete query
        print("✅ BBO test completed (cleanup skipped)")
    
    def test_user_session_operations(self):
        """Test user session operations."""
        print("\n=== Testing User Session Operations ===")
        
        # Create test session
        test_session = UserSession(
            unique_user_id="test_user_123",
            fcm_id="TEST_FCM",
            ib_id="TEST_IB",
            heartbeat_interval=60.0,
            login_timestamp=datetime.now(),
            is_active=True,
            session_info={"test": "data"}
        )
        
        # Insert session
        session_id = self.session_dao.insert(test_session)
        self.assertIsNotNone(session_id, "Session insertion should return ID")
        print(f"✅ Session inserted with ID: {session_id}")
        
        # Note: UserSessionDAO doesn't have find_by_user_id method
        # Just verify the insert returned a valid ID
        self.assertIsInstance(session_id, int, "Session insert should return integer ID")
        print("✅ Session insert verified (no retrieval method available)")
        
        # Clean up would require custom delete query
        print("✅ Session test completed (cleanup skipped)")


class TestDatabasePerformance(unittest.TestCase):
    """Test database performance and bulk operations."""
    
    def setUp(self):
        """Set up test environment."""
        if not DATABASE_AVAILABLE:
            self.skipTest("Database components not available")
        
        create_all_tables()
        self.symbol_dao = SymbolDAO()
    
    def test_bulk_symbol_operations(self):
        """Test bulk symbol insertion and retrieval."""
        print("\n=== Testing Bulk Operations ===")
        
        # Create multiple test symbols
        test_symbols = []
        for i in range(50):
            symbol = Symbol(
                symbol=f"BULK_TEST_{i}",
                exchange="BULK_EXCHANGE",
                symbol_name=f"Bulk Test Symbol {i}",
                product_code="BULK",
                instrument_type="FUTURE"
            )
            test_symbols.append(symbol)
        
        # Insert symbols
        start_time = datetime.now()
        inserted_ids = []
        for symbol in test_symbols:
            symbol_id = self.symbol_dao.upsert(symbol)
            inserted_ids.append(symbol_id)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.assertEqual(len(inserted_ids), 50, "All symbols should be inserted")
        print(f"✅ Bulk insert completed in {duration:.2f} seconds")
        
        # Retrieve symbols by exchange
        retrieved_symbols = self.symbol_dao.find_by_exchange("BULK_EXCHANGE")
        self.assertGreaterEqual(len(retrieved_symbols), 50, "All symbols should be retrievable")
        print(f"✅ Retrieved {len(retrieved_symbols)} symbols")
        
        # Clean up would require custom delete queries
        print("✅ Bulk test completed (cleanup skipped)")


def run_database_tests():
    """Run all database tests."""
    print("🧪 Running Consolidated Database Tests")
    print("=" * 60)
    
    if not DATABASE_AVAILABLE:
        print("❌ Database components not available. Skipping database tests.")
        return False
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestDatabaseConnection))
    suite.addTest(unittest.makeSuite(TestDatabaseCRUD))
    suite.addTest(unittest.makeSuite(TestDatabasePerformance))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 Tests run: {result.testsRun}")
    print(f"✅ Successes: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"💥 Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n🔍 Failures:")
        for test, error in result.failures:
            print(f"  - {test}: {error}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    return len(result.failures) == 0 and len(result.errors) == 0


if __name__ == "__main__":
    success = run_database_tests()
    sys.exit(0 if success else 1)