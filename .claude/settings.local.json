{"permissions": {"allow": ["<PERSON><PERSON>(protoc:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python3:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(cp:*)", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(cat:*)", "Bash(rg:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "Bash(PYTHONPATH=/home/<USER>/Documents/Projects/RITHMIC_API_JUNE_17/RProtocolAPI.********/src python3 test_optimized_websockets.py)", "mcp__server-git__git_status", "mcp__filesystem__list_directory", "mcp__sequential-thinking__sequentialthinking", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(pip install:*)", "<PERSON><PERSON>(mysql:*)", "Bash(PYTHONPATH=/home/<USER>/Documents/Projects/RITHMIC_API_JUNE_17/RProtocolAPI.******** python3 -c \"\nimport sys\nsys.path.insert(0, '.')\nfrom dotenv import load_dotenv\nload_dotenv()\n\n# Test database import\ntry:\n    from src.database.database_manager import get_database_manager\n    print('✅ Database manager import successful')\n    \n    db = get_database_manager()\n    if db.test_connection():\n        print('✅ Database connection successful')\n    else:\n        print('❌ Database connection failed')\n        \nexcept Exception as e:\n    print(f'❌ Database test failed: {e}')\n\")", "<PERSON><PERSON>(sed:*)", "Bash(PYTHONPATH=src python3 tests/test_core.py)", "Bash(PYTHONPATH=. python3 -m src.scripts.test_authentication)", "Bash(PYTHONPATH=. timeout 30s python3 -m src.scripts.test_authentication)", "Bash(PYTHONPATH=. python3 tests/test_core.py)", "Bash(PYTHONPATH=. timeout 60s python3 -m src.scripts.find_front_month_contract)", "Bash(PYTHONPATH=. timeout 20s python3 -m src.scripts.subscribe_level1_data 10)", "Bash(PYTHONPATH=. timeout 20s python3 -m src.scripts.subscribe_depth_by_order 10)", "Bash(PYTHONPATH=. timeout 15s python3 -m src.scripts.subscribe_depth_by_order 8)", "Bash(PYTHONPATH=. timeout 15s python3 examples/basic_usage.py)", "Bash(PYTHONPATH=. timeout 15s python3 examples/advanced_usage.py)", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__directory_tree", "mcp__filesystem__search_files", "mcp__filesystem__read_file", "<PERSON><PERSON>(pkill:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(env)", "Bash(git checkout:*)", "mcp__filesystem__read_multiple_files", "Bash(RITHMIC_USER=\"INVALID_USER\" RITHMIC_PASSWORD=\"INVALID_PASSWORD\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(unset:*)", "Bash(SEARCH_EXCHANGE=\"\" SEARCH_PRODUCT_CODE=\"\" SEARCH_PATTERN=\"*\" SEARCH_PATTERN_TYPE=2 timeout 30s python3 search_contracts.py)", "Bash(SEARCH_EXCHANGE=\"CME\" SEARCH_PRODUCT_CODE=\"ES\" SEARCH_PATTERN=\"ES*\" timeout 30s python3 search_contracts.py)", "Bash(SYMBOL=\"AAPL\" EXCHANGE=\"NASDAQ\" timeout 15s python3 level1_bbo_trades.py)", "Bash(SYMBOL=\"AAPL\" EXCHANGE=\"NYSE\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"BTC-USD\" EXCHANGE=\"BATS\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"NQZ5\" EXCHANGE=\"CME\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"CLZ5\" EXCHANGE=\"NYMEX\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"ZNZ5\" EXCHANGE=\"CBOT\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"ESZ5\" EXCHANGE=\"INVALID_EXCHANGE\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"YMZ5\" EXCHANGE=\"CME\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"RTYZ5\" EXCHANGE=\"CME\" timeout 15s python3 -u level1_bbo_trades.py)", "Bash(SYMBOL=\"RTYZ5\" EXCHANGE=\"CME\" timeout 10s python3 -u level1_bbo_trades.py)", "Bash(SEARCH_PATTERN=\"RTY*\" timeout 10s python3 search_contracts.py)", "Bash(SYMBOL=\"AAPL\" EXCHANGE=\"NYSE\" timeout 10s python3 -u level1_bbo_trades.py)", "mcp__server-git__git_log", "mcp__server-git__git_show", "Bash(RITHMIC_USER=\"INVALID_USER\" RITHMIC_PASSWORD=\"INVALID_PASS\" python3 discover_systems.py)", "Bash(RITHMIC_USER=\"INVALID_USER\" RITHMIC_PASSWORD=\"INVALID_PASS\" python3 search/search_contracts.py --pattern=\"ES\")", "Bash(HIST_SYMBOL=\"\" HIST_EXCHANGE=\"\" python3 historical/historical_volume_bars.py --symbol=\"\" --exchange=\"\")", "Bash(tree:*)", "<PERSON><PERSON>(test:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "<PERSON><PERSON>(tail:*)", "Bash(done)", "<PERSON><PERSON>(curl:*)", "Bash(timedatectl:*)", "Bash(kill:*)", "Bash(./scripts/install_cron.sh:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(PYTHONPATH=\"/home/<USER>/Documents/Projects/RITHMIC_API_JUNE_17/RProtocolAPI.********:/home/<USER>/Documents/Projects/RITHMIC_API_JUNE_17/RProtocolAPI.********/simple-demos\" python3 tests/test_api_connection.py --verbose)", "<PERSON><PERSON>(crontab:*)", "Bash(ps:*)", "mcp__server-time__get_current_time"], "deny": []}}