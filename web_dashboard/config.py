"""
Web Dashboard Configuration
==========================

Configuration management for the web dashboard application.
"""

import os
from pathlib import Path
from pydantic_settings import BaseSettings
from functools import lru_cache
from enum import Enum

class InfraType(Enum):
    """Rithmic infrastructure types."""
    SYSTEM = 1
    TICKER_PLANT = 2
    HISTORY_PLANT = 3
    ORDER_PLANT = 4
    PNL_PLANT = 5
    REPOSITORY_PLANT = 6

class Settings(BaseSettings):
    """Application settings."""
    
    # Server configuration
    port: int = int(os.getenv('WEB_DASHBOARD_PORT', '8000'))
    host: str = "127.0.0.1"  # localhost only for security
    debug: bool = bool(os.getenv('DEBUG', 'False').lower() == 'true')
    
    # Project paths
    project_root: Path = Path(__file__).parent.parent
    simple_demos_path: Path = project_root / "simple-demos"
    src_path: Path = project_root / "src"
    
    # Database configuration (from existing environment)
    mysql_host: str = os.getenv('MYSQL_HOST', 'localhost')
    mysql_port: int = int(os.getenv('MYSQL_PORT', '3306'))
    mysql_user: str = os.getenv('MYSQL_USER', 'root')
    mysql_password: str = os.getenv('MYSQL_PASSWORD', '')
    mysql_database: str = os.getenv('MYSQL_DATABASE', 'rithmic_api')
    
    # Rithmic API configuration (from existing environment)
    rithmic_user: str = os.getenv('RITHMIC_USER', '')
    rithmic_password: str = os.getenv('RITHMIC_PASSWORD', '')
    rithmic_system: str = os.getenv('RITHMIC_SYSTEM', 'Rithmic Paper Trading')
    rithmic_gateway: str = os.getenv('RITHMIC_GATEWAY', 'Chicago Area')
    rithmic_uri: str = os.getenv('RITHMIC_URI', 'wss://rprotocol.rithmic.com:443')
    
    # WebSocket configuration
    ws_max_connections: int = 100
    ws_ping_interval: int = 30
    ws_ping_timeout: int = 10
    
    class Config:
        env_file = ".env"
        case_sensitive = False

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()

def get_database_config() -> dict:
    """Get database configuration dictionary."""
    settings = get_settings()
    return {
        'host': settings.mysql_host,
        'port': settings.mysql_port,
        'user': settings.mysql_user,
        'password': settings.mysql_password,
        'database': settings.mysql_database,
        'pool_size': 5,
        'max_overflow': 10,
        'pool_timeout': 30,
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci',
    }

def get_rithmic_config() -> dict:
    """Get Rithmic API configuration dictionary."""
    settings = get_settings()
    return {
        'user': settings.rithmic_user,
        'password': settings.rithmic_password,
        'system': settings.rithmic_system,
        'gateway': settings.rithmic_gateway,
        'uri': settings.rithmic_uri,
    }

def get_config():
    """Get general configuration object for compatibility."""
    class Config:
        def __init__(self):
            settings = get_settings()
            # Basic settings
            self.host = settings.host
            self.port = settings.port
            self.debug = settings.debug
            
            # Database settings
            self.mysql_host = settings.mysql_host
            self.mysql_port = settings.mysql_port
            self.mysql_user = settings.mysql_user
            self.mysql_password = settings.mysql_password
            self.mysql_database = settings.mysql_database
            
            # Rithmic settings
            self.rithmic_user = settings.rithmic_user
            self.rithmic_password = settings.rithmic_password
            self.rithmic_system = settings.rithmic_system
            self.rithmic_gateway = settings.rithmic_gateway
            self.rithmic_uri = settings.rithmic_uri
            
            # Additional settings for compatibility
            self.message_timeout = 30.0
            self.save_responses = True
            self.max_connections = 100
            
    return Config()