"""
Logs WebSocket Handler
=====================

WebSocket handler for real-time log streaming from processes and system events.
"""

import sys
import json
import asyncio
from pathlib import Path
from typing import Set, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from web_dashboard.config import get_database_config
from web_dashboard.services.data_service import DataService

class LogsConnectionManager:
    """Manager for logs WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.subscriptions: Dict[WebSocket, Dict[str, Any]] = {}
        self.data_service = None
        
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.add(websocket)
        self.subscriptions[websocket] = {
            "log_levels": {"INFO", "WARNING", "ERROR"},
            "processes": set(),
            "components": set(),
            "last_heartbeat": datetime.now()
        }
        
        # Initialize data service
        if not self.data_service:
            try:
                config = get_database_config()
                self.data_service = DataService(config)
            except Exception as e:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Database connection failed: {str(e)}"
                }))
        
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connection",
            "status": "connected",
            "message": "Logs WebSocket connected",
            "timestamp": datetime.now().isoformat()
        }))
        
        # Send recent logs
        await self.send_recent_logs(websocket)
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        self.active_connections.discard(websocket)
        self.subscriptions.pop(websocket, None)
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific client."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception:
            self.disconnect(websocket)
    
    async def broadcast_log(self, log_entry: dict):
        """Broadcast a log entry to subscribed clients."""
        if not self.active_connections:
            return
        
        disconnected = set()
        
        for connection in self.active_connections:
            try:
                subscription = self.subscriptions.get(connection, {})
                
                # Check if client is subscribed to this log
                if self.should_send_log(log_entry, subscription):
                    await connection.send_text(json.dumps({
                        "type": "log",
                        "data": log_entry,
                        "timestamp": datetime.now().isoformat()
                    }))
            except Exception:
                disconnected.add(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)
    
    def should_send_log(self, log_entry: dict, subscription: dict) -> bool:
        """Check if a log entry should be sent to a subscriber."""
        # Check log level filter
        log_level = log_entry.get("level", "INFO")
        if log_level not in subscription.get("log_levels", set()):
            return False
        
        # Check process filter
        process_filter = subscription.get("processes", set())
        if process_filter:
            process_name = log_entry.get("process")
            if process_name and process_name not in process_filter:
                return False
        
        # Check component filter
        component_filter = subscription.get("components", set())
        if component_filter:
            component = log_entry.get("component")
            if component and component not in component_filter:
                return False
        
        return True
    
    async def handle_subscription(self, websocket: WebSocket, data: dict):
        """Handle log subscription requests."""
        try:
            subscription = self.subscriptions.get(websocket, {})
            
            # Update log levels
            if "log_levels" in data:
                subscription["log_levels"] = set(data["log_levels"])
            
            # Update process filter
            if "processes" in data:
                subscription["processes"] = set(data["processes"])
            
            # Update component filter  
            if "components" in data:
                subscription["components"] = set(data["components"])
            
            self.subscriptions[websocket] = subscription
            
            await self.send_personal_message({
                "type": "subscription",
                "status": "updated",
                "filters": {
                    "log_levels": list(subscription["log_levels"]),
                    "processes": list(subscription["processes"]),
                    "components": list(subscription["components"])
                },
                "timestamp": datetime.now().isoformat()
            }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Subscription error: {str(e)}"
            }, websocket)
    
    async def send_recent_logs(self, websocket: WebSocket, limit: int = 50):
        """Send recent log entries to a client."""
        if not self.data_service:
            return
        
        try:
            # Get recent system events (logs)
            query = """
                SELECT event_type as level, component, description as message, 
                       timestamp, details
                FROM system_events 
                ORDER BY timestamp DESC 
                LIMIT %s
            """
            logs = self.data_service.execute_query(query, (limit,))
            
            # Send logs in reverse order (oldest first)
            for log in reversed(logs):
                log_entry = {
                    "level": log.get("level", "INFO"),
                    "component": log.get("component", "system"),
                    "message": log.get("message", ""),
                    "timestamp": log.get("timestamp").isoformat() if log.get("timestamp") else datetime.now().isoformat(),
                    "details": log.get("details", {})
                }
                
                await self.send_personal_message({
                    "type": "log",
                    "data": log_entry,
                    "timestamp": datetime.now().isoformat()
                }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Error fetching recent logs: {str(e)}"
            }, websocket)
    
    async def handle_log_query(self, websocket: WebSocket, data: dict):
        """Handle log query requests."""
        try:
            component = data.get("component")
            level = data.get("level")
            limit = min(data.get("limit", 100), 1000)  # Max 1000 logs
            
            query = "SELECT * FROM system_events"
            conditions = []
            params = []
            
            if component:
                conditions.append("component = %s")
                params.append(component)
            
            if level:
                conditions.append("event_type = %s")
                params.append(level)
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            query += f" ORDER BY timestamp DESC LIMIT {limit}"
            
            logs = self.data_service.execute_query(query, tuple(params))
            
            await self.send_personal_message({
                "type": "log_query_result",
                "query": data,
                "count": len(logs),
                "logs": logs,
                "timestamp": datetime.now().isoformat()
            }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Log query error: {str(e)}"
            }, websocket)

# Global connection manager instance
manager = LogsConnectionManager()

async def handle_logs_connection(websocket: WebSocket):
    """Handle logs WebSocket connection."""
    await manager.connect(websocket)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "subscription":
                await manager.handle_subscription(websocket, message)
            elif message_type == "log_query":
                await manager.handle_log_query(websocket, message)
            elif message_type == "heartbeat":
                manager.subscriptions[websocket]["last_heartbeat"] = datetime.now()
                await manager.send_personal_message({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            elif message_type == "ping":
                await manager.send_personal_message({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            else:
                await manager.send_personal_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                }, websocket)
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        await manager.send_personal_message({
            "type": "error",
            "message": f"WebSocket error: {str(e)}"
        }, websocket)
        manager.disconnect(websocket)

# Function to broadcast logs from external sources
async def broadcast_log_entry(log_entry: dict):
    """Broadcast a log entry to all connected clients."""
    await manager.broadcast_log(log_entry)