"""
Market Data WebSocket Handler
============================

WebSocket handler for real-time market data streaming including
Level 1 BBO/Trades and Level 3 depth-by-order data.
"""

import sys
import json
import asyncio
from pathlib import Path
from typing import Set, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from web_dashboard.config import get_database_config
from web_dashboard.services.data_service import DataService

class MarketDataConnectionManager:
    """Manager for market data WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.subscriptions: Dict[WebSocket, Dict[str, Any]] = {}
        self.data_service = None
        
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.add(websocket)
        self.subscriptions[websocket] = {
            "symbols": set(),
            "data_types": set(),
            "last_heartbeat": datetime.now()
        }
        
        # Initialize data service if needed
        if not self.data_service:
            try:
                config = get_database_config()
                self.data_service = DataService(config)
            except Exception as e:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Database connection failed: {str(e)}"
                }))
        
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connection",
            "status": "connected",
            "message": "Market data WebSocket connected",
            "timestamp": datetime.now().isoformat()
        }))
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        self.active_connections.discard(websocket)
        self.subscriptions.pop(websocket, None)
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific client."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception:
            # Connection might be closed
            self.disconnect(websocket)
    
    async def broadcast(self, message: dict):
        """Broadcast a message to all connected clients."""
        if not self.active_connections:
            return
        
        message_text = json.dumps(message)
        disconnected = set()
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message_text)
            except Exception:
                disconnected.add(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)
    
    async def handle_subscription(self, websocket: WebSocket, data: dict):
        """Handle subscription requests."""
        try:
            action = data.get("action")
            symbol = data.get("symbol")
            exchange = data.get("exchange")
            data_types = data.get("data_types", ["trades", "quotes"])
            
            if not symbol or not exchange:
                await self.send_personal_message({
                    "type": "error",
                    "message": "Symbol and exchange are required for subscription"
                }, websocket)
                return
            
            subscription_key = f"{symbol}@{exchange}"
            
            if action == "subscribe":
                self.subscriptions[websocket]["symbols"].add(subscription_key)
                self.subscriptions[websocket]["data_types"].update(data_types)
                
                await self.send_personal_message({
                    "type": "subscription",
                    "status": "subscribed",
                    "symbol": symbol,
                    "exchange": exchange,
                    "data_types": data_types,
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                
                # Send recent data
                await self.send_recent_data(websocket, symbol, exchange, data_types)
                
            elif action == "unsubscribe":
                self.subscriptions[websocket]["symbols"].discard(subscription_key)
                
                await self.send_personal_message({
                    "type": "subscription",
                    "status": "unsubscribed", 
                    "symbol": symbol,
                    "exchange": exchange,
                    "timestamp": datetime.now().isoformat()
                }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Subscription error: {str(e)}"
            }, websocket)
    
    async def send_recent_data(self, websocket: WebSocket, symbol: str, exchange: str, data_types: list):
        """Send recent market data for a symbol."""
        if not self.data_service:
            return
        
        try:
            # Send recent trades
            if "trades" in data_types:
                trade_query = """
                    SELECT * FROM last_trades 
                    WHERE symbol = %s AND exchange = %s 
                    ORDER BY timestamp DESC 
                    LIMIT 10
                """
                trades = self.data_service.execute_query(trade_query, (symbol, exchange))
                
                if trades:
                    await self.send_personal_message({
                        "type": "market_data",
                        "data_type": "trades",
                        "symbol": symbol,
                        "exchange": exchange,
                        "data": trades,
                        "timestamp": datetime.now().isoformat()
                    }, websocket)
            
            # Send recent quotes
            if "quotes" in data_types:
                quote_query = """
                    SELECT * FROM best_bid_offer 
                    WHERE symbol = %s AND exchange = %s 
                    ORDER BY timestamp DESC 
                    LIMIT 10
                """
                quotes = self.data_service.execute_query(quote_query, (symbol, exchange))
                
                if quotes:
                    await self.send_personal_message({
                        "type": "market_data",
                        "data_type": "quotes",
                        "symbol": symbol,
                        "exchange": exchange,
                        "data": quotes,
                        "timestamp": datetime.now().isoformat()
                    }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Error fetching recent data: {str(e)}"
            }, websocket)
    
    async def handle_heartbeat(self, websocket: WebSocket):
        """Handle heartbeat from client."""
        self.subscriptions[websocket]["last_heartbeat"] = datetime.now()
        await self.send_personal_message({
            "type": "heartbeat",
            "timestamp": datetime.now().isoformat()
        }, websocket)
    
    async def send_market_update(self, symbol: str, exchange: str, data_type: str, data: dict):
        """Send market data update to subscribed clients."""
        subscription_key = f"{symbol}@{exchange}"
        
        for websocket, subscription in self.subscriptions.items():
            if (subscription_key in subscription["symbols"] and 
                data_type in subscription["data_types"]):
                
                await self.send_personal_message({
                    "type": "market_data",
                    "data_type": data_type,
                    "symbol": symbol,
                    "exchange": exchange,
                    "data": data,
                    "timestamp": datetime.now().isoformat()
                }, websocket)

# Global connection manager instance
manager = MarketDataConnectionManager()

async def handle_market_data_connection(websocket: WebSocket):
    """Handle market data WebSocket connection."""
    await manager.connect(websocket)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "subscription":
                await manager.handle_subscription(websocket, message)
            elif message_type == "heartbeat":
                await manager.handle_heartbeat(websocket)
            elif message_type == "ping":
                await manager.send_personal_message({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            else:
                await manager.send_personal_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                }, websocket)
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        await manager.send_personal_message({
            "type": "error",
            "message": f"WebSocket error: {str(e)}"
        }, websocket)
        manager.disconnect(websocket)