"""
Processes WebSocket Handler
==========================

WebSocket handler for real-time process status updates and monitoring.
"""

import sys
import json
import asyncio
from pathlib import Path
from typing import Set, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from web_dashboard.services.process_manager import ProcessManager

class ProcessesConnectionManager:
    """Manager for process monitoring WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.subscriptions: Dict[WebSocket, Dict[str, Any]] = {}
        self.process_manager = ProcessManager()
        self.monitoring_task = None
        
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.add(websocket)
        self.subscriptions[websocket] = {
            "processes": set(),  # Specific process IDs to monitor
            "all_processes": True,  # Monitor all processes
            "last_heartbeat": datetime.now()
        }
        
        # Start monitoring task if not already running
        if not self.monitoring_task or self.monitoring_task.done():
            self.monitoring_task = asyncio.create_task(self.monitor_processes())
        
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connection",
            "status": "connected",
            "message": "Process monitoring WebSocket connected",
            "timestamp": datetime.now().isoformat()
        }))
        
        # Send current process status
        await self.send_process_status(websocket)
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        self.active_connections.discard(websocket)
        self.subscriptions.pop(websocket, None)
        
        # Stop monitoring if no connections
        if not self.active_connections and self.monitoring_task:
            self.monitoring_task.cancel()
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific client."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception:
            self.disconnect(websocket)
    
    async def broadcast_process_update(self, process_info: dict):
        """Broadcast process update to subscribed clients."""
        if not self.active_connections:
            return
        
        process_id = process_info.get("id")
        disconnected = set()
        
        for connection in self.active_connections:
            try:
                subscription = self.subscriptions.get(connection, {})
                
                # Check if client is subscribed to this process
                if (subscription.get("all_processes") or 
                    process_id in subscription.get("processes", set())):
                    
                    await connection.send_text(json.dumps({
                        "type": "process_update",
                        "data": process_info,
                        "timestamp": datetime.now().isoformat()
                    }))
            except Exception:
                disconnected.add(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)
    
    async def send_process_status(self, websocket: WebSocket):
        """Send current process status to a client."""
        try:
            # Get all processes
            processes = self.process_manager.get_all_processes()
            
            # Get process statistics
            stats = self.process_manager.get_process_stats()
            
            await self.send_personal_message({
                "type": "process_status",
                "processes": processes,
                "stats": stats,
                "timestamp": datetime.now().isoformat()
            }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Error fetching process status: {str(e)}"
            }, websocket)
    
    async def handle_subscription(self, websocket: WebSocket, data: dict):
        """Handle process subscription requests."""
        try:
            subscription = self.subscriptions.get(websocket, {})
            
            # Update process filter
            if "processes" in data:
                subscription["processes"] = set(data["processes"])
            
            # Update all processes flag
            if "all_processes" in data:
                subscription["all_processes"] = bool(data["all_processes"])
            
            self.subscriptions[websocket] = subscription
            
            await self.send_personal_message({
                "type": "subscription",
                "status": "updated",
                "filters": {
                    "processes": list(subscription["processes"]),
                    "all_processes": subscription["all_processes"]
                },
                "timestamp": datetime.now().isoformat()
            }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Subscription error: {str(e)}"
            }, websocket)
    
    async def handle_process_control(self, websocket: WebSocket, data: dict):
        """Handle process control requests."""
        try:
            action = data.get("action")
            process_id = data.get("process_id")
            
            if not action:
                await self.send_personal_message({
                    "type": "error",
                    "message": "Action is required"
                }, websocket)
                return
            
            if action == "start":
                script_name = data.get("script_name")
                args = data.get("args", [])
                
                if not script_name:
                    await self.send_personal_message({
                        "type": "error",
                        "message": "Script name is required for start action"
                    }, websocket)
                    return
                
                new_process_id = self.process_manager.start_process(script_name, args)
                
                await self.send_personal_message({
                    "type": "process_control_result",
                    "action": "start",
                    "success": True,
                    "process_id": new_process_id,
                    "message": f"Process {script_name} started successfully",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            
            elif action in ["stop", "restart"]:
                if not process_id:
                    await self.send_personal_message({
                        "type": "error",
                        "message": f"Process ID is required for {action} action"
                    }, websocket)
                    return
                
                if action == "stop":
                    success = self.process_manager.stop_process(process_id)
                else:  # restart
                    success = self.process_manager.restart_process(process_id)
                
                await self.send_personal_message({
                    "type": "process_control_result",
                    "action": action,
                    "process_id": process_id,
                    "success": success,
                    "message": f"Process {action} {'successful' if success else 'failed'}",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            
            else:
                await self.send_personal_message({
                    "type": "error",
                    "message": f"Unknown action: {action}"
                }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Process control error: {str(e)}"
            }, websocket)
    
    async def handle_process_logs(self, websocket: WebSocket, data: dict):
        """Handle process log requests."""
        try:
            process_id = data.get("process_id")
            lines = min(data.get("lines", 50), 500)  # Max 500 lines
            
            if not process_id:
                await self.send_personal_message({
                    "type": "error",
                    "message": "Process ID is required for log request"
                }, websocket)
                return
            
            logs = self.process_manager.get_process_logs(process_id, lines)
            
            await self.send_personal_message({
                "type": "process_logs",
                "process_id": process_id,
                "logs": logs,
                "timestamp": datetime.now().isoformat()
            }, websocket)
        
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"Log request error: {str(e)}"
            }, websocket)
    
    async def monitor_processes(self):
        """Background task to monitor process changes."""
        last_process_states = {}
        
        while self.active_connections:
            try:
                # Get current process states
                current_processes = self.process_manager.get_all_processes()
                current_states = {proc["id"]: proc for proc in current_processes}
                
                # Check for changes
                for process_id, process_info in current_states.items():
                    last_info = last_process_states.get(process_id)
                    
                    # Send update if process is new or status changed
                    if (not last_info or 
                        last_info.get("status") != process_info.get("status") or
                        abs((last_info.get("cpu_percent", 0) or 0) - (process_info.get("cpu_percent", 0) or 0)) > 5):
                        
                        await self.broadcast_process_update(process_info)
                
                # Check for removed processes
                for process_id in last_process_states:
                    if process_id not in current_states:
                        await self.broadcast_process_update({
                            "id": process_id,
                            "status": "removed",
                            "timestamp": datetime.now().isoformat()
                        })
                
                last_process_states = current_states
                
                # Wait before next check
                await asyncio.sleep(2)  # Check every 2 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Process monitoring error: {e}")
                await asyncio.sleep(5)  # Wait longer on error

# Global connection manager instance
manager = ProcessesConnectionManager()

async def handle_processes_connection(websocket: WebSocket):
    """Handle processes WebSocket connection."""
    await manager.connect(websocket)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "subscription":
                await manager.handle_subscription(websocket, message)
            elif message_type == "process_control":
                await manager.handle_process_control(websocket, message)
            elif message_type == "process_logs":
                await manager.handle_process_logs(websocket, message)
            elif message_type == "refresh":
                await manager.send_process_status(websocket)
            elif message_type == "heartbeat":
                manager.subscriptions[websocket]["last_heartbeat"] = datetime.now()
                await manager.send_personal_message({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            elif message_type == "ping":
                await manager.send_personal_message({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            else:
                await manager.send_personal_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                }, websocket)
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        await manager.send_personal_message({
            "type": "error",
            "message": f"WebSocket error: {str(e)}"
        }, websocket)
        manager.disconnect(websocket)