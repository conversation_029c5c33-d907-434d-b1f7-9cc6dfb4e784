# Professional Futures Trading Web Dashboard

A comprehensive web-based dashboard that replaces the native GUI interface, providing modern web access to all Rithmic API project functionality through a professional trading interface.

## Quick Start

```bash
# Navigate to web dashboard directory
cd web_dashboard/

# Install dependencies
pip install -r requirements.txt

# Start the dashboard server
python main.py

# Access dashboard at http://localhost:8000
```

## Overview

The web dashboard provides a modern, cross-platform interface for monitoring, configuring, and controlling all aspects of the Rithmic API project. It features a professional dark theme designed specifically for trading applications.

## Dashboard Features

### Overview Section
- **System Statistics**: Real-time monitoring of key metrics
- **Process Status**: Live indicators for running processes
- **Database Status**: Connection health monitoring
- **Interactive Charts**: Market activity and system performance visualization

### Market Data Section
- **Real-time Subscription Interface**: Symbol and exchange selection
- **Live Data Display**: Last trades and best bid/offer tables
- **Streaming Controls**: Subscribe/unsubscribe to market data feeds
- **Multi-Exchange Support**: CME, CBOT, NYMEX, COMEX

### Database Management
- **Table Browser**: Complete access to all 19+ database tables
- **Interactive Querying**: Advanced filtering with WHERE clauses
- **Pagination**: Handle large datasets efficiently
- **CSV Export**: Download filtered data for analysis
- **Connection Testing**: Real-time database health checks

### Process Management
- **Script Control**: Start/stop/monitor simple-demos scripts
- **Status Monitoring**: Real-time process health and resource usage
- **Log Viewing**: Access individual process logs
- **Auto-Discovery**: Automatic detection of available scripts from simple-demos directory

### Configuration Management
- **Database Settings**: Host, port, credentials, database selection
- **Rithmic API Config**: User, system, gateway, URI settings
- **Environment Variables**: Complete system environment display
- **Validation**: Test configurations before applying

### System Logs
- **Real-time Streaming**: Live log updates from system_events table
- **Level Filtering**: Filter by INFO, WARNING, ERROR levels
- **Component Filtering**: Focus on specific system components
- **Auto-scroll**: Automatic scrolling with manual override

## API Reference

### Database API (`/api/database/`)

```bash
# Get database connection status
GET /api/database/status
Response: {"connected": true, "host": "...", "database": "..."}

# List all tables with row counts
GET /api/database/tables
Response: [{"name": "symbols", "row_count": 173}, ...]

# Query table data with pagination
POST /api/database/tables/{table_name}/data
Body: {
  "table": "symbols",
  "page": 0,
  "page_size": 100,
  "where_clause": "exchange = 'CME'"
}

# Export table data as CSV
GET /api/database/export?table=symbols&format=csv
```

### Process API (`/api/processes/`)

```bash
# List all running processes
GET /api/processes/
Response: [{"id": "...", "name": "...", "status": "running", ...}]

# Get available scripts
GET /api/processes/scripts/available
Response: {"scripts": [{"name": "level1_bbo_trades", "category": "Real-time", ...}]}

# Start a new process
POST /api/processes/start
Body: {"script_name": "level1_bbo_trades", "args": []}

# Stop a running process
POST /api/processes/{process_id}/stop

# Get process logs
GET /api/processes/{process_id}/logs
```

### Configuration API (`/api/config/`)

```bash
# Get database configuration
GET /api/config/database
Response: {"host": "...", "port": 3306, "user": "...", ...}

# Get Rithmic API configuration
GET /api/config/rithmic
Response: {"user": "...", "system": "...", "gateway": "...", ...}

# Get environment variables
GET /api/config/environment
Response: {"environment_variables": [...]}
```

### Market Data API (`/api/market_data/`)

```bash
# Get market data statistics
GET /api/market_data/stats
Response: {"total_symbols": 173, "active_symbols": 5, ...}

# Search symbols
GET /api/market_data/symbols?search=ES
```

### Health Check

```bash
# Server health status
GET /health
Response: {"status": "healthy", "service": "Professional Futures Trading Dashboard", "version": "1.0.0"}
```

## WebSocket Endpoints (In Development)

Real-time streaming endpoints for live data updates:

```bash
ws://localhost:8000/ws/market_data   # Real-time market data streaming
ws://localhost:8000/ws/logs          # Real-time log streaming  
ws://localhost:8000/ws/processes     # Real-time process updates
```

*Note: WebSocket functionality is currently in development due to dependency compatibility issues. All functionality is available via REST API with periodic polling.*

## Technical Architecture

### Backend
- **Framework**: FastAPI with async/await support
- **Database**: MySQL integration via existing DatabaseManager
- **Authentication**: Environment-based configuration
- **API Design**: RESTful endpoints with JSON responses
- **Process Management**: Subprocess control with monitoring

### Frontend
- **Interface**: Modern JavaScript (ES6+) with native APIs
- **Styling**: Professional dark theme with CSS Grid/Flexbox
- **Charts**: Chart.js for real-time data visualization
- **WebSocket**: Native WebSocket API for real-time updates
- **Responsive**: Mobile-friendly responsive design

### File Structure

```
web_dashboard/
├── main.py                 # FastAPI application entry point
├── config.py              # Configuration management with pydantic
├── requirements.txt       # Python dependencies
├── README.md              # This documentation file
│
├── api/                   # REST API endpoints
│   ├── __init__.py
│   ├── database.py       # Database management API
│   ├── processes.py      # Process control API
│   ├── configuration.py  # Configuration API
│   └── market_data.py    # Market data API
│
├── services/             # Business logic layer
│   ├── __init__.py
│   ├── data_service.py   # Database operations wrapper
│   └── process_manager.py # Process management with threading
│
├── websockets/           # WebSocket handlers
│   ├── __init__.py
│   ├── market_data_ws.py # Market data streaming
│   ├── logs_ws.py        # Log streaming
│   └── processes_ws.py   # Process status streaming
│
└── static/               # Frontend assets
    ├── index.html        # Main dashboard page
    ├── css/
    │   └── dashboard.css # Professional trading interface styling
    └── js/               # JavaScript modules
        ├── dashboard.js  # Main dashboard logic and navigation
        ├── websockets.js # WebSocket connection management
        ├── database.js   # Database browser functionality
        ├── processes.js  # Process management interface
        └── charts.js     # Chart.js integration and management
```

## Integration with Existing System

The web dashboard seamlessly integrates with all existing project components:

- **Database Access**: Uses existing DatabaseManager for all database operations
- **Script Management**: Manages all simple-demos scripts through process API
- **Configuration**: Reads from existing environment variables and config files
- **Data Access**: Full access to all database tables and historical data
- **Credentials**: Compatible with existing Rithmic API credentials and settings

## Migration from Native GUI

The web dashboard provides enhanced functionality over the native GUI:

### Advantages
- **Cross-platform**: Works on any device with a web browser
- **Remote Access**: Can be accessed from any machine on the network
- **Modern Interface**: Professional trading-focused design with dark theme
- **Enhanced Features**: Advanced filtering, export capabilities, real-time charts
- **API Access**: Complete programmatic access to all functionality
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **No Installation**: No client-side software installation required

### Feature Comparison
| Feature | Native GUI | Web Dashboard |
|---------|------------|---------------|
| Database Access | ✅ | ✅ Enhanced with filtering |
| Process Control | ✅ | ✅ Enhanced with monitoring |
| Configuration | ✅ | ✅ Enhanced with validation |
| Market Data | ✅ | ✅ Enhanced with charts |
| Remote Access | ❌ | ✅ |
| Mobile Support | ❌ | ✅ |
| API Access | ❌ | ✅ |
| Data Export | ❌ | ✅ |

## Configuration

### Environment Variables

The dashboard reads configuration from environment variables:

```bash
# Database Configuration
MYSQL_HOST=**************
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=debian
MYSQL_DATABASE=rithmic_api

# Rithmic API Configuration
RITHMIC_USER=PP-013155
RITHMIC_PASSWORD=b7neA8k6JA
RITHMIC_SYSTEM="Rithmic Paper Trading"
RITHMIC_GATEWAY="Chicago Area"
RITHMIC_URI=wss://rprotocol.rithmic.com:443

# Dashboard Configuration
WEB_DASHBOARD_PORT=8000
DEBUG=false
```

### Server Configuration

Default server settings in `config.py`:
- **Host**: 127.0.0.1 (localhost only for security)
- **Port**: 8000 (configurable via environment)
- **CORS**: Enabled for development
- **Static Files**: Served from `/static/` path
- **WebSocket**: Configurable connection limits

## Security Considerations

### Production Deployment
- **Host Binding**: Default localhost-only binding for security
- **Environment Variables**: Sensitive data stored in environment, not code
- **CORS**: Configure appropriately for production environment
- **HTTPS**: Consider reverse proxy with SSL/TLS for production
- **Authentication**: Add authentication layer for multi-user environments

### Data Protection
- **Credentials**: Database passwords hidden in API responses
- **Logging**: Sensitive data excluded from logs
- **Network**: Default configuration prevents external access

## Troubleshooting

### Server Won't Start

```bash
# Check Python dependencies
pip install -r requirements.txt

# Check port availability
netstat -ln | grep :8000
# Or use: lsof -i :8000

# Check for specific errors
python main.py
```

Common issues:
- **Port in use**: Another service using port 8000
- **Missing dependencies**: Run `pip install -r requirements.txt`
- **Python version**: Requires Python 3.8+

### Database Connection Issues

```bash
# Test database connection via API
curl http://localhost:8000/api/database/status

# Check environment variables
echo $MYSQL_HOST $MYSQL_USER $MYSQL_PASSWORD

# Test direct MySQL connection
mysql -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE
```

Common issues:
- **Wrong credentials**: Check environment variables
- **Network connectivity**: Verify host and port
- **Database permissions**: Ensure user has required privileges

### WebSocket Connection Issues

WebSocket functionality is currently in development due to dependency compatibility issues between uvicorn and websockets libraries. 

**Current Status**: 
- WebSocket handlers implemented and ready
- REST API provides complete functionality
- Frontend uses periodic polling for real-time updates

**Workaround**:
The dashboard automatically falls back to REST API polling, providing the same functionality with slightly higher latency.

### Frontend Issues

```bash
# Check static file serving
curl -I http://localhost:8000/static/css/dashboard.css

# Check browser console for JavaScript errors
# Open browser dev tools (F12) and check Console tab

# Verify API connectivity
curl http://localhost:8000/health
```

### Performance Issues

For large datasets:
- **Pagination**: Use page_size parameter to limit results
- **Filtering**: Apply WHERE clauses to reduce data volume
- **Browser**: Close unused browser tabs to free memory
- **Database**: Monitor MySQL performance and connections

## Development

### Adding New Features

1. **Backend**: Add new API endpoints in `api/` directory
2. **Frontend**: Add JavaScript functionality in `static/js/`
3. **Styling**: Modify `static/css/dashboard.css` for UI changes
4. **WebSockets**: Add real-time features in `websockets/` directory

### Code Style
- **Python**: Follow PEP 8 guidelines
- **JavaScript**: Use ES6+ features, avoid jQuery dependencies
- **CSS**: Use CSS custom properties for theming
- **API**: Follow REST conventions

### Testing
```bash
# Test API endpoints
curl http://localhost:8000/api/database/status
curl http://localhost:8000/api/processes/

# Test frontend
# Open http://localhost:8000 in browser

# Test WebSocket (when enabled)
# Use browser dev tools WebSocket inspector
```

## Support

### Getting Help
- **Documentation**: This README file
- **API Reference**: Built-in FastAPI docs at `/docs` endpoint
- **Logs**: Check server output for error messages
- **Browser Console**: Check for JavaScript errors in browser dev tools

### Reporting Issues
When reporting issues, include:
- Server logs output
- Browser console errors (if frontend issue)
- Steps to reproduce
- Environment details (OS, Python version, browser)

## Version History

- **v1.0.0**: Initial release with complete dashboard functionality
  - REST API implementation
  - Professional web interface
  - Database management
  - Process control
  - Configuration management
  - Real-time charts
  - Comprehensive documentation

---

*Professional Futures Trading Web Dashboard - Modern web interface for Rithmic API project management*