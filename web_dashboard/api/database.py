"""
Database Management API
======================

REST API endpoints for database operations including table browsing,
data querying, schema inspection, and data export functionality.
"""

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
import csv
import io
from fastapi.responses import StreamingResponse

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from web_dashboard.config import get_database_config
from web_dashboard.services.data_service import DataService

router = APIRouter()

# Pydantic models
class DatabaseStatus(BaseModel):
    connected: bool
    host: str
    database: str
    connection_info: str

class TableInfo(BaseModel):
    name: str
    row_count: int

class ColumnInfo(BaseModel):
    name: str
    type: str
    nullable: bool
    key: str
    default: Optional[str]

class QueryRequest(BaseModel):
    query: str
    limit: Optional[int] = 1000

class FilterRequest(BaseModel):
    table: str
    where_clause: Optional[str] = None
    page: int = 0
    page_size: int = 100

def get_data_service():
    """Dependency to get DataService instance."""
    try:
        config = get_database_config()
        return DataService(config)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database configuration error: {str(e)}")

@router.get("/status", response_model=DatabaseStatus)
async def get_database_status(data_service: DataService = Depends(get_data_service)):
    """Get database connection status."""
    try:
        if data_service.test_connection():
            config = get_database_config()
            return DatabaseStatus(
                connected=True,
                host=config['host'],
                database=config['database'],
                connection_info=f"{config['database']}@{config['host']}:{config['port']}"
            )
        else:
            raise HTTPException(status_code=503, detail="Database connection failed")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tables", response_model=List[TableInfo])
async def get_tables(data_service: DataService = Depends(get_data_service)):
    """Get list of database tables with row counts."""
    try:
        tables = data_service.get_tables()
        result = []
        for table in tables:
            table_name = table.get('table_name') or table.get('TABLE_NAME')
            if table_name:
                row_count = data_service.get_table_row_count(table_name)
                result.append(TableInfo(name=table_name, row_count=row_count))
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching tables: {str(e)}")

@router.get("/tables/{table_name}/schema", response_model=List[ColumnInfo])
async def get_table_schema(table_name: str, data_service: DataService = Depends(get_data_service)):
    """Get schema information for a specific table."""
    try:
        columns = data_service.get_table_columns(table_name)
        result = []
        for col in columns:
            result.append(ColumnInfo(
                name=col.get('column_name') or col.get('COLUMN_NAME'),
                type=col.get('data_type') or col.get('DATA_TYPE'),
                nullable=col.get('is_nullable') or col.get('IS_NULLABLE') == 'YES',
                key=col.get('column_key') or col.get('COLUMN_KEY') or '',
                default=col.get('column_default') or col.get('COLUMN_DEFAULT')
            ))
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching schema: {str(e)}")

@router.post("/tables/{table_name}/data")
async def get_table_data(
    table_name: str,
    filter_req: FilterRequest,
    data_service: DataService = Depends(get_data_service)
):
    """Get paginated table data with optional filtering."""
    try:
        offset = filter_req.page * filter_req.page_size
        data = data_service.get_table_data(
            table_name,
            limit=filter_req.page_size,
            offset=offset,
            where_clause=filter_req.where_clause
        )
        
        # Get total count for pagination
        total_count = data_service.get_table_row_count(
            table_name, 
            where_clause=filter_req.where_clause
        )
        total_pages = (total_count + filter_req.page_size - 1) // filter_req.page_size
        
        return {
            "data": data,
            "pagination": {
                "page": filter_req.page,
                "page_size": filter_req.page_size,
                "total_pages": total_pages,
                "total_rows": total_count
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching data: {str(e)}")

@router.post("/query")
async def execute_query(
    query_req: QueryRequest,
    data_service: DataService = Depends(get_data_service)
):
    """Execute a custom SQL query."""
    try:
        # Basic validation - only allow SELECT queries for security
        query_lower = query_req.query.lower().strip()
        if not query_lower.startswith('select'):
            raise HTTPException(status_code=400, detail="Only SELECT queries are allowed")
        
        # Add LIMIT if not present
        if 'limit' not in query_lower and query_req.limit:
            query = f"{query_req.query} LIMIT {query_req.limit}"
        else:
            query = query_req.query
            
        result = data_service.execute_query(query)
        return {
            "query": query,
            "row_count": len(result),
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Query execution error: {str(e)}")

@router.get("/tables/{table_name}/export")
async def export_table_csv(
    table_name: str,
    where_clause: Optional[str] = Query(None),
    data_service: DataService = Depends(get_data_service)
):
    """Export table data to CSV format."""
    try:
        # Get all data for export
        data = data_service.get_table_data(
            table_name,
            where_clause=where_clause,
            limit=None  # No limit for export
        )
        
        if not data:
            raise HTTPException(status_code=404, detail="No data found to export")
        
        # Create CSV content
        output = io.StringIO()
        fieldnames = list(data[0].keys())
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
        csv_content = output.getvalue()
        
        # Return as streaming response
        def generate():
            yield csv_content
        
        filename = f"{table_name}_export.csv"
        headers = {"Content-Disposition": f"attachment; filename={filename}"}
        
        return StreamingResponse(
            io.BytesIO(csv_content.encode()).read,
            media_type="text/csv",
            headers=headers
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Export error: {str(e)}")