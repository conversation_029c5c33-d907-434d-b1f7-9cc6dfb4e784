"""
Process Management API
======================

REST API endpoints for managing and monitoring simple-demos scripts
and other data collection processes.
"""

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from web_dashboard.services.process_manager import ProcessManager

router = APIRouter()

# Pydantic models
class ProcessInfo(BaseModel):
    id: str
    name: str
    script_path: str
    status: str  # running, stopped, error
    pid: Optional[int]
    start_time: Optional[str]
    uptime: Optional[str]
    cpu_percent: Optional[float]
    memory_mb: Optional[float]

class ProcessStartRequest(BaseModel):
    script_name: str
    args: Optional[List[str]] = []

class ProcessLog(BaseModel):
    timestamp: str
    level: str
    message: str

class ProcessStats(BaseModel):
    total_processes: int
    running_processes: int
    stopped_processes: int
    error_processes: int

def get_process_manager():
    """Dependency to get ProcessManager instance."""
    return ProcessManager()

@router.get("/", response_model=List[ProcessInfo])
async def get_all_processes(manager: ProcessManager = Depends(get_process_manager)):
    """Get list of all managed processes."""
    try:
        processes = manager.get_all_processes()
        return [ProcessInfo(**proc) for proc in processes]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching processes: {str(e)}")

@router.get("/stats", response_model=ProcessStats)
async def get_process_stats(manager: ProcessManager = Depends(get_process_manager)):
    """Get process statistics."""
    try:
        stats = manager.get_process_stats()
        return ProcessStats(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching stats: {str(e)}")

@router.get("/{process_id}", response_model=ProcessInfo)
async def get_process(process_id: str, manager: ProcessManager = Depends(get_process_manager)):
    """Get information about a specific process."""
    try:
        process = manager.get_process(process_id)
        if not process:
            raise HTTPException(status_code=404, detail=f"Process {process_id} not found")
        return ProcessInfo(**process)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching process: {str(e)}")

@router.post("/start")
async def start_process(request: ProcessStartRequest, manager: ProcessManager = Depends(get_process_manager)):
    """Start a new process."""
    try:
        process_id = manager.start_process(request.script_name, request.args)
        return {"message": f"Process started successfully", "process_id": process_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting process: {str(e)}")

@router.post("/{process_id}/stop")
async def stop_process(process_id: str, manager: ProcessManager = Depends(get_process_manager)):
    """Stop a running process."""
    try:
        success = manager.stop_process(process_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Process {process_id} not found or already stopped")
        return {"message": f"Process {process_id} stopped successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error stopping process: {str(e)}")

@router.post("/{process_id}/restart")
async def restart_process(process_id: str, manager: ProcessManager = Depends(get_process_manager)):
    """Restart a process."""
    try:
        success = manager.restart_process(process_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Process {process_id} not found")
        return {"message": f"Process {process_id} restarted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error restarting process: {str(e)}")

@router.get("/{process_id}/logs")
async def get_process_logs(
    process_id: str, 
    lines: int = 100,
    manager: ProcessManager = Depends(get_process_manager)
):
    """Get recent logs for a process."""
    try:
        logs = manager.get_process_logs(process_id, lines)
        return {"process_id": process_id, "logs": logs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching logs: {str(e)}")

@router.get("/scripts/available")
async def get_available_scripts(manager: ProcessManager = Depends(get_process_manager)):
    """Get list of available scripts that can be started."""
    try:
        scripts = manager.get_available_scripts()
        return {"scripts": scripts}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching scripts: {str(e)}")

@router.delete("/{process_id}")
async def remove_process(process_id: str, manager: ProcessManager = Depends(get_process_manager)):
    """Remove a stopped process from management."""
    try:
        success = manager.remove_process(process_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Process {process_id} not found or still running")
        return {"message": f"Process {process_id} removed successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error removing process: {str(e)}")