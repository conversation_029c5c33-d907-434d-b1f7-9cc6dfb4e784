"""
Configuration Management API
===========================

REST API endpoints for managing application configuration including
environment variables, database settings, and Rithmic API credentials.
"""

import os
from typing import Dict, Any
from pathlib import Path
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from web_dashboard.config import get_settings, get_database_config, get_rithmic_config

router = APIRouter()

# Pydantic models
class DatabaseConfig(BaseModel):
    host: str = Field(..., description="Database host")
    port: int = Field(..., description="Database port")
    user: str = Field(..., description="Database user")
    password: str = Field(..., description="Database password")
    database: str = Field(..., description="Database name")

class RithmicConfig(BaseModel):
    user: str = Field(..., description="Rithmic username")
    password: str = Field(..., description="Rithmic password") 
    system: str = Field(..., description="Rithmic system name")
    gateway: str = Field(..., description="Rithmic gateway")
    uri: str = Field(..., description="Rithmic WebSocket URI")

class EnvironmentVariable(BaseModel):
    key: str
    value: str
    description: str = ""

class ConfigurationStatus(BaseModel):
    database_configured: bool
    rithmic_configured: bool
    environment_loaded: bool
    config_file_exists: bool

@router.get("/status", response_model=ConfigurationStatus)
async def get_configuration_status():
    """Get overall configuration status."""
    try:
        settings = get_settings()
        env_file_path = settings.project_root / '.env'
        
        # Check if critical configurations are set
        db_config = get_database_config()
        rithmic_config = get_rithmic_config()
        
        database_configured = all([
            db_config['host'],
            db_config['user'],
            db_config['database']
        ])
        
        rithmic_configured = all([
            rithmic_config['user'],
            rithmic_config['system'],
            rithmic_config['uri']
        ])
        
        return ConfigurationStatus(
            database_configured=database_configured,
            rithmic_configured=rithmic_configured,
            environment_loaded=True,
            config_file_exists=env_file_path.exists()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Configuration status error: {str(e)}")

@router.get("/database", response_model=DatabaseConfig)
async def get_database_configuration():
    """Get current database configuration."""
    try:
        config = get_database_config()
        return DatabaseConfig(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password="[HIDDEN]",  # Don't expose password
            database=config['database']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database config error: {str(e)}")

@router.get("/rithmic", response_model=RithmicConfig)
async def get_rithmic_configuration():
    """Get current Rithmic API configuration."""
    try:
        config = get_rithmic_config()
        return RithmicConfig(
            user=config['user'],
            password="[HIDDEN]",  # Don't expose password
            system=config['system'],
            gateway=config['gateway'],
            uri=config['uri']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Rithmic config error: {str(e)}")

@router.get("/environment")
async def get_environment_variables():
    """Get relevant environment variables."""
    try:
        settings = get_settings()
        
        # Define environment variables to expose (without sensitive values)
        env_vars = [
            {"key": "MYSQL_HOST", "value": settings.mysql_host, "description": "Database host"},
            {"key": "MYSQL_PORT", "value": str(settings.mysql_port), "description": "Database port"},
            {"key": "MYSQL_USER", "value": settings.mysql_user, "description": "Database user"},
            {"key": "MYSQL_DATABASE", "value": settings.mysql_database, "description": "Database name"},
            {"key": "RITHMIC_USER", "value": settings.rithmic_user, "description": "Rithmic username"},
            {"key": "RITHMIC_SYSTEM", "value": settings.rithmic_system, "description": "Rithmic system"},
            {"key": "RITHMIC_GATEWAY", "value": settings.rithmic_gateway, "description": "Rithmic gateway"},
            {"key": "RITHMIC_URI", "value": settings.rithmic_uri, "description": "Rithmic WebSocket URI"},
            {"key": "WEB_DASHBOARD_PORT", "value": str(settings.port), "description": "Web dashboard port"},
        ]
        
        return {"environment_variables": env_vars}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Environment variables error: {str(e)}")

@router.post("/environment/update")
async def update_environment_variable(env_var: EnvironmentVariable):
    """Update an environment variable."""
    try:
        # For security, only allow updating specific variables
        allowed_vars = {
            'MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_DATABASE',
            'RITHMIC_USER', 'RITHMIC_SYSTEM', 'RITHMIC_GATEWAY', 'RITHMIC_URI',
            'WEB_DASHBOARD_PORT'
        }
        
        if env_var.key not in allowed_vars:
            raise HTTPException(status_code=400, detail=f"Variable {env_var.key} cannot be updated")
        
        # Don't allow updating sensitive variables through API
        sensitive_vars = {'MYSQL_PASSWORD', 'RITHMIC_PASSWORD'}
        if env_var.key in sensitive_vars:
            raise HTTPException(status_code=400, detail="Sensitive variables cannot be updated via API")
        
        # Update environment variable in current process
        os.environ[env_var.key] = env_var.value
        
        return {"message": f"Environment variable {env_var.key} updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Update error: {str(e)}")

@router.get("/validation")
async def validate_configuration():
    """Validate current configuration."""
    try:
        issues = []
        
        # Validate database configuration
        db_config = get_database_config()
        if not db_config['host']:
            issues.append("Database host not configured")
        if not db_config['user']:
            issues.append("Database user not configured")
        if not db_config['database']:
            issues.append("Database name not configured")
            
        # Validate Rithmic configuration
        rithmic_config = get_rithmic_config()
        if not rithmic_config['user']:
            issues.append("Rithmic user not configured")
        if not rithmic_config['system']:
            issues.append("Rithmic system not configured")
        if not rithmic_config['uri']:
            issues.append("Rithmic URI not configured")
        
        # Check if .env file exists
        settings = get_settings()
        env_file = settings.project_root / '.env'
        if not env_file.exists():
            issues.append(".env file not found")
            
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "message": "Configuration is valid" if len(issues) == 0 else f"Found {len(issues)} configuration issues"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation error: {str(e)}")