"""
Code Generation API Endpoints
=============================

Provides REST API endpoints for generating Python scripts from web dashboard operations.
Equivalent to the Show Code functionality from the original GUI.
"""

import os
from fastapi import APIRouter, HTTPException, Request, Response
from fastapi.responses import PlainTextResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
import json

from config import get_database_config, get_rithmic_config
from services.code_generator import CodeGenerator

router = APIRouter()

# Pydantic models for request validation
class DatabaseQueryRequest(BaseModel):
    """Request model for database query script generation."""
    table_name: str = Field(..., description="Database table name")
    where_clause: Optional[str] = Field(None, description="SQL WHERE clause")
    chunk_size: int = Field(10000, description="Rows per thread/chunk")
    max_workers: str = Field("os.cpu_count()", description="Maximum number of workers")
    threading_strategy: str = Field("ThreadPoolExecutor", description="Threading strategy")
    export_csv: bool = Field(True, description="Include CSV export functionality")
    simple_mode: bool = Field(False, description="Generate simple single-threaded script")

class ConfigurationRequest(BaseModel):
    """Request model for configuration script generation."""
    include_database: bool = Field(True, description="Include database configuration")
    include_rithmic: bool = Field(True, description="Include Rithmic API configuration")
    use_env_vars: bool = Field(True, description="Use environment variables")

class DataCollectionRequest(BaseModel):
    """Request model for data collection script generation."""
    contracts: List[str] = Field([], description="Selected futures contracts")
    data_types: List[str] = Field([], description="Data types to collect (level1, level3, trades, quotes)")
    collection_mode: str = Field("continuous", description="Collection mode (continuous, historical, sample)")

class SymbolSearchRequest(BaseModel):
    """Request model for symbol search script generation."""
    filters: Optional[Dict[str, str]] = Field(None, description="Search filters (exchange_filter, symbol_pattern, product_code)")

@router.post("/database-query", response_class=PlainTextResponse)
async def generate_database_query_script(request: DatabaseQueryRequest):
    """Generate database query script with multi-threading options."""
    try:
        # Get current configurations
        db_config = get_database_config()
        
        # Initialize code generator
        code_generator = CodeGenerator(db_config=db_config)
        
        # Generate the script
        if request.simple_mode:
            script = code_generator.generate_simple_query_script(
                table_name=request.table_name,
                where_clause=request.where_clause,
                export_csv=request.export_csv
            )
        else:
            script = code_generator.generate_multi_threaded_query_script(
                table_name=request.table_name,
                where_clause=request.where_clause,
                chunk_size=request.chunk_size,
                max_workers=request.max_workers,
                threading_strategy=request.threading_strategy,
                export_csv=request.export_csv
            )
        
        return script
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating database query script: {str(e)}")

@router.post("/configuration", response_class=PlainTextResponse)
async def generate_configuration_script(request: ConfigurationRequest):
    """Generate configuration script for database and Rithmic API setup."""
    try:
        # Get current configurations
        db_config = get_database_config() if request.include_database else None
        rithmic_config = get_rithmic_config() if request.include_rithmic else None
        
        # Initialize code generator
        code_generator = CodeGenerator(db_config=db_config, rithmic_config=rithmic_config)
        
        # Generate the script
        script = code_generator.generate_configuration_script(
            include_database=request.include_database,
            include_rithmic=request.include_rithmic,
            use_env_vars=request.use_env_vars
        )
        
        return script
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating configuration script: {str(e)}")

@router.post("/data-collection", response_class=PlainTextResponse)
async def generate_data_collection_script(request: DataCollectionRequest):
    """Generate data collection script for market data gathering."""
    try:
        # Get current configurations
        db_config = get_database_config()
        rithmic_config = get_rithmic_config()
        
        # Initialize code generator
        code_generator = CodeGenerator(db_config=db_config, rithmic_config=rithmic_config)
        
        # Generate the script
        script = code_generator.generate_data_collection_script(
            contracts=request.contracts,
            data_types=request.data_types,
            collection_mode=request.collection_mode
        )
        
        return script
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating data collection script: {str(e)}")

@router.get("/templates")
async def get_script_templates():
    """Get available script templates and their descriptions."""
    try:
        templates = {
            "database_query": {
                "name": "Database Query Script",
                "description": "Multi-threaded database query with CSV export",
                "parameters": {
                    "table_name": "Required: Database table name",
                    "where_clause": "Optional: SQL WHERE clause for filtering",
                    "chunk_size": "Optional: Rows per thread (default: 10000)",
                    "max_workers": "Optional: Max worker threads (default: CPU count)",
                    "threading_strategy": "Optional: ThreadPoolExecutor, ProcessPoolExecutor, or asyncio",
                    "export_csv": "Optional: Include CSV export (default: true)",
                    "simple_mode": "Optional: Generate simple single-threaded version"
                }
            },
            "configuration": {
                "name": "Configuration Script",
                "description": "Database and Rithmic API configuration setup",
                "parameters": {
                    "include_database": "Optional: Include database config (default: true)",
                    "include_rithmic": "Optional: Include Rithmic API config (default: true)",
                    "use_env_vars": "Optional: Use environment variables (default: true)"
                }
            },
            "data_collection": {
                "name": "Data Collection Script",
                "description": "Market data collection with multiple data types",
                "parameters": {
                    "contracts": "Optional: List of futures contracts",
                    "data_types": "Optional: Data types (level1, level3, trades, quotes)",
                    "collection_mode": "Optional: continuous, historical, or sample"
                }
            }
        }
        
        return templates
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting templates: {str(e)}")

@router.get("/threading-strategies")
async def get_threading_strategies():
    """Get available threading strategies and their descriptions."""
    try:
        strategies = {
            "ThreadPoolExecutor": {
                "name": "Thread Pool Executor",
                "description": "Recommended for I/O bound operations (database queries)",
                "best_for": "Database queries, file operations, network requests",
                "pros": ["Low memory overhead", "Good for I/O bound tasks", "Easy to use"],
                "cons": ["Limited by GIL for CPU-bound tasks"]
            },
            "ProcessPoolExecutor": {
                "name": "Process Pool Executor", 
                "description": "Best for CPU intensive operations",
                "best_for": "Data processing, calculations, CPU-bound tasks",
                "pros": ["True parallelism", "No GIL limitations", "Good for CPU tasks"],
                "cons": ["Higher memory overhead", "Inter-process communication cost"]
            },
            "asyncio": {
                "name": "Asyncio",
                "description": "Asynchronous I/O for concurrent operations",
                "best_for": "High-concurrency I/O operations, web requests",
                "pros": ["Very efficient for I/O", "Low resource usage", "High concurrency"],
                "cons": ["More complex code", "Requires async-compatible libraries"]
            }
        }
        
        return strategies
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting threading strategies: {str(e)}")

@router.post("/symbol-search", response_class=PlainTextResponse)
async def generate_symbol_search_script(request: SymbolSearchRequest):
    """Generate symbol search and discovery script."""
    try:
        # Get current configurations
        db_config = get_database_config()
        
        # Initialize code generator
        code_generator = CodeGenerator(db_config=db_config)
        
        # Generate the script
        script = code_generator.generate_symbol_search_script(
            filters=request.filters or {}
        )
        
        return script
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating symbol search script: {str(e)}")

@router.post("/validate-parameters")
async def validate_script_parameters(parameters: Dict[str, Any]):
    """Validate script generation parameters."""
    try:
        validation_results = {}
        
        # Validate chunk_size
        if 'chunk_size' in parameters:
            try:
                chunk_size = int(parameters['chunk_size'])
                if chunk_size <= 0:
                    validation_results['chunk_size'] = "Chunk size must be positive"
                elif chunk_size > 1000000:
                    validation_results['chunk_size'] = "Warning: Very large chunk size may cause memory issues"
                else:
                    validation_results['chunk_size'] = "Valid"
            except ValueError:
                validation_results['chunk_size'] = "Chunk size must be an integer"
        
        # Validate max_workers
        if 'max_workers' in parameters:
            max_workers = parameters['max_workers']
            if max_workers not in ['os.cpu_count()', '1', '2', '4', '8', '16']:
                try:
                    workers = int(max_workers)
                    if workers <= 0:
                        validation_results['max_workers'] = "Max workers must be positive"
                    elif workers > 32:
                        validation_results['max_workers'] = "Warning: Very high worker count may degrade performance"
                    else:
                        validation_results['max_workers'] = "Valid"
                except ValueError:
                    validation_results['max_workers'] = "Max workers must be integer or 'os.cpu_count()'"
            else:
                validation_results['max_workers'] = "Valid"
        
        # Validate table_name
        if 'table_name' in parameters:
            table_name = parameters['table_name']
            if not table_name or not isinstance(table_name, str):
                validation_results['table_name'] = "Table name is required"
            elif not table_name.replace('_', '').replace('-', '').isalnum():
                validation_results['table_name'] = "Warning: Table name contains special characters"
            else:
                validation_results['table_name'] = "Valid"
        
        return {
            "valid": all(result == "Valid" or result.startswith("Warning:") 
                        for result in validation_results.values()),
            "results": validation_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error validating parameters: {str(e)}")