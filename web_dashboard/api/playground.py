#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API Playground Module
====================

Provides endpoints for the Rithmic API playground interface, allowing interactive
testing and exploration of all Rithmic Protocol Buffer templates and endpoints.

Features:
- Template discovery and documentation
- Live API interaction with Rithmic servers
- Request/response validation and logging
- Session management and connection handling
- Code generation for tested requests
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))
sys.path.append(str(Path(__file__).parent.parent.parent / 'endpoints' / 'shared'))

# Initialize logger first
logger = logging.getLogger(__name__)

try:
    from services.protocol_parser import ProtocolParser
    from services.session_manager import PlaygroundSessionManager
    from services.testing_suite import AdvancedTestingSuite
    ADVANCED_FEATURES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Advanced playground features not available: {e}")
    ADVANCED_FEATURES_AVAILABLE = False
    
    # Create basic mock classes
    class ProtocolParser:
        async def initialize(self): pass
        async def get_all_templates(self): return []
        async def get_template_categories(self): return {}
        async def get_template_info(self, template_id): return None
        async def validate_request(self, template_id, request_data): 
            from collections import namedtuple
            ValidationResult = namedtuple('ValidationResult', ['valid', 'errors', 'warnings', 'suggestions'])
            return ValidationResult(True, [], [], [])
        async def generate_python_code(self, *args): return "# Mock Python code"
        async def generate_javascript_code(self, *args): return "// Mock JavaScript code"
    
    class PlaygroundSessionManager:
        async def get_session_info(self): return {"session_id": "mock", "connection_status": "mock"}
        async def get_request_history(self): return []
        async def clear_request_history(self): pass
        async def list_sessions(self): return []
        async def get_session_templates(self): return []
        async def get_session_analytics(self): return {"summary": {"total_sessions": 0}}
        async def load_templates(self): pass
    
    class AdvancedTestingSuite:
        def __init__(self, *args): pass
        async def get_available_test_suites(self): return {}
        async def run_test_suite(self, *args): return {"status": "mock"}
        async def run_load_test(self, *args): return {"status": "mock"}

router = APIRouter()

# Pydantic models for API requests/responses
class TemplateInfo(BaseModel):
    template_id: int
    template_name: str
    description: str
    category: str
    request_proto: Optional[str] = None
    response_proto: Optional[str] = None
    infrastructure_plant: str
    required_fields: List[str] = []
    optional_fields: List[str] = []
    examples: List[Dict[str, Any]] = []

class PlaygroundRequest(BaseModel):
    template_id: int
    template_name: str
    infrastructure_plant: str
    request_data: Dict[str, Any]
    session_id: Optional[str] = None

class PlaygroundResponse(BaseModel):
    success: bool
    template_id: int
    template_name: str
    request_data: Dict[str, Any]
    response_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time_ms: float
    timestamp: str
    session_id: Optional[str] = None

class SessionInfo(BaseModel):
    session_id: str
    infrastructure_plant: str
    connection_status: str
    authenticated: bool
    created_at: str
    last_activity: str
    request_count: int

# Global instances
protocol_parser = None
session_manager = None
testing_suite = None
playground_clients: Dict[str, Any] = {}

@router.on_event("startup")
async def startup_playground():
    """Initialize playground components on startup."""
    global protocol_parser, session_manager, testing_suite
    try:
        # Initialize protocol parser
        protocol_parser = ProtocolParser()
        await protocol_parser.initialize()
        
        # Initialize session manager
        session_manager = PlaygroundSessionManager()
        await session_manager.load_templates()
        
        # Initialize testing suite
        testing_suite = AdvancedTestingSuite(session_manager, protocol_parser)
        
        logger.info("API Playground with advanced features initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize API Playground: {e}")

@router.on_event("shutdown")
async def shutdown_playground():
    """Cleanup playground resources on shutdown."""
    global playground_clients
    for client in playground_clients.values():
        try:
            await client.disconnect()
        except Exception as e:
            logger.warning(f"Error disconnecting playground client: {e}")
    playground_clients.clear()

# Template Discovery and Documentation Endpoints

@router.get("/templates", response_model=List[TemplateInfo])
async def get_all_templates():
    """Get all available Rithmic Protocol Buffer templates."""
    if not protocol_parser:
        raise HTTPException(status_code=500, detail="Protocol parser not initialized")
    
    try:
        templates = await protocol_parser.get_all_templates()
        return templates
    except Exception as e:
        logger.error(f"Error getting templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/templates/categories")
async def get_template_categories():
    """Get all template categories with counts."""
    if not protocol_parser:
        raise HTTPException(status_code=500, detail="Protocol parser not initialized")
    
    try:
        categories = await protocol_parser.get_template_categories()
        return {"categories": categories}
    except Exception as e:
        logger.error(f"Error getting template categories: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/templates/{template_id}", response_model=TemplateInfo)
async def get_template_info(template_id: int):
    """Get detailed information for a specific template."""
    if not protocol_parser:
        raise HTTPException(status_code=500, detail="Protocol parser not initialized")
    
    try:
        template_info = await protocol_parser.get_template_info(template_id)
        if not template_info:
            raise HTTPException(status_code=404, detail=f"Template {template_id} not found")
        return template_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting template info for {template_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/templates/category/{category}")
async def get_templates_by_category(category: str):
    """Get all templates in a specific category."""
    if not protocol_parser:
        raise HTTPException(status_code=500, detail="Protocol parser not initialized")
    
    try:
        templates = await protocol_parser.get_templates_by_category(category)
        return {"category": category, "templates": templates}
    except Exception as e:
        logger.error(f"Error getting templates for category {category}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Session Management Endpoints

@router.post("/sessions/create")
async def create_session(infrastructure_plant: str):
    """Create a new playground session for the specified infrastructure plant."""
    try:
        session_id = f"playground_{infrastructure_plant}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        client = RithmicPlaygroundClient(infrastructure_plant, session_id)
        playground_clients[session_id] = client
        
        session_info = SessionInfo(
            session_id=session_id,
            infrastructure_plant=infrastructure_plant,
            connection_status="created",
            authenticated=False,
            created_at=datetime.now().isoformat(),
            last_activity=datetime.now().isoformat(),
            request_count=0
        )
        
        return {"session": session_info}
    
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions", response_model=List[SessionInfo])
async def get_active_sessions():
    """Get all active playground sessions."""
    try:
        sessions = []
        for session_id, client in playground_clients.items():
            session_info = await client.get_session_info()
            sessions.append(session_info)
        return sessions
    except Exception as e:
        logger.error(f"Error getting active sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions/{session_id}", response_model=SessionInfo)
async def get_session_info(session_id: str):
    """Get information about a specific session."""
    if session_id not in playground_clients:
        raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
    
    try:
        client = playground_clients[session_id]
        session_info = await client.get_session_info()
        return session_info
    except Exception as e:
        logger.error(f"Error getting session info for {session_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/{session_id}/connect")
async def connect_session(session_id: str):
    """Connect and authenticate a playground session."""
    if session_id not in playground_clients:
        raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
    
    try:
        client = playground_clients[session_id]
        success = await client.connect_and_authenticate()
        
        if success:
            return {"status": "connected", "authenticated": True}
        else:
            return {"status": "failed", "authenticated": False, "error": "Authentication failed"}
    
    except Exception as e:
        logger.error(f"Error connecting session {session_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a playground session and cleanup resources."""
    if session_id not in playground_clients:
        raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
    
    try:
        client = playground_clients[session_id]
        await client.disconnect()
        del playground_clients[session_id]
        
        return {"status": "deleted", "session_id": session_id}
    
    except Exception as e:
        logger.error(f"Error deleting session {session_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# API Testing Endpoints

@router.post("/test", response_model=PlaygroundResponse)
async def test_api_request(request: PlaygroundRequest):
    """Test a Rithmic API request through the playground."""
    
    # Validate session if provided
    client = None
    if request.session_id:
        if request.session_id not in playground_clients:
            raise HTTPException(status_code=404, detail=f"Session {request.session_id} not found")
        client = playground_clients[request.session_id]
    
    try:
        start_time = datetime.now()
        
        # Validate request data against protocol schema
        if protocol_parser:
            validation_result = await protocol_parser.validate_request(
                request.template_id, request.request_data
            )
            if not validation_result.valid:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Request validation failed: {validation_result.errors}"
                )
        
        # Execute the API request
        if client and await client.is_connected():
            # Use existing session
            response_data = await client.send_request(
                request.template_id, 
                request.template_name,
                request.request_data
            )
        else:
            # Create temporary client for one-off requests
            temp_client = RithmicPlaygroundClient(request.infrastructure_plant)
            try:
                await temp_client.connect_and_authenticate()
                response_data = await temp_client.send_request(
                    request.template_id,
                    request.template_name, 
                    request.request_data
                )
            finally:
                await temp_client.disconnect()
        
        end_time = datetime.now()
        execution_time_ms = (end_time - start_time).total_seconds() * 1000
        
        return PlaygroundResponse(
            success=True,
            template_id=request.template_id,
            template_name=request.template_name,
            request_data=request.request_data,
            response_data=response_data,
            execution_time_ms=execution_time_ms,
            timestamp=end_time.isoformat(),
            session_id=request.session_id
        )
    
    except HTTPException:
        raise
    except Exception as e:
        end_time = datetime.now()
        execution_time_ms = (end_time - start_time).total_seconds() * 1000
        
        logger.error(f"Error testing API request: {e}")
        
        return PlaygroundResponse(
            success=False,
            template_id=request.template_id,
            template_name=request.template_name,
            request_data=request.request_data,
            error_message=str(e),
            execution_time_ms=execution_time_ms,
            timestamp=end_time.isoformat(),
            session_id=request.session_id
        )

@router.post("/validate")
async def validate_request(template_id: int, request_data: Dict[str, Any]):
    """Validate a request without sending it to the API."""
    if not protocol_parser:
        raise HTTPException(status_code=500, detail="Protocol parser not initialized")
    
    try:
        validation_result = await protocol_parser.validate_request(template_id, request_data)
        return {
            "valid": validation_result.valid,
            "errors": validation_result.errors,
            "warnings": validation_result.warnings,
            "suggestions": validation_result.suggestions
        }
    except Exception as e:
        logger.error(f"Error validating request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Code Generation Endpoints

@router.post("/generate/python")
async def generate_python_code(request: PlaygroundRequest):
    """Generate Python code for a tested API request."""
    try:
        if not protocol_parser:
            raise HTTPException(status_code=500, detail="Protocol parser not initialized")
        
        python_code = await protocol_parser.generate_python_code(
            request.template_id,
            request.template_name,
            request.infrastructure_plant,
            request.request_data
        )
        
        return {
            "language": "python",
            "code": python_code,
            "template_id": request.template_id,
            "template_name": request.template_name
        }
    
    except Exception as e:
        logger.error(f"Error generating Python code: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate/javascript")
async def generate_javascript_code(request: PlaygroundRequest):
    """Generate JavaScript code for a tested API request."""
    try:
        if not protocol_parser:
            raise HTTPException(status_code=500, detail="Protocol parser not initialized")
        
        js_code = await protocol_parser.generate_javascript_code(
            request.template_id,
            request.template_name,
            request.infrastructure_plant,
            request.request_data
        )
        
        return {
            "language": "javascript",
            "code": js_code,
            "template_id": request.template_id,
            "template_name": request.template_name
        }
    
    except Exception as e:
        logger.error(f"Error generating JavaScript code: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Request History and Logging

@router.get("/history")
async def get_request_history(session_id: Optional[str] = None, limit: int = 50):
    """Get request history for playground sessions."""
    try:
        if session_id:
            if session_id not in playground_clients:
                raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
            client = playground_clients[session_id]
            history = await client.get_request_history(limit)
        else:
            # Get history from all sessions
            history = []
            for client in playground_clients.values():
                session_history = await client.get_request_history(limit)
                history.extend(session_history)
            
            # Sort by timestamp and limit
            history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            history = history[:limit]
        
        return {"history": history}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting request history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/history")
async def clear_request_history(session_id: Optional[str] = None):
    """Clear request history for playground sessions."""
    try:
        if session_id:
            if session_id not in playground_clients:
                raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
            client = playground_clients[session_id]
            await client.clear_request_history()
            return {"status": "cleared", "session_id": session_id}
        else:
            # Clear history for all sessions
            for client in playground_clients.values():
                await client.clear_request_history()
            return {"status": "cleared", "sessions": "all"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing request history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoint for real-time playground updates
@router.websocket("/ws")
async def playground_websocket(websocket: WebSocket):
    """WebSocket endpoint for real-time playground updates."""
    await websocket.accept()
    
    try:
        while True:
            # Wait for client messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle different message types
            if message.get('type') == 'ping':
                await websocket.send_text(json.dumps({'type': 'pong'}))
            
            elif message.get('type') == 'session_status':
                session_id = message.get('session_id')
                if session_id in playground_clients:
                    client = playground_clients[session_id]
                    status = await client.get_session_info()
                    await websocket.send_text(json.dumps({
                        'type': 'session_status',
                        'session_id': session_id,
                        'status': status.dict()
                    }))
                else:
                    await websocket.send_text(json.dumps({
                        'type': 'error',
                        'message': f'Session {session_id} not found'
                    }))
            
            # Add more message handlers as needed
            
    except WebSocketDisconnect:
        logger.info("Playground WebSocket client disconnected")
    except Exception as e:
        logger.error(f"Error in playground WebSocket: {e}")
        try:
            await websocket.send_text(json.dumps({
                'type': 'error',
                'message': str(e)
            }))
        except:
            pass

# Health check endpoint
@router.get("/health")
async def playground_health():
    """Health check for the API playground."""
    return {
        "status": "healthy",
        "advanced_features_available": ADVANCED_FEATURES_AVAILABLE,
        "protocol_parser_initialized": protocol_parser is not None,
        "session_manager_initialized": session_manager is not None,
        "testing_suite_initialized": testing_suite is not None,
        "active_sessions": len(playground_clients),
        "timestamp": datetime.now().isoformat()
    }

# Session Management Endpoints
@router.post("/sessions")
async def create_session(infrastructure_plant: str, session_id: Optional[str] = None, 
                        template_name: Optional[str] = None):
    """Create a new playground session."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        session_id = await session_manager.create_session(
            infrastructure_plant, session_id, template_name
        )
        return {"session_id": session_id, "status": "created"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/{session_id}/connect")
async def connect_session(session_id: str):
    """Connect and authenticate a session."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        success = await session_manager.connect_session(session_id)
        return {"session_id": session_id, "connected": success}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/{session_id}/disconnect")
async def disconnect_session(session_id: str):
    """Disconnect a session."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        await session_manager.disconnect_session(session_id)
        return {"session_id": session_id, "status": "disconnected"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/sessions/{session_id}")
async def remove_session(session_id: str):
    """Remove a session completely."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        await session_manager.remove_session(session_id)
        return {"session_id": session_id, "status": "removed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions")
async def list_sessions():
    """List all active sessions."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        sessions = await session_manager.list_sessions()
        return {"sessions": sessions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions/templates")
async def get_session_templates():
    """Get all available session templates."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        templates = await session_manager.get_session_templates()
        return {"templates": templates}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/from-template/{template_name}")
async def create_session_from_template(template_name: str, session_id: Optional[str] = None):
    """Create a session from a template."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        session_id = await session_manager.create_session_from_template(template_name, session_id)
        return {"session_id": session_id, "template": template_name, "status": "created"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/{session_id}/run-workflow")
async def run_template_workflow(session_id: str):
    """Run the default workflow for a session template."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        results = await session_manager.run_template_workflow(session_id)
        return {"session_id": session_id, "workflow_results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions/analytics")
async def get_session_analytics():
    """Get comprehensive session analytics."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        analytics = await session_manager.get_session_analytics()
        return {"analytics": analytics}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/cleanup")
async def cleanup_old_sessions(max_age_hours: int = 24):
    """Clean up old disconnected sessions."""
    if not session_manager:
        raise HTTPException(status_code=503, detail="Session manager not initialized")
    
    try:
        cleaned_count = await session_manager.cleanup_old_sessions(max_age_hours)
        return {"cleaned_sessions": cleaned_count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Testing Suite Endpoints
@router.get("/testing/suites")
async def get_test_suites():
    """Get all available test suites."""
    if not testing_suite:
        raise HTTPException(status_code=503, detail="Testing suite not initialized")
    
    try:
        suites = await testing_suite.get_available_test_suites()
        return {"test_suites": suites}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/testing/run-suite/{suite_name}")
async def run_test_suite(suite_name: str, parallel_sessions: int = 1):
    """Run a complete test suite."""
    if not testing_suite:
        raise HTTPException(status_code=503, detail="Testing suite not initialized")
    
    if parallel_sessions < 1 or parallel_sessions > 10:
        raise HTTPException(status_code=400, detail="parallel_sessions must be between 1 and 10")
    
    try:
        result = await testing_suite.run_test_suite(suite_name, parallel_sessions)
        return {"test_result": result.__dict__}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/testing/load-test/{test_case_name}")
async def run_load_test(test_case_name: str, concurrent_sessions: int = 5, 
                       requests_per_session: int = 10):
    """Run load testing on a specific test case."""
    if not testing_suite:
        raise HTTPException(status_code=503, detail="Testing suite not initialized")
    
    if concurrent_sessions < 1 or concurrent_sessions > 20:
        raise HTTPException(status_code=400, detail="concurrent_sessions must be between 1 and 20")
    
    if requests_per_session < 1 or requests_per_session > 100:
        raise HTTPException(status_code=400, detail="requests_per_session must be between 1 and 100")
    
    try:
        result = await testing_suite.run_load_test(
            test_case_name, concurrent_sessions, requests_per_session
        )
        return {"load_test_result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/testing/generate-report")
async def generate_test_report(suite_result_data: Dict[str, Any], output_file: Optional[str] = None):
    """Generate a comprehensive test report."""
    if not testing_suite:
        raise HTTPException(status_code=503, detail="Testing suite not initialized")
    
    try:
        # Convert dict back to TestSuiteResult object (simplified)
        report = await testing_suite.generate_test_report(suite_result_data, output_file)
        return {"report": report, "output_file": output_file}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))