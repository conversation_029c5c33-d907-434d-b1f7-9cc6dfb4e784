"""
Market Data API
===============

REST API endpoints for market data operations including symbol search,
real-time data status, and historical data queries.
"""

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from web_dashboard.services.data_service import DataService
from web_dashboard.config import get_database_config

router = APIRouter()

# Pydantic models
class SymbolInfo(BaseModel):
    symbol: str
    exchange: str
    description: str = ""
    last_update: Optional[str] = None

class MarketDataStats(BaseModel):
    total_symbols: int
    active_symbols: int
    total_trades: int
    total_quotes: int
    last_update: Optional[str] = None

class RealtimeDataStatus(BaseModel):
    symbol: str
    exchange: str
    status: str  # active, inactive, error
    last_trade_time: Optional[str] = None
    last_quote_time: Optional[str] = None
    trade_count: int = 0
    quote_count: int = 0

class HistoricalDataSummary(BaseModel):
    symbol: str
    exchange: str
    bar_type: str
    earliest_date: Optional[str] = None
    latest_date: Optional[str] = None
    total_bars: int = 0

def get_data_service():
    """Dependency to get DataService instance."""
    try:
        config = get_database_config()
        return DataService(config)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database configuration error: {str(e)}")

@router.get("/symbols", response_model=List[SymbolInfo])
async def get_symbols(
    exchange: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    data_service: DataService = Depends(get_data_service)
):
    """Get list of symbols with optional filtering."""
    try:
        # Build query
        query = "SELECT DISTINCT symbol, exchange FROM symbols"
        conditions = []
        params = []
        
        if exchange:
            conditions.append("exchange = %s")
            params.append(exchange)
        
        if search:
            conditions.append("symbol LIKE %s")
            params.append(f"%{search}%")
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += f" ORDER BY symbol LIMIT {limit}"
        
        results = data_service.execute_query(query, tuple(params))
        
        symbols = []
        for row in results:
            symbols.append(SymbolInfo(
                symbol=row['symbol'],
                exchange=row['exchange'],
                description=f"{row['symbol']} on {row['exchange']}"
            ))
        
        return symbols
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching symbols: {str(e)}")

@router.get("/stats", response_model=MarketDataStats)
async def get_market_data_stats(data_service: DataService = Depends(get_data_service)):
    """Get overall market data statistics."""
    try:
        # Get symbol count
        symbol_query = "SELECT COUNT(DISTINCT CONCAT(symbol, '-', exchange)) as count FROM symbols"
        symbol_result = data_service.execute_query(symbol_query)
        total_symbols = symbol_result[0]['count'] if symbol_result else 0
        
        # Get active symbols (symbols with recent data)
        cutoff_time = datetime.now() - timedelta(hours=24)
        active_query = """
            SELECT COUNT(DISTINCT CONCAT(symbol, '-', exchange)) as count 
            FROM best_bid_offer 
            WHERE timestamp > %s
        """
        active_result = data_service.execute_query(active_query, (cutoff_time,))
        active_symbols = active_result[0]['count'] if active_result else 0
        
        # Get trade count
        trade_query = "SELECT COUNT(*) as count FROM last_trades"
        trade_result = data_service.execute_query(trade_query)
        total_trades = trade_result[0]['count'] if trade_result else 0
        
        # Get quote count
        quote_query = "SELECT COUNT(*) as count FROM best_bid_offer"
        quote_result = data_service.execute_query(quote_query)
        total_quotes = quote_result[0]['count'] if quote_result else 0
        
        # Get last update time
        last_update_query = """
            SELECT MAX(timestamp) as last_update 
            FROM best_bid_offer 
            UNION ALL 
            SELECT MAX(timestamp) FROM last_trades 
            ORDER BY last_update DESC 
            LIMIT 1
        """
        last_update_result = data_service.execute_query(last_update_query)
        last_update = None
        if last_update_result and last_update_result[0]['last_update']:
            last_update = last_update_result[0]['last_update'].isoformat()
        
        return MarketDataStats(
            total_symbols=total_symbols,
            active_symbols=active_symbols,
            total_trades=total_trades,
            total_quotes=total_quotes,
            last_update=last_update
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching stats: {str(e)}")

@router.get("/realtime/status", response_model=List[RealtimeDataStatus])
async def get_realtime_status(
    symbol: Optional[str] = Query(None),
    exchange: Optional[str] = Query(None),
    data_service: DataService = Depends(get_data_service)
):
    """Get real-time data status for symbols."""
    try:
        # Build query for symbols with recent activity
        query = """
            SELECT 
                symbol,
                exchange,
                MAX(CASE WHEN source_table = 'last_trades' THEN timestamp END) as last_trade_time,
                MAX(CASE WHEN source_table = 'best_bid_offer' THEN timestamp END) as last_quote_time,
                SUM(CASE WHEN source_table = 'last_trades' THEN 1 ELSE 0 END) as trade_count,
                SUM(CASE WHEN source_table = 'best_bid_offer' THEN 1 ELSE 0 END) as quote_count
            FROM (
                SELECT symbol, exchange, timestamp, 'last_trades' as source_table FROM last_trades
                UNION ALL
                SELECT symbol, exchange, timestamp, 'best_bid_offer' as source_table FROM best_bid_offer
            ) combined
        """
        
        conditions = []
        params = []
        
        if symbol:
            conditions.append("symbol = %s")
            params.append(symbol)
        
        if exchange:
            conditions.append("exchange = %s")
            params.append(exchange)
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " GROUP BY symbol, exchange ORDER BY symbol"
        
        results = data_service.execute_query(query, tuple(params))
        
        status_list = []
        cutoff_time = datetime.now() - timedelta(minutes=30)
        
        for row in results:
            # Determine status based on recent activity
            last_trade = row.get('last_trade_time')
            last_quote = row.get('last_quote_time')
            
            status = "inactive"
            if last_trade and last_trade > cutoff_time:
                status = "active"
            elif last_quote and last_quote > cutoff_time:
                status = "active"
            
            status_list.append(RealtimeDataStatus(
                symbol=row['symbol'],
                exchange=row['exchange'],
                status=status,
                last_trade_time=last_trade.isoformat() if last_trade else None,
                last_quote_time=last_quote.isoformat() if last_quote else None,
                trade_count=row.get('trade_count', 0),
                quote_count=row.get('quote_count', 0)
            ))
        
        return status_list
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching realtime status: {str(e)}")

@router.get("/historical/summary", response_model=List[HistoricalDataSummary])
async def get_historical_summary(
    symbol: Optional[str] = Query(None),
    exchange: Optional[str] = Query(None),
    data_service: DataService = Depends(get_data_service)
):
    """Get historical data summary."""
    try:
        # Check if time_bars table exists
        tables_query = "SHOW TABLES LIKE 'time_bars'"
        tables_result = data_service.execute_query(tables_query)
        
        if not tables_result:
            return []  # No historical data tables
        
        # Build query for historical data summary
        query = """
            SELECT 
                symbol,
                exchange,
                'time_bars' as bar_type,
                MIN(timestamp) as earliest_date,
                MAX(timestamp) as latest_date,
                COUNT(*) as total_bars
            FROM time_bars
        """
        
        conditions = []
        params = []
        
        if symbol:
            conditions.append("symbol = %s")
            params.append(symbol)
        
        if exchange:
            conditions.append("exchange = %s")
            params.append(exchange)
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " GROUP BY symbol, exchange ORDER BY symbol"
        
        results = data_service.execute_query(query, tuple(params))
        
        summaries = []
        for row in results:
            summaries.append(HistoricalDataSummary(
                symbol=row['symbol'],
                exchange=row['exchange'],
                bar_type=row['bar_type'],
                earliest_date=row['earliest_date'].isoformat() if row['earliest_date'] else None,
                latest_date=row['latest_date'].isoformat() if row['latest_date'] else None,
                total_bars=row['total_bars']
            ))
        
        return summaries
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching historical summary: {str(e)}")

@router.get("/exchanges")
async def get_exchanges(data_service: DataService = Depends(get_data_service)):
    """Get list of available exchanges."""
    try:
        query = "SELECT DISTINCT exchange FROM symbols ORDER BY exchange"
        results = data_service.execute_query(query)
        exchanges = [row['exchange'] for row in results]
        return {"exchanges": exchanges}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching exchanges: {str(e)}")

@router.get("/recent/{symbol}")
async def get_recent_data(
    symbol: str,
    exchange: Optional[str] = Query(None),
    data_type: str = Query("trades", regex="^(trades|quotes|both)$"),
    limit: int = Query(100, le=1000),
    data_service: DataService = Depends(get_data_service)
):
    """Get recent market data for a symbol."""
    try:
        results = {}
        
        # Build base conditions
        conditions = ["symbol = %s"]
        params = [symbol]
        
        if exchange:
            conditions.append("exchange = %s")
            params.append(exchange)
        
        where_clause = " AND ".join(conditions)
        
        if data_type in ["trades", "both"]:
            trade_query = f"""
                SELECT * FROM last_trades 
                WHERE {where_clause} 
                ORDER BY timestamp DESC 
                LIMIT {limit}
            """
            trades = data_service.execute_query(trade_query, tuple(params))
            results["trades"] = trades
        
        if data_type in ["quotes", "both"]:
            quote_query = f"""
                SELECT * FROM best_bid_offer 
                WHERE {where_clause} 
                ORDER BY timestamp DESC 
                LIMIT {limit}
            """
            quotes = data_service.execute_query(quote_query, tuple(params))
            results["quotes"] = quotes
        
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching recent data: {str(e)}")