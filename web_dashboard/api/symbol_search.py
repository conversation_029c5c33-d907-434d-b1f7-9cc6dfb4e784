"""
Symbol Search API Endpoints
===========================

Provides comprehensive symbol search and discovery functionality for the web dashboard.
Ports functionality from the original GUI's symbol search tab.
"""

from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import PlainTextResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import re
import csv
import io
from datetime import datetime
import logging

from config import get_database_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic Models
class SymbolDiscoveryRequest(BaseModel):
    """Request model for symbol discovery"""
    exchange_filter: str = Field(default="All", description="Exchange filter (All, CME, CBOT, NYMEX, COMEX, ICE)")
    symbol_pattern: str = Field(default="", description="Symbol pattern with wildcards (ES*, *Z25, etc.)")
    product_code: str = Field(default="All", description="Product code filter (All, ES, NQ, YM, RTY, ZN, ZB, ZF, ZT)")

class SymbolInfo(BaseModel):
    """Symbol information model"""
    symbol: str
    exchange: str
    product_code: str
    description: str
    tick_size: float
    contract_size: int
    status: str

class SymbolDiscoveryResponse(BaseModel):
    """Response model for symbol discovery"""
    symbols: List[SymbolInfo]
    total_count: int
    filters_applied: Dict[str, str]

class SymbolSaveRequest(BaseModel):
    """Request model for saving symbols to database"""
    symbols: List[SymbolInfo]

class SymbolExportRequest(BaseModel):
    """Request model for exporting symbols"""
    symbols: List[SymbolInfo]
    filename: Optional[str] = None

# Symbol Search Service
class SymbolSearchService:
    """Service class for symbol search operations"""
    
    def __init__(self):
        self.db_config = get_database_config()
        
        # Comprehensive futures contracts mapping (ported from GUI)
        self.sample_symbols = {
            'ES': ['ESZ24', 'ESH25', 'ESM25', 'ESU25', 'ESZ25', 'ESH26'],
            'NQ': ['NQZ24', 'NQH25', 'NQM25', 'NQU25', 'NQZ25', 'NQH26'],
            'YM': ['YMZ24', 'YMH25', 'YMM25', 'YMU25', 'YMZ25', 'YMH26'],
            'RTY': ['RTYZ24', 'RTYH25', 'RTYM25', 'RTYU25', 'RTYZ25'],
            'ZN': ['ZNZ24', 'ZNH25', 'ZNM25', 'ZNU25', 'ZNZ25'],
            'ZB': ['ZBZ24', 'ZBH25', 'ZBM25', 'ZBU25', 'ZBZ25'],
            'ZF': ['ZFZ24', 'ZFH25', 'ZFM25', 'ZFU25', 'ZFZ25'],
            'ZT': ['ZTZ24', 'ZTH25', 'ZTM25', 'ZTU25', 'ZTZ25'],
            'CL': ['CLZ24', 'CLF25', 'CLG25', 'CLH25', 'CLJ25'],
            'NG': ['NGZ24', 'NGF25', 'NGG25', 'NGH25', 'NGJ25'],
            'GC': ['GCZ24', 'GCG25', 'GCJ25', 'GCM25', 'GCQ25'],
            'SI': ['SIZ24', 'SIH25', 'SIK25', 'SIN25', 'SIU25'],
            'HG': ['HGZ24', 'HGH25', 'HGK25', 'HGN25', 'HGU25'],
            'PA': ['PAZ24', 'PAH25', 'PAM25', 'PAU25', 'PAZ25']
        }
        
        # Symbol metadata mapping (ported from GUI)
        self.metadata_map = {
            'ES': {'exchange': 'CME', 'description': 'E-mini S&P 500', 'tick_size': 0.25, 'contract_size': 50},
            'NQ': {'exchange': 'CME', 'description': 'E-mini NASDAQ-100', 'tick_size': 0.25, 'contract_size': 20},
            'YM': {'exchange': 'CBOT', 'description': 'E-mini Dow Jones', 'tick_size': 1.0, 'contract_size': 5},
            'RTY': {'exchange': 'CME', 'description': 'E-mini Russell 2000', 'tick_size': 0.1, 'contract_size': 50},
            'ZN': {'exchange': 'CBOT', 'description': '10-Year Treasury Note', 'tick_size': 0.015625, 'contract_size': 100000},
            'ZB': {'exchange': 'CBOT', 'description': '30-Year Treasury Bond', 'tick_size': 0.03125, 'contract_size': 100000},
            'ZF': {'exchange': 'CBOT', 'description': '5-Year Treasury Note', 'tick_size': 0.0078125, 'contract_size': 100000},
            'ZT': {'exchange': 'CBOT', 'description': '2-Year Treasury Note', 'tick_size': 0.00390625, 'contract_size': 200000},
            'CL': {'exchange': 'NYMEX', 'description': 'Crude Oil', 'tick_size': 0.01, 'contract_size': 1000},
            'NG': {'exchange': 'NYMEX', 'description': 'Natural Gas', 'tick_size': 0.001, 'contract_size': 10000},
            'GC': {'exchange': 'COMEX', 'description': 'Gold', 'tick_size': 0.1, 'contract_size': 100},
            'SI': {'exchange': 'COMEX', 'description': 'Silver', 'tick_size': 0.005, 'contract_size': 5000},
            'HG': {'exchange': 'COMEX', 'description': 'Copper', 'tick_size': 0.0005, 'contract_size': 25000},
            'PA': {'exchange': 'NYMEX', 'description': 'Palladium', 'tick_size': 0.05, 'contract_size': 100}
        }
        
    def generate_symbol_metadata(self, symbol: str, product_code: str) -> SymbolInfo:
        """Generate metadata for a symbol based on product code patterns."""
        base_info = self.metadata_map.get(product_code, {
            'exchange': 'CME', 
            'description': f'{product_code} Contract', 
            'tick_size': 0.01, 
            'contract_size': 1
        })
        
        return SymbolInfo(
            symbol=symbol,
            exchange=base_info['exchange'],
            product_code=product_code,
            description=f"{base_info['description']} ({symbol})",
            tick_size=base_info['tick_size'],
            contract_size=base_info['contract_size'],
            status='Discovered'
        )
    
    def discover_symbols(self, request: SymbolDiscoveryRequest) -> SymbolDiscoveryResponse:
        """Discover symbols based on filters and patterns."""
        try:
            # Start with all symbols
            symbols_to_process = self.sample_symbols.copy()
            
            # Filter by product code if specified
            if request.product_code != 'All':
                symbols_to_process = {request.product_code: symbols_to_process.get(request.product_code, [])}
            
            # Filter by exchange if specified
            if request.exchange_filter != 'All':
                filtered_symbols = {}
                for product_code, symbols in symbols_to_process.items():
                    metadata = self.metadata_map.get(product_code, {})
                    if metadata.get('exchange') == request.exchange_filter:
                        filtered_symbols[product_code] = symbols
                symbols_to_process = filtered_symbols
            
            # Filter by pattern if specified
            if request.symbol_pattern.strip():
                pattern = request.symbol_pattern.strip()
                pattern_regex = re.compile(pattern.replace('*', '.*'), re.IGNORECASE)
                
                filtered_symbols = {}
                for product_code, symbols in symbols_to_process.items():
                    matching_symbols = [s for s in symbols if pattern_regex.match(s)]
                    if matching_symbols:
                        filtered_symbols[product_code] = matching_symbols
                symbols_to_process = filtered_symbols
            
            # Generate symbol metadata
            discovered_symbols = []
            for product_code, symbols in symbols_to_process.items():
                for symbol in symbols:
                    symbol_info = self.generate_symbol_metadata(symbol, product_code)
                    discovered_symbols.append(symbol_info)
            
            # Sort by symbol name
            discovered_symbols.sort(key=lambda x: x.symbol)
            
            return SymbolDiscoveryResponse(
                symbols=discovered_symbols,
                total_count=len(discovered_symbols),
                filters_applied={
                    'exchange_filter': request.exchange_filter,
                    'symbol_pattern': request.symbol_pattern,
                    'product_code': request.product_code
                }
            )
            
        except Exception as e:
            logger.error(f"Error discovering symbols: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error discovering symbols: {str(e)}")
    
    def create_symbols_table(self):
        """Create symbols table if it doesn't exist."""
        try:
            import mysql.connector
            from mysql.connector import Error
            
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor()
            
            create_table_query = """
                CREATE TABLE IF NOT EXISTS symbols (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL UNIQUE,
                    exchange VARCHAR(20),
                    product_code VARCHAR(10),
                    symbol_name TEXT,
                    tick_size DECIMAL(10,6),
                    lot_size INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_exchange (exchange),
                    INDEX idx_product_code (product_code)
                )
            """
            
            cursor.execute(create_table_query)
            connection.commit()
            
            cursor.close()
            connection.close()
            return True
            
        except Exception as e:
            logger.error(f"Error creating symbols table: {str(e)}")
            return False
    
    def save_symbols_to_database(self, symbols: List[SymbolInfo]) -> Dict[str, Any]:
        """Save discovered symbols to database."""
        try:
            import mysql.connector
            from mysql.connector import Error
            
            # Create table if not exists
            if not self.create_symbols_table():
                raise Exception("Failed to create symbols table")
            
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor()
            
            saved_count = 0
            skipped_count = 0
            
            for symbol_info in symbols:
                try:
                    insert_query = """
                        INSERT INTO symbols (symbol, exchange, product_code, symbol_name, tick_size, lot_size)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        exchange = VALUES(exchange),
                        product_code = VALUES(product_code),
                        symbol_name = VALUES(symbol_name),
                        tick_size = VALUES(tick_size),
                        lot_size = VALUES(lot_size),
                        updated_at = CURRENT_TIMESTAMP
                    """
                    
                    cursor.execute(insert_query, (
                        symbol_info.symbol,
                        symbol_info.exchange,
                        symbol_info.product_code,
                        symbol_info.description,
                        symbol_info.tick_size,
                        symbol_info.contract_size
                    ))
                    
                    if cursor.rowcount > 0:
                        saved_count += 1
                    else:
                        skipped_count += 1
                        
                except mysql.connector.IntegrityError:
                    skipped_count += 1
                    continue
            
            connection.commit()
            cursor.close()
            connection.close()
            
            return {
                'success': True,
                'saved_count': saved_count,
                'skipped_count': skipped_count,
                'total_processed': len(symbols)
            }
            
        except Exception as e:
            logger.error(f"Error saving symbols to database: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error saving symbols: {str(e)}")

# Initialize service
symbol_search_service = SymbolSearchService()

# API Endpoints
@router.post("/discover", response_model=SymbolDiscoveryResponse)
async def discover_symbols(request: SymbolDiscoveryRequest):
    """
    Discover symbols based on exchange, pattern, and product code filters.
    Supports wildcard patterns like ES*, *Z25, NQ*25, etc.
    """
    return symbol_search_service.discover_symbols(request)

@router.post("/export", response_class=PlainTextResponse)
async def export_symbols(request: SymbolExportRequest):
    """Export symbol search results to CSV format."""
    try:
        # Generate CSV content
        output = io.StringIO()
        fieldnames = ['symbol', 'exchange', 'product_code', 'description', 'tick_size', 'contract_size', 'status']
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        
        writer.writeheader()
        for symbol in request.symbols:
            writer.writerow({
                'symbol': symbol.symbol,
                'exchange': symbol.exchange,
                'product_code': symbol.product_code,
                'description': symbol.description,
                'tick_size': symbol.tick_size,
                'contract_size': symbol.contract_size,
                'status': symbol.status
            })
        
        csv_content = output.getvalue()
        output.close()
        
        # Generate filename if not provided
        filename = request.filename or f"symbol_search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        return Response(
            content=csv_content,
            media_type='text/csv',
            headers={'Content-Disposition': f'attachment; filename="{filename}"'}
        )
        
    except Exception as e:
        logger.error(f"Error exporting symbols: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error exporting symbols: {str(e)}")

@router.post("/save")
async def save_symbols(request: SymbolSaveRequest):
    """Save discovered symbols to the database."""
    result = symbol_search_service.save_symbols_to_database(request.symbols)
    return result

@router.get("/details/{symbol}")
async def get_symbol_details(symbol: str):
    """Get detailed information for a specific symbol."""
    try:
        # Extract product code from symbol (basic pattern matching)
        product_code = ''.join([c for c in symbol if c.isalpha()])
        
        # Generate detailed metadata
        symbol_info = symbol_search_service.generate_symbol_metadata(symbol, product_code)
        
        # Add additional details
        details = {
            "basic_info": symbol_info.dict(),
            "trading_details": {
                "market_hours": "17:00-16:00 CT (typical futures hours)",
                "settlement": "Cash settled" if product_code in ['ES', 'NQ', 'YM', 'RTY'] else "Physical delivery",
                "margin_info": "Varies by broker and position size",
                "expiration": "Third Friday of contract month (typical)"
            },
            "risk_metrics": {
                "point_value": f"${symbol_info.tick_size * symbol_info.contract_size:.2f} per tick",
                "daily_limit": "Varies by contract and market conditions",
                "volatility": "Historical volatility data not available in demo"
            }
        }
        
        return details
        
    except Exception as e:
        logger.error(f"Error getting symbol details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting symbol details: {str(e)}")

@router.get("/pattern-examples")
async def get_pattern_examples():
    """Get examples of symbol search patterns."""
    return {
        "examples": [
            {"pattern": "ES*", "description": "All E-mini S&P 500 contracts"},
            {"pattern": "NQ*", "description": "All E-mini NASDAQ contracts"},
            {"pattern": "*Z25", "description": "All December 2025 contracts"},
            {"pattern": "ES*24", "description": "All E-mini S&P 500 2024 contracts"},
            {"pattern": "*H*", "description": "All March contracts"},
            {"pattern": "ZN*", "description": "All 10-Year Treasury Note contracts"},
            {"pattern": "GC*", "description": "All Gold futures contracts"}
        ],
        "wildcards": {
            "*": "Matches any number of characters",
            "?": "Matches exactly one character (not implemented in basic version)"
        },
        "tips": [
            "Use * to match multiple characters",
            "Combine letters and * for flexible matching",
            "Case insensitive matching is supported",
            "Product codes: ES, NQ, YM, RTY (equity), ZN, ZB, ZF, ZT (bonds), CL, NG (energy), GC, SI (metals)"
        ]
    }