#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rithmic Playground Client
========================

Provides WebSocket client functionality for the API playground to interact
with Rithmic infrastructure plants in real-time.

Features:
- Multi-plant connection management
- Session persistence and state tracking
- Request/response logging and history
- Authentication and heartbeat handling
- Real-time streaming data support
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from collections import deque

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))
sys.path.append(str(Path(__file__).parent.parent.parent / 'endpoints' / 'shared'))

from config import get_config, InfraType
from auth import create_authenticated_connection
import message_handler
from message_handler import parse_message

logger = logging.getLogger(__name__)

class RithmicPlaygroundClient:
    """WebSocket client for Rithmic API playground interactions."""
    
    def __init__(self, infrastructure_plant: str, session_id: Optional[str] = None):
        """Initialize the playground client."""
        self.infrastructure_plant = infrastructure_plant
        self.session_id = session_id or f"playground_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.connection = None
        self.authenticator = None
        self.connected = False
        self.authenticated = False
        self.config = get_config()
        
        # Session tracking
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.request_count = 0
        
        # Request/response history
        self.request_history = deque(maxlen=1000)
        self.streaming_data = deque(maxlen=5000)
        
        # Heartbeat management
        self.heartbeat_task = None
        self.heartbeat_interval = 30  # seconds
        
        # Infrastructure type mapping
        self.infra_type_mapping = {
            "System": InfraType.SYSTEM,
            "Ticker Plant": InfraType.TICKER_PLANT,
            "History Plant": InfraType.HISTORY_PLANT,
            "Order Plant": InfraType.ORDER_PLANT,
            "PnL Plant": InfraType.PNL_PLANT,
            "Repository Plant": InfraType.REPOSITORY_PLANT
        }
    
    async def connect_and_authenticate(self) -> bool:
        """Connect to Rithmic infrastructure and authenticate."""
        try:
            logger.info(f"Connecting playground session {self.session_id} to {self.infrastructure_plant}")
            
            # Get infrastructure type
            infra_type = self.infra_type_mapping.get(self.infrastructure_plant)
            if not infra_type:
                raise ValueError(f"Unknown infrastructure plant: {self.infrastructure_plant}")
            
            # Create authenticated connection
            self.connection, self.authenticator = await create_authenticated_connection(
                infra_type, self.session_id
            )
            
            if not self.connection or not self.authenticator:
                logger.error(f"Failed to create authenticated connection for {self.session_id}")
                return False
            
            self.connected = True
            self.authenticated = True
            self.last_activity = datetime.now()
            
            # Start heartbeat if needed
            if self.infrastructure_plant != "System":
                await self._start_heartbeat()
            
            logger.info(f"Playground session {self.session_id} connected and authenticated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting playground session {self.session_id}: {e}")
            await self._cleanup_connection()
            return False
    
    async def disconnect(self):
        """Disconnect from Rithmic infrastructure."""
        try:
            logger.info(f"Disconnecting playground session {self.session_id}")
            
            # Stop heartbeat
            await self._stop_heartbeat()
            
            # Logout and disconnect
            if self.authenticator:
                try:
                    await self.authenticator.logout()
                except Exception as e:
                    logger.warning(f"Error during logout for {self.session_id}: {e}")
            
            if self.connection:
                try:
                    await self.connection.disconnect()
                except Exception as e:
                    logger.warning(f"Error disconnecting {self.session_id}: {e}")
            
            await self._cleanup_connection()
            logger.info(f"Playground session {self.session_id} disconnected successfully")
            
        except Exception as e:
            logger.error(f"Error disconnecting playground session {self.session_id}: {e}")
            await self._cleanup_connection()
    
    async def _cleanup_connection(self):
        """Clean up connection state."""
        self.connected = False
        self.authenticated = False
        self.connection = None
        self.authenticator = None
        await self._stop_heartbeat()
    
    async def is_connected(self) -> bool:
        """Check if the client is connected and authenticated."""
        return self.connected and self.authenticated and self.connection is not None
    
    async def send_request(self, template_id: int, template_name: str, 
                          request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send an API request and return the response."""
        if not await self.is_connected():
            raise RuntimeError(f"Session {self.session_id} is not connected")
        
        start_time = datetime.now()
        self.request_count += 1
        self.last_activity = start_time
        
        try:
            # Create request using protocol buffer
            request_message = await self._create_protobuf_message(template_name, request_data)
            
            # Serialize and send
            serialized_request = request_message.SerializeToString()
            logger.debug(f"Sending request for template {template_id} ({len(serialized_request)} bytes)")
            
            success = await self.connection.send_message(serialized_request)
            if not success:
                raise RuntimeError("Failed to send request message")
            
            # Wait for response
            response_bytes = await self.connection.receive_message(
                timeout=self.config.message_timeout
            )
            
            if not response_bytes:
                raise RuntimeError("No response received within timeout")
            
            # Parse response
            response_data = await self._parse_response(response_bytes, template_id)
            
            end_time = datetime.now()
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Log request/response to history
            await self._log_request_response(
                template_id, template_name, request_data, response_data, 
                execution_time_ms, success=True
            )
            
            return response_data
            
        except Exception as e:
            end_time = datetime.now()
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Log failed request
            await self._log_request_response(
                template_id, template_name, request_data, None,
                execution_time_ms, success=False, error=str(e)
            )
            
            logger.error(f"Error sending request for template {template_id}: {e}")
            raise
    
    async def _create_protobuf_message(self, template_name: str, request_data: Dict[str, Any]):
        """Create a protocol buffer message from request data."""
        try:
            # Import the appropriate protobuf module
            module_name = f"{template_name}_pb2"
            proto_module = __import__(module_name)
            
            # Get the message class (convert snake_case to PascalCase)
            class_name = ''.join(word.capitalize() for word in template_name.split('_'))
            message_class = getattr(proto_module, class_name)
            
            # Create and populate the message
            message = message_class()
            
            for field_name, field_value in request_data.items():
                if hasattr(message, field_name):
                    if isinstance(field_value, list):
                        # Handle repeated fields
                        field = getattr(message, field_name)
                        for item in field_value:
                            field.append(item)
                    else:
                        setattr(message, field_name, field_value)
            
            return message
            
        except ImportError as e:
            raise RuntimeError(f"Could not import protobuf module {module_name}: {e}")
        except AttributeError as e:
            raise RuntimeError(f"Could not find message class {class_name}: {e}")
        except Exception as e:
            raise RuntimeError(f"Error creating protobuf message: {e}")
    
    async def _parse_response(self, response_bytes: bytes, template_id: int) -> Dict[str, Any]:
        """Parse a response message."""
        try:
            # Use message_handler to parse the response
            message_info = parse_message(response_bytes)
            
            if not message_info:
                return {
                    "raw_bytes_length": len(response_bytes),
                    "parsed": False,
                    "error": "Could not parse response message"
                }
            
            response_data = {
                "template_id": message_info.template_id,
                "template_name": message_info.template_name,
                "message_size": len(response_bytes),
                "parsed": True
            }
            
            # Add parsed fields if available
            if hasattr(message_info, 'fields'):
                response_data.update(message_info.fields)
            
            return response_data
            
        except Exception as e:
            logger.warning(f"Error parsing response for template {template_id}: {e}")
            return {
                "raw_bytes_length": len(response_bytes),
                "parsed": False,
                "error": str(e),
                "template_id": template_id
            }
    
    async def _log_request_response(self, template_id: int, template_name: str,
                                  request_data: Dict[str, Any], response_data: Optional[Dict[str, Any]],
                                  execution_time_ms: float, success: bool = True, 
                                  error: Optional[str] = None):
        """Log request/response to history."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "template_id": template_id,
            "template_name": template_name,
            "infrastructure_plant": self.infrastructure_plant,
            "request_data": request_data,
            "response_data": response_data,
            "execution_time_ms": execution_time_ms,
            "success": success,
            "error": error
        }
        
        self.request_history.append(log_entry)
        
        # Log to file if configured
        if self.config.save_responses:
            await self._save_log_entry(log_entry)
    
    async def _save_log_entry(self, log_entry: Dict[str, Any]):
        """Save log entry to file."""
        try:
            log_dir = Path("playground_logs")
            log_dir.mkdir(exist_ok=True)
            
            log_file = log_dir / f"session_{self.session_id}.jsonl"
            
            with open(log_file, 'a') as f:
                json.dump(log_entry, f)
                f.write('\n')
                
        except Exception as e:
            logger.warning(f"Error saving log entry: {e}")
    
    async def get_session_info(self) -> Dict[str, Any]:
        """Get session information."""
        return {
            "session_id": self.session_id,
            "infrastructure_plant": self.infrastructure_plant,
            "connection_status": "connected" if self.connected else "disconnected",
            "authenticated": self.authenticated,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "request_count": self.request_count,
            "history_count": len(self.request_history),
            "streaming_data_count": len(self.streaming_data)
        }
    
    async def get_request_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get request history for this session."""
        history = list(self.request_history)
        history.reverse()  # Most recent first
        return history[:limit]
    
    async def clear_request_history(self):
        """Clear request history for this session."""
        self.request_history.clear()
        self.streaming_data.clear()
    
    async def _start_heartbeat(self):
        """Start heartbeat task to keep connection alive."""
        if self.heartbeat_task:
            return
        
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        logger.debug(f"Started heartbeat for session {self.session_id}")
    
    async def _stop_heartbeat(self):
        """Stop heartbeat task."""
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            self.heartbeat_task = None
            logger.debug(f"Stopped heartbeat for session {self.session_id}")
    
    async def _heartbeat_loop(self):
        """Heartbeat loop to maintain connection."""
        try:
            while self.connected:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.connected:
                    break
                
                try:
                    # Send heartbeat request
                    await self._send_heartbeat()
                    self.last_activity = datetime.now()
                    
                except Exception as e:
                    logger.warning(f"Heartbeat failed for session {self.session_id}: {e}")
                    # Don't disconnect on heartbeat failure, just log it
                    
        except asyncio.CancelledError:
            logger.debug(f"Heartbeat loop cancelled for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error in heartbeat loop for session {self.session_id}: {e}")
    
    async def _send_heartbeat(self):
        """Send a heartbeat message."""
        if not self.connection:
            return
        
        try:
            # Create heartbeat request
            import request_heartbeat_pb2
            
            heartbeat_request = request_heartbeat_pb2.RequestHeartbeat()
            heartbeat_request.template_id = 18  # Heartbeat request template ID
            heartbeat_request.user_msg.append(f"Heartbeat from playground session {self.session_id}")
            
            # Send heartbeat
            serialized_request = heartbeat_request.SerializeToString()
            await self.connection.send_message(serialized_request)
            
            # Wait for heartbeat response (optional)
            try:
                response_bytes = await self.connection.receive_message(timeout=5.0)
                if response_bytes:
                    # Parse heartbeat response
                    message_info = parse_message(response_bytes)
                    if message_info and message_info.template_id == 19:  # Heartbeat response
                        logger.debug(f"Heartbeat response received for session {self.session_id}")
            except asyncio.TimeoutError:
                # Heartbeat response timeout is not critical
                pass
                
        except Exception as e:
            logger.warning(f"Error sending heartbeat for session {self.session_id}: {e}")
    
    async def start_streaming(self, template_id: int, template_name: str, 
                            request_data: Dict[str, Any]) -> bool:
        """Start streaming data subscription."""
        try:
            # Send subscription request
            await self.send_request(template_id, template_name, request_data)
            
            # Start streaming data collection task
            streaming_task = asyncio.create_task(self._collect_streaming_data())
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting streaming for session {self.session_id}: {e}")
            return False
    
    async def _collect_streaming_data(self):
        """Collect streaming data in the background."""
        try:
            while self.connected:
                try:
                    # Receive streaming messages
                    message_bytes = await self.connection.receive_message(timeout=1.0)
                    
                    if message_bytes:
                        message_info = parse_message(message_bytes)
                        if message_info:
                            streaming_entry = {
                                "timestamp": datetime.now().isoformat(),
                                "session_id": self.session_id,
                                "template_id": message_info.template_id,
                                "template_name": message_info.template_name,
                                "data": getattr(message_info, 'fields', {})
                            }
                            self.streaming_data.append(streaming_entry)
                            
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    logger.warning(f"Error collecting streaming data: {e}")
                    break
                    
        except asyncio.CancelledError:
            logger.debug(f"Streaming data collection cancelled for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error in streaming data collection for session {self.session_id}: {e}")
    
    async def get_streaming_data(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent streaming data."""
        data = list(self.streaming_data)
        data.reverse()  # Most recent first
        return data[:limit]