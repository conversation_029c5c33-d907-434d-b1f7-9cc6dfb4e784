"""
Process Manager Service
======================

Service for managing and monitoring simple-demos scripts and other processes.
"""

import os
import sys
import subprocess
import psutil
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import threading
import queue

class ProcessManager:
    """Manager for controlling and monitoring processes."""
    
    def __init__(self):
        """Initialize process manager."""
        self.processes: Dict[str, Dict[str, Any]] = {}
        self.project_root = Path(__file__).parent.parent.parent
        self.simple_demos_path = self.project_root / "simple-demos"
        self.log_queues: Dict[str, queue.Queue] = {}
        
    def get_available_scripts(self) -> List[Dict[str, str]]:
        """Get list of available scripts that can be run."""
        scripts = []
        
        # Scan simple-demos directory for Python scripts
        script_categories = {
            "realtime": "Real-time Data Collection",
            "historical": "Historical Data Collection", 
            "search": "Symbol Search",
            "": "System Scripts"
        }
        
        for category, description in script_categories.items():
            category_path = self.simple_demos_path / category if category else self.simple_demos_path
            
            if category_path.exists():
                for script_file in category_path.glob("*.py"):
                    if script_file.name != "__init__.py":
                        script_path = str(script_file.relative_to(self.project_root))
                        scripts.append({
                            "name": script_file.stem,
                            "path": script_path,
                            "category": description,
                            "full_name": f"{category}/{script_file.name}" if category else script_file.name
                        })
        
        return sorted(scripts, key=lambda x: (x["category"], x["name"]))
    
    def start_process(self, script_name: str, args: List[str] = None) -> str:
        """Start a new process."""
        if args is None:
            args = []
            
        # Find the script
        script_path = self._find_script_path(script_name)
        if not script_path:
            raise ValueError(f"Script {script_name} not found")
        
        # Generate unique process ID
        process_id = str(uuid.uuid4())[:8]
        
        # Prepare command
        cmd = [sys.executable, str(script_path)] + args
        
        # Start process
        try:
            proc = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=str(self.project_root)
            )
            
            # Create log queue for this process
            log_queue = queue.Queue(maxsize=1000)
            self.log_queues[process_id] = log_queue
            
            # Start log monitoring thread
            log_thread = threading.Thread(
                target=self._monitor_process_output,
                args=(proc, log_queue),
                daemon=True
            )
            log_thread.start()
            
            # Store process info
            self.processes[process_id] = {
                "id": process_id,
                "name": script_name,
                "script_path": str(script_path),
                "status": "running",
                "pid": proc.pid,
                "start_time": datetime.now().isoformat(),
                "process": proc,
                "log_thread": log_thread
            }
            
            return process_id
            
        except Exception as e:
            raise RuntimeError(f"Failed to start process: {str(e)}")
    
    def stop_process(self, process_id: str) -> bool:
        """Stop a running process."""
        if process_id not in self.processes:
            return False
        
        proc_info = self.processes[process_id]
        proc = proc_info.get("process")
        
        if proc and proc.poll() is None:  # Process is still running
            try:
                # Try graceful termination first
                proc.terminate()
                
                # Wait up to 5 seconds for graceful shutdown
                try:
                    proc.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if graceful termination fails
                    proc.kill()
                    proc.wait()
                
                proc_info["status"] = "stopped"
                return True
            except Exception:
                return False
        
        return False
    
    def restart_process(self, process_id: str) -> bool:
        """Restart a process."""
        if process_id not in self.processes:
            return False
        
        proc_info = self.processes[process_id]
        script_name = proc_info["name"]
        
        # Stop the process first
        self.stop_process(process_id)
        
        # Remove old process info
        del self.processes[process_id]
        if process_id in self.log_queues:
            del self.log_queues[process_id]
        
        # Start new process
        try:
            new_process_id = self.start_process(script_name)
            return True
        except Exception:
            return False
    
    def get_process(self, process_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific process."""
        if process_id not in self.processes:
            return None
        
        proc_info = self.processes[process_id].copy()
        
        # Update process status and stats
        proc = proc_info.get("process")
        if proc:
            try:
                if proc.poll() is None:  # Still running
                    # Get process stats using psutil
                    psutil_proc = psutil.Process(proc.pid)
                    proc_info["status"] = "running"
                    proc_info["cpu_percent"] = psutil_proc.cpu_percent()
                    proc_info["memory_mb"] = psutil_proc.memory_info().rss / 1024 / 1024
                    
                    # Calculate uptime
                    start_time = datetime.fromisoformat(proc_info["start_time"])
                    uptime = datetime.now() - start_time
                    proc_info["uptime"] = str(uptime).split('.')[0]  # Remove microseconds
                else:
                    proc_info["status"] = "stopped"
                    proc_info["cpu_percent"] = 0.0
                    proc_info["memory_mb"] = 0.0
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                proc_info["status"] = "error"
        
        # Remove internal objects from response
        proc_info.pop("process", None)
        proc_info.pop("log_thread", None)
        
        return proc_info
    
    def get_all_processes(self) -> List[Dict[str, Any]]:
        """Get information about all managed processes."""
        return [self.get_process(pid) for pid in self.processes.keys()]
    
    def get_process_stats(self) -> Dict[str, int]:
        """Get overall process statistics."""
        stats = {"total_processes": 0, "running_processes": 0, "stopped_processes": 0, "error_processes": 0}
        
        for process_id in self.processes.keys():
            proc_info = self.get_process(process_id)
            if proc_info:
                stats["total_processes"] += 1
                status = proc_info["status"]
                if status == "running":
                    stats["running_processes"] += 1
                elif status == "stopped":
                    stats["stopped_processes"] += 1
                elif status == "error":
                    stats["error_processes"] += 1
        
        return stats
    
    def get_process_logs(self, process_id: str, lines: int = 100) -> List[Dict[str, str]]:
        """Get recent logs for a process."""
        if process_id not in self.log_queues:
            return []
        
        log_queue = self.log_queues[process_id]
        logs = []
        
        # Get logs from queue (non-blocking)
        temp_logs = []
        try:
            while not log_queue.empty():
                temp_logs.append(log_queue.get_nowait())
        except queue.Empty:
            pass
        
        # Return last N lines
        recent_logs = temp_logs[-lines:] if len(temp_logs) > lines else temp_logs
        
        for log_entry in recent_logs:
            logs.append({
                "timestamp": log_entry.get("timestamp", ""),
                "level": log_entry.get("level", "INFO"),
                "message": log_entry.get("message", "")
            })
        
        return logs
    
    def remove_process(self, process_id: str) -> bool:
        """Remove a stopped process from management."""
        if process_id not in self.processes:
            return False
        
        proc_info = self.processes[process_id]
        proc = proc_info.get("process")
        
        # Only allow removal of stopped processes
        if proc and proc.poll() is None:  # Still running
            return False
        
        # Clean up
        del self.processes[process_id]
        if process_id in self.log_queues:
            del self.log_queues[process_id]
        
        return True
    
    def _find_script_path(self, script_name: str) -> Optional[Path]:
        """Find the full path to a script by name."""
        # Check if it's already a path
        if "/" in script_name:
            script_path = self.project_root / script_name
            if script_path.exists():
                return script_path
        
        # Search in simple-demos subdirectories
        search_dirs = [
            self.simple_demos_path,
            self.simple_demos_path / "realtime",
            self.simple_demos_path / "historical", 
            self.simple_demos_path / "search"
        ]
        
        for search_dir in search_dirs:
            script_path = search_dir / f"{script_name}.py"
            if script_path.exists():
                return script_path
        
        return None
    
    def _monitor_process_output(self, proc: subprocess.Popen, log_queue: queue.Queue):
        """Monitor process output and store in log queue."""
        while True:
            line = proc.stdout.readline()
            if not line:
                break
            
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": line.strip()
            }
            
            try:
                log_queue.put(log_entry, block=False)
            except queue.Full:
                # Remove oldest entry and add new one
                try:
                    log_queue.get_nowait()
                    log_queue.put(log_entry, block=False)
                except queue.Empty:
                    pass