"""
Code Generator Service
======================

Service for generating Python scripts from web dashboard operations.
Ported from the original GUI CodeGenerator class with web-specific enhancements.
"""

import os
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pathlib import Path


class CodeGenerator:
    """
    Code generation framework for creating equivalent Python scripts from web dashboard operations.
    Generates standalone scripts with proper imports, authentication, and error handling.
    """
    
    def __init__(self, db_config: Optional[Dict] = None, rithmic_config: Optional[Dict] = None):
        """Initialize code generator with configuration dictionaries."""
        self.db_config = db_config or {}
        self.rithmic_config = rithmic_config or {}
        
    def get_base_imports(self) -> str:
        """Generate base imports for all scripts."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return f'''#!/usr/bin/env python3
"""
Generated script from Professional Futures Trading Web Dashboard
Auto-generated on: {timestamp}
"""

import os
import sys
import time
import json
import threading
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import mysql.connector
from mysql.connector import Error

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not available. Install with: pip install python-dotenv")

# Add src directory to path for local imports
script_dir = Path(__file__).parent
src_dir = script_dir / 'src'
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

try:
    from database.database_manager import DatabaseManager
except ImportError:
    print("Warning: DatabaseManager not available. Database operations may not work.")
'''

    def get_database_config_code(self, use_env_vars: bool = True) -> str:
        """Generate database configuration code."""
        if use_env_vars or not self.db_config:
            return '''
# Database configuration (using environment variables)
database_config = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'rithmic_api')
}

def get_database_manager():
    """Create and return database manager instance."""
    try:
        return DatabaseManager(database_config)
    except Exception as e:
        print(f"Error creating database manager: {e}")
        return None
'''
        else:
            # Use actual config values (be careful with passwords)
            password_display = "****" if self.db_config.get('password') else ""
            return f'''
# Database configuration from web dashboard settings
database_config = {{
    'host': '{self.db_config.get('host', 'localhost')}',
    'port': {self.db_config.get('port', 3306)},
    'user': '{self.db_config.get('user', 'root')}',
    'password': '{password_display}',  # Update with actual password
    'database': '{self.db_config.get('database', 'rithmic_api')}'
}}

def get_database_manager():
    """Create and return database manager instance."""
    try:
        return DatabaseManager(database_config)
    except Exception as e:
        print(f"Error creating database manager: {{e}}")
        return None
'''

    def get_rithmic_config_code(self, use_env_vars: bool = True) -> str:
        """Generate Rithmic API configuration code."""
        if use_env_vars or not self.rithmic_config:
            return '''
# Rithmic API configuration (using environment variables)
rithmic_config = {
    'uri': os.getenv('RITHMIC_URI', 'wss://rituz00100.rithmic.com:443'),
    'user': os.getenv('RITHMIC_USER', 'PP-013155'),
    'password': os.getenv('RITHMIC_PASSWORD', 'b7neA8k6JA'),
    'system_name': os.getenv('RITHMIC_SYSTEM_NAME', 'Rithmic Paper Trading'),
    'infra_type': os.getenv('RITHMIC_INFRA_TYPE', 'Ticker Plant')
}
'''
        else:
            # Use actual config values (be careful with passwords)
            password_display = "****" if self.rithmic_config.get('password') else ""
            return f'''
# Rithmic API configuration from web dashboard settings
rithmic_config = {{
    'uri': '{self.rithmic_config.get('uri', 'wss://rituz00100.rithmic.com:443')}',
    'user': '{self.rithmic_config.get('user', 'PP-013155')}',
    'password': '{password_display}',  # Update with actual password
    'system_name': '{self.rithmic_config.get('system_name', 'Rithmic Paper Trading')}',
    'infra_type': '{self.rithmic_config.get('infra_type', 'Ticker Plant')}'
}}
'''

    def generate_simple_query_script(self, table_name: str, where_clause: Optional[str] = None, 
                                   export_csv: bool = True) -> str:
        """Generate a simple single-threaded database query script."""
        
        script = self.get_base_imports()
        script += self.get_database_config_code()
        
        where_condition = f' WHERE {where_clause}' if where_clause else ''
        where_comment = f'# Filter: {where_clause}' if where_clause else '# No filter applied'
        
        script += f'''

def query_{table_name}():
    """Query data from {table_name} table (single-threaded)."""
    
    print(f"🔍 Starting simple query for table: {table_name}")
    print("=" * 50)
    
    # Database configuration
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Failed to initialize database manager")
        return False
    
    try:
        # Test connection
        if not db_manager.test_connection():
            print("❌ Failed to connect to database")
            return False
            
        print("✅ Database connection successful")
        
        # Build query
        query = "SELECT * FROM `{table_name}`{where_condition}"
        {where_comment}
        
        print(f"📊 Executing query: {{query[:100]}}" + ("..." if len(query) > 100 else ""))
        
        # Execute query
        start_time = datetime.now()
        data = db_manager.execute_query(query)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"✅ Retrieved {{len(data)}} rows in {{execution_time:.2f}} seconds")
        
        # Show sample data
        if data:
            print("\\nSample data (first 3 rows):")
            for i, row in enumerate(data[:3]):
                print(f"Row {{i+1}}: {{dict(list(row.items())[:5])}}...")  # Show first 5 columns
'''

        if export_csv:
            script += f'''                
        # Export to CSV
        if data:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"{table_name}_simple_{{timestamp}}.csv"
            
            import csv
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                if data:
                    fieldnames = list(data[0].keys())
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
                    
            print(f"📄 Exported to {{csv_filename}}")
'''

        script += f'''            
        print("\\n" + "=" * 50)
        print("📊 SUMMARY")
        print(f"Rows retrieved: {{len(data):,}}")
        print(f"Execution time: {{execution_time:.2f}} seconds")
        print(f"Rows per second: {{len(data)/max(1,execution_time):,.0f}}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during query: {{e}}")
        return False

if __name__ == "__main__":
    success = query_{table_name}()
    print("\\n✅ Query completed successfully" if success else "\\n❌ Query failed")
'''
        
        return script

    def generate_multi_threaded_query_script(self, table_name: str, where_clause: Optional[str] = None,
                                           chunk_size: int = 10000, max_workers: str = "os.cpu_count()",
                                           threading_strategy: str = "ThreadPoolExecutor",
                                           export_csv: bool = True) -> str:
        """Generate a multi-threaded database query script."""
        
        script = self.get_base_imports()
        script += self.get_database_config_code()
        
        # Add threading imports based on strategy
        if threading_strategy == "ThreadPoolExecutor":
            script += "\nfrom concurrent.futures import ThreadPoolExecutor, as_completed\n"
        elif threading_strategy == "ProcessPoolExecutor":
            script += "\nfrom concurrent.futures import ProcessPoolExecutor, as_completed\n"
        elif threading_strategy == "asyncio":
            script += "\nimport asyncio\nimport aiomysql\n"
        
        where_condition = f' WHERE {where_clause}' if where_clause else ''
        where_comment = f'# Filter: {where_clause}' if where_clause else '# No filter applied'
        
        script += f'''
import csv
from threading import Lock

# Global variables for thread coordination
results_lock = Lock()
all_results = []
total_processed = 0

def get_table_count(db_manager):
    """Get total row count for the table."""
    query = "SELECT COUNT(*) as count FROM `{table_name}`{where_condition}"
    {where_comment}
    
    result = db_manager.execute_query(query)
    return result[0]['count'] if result else 0

def process_chunk(chunk_info):
    """Process a single chunk of data."""
    chunk_id, offset, limit = chunk_info
    
    # Create new database connection for this thread
    db_manager = get_database_manager()
    if not db_manager:
        print(f"❌ Thread {{chunk_id}}: Failed to create database manager")
        return []
    
    try:
        query = "SELECT * FROM `{table_name}`{where_condition} LIMIT {{limit}} OFFSET {{offset}}"
        {where_comment}
        
        start_time = datetime.now()
        chunk_data = db_manager.execute_query(query)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # Thread-safe result collection
        global total_processed
        with results_lock:
            all_results.extend(chunk_data)
            total_processed += len(chunk_data)
            
        print(f"✅ Thread {{chunk_id}}: Retrieved {{len(chunk_data)}} rows in {{execution_time:.2f}}s")
        return chunk_data
        
    except Exception as e:
        print(f"❌ Thread {{chunk_id}}: Error - {{e}}")
        return []
    finally:
        db_manager.close_connection()

def query_{table_name}_multithreaded():
    """Query data from {table_name} table using {threading_strategy}."""
    
    print(f"🚀 Starting multi-threaded query for table: {table_name}")
    print(f"Threading Strategy: {threading_strategy}")
    print(f"Chunk Size: {chunk_size:,} rows")
    print(f"Max Workers: {max_workers}")
    print("=" * 60)
    
    # Initialize database manager for counting
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Failed to initialize database manager")
        return False
    
    try:
        # Test connection
        if not db_manager.test_connection():
            print("❌ Failed to connect to database")
            return False
            
        print("✅ Database connection successful")
        
        # Get total row count
        total_rows = get_table_count(db_manager)
        print(f"📊 Total rows to process: {{total_rows:,}}")
        
        if total_rows == 0:
            print("ℹ️ No data found")
            return True
        
        # Calculate chunks
        chunks = []
        for i in range(0, total_rows, {chunk_size}):
            chunk_id = len(chunks) + 1
            offset = i
            limit = min({chunk_size}, total_rows - i)
            chunks.append((chunk_id, offset, limit))
        
        print(f"📋 Created {{len(chunks)}} chunks for processing")
        
        # Execute with chosen threading strategy
        start_time = datetime.now()
        
        max_workers_val = {max_workers}
        '''

        if threading_strategy == "ThreadPoolExecutor":
            script += '''
        with ThreadPoolExecutor(max_workers=max_workers_val) as executor:
            future_to_chunk = {executor.submit(process_chunk, chunk): chunk for chunk in chunks}
            
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    chunk_data = future.result()
                except Exception as exc:
                    print(f"❌ Chunk {chunk[0]} generated an exception: {exc}")
'''
        elif threading_strategy == "ProcessPoolExecutor":
            script += '''
        with ProcessPoolExecutor(max_workers=max_workers_val) as executor:
            future_to_chunk = {executor.submit(process_chunk, chunk): chunk for chunk in chunks}
            
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    chunk_data = future.result()
                except Exception as exc:
                    print(f"❌ Chunk {chunk[0]} generated an exception: {exc}")
'''
        elif threading_strategy == "asyncio":
            script += '''
        # Note: Asyncio implementation would require async/await pattern
        # This is a simplified version - full asyncio would need aiomysql
        print("⚠️ Asyncio implementation requires additional setup with aiomysql")
        print("Falling back to ThreadPoolExecutor...")
        
        with ThreadPoolExecutor(max_workers=max_workers_val) as executor:
            future_to_chunk = {executor.submit(process_chunk, chunk): chunk for chunk in chunks}
            
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    chunk_data = future.result()
                except Exception as exc:
                    print(f"❌ Chunk {chunk[0]} generated an exception: {exc}")
'''

        script += '''
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\\n✅ Multi-threaded processing completed!")
        print(f"Total rows retrieved: {total_processed:,}")
        print(f"Total execution time: {execution_time:.2f} seconds")
        print(f"Rows per second: {total_processed/max(1,execution_time):,.0f}")
        print(f"Chunks processed: {len(chunks)}")
        print(f"Average chunk time: {execution_time/len(chunks):.2f} seconds")
'''

        if export_csv:
            script += f'''
        # Export to CSV
        if all_results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"{table_name}_multithreaded_{{timestamp}}.csv"
            
            print(f"\\n📄 Exporting {{len(all_results):,}} rows to {{csv_filename}}...")
            
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                if all_results:
                    fieldnames = list(all_results[0].keys())
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(all_results)
                    
            print(f"✅ Export completed: {{csv_filename}}")
'''

        script += f'''        
        return True
        
    except Exception as e:
        print(f"❌ Error during multi-threaded query: {{e}}")
        return False
    finally:
        db_manager.close_connection()

if __name__ == "__main__":
    success = query_{table_name}_multithreaded()
    print("\\n🎉 Multi-threaded query completed successfully" if success else "\\n❌ Multi-threaded query failed")
'''
        
        return script

    def generate_configuration_script(self, include_database: bool = True, 
                                    include_rithmic: bool = True, use_env_vars: bool = True) -> str:
        """Generate configuration setup script."""
        
        script = self.get_base_imports()
        
        if include_database:
            script += self.get_database_config_code(use_env_vars)
        
        if include_rithmic:
            script += self.get_rithmic_config_code(use_env_vars)
        
        script += '''

def test_database_connection():
    """Test database connection."""
    print("🔍 Testing database connection...")
    
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Failed to create database manager")
        return False
    
    try:
        if db_manager.test_connection():
            print("✅ Database connection successful")
            
            # Show database info
            connection_info = db_manager.get_connection_info()
            print(f"📊 Connected to: {connection_info}")
            
            # Show table count
            tables = db_manager.get_tables()
            print(f"📋 Available tables: {len(tables)}")
            
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False
    finally:
        db_manager.close_connection()
'''

        if include_rithmic:
            script += '''

def test_rithmic_configuration():
    """Test Rithmic API configuration."""
    print("\\n🔍 Testing Rithmic API configuration...")
    
    # Basic configuration validation
    required_fields = ['uri', 'user', 'password', 'system_name']
    missing_fields = []
    
    for field in required_fields:
        if not rithmic_config.get(field):
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ Missing Rithmic configuration fields: {missing_fields}")
        return False
    
    print(f"✅ Rithmic configuration appears valid")
    print(f"📡 URI: {rithmic_config['uri']}")
    print(f"👤 User: {rithmic_config['user']}")
    print(f"🖥️ System: {rithmic_config['system_name']}")
    print(f"🏭 Infrastructure: {rithmic_config['infra_type']}")
    
    # Note: Full connection test would require Rithmic API implementation
    print("ℹ️ Note: Full API connection test requires Rithmic WebSocket implementation")
    
    return True
'''

        script += '''

def main():
    """Main configuration test function."""
    print("🚀 Configuration Test Script")
    print("=" * 50)
    
    success = True
    
'''

        if include_database:
            script += '''    # Test database
    if not test_database_connection():
        success = False
'''

        if include_rithmic:
            script += '''    # Test Rithmic API
    if not test_rithmic_configuration():
        success = False
'''

        script += '''    
    print("\\n" + "=" * 50)
    if success:
        print("🎉 All configuration tests passed!")
    else:
        print("❌ Some configuration tests failed")
        
    return success

if __name__ == "__main__":
    main()
'''
        
        return script

    def generate_data_collection_script(self, contracts: List[str], data_types: List[str], 
                                      collection_mode: str = "continuous") -> str:
        """Generate data collection script for market data gathering."""
        
        script = self.get_base_imports()
        script += self.get_database_config_code()
        script += self.get_rithmic_config_code()
        
        contracts_str = "', '".join(contracts) if contracts else "ES_U23"
        data_types_str = "', '".join(data_types) if data_types else "level1"
        
        script += f'''

def main():
    """Main data collection function."""
    print("🚀 Starting Professional Futures Data Collection")
    print("=" * 60)
    
    # Configuration
    selected_contracts = ['{contracts_str}']
    selected_data_types = ['{data_types_str}']
    collection_mode = '{collection_mode}'
    
    print(f"📊 Contracts: {{selected_contracts}}")
    print(f"📈 Data types: {{selected_data_types}}")
    print(f"⚙️ Mode: {{collection_mode}}")
    print()
    
    # Initialize database manager
    db_manager = get_database_manager()
    if not db_manager:
        print("❌ Failed to initialize database manager")
        return False
    
    # Test database connection
    if not db_manager.test_connection():
        print("❌ Database connection test failed")
        return False
    
    print("✅ Database connection successful")
    
    processes = []
    
    try:
'''

        # Add process creation code for each data type
        for data_type in data_types:
            if data_type == 'level1':
                script += f'''
        # Start Level 1 market data collection
        print("📊 Starting Level 1 market data collection...")
        level1_cmd = [
            sys.executable, 
            'simple-demos/level1_bbo_trades.py',
            '--contracts', '{contracts_str}'
        ]
        level1_process = subprocess.Popen(level1_cmd, 
                                        stdout=subprocess.PIPE, 
                                        stderr=subprocess.PIPE,
                                        text=True)
        processes.append(('Level 1', level1_process))
'''
            elif data_type == 'level3':
                script += f'''
        # Start Level 3 depth data collection
        print("📚 Starting Level 3 depth data collection...")
        level3_cmd = [
            sys.executable, 
            'simple-demos/level3_depth_data.py',
            '--contracts', '{contracts_str}'
        ]
        level3_process = subprocess.Popen(level3_cmd, 
                                        stdout=subprocess.PIPE, 
                                        stderr=subprocess.PIPE,
                                        text=True)
        processes.append(('Level 3', level3_process))
'''
            elif data_type == 'historical':
                script += f'''
        # Start historical data collection
        print("🕐 Starting historical data collection...")
        historical_cmd = [
            sys.executable, 
            'simple-demos/historical_time_bars.py',
            '--contracts', '{contracts_str}'
        ]
        historical_process = subprocess.Popen(historical_cmd, 
                                            stdout=subprocess.PIPE, 
                                            stderr=subprocess.PIPE,
                                            text=True)
        processes.append(('Historical', historical_process))
'''

        script += f'''        
        print(f"\\n🏃 Started {{len(processes)}} data collection processes")
        
        if collection_mode == 'continuous':
            print("\\n⏰ Running in continuous mode. Press Ctrl+C to stop...")
            print("Process status will be monitored every 30 seconds")
            
            try:
                while True:
                    # Monitor processes
                    active_processes = []
                    for name, process in processes:
                        if process.poll() is None:  # Still running
                            active_processes.append(name)
                        else:
                            print(f"⚠️ Process {{name}} has stopped (exit code: {{process.returncode}})")
                    
                    if active_processes:
                        print(f"✅ Active processes: {{', '.join(active_processes)}}")
                    else:
                        print("❌ All processes have stopped")
                        break
                    
                    time.sleep(30)  # Check every 30 seconds
                    
            except KeyboardInterrupt:
                print("\\n🛑 Stopping data collection...")
                
        elif collection_mode == 'sample':
            print("\\n📋 Running in sample mode (60 seconds)...")
            time.sleep(60)
            print("⏱️ Sample collection completed")
            
        else:  # historical mode
            print("\\n📊 Running in historical mode...")
            print("Waiting for processes to complete...")
            
            # Wait for all processes to complete
            for name, process in processes:
                process.wait()
                print(f"✅ {{name}} process completed (exit code: {{process.returncode}})")
        
        # Cleanup
        for name, process in processes:
            if process.poll() is None:  # Still running
                print(f"🛑 Terminating {{name}} process...")
                process.terminate()
                process.wait()
        
        print("\\n" + "=" * 60)
        print("📊 DATA COLLECTION SUMMARY")
        print(f"Contracts processed: {{len(selected_contracts)}}")
        print(f"Data types collected: {{len(selected_data_types)}}")
        print(f"Collection mode: {{collection_mode}}")
        print("🎉 Data collection completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during data collection: {{e}}")
        
        # Cleanup on error
        for name, process in processes:
            if process.poll() is None:
                process.terminate()
                
        return False
    finally:
        db_manager.close_connection()

if __name__ == "__main__":
    success = main()
    print("\\n✅ Data collection completed successfully" if success else "\\n❌ Data collection failed")
'''
        
        return script
    
    def generate_symbol_search_script(self, filters=None):
        """Generate symbol search and discovery script."""
        filters = filters or {}
        
        script = self.get_base_imports()
        script += self.get_database_config_code()
        
        script += f'''

def discover_symbols(exchange_filter="{filters.get('exchange_filter', 'All')}", 
                    symbol_pattern="{filters.get('symbol_pattern', '')}", 
                    product_code="{filters.get('product_code', 'All')}"):
    """
    Discover futures symbols with pattern matching and filtering.
    
    Args:
        exchange_filter: Exchange to filter (All, CME, CBOT, NYMEX, COMEX, ICE)
        symbol_pattern: Pattern with wildcards (ES*, *Z25, NQ*25, etc.)
        product_code: Product code filter (All, ES, NQ, YM, RTY, etc.)
    """
    print("🔍 Professional Symbol Discovery Tool")
    print("=" * 50)
    print(f"Exchange Filter: {{exchange_filter}}")
    print(f"Symbol Pattern: {{symbol_pattern or 'None'}}")
    print(f"Product Code: {{product_code}}")
    print()
    
    # Sample symbols database (in production, query from real API/database)
    sample_symbols = {{
        'ES': ['ESZ24', 'ESH25', 'ESM25', 'ESU25', 'ESZ25', 'ESH26'],
        'NQ': ['NQZ24', 'NQH25', 'NQM25', 'NQU25', 'NQZ25', 'NQH26'], 
        'YM': ['YMZ24', 'YMH25', 'YMM25', 'YMU25', 'YMZ25', 'YMH26'],
        'RTY': ['RTYZ24', 'RTYH25', 'RTYM25', 'RTYU25', 'RTYZ25'],
        'ZN': ['ZNZ24', 'ZNH25', 'ZNM25', 'ZNU25', 'ZNZ25'],
        'ZB': ['ZBZ24', 'ZBH25', 'ZBM25', 'ZBU25', 'ZBZ25'],
        'CL': ['CLZ24', 'CLF25', 'CLG25', 'CLH25', 'CLJ25'],
        'GC': ['GCZ24', 'GCG25', 'GCJ25', 'GCM25', 'GCQ25']
    }}
    
    # Symbol metadata mapping
    metadata_map = {{
        'ES': {{'exchange': 'CME', 'description': 'E-mini S&P 500', 'tick_size': 0.25, 'contract_size': 50}},
        'NQ': {{'exchange': 'CME', 'description': 'E-mini NASDAQ-100', 'tick_size': 0.25, 'contract_size': 20}},
        'YM': {{'exchange': 'CBOT', 'description': 'E-mini Dow Jones', 'tick_size': 1.0, 'contract_size': 5}},
        'RTY': {{'exchange': 'CME', 'description': 'E-mini Russell 2000', 'tick_size': 0.1, 'contract_size': 50}},
        'ZN': {{'exchange': 'CBOT', 'description': '10-Year Treasury Note', 'tick_size': 0.015625, 'contract_size': 100000}},
        'ZB': {{'exchange': 'CBOT', 'description': '30-Year Treasury Bond', 'tick_size': 0.03125, 'contract_size': 100000}},
        'CL': {{'exchange': 'NYMEX', 'description': 'Crude Oil', 'tick_size': 0.01, 'contract_size': 1000}},
        'GC': {{'exchange': 'COMEX', 'description': 'Gold', 'tick_size': 0.1, 'contract_size': 100}}
    }}
    
    # Apply filters
    symbols_to_process = sample_symbols.copy()
    
    # Filter by product code
    if product_code != 'All':
        symbols_to_process = {{product_code: symbols_to_process.get(product_code, [])}}
    
    # Filter by exchange
    if exchange_filter != 'All':
        filtered_symbols = {{}}
        for prod_code, symbols in symbols_to_process.items():
            metadata = metadata_map.get(prod_code, {{}})
            if metadata.get('exchange') == exchange_filter:
                filtered_symbols[prod_code] = symbols
        symbols_to_process = filtered_symbols
    
    # Filter by pattern
    if symbol_pattern.strip():
        import re
        pattern = symbol_pattern.strip()
        pattern_regex = re.compile(pattern.replace('*', '.*'), re.IGNORECASE)
        
        filtered_symbols = {{}}
        for prod_code, symbols in symbols_to_process.items():
            matching_symbols = [s for s in symbols if pattern_regex.match(s)]
            if matching_symbols:
                filtered_symbols[prod_code] = matching_symbols
        symbols_to_process = filtered_symbols
    
    # Generate results
    discovered_symbols = []
    for prod_code, symbols in symbols_to_process.items():
        metadata = metadata_map.get(prod_code, {{}})
        for symbol in symbols:
            symbol_info = {{
                'symbol': symbol,
                'exchange': metadata.get('exchange', 'Unknown'),
                'product_code': prod_code,
                'description': f"{{metadata.get('description', prod_code)}} ({{symbol}})",
                'tick_size': metadata.get('tick_size', 0.01),
                'contract_size': metadata.get('contract_size', 1),
                'status': 'Discovered'
            }}
            discovered_symbols.append(symbol_info)
    
    # Display results
    print(f"✅ Discovered {{len(discovered_symbols)}} symbols:")
    print()
    
    if discovered_symbols:
        # Print header
        print(f"{{'Symbol':<10}} {{'Exchange':<8}} {{'Product':<8}} {{'Description':<30}} {{'Tick Size':<12}} {{'Contract Size':<15}}")
        print("-" * 90)
        
        # Print symbols
        for symbol_info in discovered_symbols:
            print(f"{{symbol_info['symbol']:<10}} {{symbol_info['exchange']:<8}} {{symbol_info['product_code']:<8}} "
                  f"{{symbol_info['description'][:30]:<30}} {{symbol_info['tick_size']:<12}} {{symbol_info['contract_size']:<15}}")
    else:
        print("No symbols found matching the criteria.")
    
    return discovered_symbols

def save_symbols_to_csv(symbols, filename=None):
    """Save discovered symbols to CSV file."""
    if not symbols:
        print("No symbols to save.")
        return
    
    import csv
    from datetime import datetime
    
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"discovered_symbols_{{timestamp}}.csv"
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['symbol', 'exchange', 'product_code', 'description', 'tick_size', 'contract_size', 'status']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(symbols)
        
        print(f"📤 Saved {{len(symbols)}} symbols to {{filename}}")
        return filename
        
    except Exception as e:
        print(f"❌ Error saving CSV: {{e}}")
        return None

def save_symbols_to_database(symbols):
    """Save discovered symbols to database."""
    if not symbols:
        print("No symbols to save.")
        return
    
    try:
        db_manager = get_database_manager()
        if not db_manager:
            print("❌ Database connection failed")
            return
        
        # Create symbols table if not exists
        create_table_query = """
CREATE TABLE IF NOT EXISTS symbols (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL UNIQUE,
    exchange VARCHAR(20),
    product_code VARCHAR(10),
    symbol_name TEXT,
    tick_size DECIMAL(10,6),
    lot_size INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_exchange (exchange),
    INDEX idx_product_code (product_code)
)
        """
        
        connection = db_manager.get_connection()
        cursor = connection.cursor()
        cursor.execute(create_table_query)
        connection.commit()
        
        # Insert symbols
        saved_count = 0
        for symbol_info in symbols:
            try:
                insert_query = """
INSERT INTO symbols (symbol, exchange, product_code, symbol_name, tick_size, lot_size)
VALUES (%s, %s, %s, %s, %s, %s)
ON DUPLICATE KEY UPDATE
exchange = VALUES(exchange),
product_code = VALUES(product_code),
symbol_name = VALUES(symbol_name),
tick_size = VALUES(tick_size),
lot_size = VALUES(lot_size),
updated_at = CURRENT_TIMESTAMP
                """
                
                cursor.execute(insert_query, (
                    symbol_info['symbol'],
                    symbol_info['exchange'],
                    symbol_info['product_code'],
                    symbol_info['description'],
                    symbol_info['tick_size'],
                    symbol_info['contract_size']
                ))
                saved_count += 1
                
            except Exception as e:
                print(f"⚠️  Error saving symbol {{symbol_info['symbol']}}: {{e}}")
                continue
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print(f"💾 Saved {{saved_count}} symbols to database")
        
    except Exception as e:
        print(f"❌ Database error: {{e}}")

def main():
    """Main function demonstrating symbol discovery."""
    print("🚀 Starting Symbol Discovery Demo")
    print("=" * 40)
    
    # Example 1: Discover all ES contracts
    print("\\n1. Discovering all ES contracts:")
    es_symbols = discover_symbols(product_code="ES")
    
    # Example 2: Discover December 2025 contracts across all products
    print("\\n2. Discovering December 2025 contracts:")
    dec_symbols = discover_symbols(symbol_pattern="*Z25")
    
    # Example 3: Discover CME contracts with specific pattern
    print("\\n3. Discovering CME NQ contracts:")
    nq_symbols = discover_symbols(exchange_filter="CME", symbol_pattern="NQ*")
    
    # Save results
    if es_symbols:
        save_symbols_to_csv(es_symbols, "es_contracts.csv")
        save_symbols_to_database(es_symbols)

if __name__ == "__main__":
    main()
'''

        return script
    
    def generate_rithmic_api_script(self, template_id: int, template_name: str, 
                                  infrastructure_plant: str, request_data: dict,
                                  language: str = "python", include_error_handling: bool = True) -> str:
        """Generate enhanced Rithmic API interaction script."""
        
        if language.lower() == "python":
            return self._generate_enhanced_python_api_script(
                template_id, template_name, infrastructure_plant, request_data, include_error_handling
            )
        elif language.lower() == "javascript":
            return self._generate_enhanced_javascript_api_script(
                template_id, template_name, infrastructure_plant, request_data, include_error_handling
            )
        else:
            raise ValueError(f"Unsupported language: {language}")
    
    def _generate_enhanced_python_api_script(self, template_id: int, template_name: str, 
                                           infrastructure_plant: str, request_data: dict,
                                           include_error_handling: bool = True) -> str:
        """Generate enhanced Python script for Rithmic API interaction."""
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Format request data for code
        request_data_str = self._format_python_dict(request_data, indent=8)
        
        script = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generated Rithmic API Client - {template_name}
{"=" * 50}

Auto-generated code for Rithmic {infrastructure_plant} interaction.
Template ID: {template_id}
Generated on: {timestamp}

This code demonstrates how to:
- Connect to Rithmic {infrastructure_plant}
- Send {template_name} requests
- Handle responses and errors properly
- Maintain connection with heartbeats
"""

import asyncio
import websockets
import struct
import ssl
import logging
from datetime import datetime
from pathlib import Path

# Import the specific protobuf for this template
import {template_name}_pb2

# Configuration
RITHMIC_CONFIG = {{
    'infrastructure_plant': '{infrastructure_plant}',
    'uri': 'wss://rituz00100.rithmic.com:443',  # Update with correct URI
    'ssl_cert_path': 'etc/rithmic_ssl_cert_auth_params',
    'heartbeat_interval': 30,
    'message_timeout': 30.0,
    'max_reconnect_attempts': 3
}}

# Paper trading credentials (update with your credentials)
CREDENTIALS = {{
    'user': 'PP-013155',
    'password': 'b7neA8k6JA',
    'system': 'Rithmic Paper Trading'
}}

async def create_rithmic_connection(uri, ssl_cert_path=None):
    """Create and authenticate a connection to Rithmic infrastructure."""
    # Setup SSL context if certificate is provided
    ssl_context = None
    if ssl_cert_path and Path(ssl_cert_path).exists():
        ssl_context = ssl.create_default_context()
        ssl_context.load_verify_locations(ssl_cert_path)
    
    try:
        # Connect to Rithmic server
        websocket = await websockets.connect(
            uri, 
            ssl=ssl_context,
            max_size=10 * 1024 * 1024,  # 10MB max message size
            ping_interval=30,
            ping_timeout=10
        )
        
        logging.info(f"Connected to Rithmic server: {{uri}}")
        return websocket
        
    except Exception as e:
        logging.error(f"Failed to connect to Rithmic server: {{e}}")
        raise

async def send_protobuf_message(websocket, message):
    """Send a protobuf message with proper framing."""
    try:
        # Serialize the message
        serialized = message.SerializeToString()
        
        # Create message frame (4-byte length + message)
        message_length = struct.pack('>I', len(serialized))
        frame = message_length + serialized
        
        # Send the frame
        await websocket.send(frame)
        logging.debug(f"Sent message: {{type(message).__name__}} ({{len(serialized)}} bytes)")
        
        return True
        
    except Exception as e:
        logging.error(f"Error sending message: {{e}}")
        return False

async def receive_protobuf_message(websocket, timeout=30.0):
    """Receive and parse a protobuf message."""
    try:
        # Receive message frame with timeout
        frame = await asyncio.wait_for(websocket.recv(), timeout=timeout)
        
        if len(frame) < 4:
            raise ValueError("Invalid message frame (too short)")
        
        # Extract message length and data
        message_length = struct.unpack('>I', frame[:4])[0]
        message_data = frame[4:]
        
        if len(message_data) != message_length:
            raise ValueError(f"Message length mismatch: expected {{message_length}}, got {{len(message_data)}}")
        
        logging.debug(f"Received message: {{message_length}} bytes")
        return message_data
        
    except asyncio.TimeoutError:
        logging.warning("Timeout waiting for message response")
        return None
    except Exception as e:
        logging.error(f"Error receiving message: {{e}}")
        return None

def parse_rithmic_message(message_data):
    """Parse a Rithmic protobuf message and extract key information."""
    try:
        if len(message_data) < 8:
            return None
        
        # Extract template ID (first 4 bytes after length)
        template_id = struct.unpack('>I', message_data[:4])[0]
        
        message_info = {{
            "template_id": template_id,
            "message_size": len(message_data),
            "timestamp": datetime.now().isoformat(),
            "raw_data": message_data
        }}
        
        return message_info
        
    except Exception as e:
        logging.error(f"Error parsing message: {{e}}")
        return None

async def handle_heartbeat(websocket, heartbeat_interval=30):
    """Handle heartbeat messages to keep connection alive."""
    import request_heartbeat_pb2
    
    while True:
        try:
            await asyncio.sleep(heartbeat_interval)
            
            # Create heartbeat request
            heartbeat = request_heartbeat_pb2.RequestHeartbeat()
            heartbeat.template_id = 18
            heartbeat.user_msg.append(f"Heartbeat at {{datetime.now().isoformat()}}")
            
            # Send heartbeat
            success = await send_protobuf_message(websocket, heartbeat)
            if not success:
                logging.warning("Failed to send heartbeat")
                break
                
        except asyncio.CancelledError:
            break
        except Exception as e:
            logging.error(f"Error in heartbeat handler: {{e}}")
            break

async def main():
    """Main function demonstrating {template_name} usage."""
    websocket = None
    heartbeat_task = None
    
    try:
        # Connect to Rithmic server
        websocket = await create_rithmic_connection(
            RITHMIC_CONFIG['uri'],
            RITHMIC_CONFIG['ssl_cert_path']
        )
        
        # Start heartbeat task (if not System plant)
        if RITHMIC_CONFIG['infrastructure_plant'] != 'System':
            heartbeat_task = asyncio.create_task(
                handle_heartbeat(websocket, RITHMIC_CONFIG['heartbeat_interval'])
            )
        
        # Create the request message
        request_message = {template_name}_pb2.{self._to_pascal_case(template_name)}()
        
        # Populate request fields
        request_data = {request_data_str}
        
        for field_name, field_value in request_data.items():
            if hasattr(request_message, field_name):
                if isinstance(field_value, list):
                    # Handle repeated fields
                    field = getattr(request_message, field_name)
                    for item in field_value:
                        field.append(item)
                else:
                    setattr(request_message, field_name, field_value)
        
        logging.info(f"Sending {{type(request_message).__name__}} request...")
        
        # Send the request
        success = await send_protobuf_message(websocket, request_message)
        if not success:
            raise RuntimeError("Failed to send request message")
        
        # Wait for response
        response_data = await receive_protobuf_message(
            websocket, 
            timeout=RITHMIC_CONFIG['message_timeout']
        )
        
        if response_data:
            # Parse the response
            response_info = parse_rithmic_message(response_data)
            
            if response_info:
                logging.info("Response received:")
                logging.info(f"  Template ID: {{response_info['template_id']}}")
                logging.info(f"  Message size: {{response_info['message_size']}} bytes")
                logging.info(f"  Timestamp: {{response_info['timestamp']}}")
                
                # Handle specific response parsing here
                # You may need to import and use the appropriate response protobuf
                
            else:
                logging.warning("Could not parse response message")
        else:
            logging.warning("No response received within timeout")
    
    except Exception as e:
        logging.error(f"Error in main function: {{e}}")
        raise
    
    finally:
        # Cleanup
        if heartbeat_task:
            heartbeat_task.cancel()
            try:
                await heartbeat_task
            except asyncio.CancelledError:
                pass
        
        if websocket:
            await websocket.close()
            logging.info("Connection closed")

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Run the example
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info('Application interrupted by user')
    except Exception as e:
        logging.error(f'Application error: {{e}}')
        exit(1)
'''
        
        return script
    
    def _generate_enhanced_javascript_api_script(self, template_id: int, template_name: str, 
                                               infrastructure_plant: str, request_data: dict,
                                               include_error_handling: bool = True) -> str:
        """Generate enhanced JavaScript script for Rithmic API interaction."""
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Format request data for JavaScript
        request_data_str = self._format_javascript_object(request_data, indent=8)
        
        script = f'''#!/usr/bin/env node
/**
 * Generated Rithmic API Client - {template_name}
 * {"=" * 50}
 * 
 * Auto-generated code for Rithmic {infrastructure_plant} interaction.
 * Template ID: {template_id}
 * Generated on: {timestamp}
 * 
 * This code demonstrates how to:
 * - Connect to Rithmic {infrastructure_plant}
 * - Send {template_name} requests
 * - Handle responses and errors properly
 * - Maintain connection with heartbeats
 */

const WebSocket = require('ws');
const protobuf = require('protobufjs');
const fs = require('fs');
const path = require('path');

// Configuration
const RITHMIC_CONFIG = {{
    infrastructurePlant: '{infrastructure_plant}',
    uri: 'wss://rituz00100.rithmic.com:443',  // Update with correct URI
    sslCertPath: 'etc/rithmic_ssl_cert_auth_params',
    heartbeatInterval: 30000,
    messageTimeout: 30000,
    maxReconnectAttempts: 3
}};

// Paper trading credentials (update with your credentials)
const CREDENTIALS = {{
    user: 'PP-013155',
    password: 'b7neA8k6JA',
    system: 'Rithmic Paper Trading'
}};

function createRithmicConnection(uri, options = {{}}) {{
    return new Promise((resolve, reject) => {{
        try {{
            const ws = new WebSocket(uri, options);
            
            ws.on('open', () => {{
                console.log(`Connected to Rithmic server: ${{uri}}`);
                resolve(ws);
            }});
            
            ws.on('error', (error) => {{
                console.error('WebSocket connection error:', error);
                reject(error);
            }});
            
            ws.on('close', (code, reason) => {{
                console.log(`Connection closed: ${{code}} - ${{reason}}`);
            }});
            
        }} catch (error) {{
            reject(error);
        }}
    }});
}}

function sendProtobufMessage(ws, message) {{
    return new Promise((resolve, reject) => {{
        try {{
            // Serialize the message
            const serialized = message.encode(message).finish();
            
            // Create message frame (4-byte length + message)
            const lengthBuffer = Buffer.allocUnsafe(4);
            lengthBuffer.writeUInt32BE(serialized.length, 0);
            
            const frame = Buffer.concat([lengthBuffer, serialized]);
            
            // Send the frame
            ws.send(frame, (error) => {{
                if (error) {{
                    console.error('Error sending message:', error);
                    reject(error);
                }} else {{
                    console.log(`Sent message: ${{message.constructor.name}} (${{serialized.length}} bytes)`);
                    resolve(true);
                }}
            }});
            
        }} catch (error) {{
            console.error('Error preparing message:', error);
            reject(error);
        }}
    }});
}}

function receiveProtobufMessage(ws, timeout = 30000) {{
    return new Promise((resolve, reject) => {{
        const timer = setTimeout(() => {{
            reject(new Error('Timeout waiting for message response'));
        }}, timeout);
        
        ws.once('message', (frame) => {{
            clearTimeout(timer);
            
            try {{
                if (frame.length < 4) {{
                    throw new Error('Invalid message frame (too short)');
                }}
                
                // Extract message length and data
                const messageLength = frame.readUInt32BE(0);
                const messageData = frame.slice(4);
                
                if (messageData.length !== messageLength) {{
                    throw new Error(`Message length mismatch: expected ${{messageLength}}, got ${{messageData.length}}`);
                }}
                
                console.log(`Received message: ${{messageLength}} bytes`);
                resolve(messageData);
                
            }} catch (error) {{
                console.error('Error processing received message:', error);
                reject(error);
            }}
        }});
    }});
}}

function parseRithmicMessage(messageData) {{
    try {{
        if (messageData.length < 8) {{
            return null;
        }}
        
        // Extract template ID (first 4 bytes)
        const templateId = messageData.readUInt32BE(0);
        
        const messageInfo = {{
            template_id: templateId,
            message_size: messageData.length,
            timestamp: new Date().toISOString(),
            raw_data: messageData
        }};
        
        return messageInfo;
        
    }} catch (error) {{
        console.error('Error parsing message:', error);
        return null;
    }}
}}

async function handleHeartbeat(ws, heartbeatInterval = 30000) {{
    // Load heartbeat protobuf
    const root = await protobuf.load('proto/request_heartbeat.proto');
    const RequestHeartbeat = root.lookupType('RequestHeartbeat');
    
    const heartbeatTimer = setInterval(async () => {{
        try {{
            const heartbeatMessage = RequestHeartbeat.create({{
                template_id: 18,
                user_msg: [`Heartbeat at ${{new Date().toISOString()}}`]
            }});
            
            await sendProtobufMessage(ws, heartbeatMessage);
            
        }} catch (error) {{
            console.error('Error sending heartbeat:', error);
            clearInterval(heartbeatTimer);
        }}
    }}, heartbeatInterval);
    
    return heartbeatTimer;
}}

async function main() {{
    /** Main function demonstrating {template_name} usage. */
    let ws = null;
    let heartbeatTimer = null;
    
    try {{
        // Load protobuf definitions
        const root = await protobuf.load('proto/{template_name}.proto');
        const RequestMessage = root.lookupType('{self._to_pascal_case(template_name)}');
        
        // Connect to Rithmic server
        ws = await createRithmicConnection(RITHMIC_CONFIG.uri);
        
        // Start heartbeat (if not System plant)
        if (RITHMIC_CONFIG.infrastructurePlant !== 'System') {{
            heartbeatTimer = await handleHeartbeat(ws, RITHMIC_CONFIG.heartbeatInterval);
        }}
        
        // Create the request message
        const requestData = {request_data_str};
        
        const requestMessage = RequestMessage.create(requestData);
        
        console.log(`Sending ${{RequestMessage.name}} request...`);
        
        // Send the request
        await sendProtobufMessage(ws, requestMessage);
        
        // Wait for response
        const responseData = await receiveProtobufMessage(
            ws, 
            RITHMIC_CONFIG.messageTimeout
        );
        
        if (responseData) {{
            // Parse the response
            const responseInfo = parseRithmicMessage(responseData);
            
            if (responseInfo) {{
                console.log('Response received:');
                console.log(`  Template ID: ${{responseInfo.template_id}}`);
                console.log(`  Message size: ${{responseInfo.message_size}} bytes`);
                console.log(`  Timestamp: ${{responseInfo.timestamp}}`);
                
                // Handle specific response parsing here
                // You may need to load and use the appropriate response protobuf
                
            }} else {{
                console.warn('Could not parse response message');
            }}
        }} else {{
            console.warn('No response received within timeout');
        }}
    
    }} catch (error) {{
        console.error('Error in main function:', error);
        throw error;
    
    }} finally {{
        // Cleanup
        if (heartbeatTimer) {{
            clearInterval(heartbeatTimer);
        }}
        
        if (ws) {{
            ws.close();
            console.log('Connection closed');
        }}
    }}
}}

// Run the example
main().catch(console.error);
'''
        
        return script
    
    def _format_python_dict(self, data: dict, indent: int = 0) -> str:
        """Format a dictionary as Python code."""
        lines = ["{"]
        for key, value in data.items():
            if isinstance(value, str):
                lines.append(f"{' ' * (indent + 4)}'{key}': '{value}',")
            elif isinstance(value, list):
                if all(isinstance(item, str) for item in value):
                    items = ', '.join(f"'{item}'" for item in value)
                    lines.append(f"{' ' * (indent + 4)}'{key}': [{items}],")
                else:
                    lines.append(f"{' ' * (indent + 4)}'{key}': {value},")
            else:
                lines.append(f"{' ' * (indent + 4)}'{key}': {value},")
        lines.append(f"{' ' * indent}}}")
        return "\n".join(lines)
    
    def _format_javascript_object(self, data: dict, indent: int = 0) -> str:
        """Format a dictionary as JavaScript object."""
        lines = ["{"]
        for key, value in data.items():
            if isinstance(value, str):
                lines.append(f"{' ' * (indent + 4)}{key}: '{value}',")
            elif isinstance(value, list):
                if all(isinstance(item, str) for item in value):
                    items = ', '.join(f"'{item}'" for item in value)
                    lines.append(f"{' ' * (indent + 4)}{key}: [{items}],")
                else:
                    lines.append(f"{' ' * (indent + 4)}{key}: {value},")
            else:
                lines.append(f"{' ' * (indent + 4)}{key}: {value},")
        lines.append(f"{' ' * indent}}}")
        return "\n".join(lines)
    
    def _to_pascal_case(self, snake_str: str) -> str:
        """Convert snake_case to PascalCase."""
        return ''.join(word.capitalize() for word in snake_str.split('_'))