#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Testing Suite
======================

Comprehensive testing capabilities for the Rithmic API playground including
automated test sequences, load testing, and validation workflows.

Features:
- Automated test sequence execution
- Template validation and testing
- Load testing and performance analysis
- Integration testing across multiple plants
- Test result reporting and analytics
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import statistics

from session_manager import PlaygroundSessionManager, SessionTemplate
from protocol_parser import ProtocolParser, ValidationResult

logger = logging.getLogger(__name__)

@dataclass
class TestCase:
    """Individual test case definition."""
    name: str
    description: str
    template_id: int
    template_name: str
    infrastructure_plant: str
    request_data: Dict[str, Any]
    expected_response_fields: List[str] = None
    expected_success: bool = True
    timeout_seconds: float = 30.0
    tags: List[str] = None
    prerequisites: List[str] = None  # Other test cases that must pass first
    
    def __post_init__(self):
        if self.expected_response_fields is None:
            self.expected_response_fields = []
        if self.tags is None:
            self.tags = []
        if self.prerequisites is None:
            self.prerequisites = []

@dataclass
class TestResult:
    """Result of a single test case execution."""
    test_case_name: str
    session_id: str
    success: bool
    execution_time_ms: float
    response_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    validation_errors: List[str] = None
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()
        if self.validation_errors is None:
            self.validation_errors = []

@dataclass
class TestSuiteResult:
    """Result of a complete test suite execution."""
    suite_name: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    total_execution_time_ms: float
    success_rate: float
    test_results: List[TestResult]
    session_analytics: Dict[str, Any]
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()

class AdvancedTestingSuite:
    """Advanced testing suite for the API playground."""
    
    def __init__(self, session_manager: PlaygroundSessionManager, 
                 protocol_parser: ProtocolParser):
        """Initialize the testing suite."""
        self.session_manager = session_manager
        self.protocol_parser = protocol_parser
        self.test_cases: Dict[str, TestCase] = {}
        self.test_suites: Dict[str, List[str]] = {}
        self.test_results: List[TestSuiteResult] = []
        
        # Create default test cases
        self._create_default_test_cases()
        self._create_default_test_suites()
    
    def _create_default_test_cases(self):
        """Create comprehensive default test cases."""
        
        # System and Authentication Tests
        system_info_test = TestCase(
            name="system_info_discovery",
            description="Test system information discovery",
            template_id=16,
            template_name="request_rithmic_system_info",
            infrastructure_plant="System",
            request_data={
                "user_msg": ["System info test"]
            },
            expected_response_fields=["template_id", "rp_code"],
            tags=["system", "discovery", "basic"]
        )
        
        login_test = TestCase(
            name="login_authentication",
            description="Test user authentication and login",
            template_id=10,
            template_name="request_login",
            infrastructure_plant="System",
            request_data={
                "user_msg": ["Login test"],
                "user": "PP-013155",
                "password": "b7neA8k6JA",
                "system": "Rithmic Paper Trading",
                "infra_type": 1
            },
            expected_response_fields=["template_id", "rp_code", "heartbeat_interval"],
            tags=["authentication", "login", "critical"],
            prerequisites=["system_info_discovery"]
        )
        
        heartbeat_test = TestCase(
            name="heartbeat_test",
            description="Test heartbeat mechanism",
            template_id=18,
            template_name="request_heartbeat",
            infrastructure_plant="System",
            request_data={
                "user_msg": ["Heartbeat test"]
            },
            expected_response_fields=["template_id", "rp_code"],
            tags=["heartbeat", "connection", "basic"],
            prerequisites=["login_authentication"]
        )
        
        # Market Data Tests
        market_data_update_test = TestCase(
            name="market_data_subscription",
            description="Test market data subscription",
            template_id=100,
            template_name="request_market_data_update",
            infrastructure_plant="Ticker Plant",
            request_data={
                "user_msg": ["Market data test"],
                "symbol": "ES",
                "exchange": "CME",
                "request_id": "TEST_MD_001"
            },
            expected_response_fields=["template_id", "rp_code"],
            tags=["market_data", "streaming", "ticker"],
            prerequisites=["login_authentication"]
        )
        
        depth_snapshot_test = TestCase(
            name="depth_by_order_snapshot",
            description="Test depth by order snapshot request",
            template_id=115,
            template_name="request_depth_by_order_snapshot",
            infrastructure_plant="Ticker Plant",
            request_data={
                "user_msg": ["Depth snapshot test"],
                "symbol": "ES",
                "exchange": "CME",
                "request_id": "TEST_DBO_001"
            },
            expected_response_fields=["template_id", "rp_code"],
            tags=["market_data", "depth", "snapshot"],
            prerequisites=["market_data_subscription"]
        )
        
        # Historical Data Tests
        time_bars_test = TestCase(
            name="historical_time_bars",
            description="Test historical time bars retrieval",
            template_id=200,
            template_name="request_time_bar_update",
            infrastructure_plant="History Plant",
            request_data={
                "user_msg": ["Time bars test"],
                "symbol": "ES",
                "exchange": "CME",
                "bar_type": 1,  # Minute bars
                "bar_sub_type": 1,
                "start_index": 0,
                "finish_index": 10
            },
            expected_response_fields=["template_id", "rp_code"],
            tags=["historical", "bars", "history"],
            prerequisites=["login_authentication"]
        )
        
        # Order Management Tests
        account_list_test = TestCase(
            name="account_list_request",
            description="Test account list retrieval",
            template_id=320,
            template_name="request_account_list",
            infrastructure_plant="Order Plant",
            request_data={
                "user_msg": ["Account list test"]
            },
            expected_response_fields=["template_id", "rp_code"],
            tags=["accounts", "orders", "trading"],
            prerequisites=["login_authentication"]
        )
        
        # Invalid Request Tests (Expected to fail)
        invalid_symbol_test = TestCase(
            name="invalid_symbol_request",
            description="Test handling of invalid symbol request",
            template_id=100,
            template_name="request_market_data_update",
            infrastructure_plant="Ticker Plant",
            request_data={
                "user_msg": ["Invalid symbol test"],
                "symbol": "INVALID_SYMBOL_123",
                "exchange": "CME",
                "request_id": "TEST_INVALID_001"
            },
            expected_success=False,
            tags=["error_handling", "validation", "negative"],
            prerequisites=["login_authentication"]
        )
        
        # Store test cases
        test_cases = [
            system_info_test, login_test, heartbeat_test,
            market_data_update_test, depth_snapshot_test,
            time_bars_test, account_list_test, invalid_symbol_test
        ]
        
        self.test_cases = {test.name: test for test in test_cases}
    
    def _create_default_test_suites(self):
        """Create default test suites."""
        self.test_suites = {
            "smoke_test": [
                "system_info_discovery",
                "login_authentication",
                "heartbeat_test"
            ],
            "market_data_suite": [
                "system_info_discovery",
                "login_authentication",
                "market_data_subscription",
                "depth_by_order_snapshot"
            ],
            "historical_data_suite": [
                "system_info_discovery", 
                "login_authentication",
                "historical_time_bars"
            ],
            "order_management_suite": [
                "system_info_discovery",
                "login_authentication", 
                "account_list_request"
            ],
            "comprehensive_suite": [
                "system_info_discovery",
                "login_authentication",
                "heartbeat_test",
                "market_data_subscription",
                "depth_by_order_snapshot",
                "historical_time_bars",
                "account_list_request",
                "invalid_symbol_request"
            ],
            "error_handling_suite": [
                "invalid_symbol_request"
            ]
        }
    
    async def run_test_suite(self, suite_name: str, 
                           parallel_sessions: int = 1) -> TestSuiteResult:
        """Run a complete test suite."""
        if suite_name not in self.test_suites:
            raise ValueError(f"Test suite {suite_name} not found")
        
        test_case_names = self.test_suites[suite_name]
        start_time = datetime.now()
        
        logger.info(f"🧪 Starting test suite: {suite_name}")
        logger.info(f"   Test cases: {len(test_case_names)}")
        logger.info(f"   Parallel sessions: {parallel_sessions}")
        
        # Run tests in parallel if requested
        if parallel_sessions > 1:
            results = await self._run_parallel_tests(test_case_names, parallel_sessions)
        else:
            results = await self._run_sequential_tests(test_case_names)
        
        end_time = datetime.now()
        total_execution_time = (end_time - start_time).total_seconds() * 1000
        
        # Calculate metrics
        passed_tests = len([r for r in results if r.success])
        failed_tests = len([r for r in results if not r.success])
        success_rate = (passed_tests / len(results)) * 100 if results else 0
        
        # Get session analytics
        session_analytics = await self.session_manager.get_session_analytics()
        
        suite_result = TestSuiteResult(
            suite_name=suite_name,
            total_tests=len(results),
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            skipped_tests=0,
            total_execution_time_ms=total_execution_time,
            success_rate=success_rate,
            test_results=results,
            session_analytics=session_analytics
        )
        
        self.test_results.append(suite_result)
        
        # Log results
        logger.info(f"✅ Test suite {suite_name} completed:")
        logger.info(f"   Total: {suite_result.total_tests}")
        logger.info(f"   Passed: {suite_result.passed_tests}")
        logger.info(f"   Failed: {suite_result.failed_tests}")
        logger.info(f"   Success rate: {suite_result.success_rate:.1f}%")
        logger.info(f"   Execution time: {suite_result.total_execution_time_ms:.1f}ms")
        
        return suite_result
    
    async def _run_sequential_tests(self, test_case_names: List[str]) -> List[TestResult]:
        """Run test cases sequentially."""
        results = []
        session_id = None
        
        try:
            # Create session for testing
            session_id = await self.session_manager.create_session("System", template_name="System Discovery")
            await self.session_manager.connect_session(session_id)
            
            # Execute tests in order
            for test_case_name in test_case_names:
                if test_case_name not in self.test_cases:
                    logger.warning(f"Test case {test_case_name} not found, skipping")
                    continue
                
                result = await self._execute_test_case(test_case_name, session_id)
                results.append(result)
                
                # Small delay between tests
                await asyncio.sleep(0.5)
        
        finally:
            # Clean up session
            if session_id:
                await self.session_manager.disconnect_session(session_id)
                await self.session_manager.remove_session(session_id)
        
        return results
    
    async def _run_parallel_tests(self, test_case_names: List[str], 
                                parallel_sessions: int) -> List[TestResult]:
        """Run test cases in parallel using multiple sessions."""
        # Group tests by infrastructure plant
        tests_by_plant = defaultdict(list)
        for test_case_name in test_case_names:
            if test_case_name in self.test_cases:
                test_case = self.test_cases[test_case_name]
                tests_by_plant[test_case.infrastructure_plant].append(test_case_name)
        
        # Create semaphore to limit concurrent sessions
        semaphore = asyncio.Semaphore(parallel_sessions)
        
        # Create tasks for each plant group
        tasks = []
        for plant, plant_tests in tests_by_plant.items():
            task = asyncio.create_task(
                self._run_plant_tests(plant, plant_tests, semaphore)
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        plant_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Flatten results
        results = []
        for plant_result in plant_results:
            if isinstance(plant_result, list):
                results.extend(plant_result)
            elif isinstance(plant_result, Exception):
                logger.error(f"Error in parallel test execution: {plant_result}")
        
        return results
    
    async def _run_plant_tests(self, infrastructure_plant: str, 
                             test_case_names: List[str], 
                             semaphore: asyncio.Semaphore) -> List[TestResult]:
        """Run tests for a specific infrastructure plant."""
        async with semaphore:
            session_id = None
            results = []
            
            try:
                # Create session for this plant
                session_id = await self.session_manager.create_session(infrastructure_plant)
                await self.session_manager.connect_session(session_id)
                
                # Execute tests for this plant
                for test_case_name in test_case_names:
                    result = await self._execute_test_case(test_case_name, session_id)
                    results.append(result)
                    
                    await asyncio.sleep(0.2)  # Small delay
            
            finally:
                if session_id:
                    await self.session_manager.disconnect_session(session_id)
                    await self.session_manager.remove_session(session_id)
            
            return results
    
    async def _execute_test_case(self, test_case_name: str, session_id: str) -> TestResult:
        """Execute a single test case."""
        test_case = self.test_cases[test_case_name]
        start_time = datetime.now()
        
        try:
            logger.debug(f"Executing test case: {test_case_name}")
            
            # Validate request data first
            validation_result = await self.protocol_parser.validate_request(
                test_case.template_id, test_case.request_data
            )
            
            if not validation_result.valid and test_case.expected_success:
                return TestResult(
                    test_case_name=test_case_name,
                    session_id=session_id,
                    success=False,
                    execution_time_ms=0,
                    error_message="Request validation failed",
                    validation_errors=validation_result.errors
                )
            
            # Get session client
            client = await self.session_manager.get_session_client(session_id)
            if not client:
                return TestResult(
                    test_case_name=test_case_name,
                    session_id=session_id,
                    success=False,
                    execution_time_ms=0,
                    error_message="Session client not found"
                )
            
            # Send request
            response_data = await client.send_request(
                test_case.template_id,
                test_case.template_name,
                test_case.request_data
            )
            
            end_time = datetime.now()
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Validate response
            success = self._validate_response(test_case, response_data)
            
            return TestResult(
                test_case_name=test_case_name,
                session_id=session_id,
                success=success,
                execution_time_ms=execution_time_ms,
                response_data=response_data,
                validation_errors=validation_result.errors if not validation_result.valid else []
            )
            
        except Exception as e:
            end_time = datetime.now()
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # For tests expecting failure, this might be success
            if not test_case.expected_success:
                return TestResult(
                    test_case_name=test_case_name,
                    session_id=session_id,
                    success=True,  # Expected to fail
                    execution_time_ms=execution_time_ms,
                    error_message=str(e)
                )
            
            return TestResult(
                test_case_name=test_case_name,
                session_id=session_id,
                success=False,
                execution_time_ms=execution_time_ms,
                error_message=str(e)
            )
    
    def _validate_response(self, test_case: TestCase, response_data: Dict[str, Any]) -> bool:
        """Validate response data against test case expectations."""
        if not test_case.expected_success:
            # Test expects failure - check for error indicators
            rp_code = response_data.get("rp_code", "0")
            return rp_code != "0"  # Non-zero response code indicates error
        
        # Test expects success
        if not response_data:
            return False
        
        # Check required response fields
        for field in test_case.expected_response_fields:
            if field not in response_data:
                return False
        
        # Check response code if present
        rp_code = response_data.get("rp_code", "0")
        if rp_code != "0":
            return False
        
        return True
    
    async def run_load_test(self, test_case_name: str, concurrent_sessions: int = 5,
                          requests_per_session: int = 10) -> Dict[str, Any]:
        """Run load testing on a specific test case."""
        if test_case_name not in self.test_cases:
            raise ValueError(f"Test case {test_case_name} not found")
        
        test_case = self.test_cases[test_case_name]
        logger.info(f"🔥 Starting load test: {test_case_name}")
        logger.info(f"   Concurrent sessions: {concurrent_sessions}")
        logger.info(f"   Requests per session: {requests_per_session}")
        
        start_time = datetime.now()
        
        # Create tasks for concurrent load testing
        tasks = []
        for i in range(concurrent_sessions):
            task = asyncio.create_task(
                self._run_load_test_session(test_case, requests_per_session, f"load_{i}")
            )
            tasks.append(task)
        
        # Wait for all load test sessions to complete
        session_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = datetime.now()
        total_time_ms = (end_time - start_time).total_seconds() * 1000
        
        # Aggregate results
        all_response_times = []
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        
        for session_result in session_results:
            if isinstance(session_result, dict):
                all_response_times.extend(session_result["response_times"])
                total_requests += session_result["total_requests"]
                successful_requests += session_result["successful_requests"]
                failed_requests += session_result["failed_requests"]
        
        # Calculate statistics
        if all_response_times:
            avg_response_time = statistics.mean(all_response_times)
            median_response_time = statistics.median(all_response_times)
            min_response_time = min(all_response_times)
            max_response_time = max(all_response_times)
            p95_response_time = statistics.quantiles(all_response_times, n=20)[18]  # 95th percentile
        else:
            avg_response_time = median_response_time = min_response_time = max_response_time = p95_response_time = 0
        
        requests_per_second = total_requests / (total_time_ms / 1000) if total_time_ms > 0 else 0
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        
        load_test_result = {
            "test_case": test_case_name,
            "concurrent_sessions": concurrent_sessions,
            "requests_per_session": requests_per_session,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": success_rate,
            "total_execution_time_ms": total_time_ms,
            "requests_per_second": requests_per_second,
            "response_times": {
                "average_ms": avg_response_time,
                "median_ms": median_response_time,
                "min_ms": min_response_time,
                "max_ms": max_response_time,
                "p95_ms": p95_response_time
            },
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"✅ Load test completed:")
        logger.info(f"   Total requests: {total_requests}")
        logger.info(f"   Success rate: {success_rate:.1f}%")
        logger.info(f"   Avg response time: {avg_response_time:.1f}ms")
        logger.info(f"   Requests/sec: {requests_per_second:.1f}")
        
        return load_test_result
    
    async def _run_load_test_session(self, test_case: TestCase, 
                                   requests_count: int, session_prefix: str) -> Dict[str, Any]:
        """Run load test for a single session."""
        session_id = None
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        try:
            # Create and connect session
            session_id = await self.session_manager.create_session(
                test_case.infrastructure_plant, 
                session_id=f"{session_prefix}_{datetime.now().strftime('%H%M%S')}"
            )
            await self.session_manager.connect_session(session_id)
            
            client = await self.session_manager.get_session_client(session_id)
            if not client:
                return {
                    "response_times": [],
                    "total_requests": 0,
                    "successful_requests": 0,
                    "failed_requests": requests_count
                }
            
            # Execute requests
            for i in range(requests_count):
                start_time = datetime.now()
                
                try:
                    await client.send_request(
                        test_case.template_id,
                        test_case.template_name,
                        test_case.request_data
                    )
                    
                    end_time = datetime.now()
                    response_time_ms = (end_time - start_time).total_seconds() * 1000
                    response_times.append(response_time_ms)
                    successful_requests += 1
                    
                except Exception:
                    failed_requests += 1
                
                # Small delay to avoid overwhelming the server
                await asyncio.sleep(0.1)
        
        finally:
            if session_id:
                await self.session_manager.disconnect_session(session_id)
                await self.session_manager.remove_session(session_id)
        
        return {
            "response_times": response_times,
            "total_requests": requests_count,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests
        }
    
    async def generate_test_report(self, suite_result: TestSuiteResult, 
                                 output_file: Optional[str] = None) -> str:
        """Generate a comprehensive test report."""
        report_data = {
            "test_suite_result": asdict(suite_result),
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "suite_name": suite_result.suite_name,
                "total_tests": suite_result.total_tests,
                "passed_tests": suite_result.passed_tests,
                "failed_tests": suite_result.failed_tests,
                "success_rate": suite_result.success_rate,
                "total_execution_time_ms": suite_result.total_execution_time_ms
            },
            "test_details": [asdict(result) for result in suite_result.test_results],
            "session_analytics": suite_result.session_analytics
        }
        
        if output_file:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            logger.info(f"Test report saved to: {output_path}")
            return str(output_path)
        
        return json.dumps(report_data, indent=2)
    
    async def get_available_test_suites(self) -> Dict[str, Any]:
        """Get information about available test suites."""
        suites_info = {}
        
        for suite_name, test_case_names in self.test_suites.items():
            test_cases_info = []
            for test_case_name in test_case_names:
                if test_case_name in self.test_cases:
                    test_case = self.test_cases[test_case_name]
                    test_cases_info.append({
                        "name": test_case.name,
                        "description": test_case.description,
                        "template_id": test_case.template_id,
                        "infrastructure_plant": test_case.infrastructure_plant,
                        "tags": test_case.tags,
                        "expected_success": test_case.expected_success
                    })
            
            suites_info[suite_name] = {
                "test_count": len(test_case_names),
                "test_cases": test_cases_info
            }
        
        return suites_info