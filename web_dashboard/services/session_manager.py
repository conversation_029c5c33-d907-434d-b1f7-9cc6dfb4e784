#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Session Manager Service
======================

Enhanced session management for the API playground with persistent storage,
session templates, and advanced testing capabilities.

Features:
- Persistent session storage and restoration
- Session templates and presets
- Multi-session management
- Advanced testing workflows
- Session analytics and reporting
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from collections import defaultdict

from rithmic_client import RithmicPlaygroundClient

logger = logging.getLogger(__name__)

@dataclass
class SessionTemplate:
    """Template for creating pre-configured sessions."""
    name: str
    description: str
    infrastructure_plant: str
    default_requests: List[Dict[str, Any]]
    tags: List[str]
    created_by: str = "system"
    created_at: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

@dataclass
class SessionMetrics:
    """Session performance and usage metrics."""
    session_id: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    total_bytes_sent: int = 0
    total_bytes_received: int = 0
    unique_templates_used: int = 0
    session_duration_minutes: float = 0.0
    error_rate: float = 0.0

class PlaygroundSessionManager:
    """Enhanced session manager for the API playground."""
    
    def __init__(self, storage_dir: Optional[str] = None):
        """Initialize the session manager."""
        self.storage_dir = Path(storage_dir or "playground_sessions")
        self.storage_dir.mkdir(exist_ok=True)
        
        # Active sessions
        self.active_sessions: Dict[str, RithmicPlaygroundClient] = {}
        self.session_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Session templates
        self.session_templates: Dict[str, SessionTemplate] = {}
        
        # Session metrics
        self.session_metrics: Dict[str, SessionMetrics] = {}
        
        # Initialize with default templates
        self._create_default_templates()
    
    def _create_default_templates(self):
        """Create default session templates."""
        # System Discovery Template
        system_template = SessionTemplate(
            name="System Discovery",
            description="Basic system discovery and authentication workflow",
            infrastructure_plant="System",
            default_requests=[
                {
                    "template_id": 16,
                    "template_name": "request_rithmic_system_info",
                    "request_data": {
                        "user_msg": ["System discovery from playground"]
                    }
                },
                {
                    "template_id": 10,
                    "template_name": "request_login",
                    "request_data": {
                        "user_msg": ["Login from playground"],
                        "user": "PP-013155",
                        "password": "b7neA8k6JA",
                        "system": "Rithmic Paper Trading",
                        "infra_type": 1
                    }
                }
            ],
            tags=["system", "auth", "discovery", "basic"]
        )
        
        # Market Data Template
        market_data_template = SessionTemplate(
            name="Market Data Subscription",
            description="Subscribe to real-time market data and quotes",
            infrastructure_plant="Ticker Plant",
            default_requests=[
                {
                    "template_id": 100,
                    "template_name": "request_market_data_update",
                    "request_data": {
                        "user_msg": ["Market data subscription"],
                        "symbol": "ES",
                        "exchange": "CME",
                        "request_id": "MD_ES_001"
                    }
                },
                {
                    "template_id": 115,
                    "template_name": "request_depth_by_order_snapshot",
                    "request_data": {
                        "user_msg": ["Depth by order snapshot"],
                        "symbol": "ES",
                        "exchange": "CME",
                        "request_id": "DBO_ES_001"
                    }
                }
            ],
            tags=["market_data", "ticker", "streaming", "quotes"]
        )
        
        # Historical Data Template
        history_template = SessionTemplate(
            name="Historical Data Retrieval",
            description="Retrieve historical time bars and tick data",
            infrastructure_plant="History Plant",
            default_requests=[
                {
                    "template_id": 200,
                    "template_name": "request_time_bar_update",
                    "request_data": {
                        "user_msg": ["Historical bars request"],
                        "symbol": "ES",
                        "exchange": "CME",
                        "bar_type": 1,  # Minute bars
                        "bar_sub_type": 1,
                        "start_index": 0,
                        "finish_index": 100
                    }
                }
            ],
            tags=["history", "bars", "historical", "data"]
        )
        
        # Order Management Template
        order_template = SessionTemplate(
            name="Order Management",
            description="Account information and order management workflow",
            infrastructure_plant="Order Plant",
            default_requests=[
                {
                    "template_id": 320,
                    "template_name": "request_account_list",
                    "request_data": {
                        "user_msg": ["Account list request"]
                    }
                },
                {
                    "template_id": 318,
                    "template_name": "request_account_rms_info",
                    "request_data": {
                        "user_msg": ["Account RMS info"],
                        "account_id": "account_001"
                    }
                }
            ],
            tags=["orders", "accounts", "trading", "rms"]
        )
        
        # Store templates
        self.session_templates = {
            template.name: template
            for template in [system_template, market_data_template, history_template, order_template]
        }
        
        # Save templates to disk
        asyncio.create_task(self._save_templates())
    
    async def _save_templates(self):
        """Save session templates to disk."""
        try:
            templates_file = self.storage_dir / "session_templates.json"
            templates_data = {
                name: asdict(template) 
                for name, template in self.session_templates.items()
            }
            
            with open(templates_file, 'w') as f:
                json.dump(templates_data, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Error saving session templates: {e}")
    
    async def load_templates(self):
        """Load session templates from disk."""
        try:
            templates_file = self.storage_dir / "session_templates.json"
            if not templates_file.exists():
                return
            
            with open(templates_file, 'r') as f:
                templates_data = json.load(f)
            
            for name, template_dict in templates_data.items():
                self.session_templates[name] = SessionTemplate(**template_dict)
                
            logger.info(f"Loaded {len(self.session_templates)} session templates")
            
        except Exception as e:
            logger.warning(f"Error loading session templates: {e}")
    
    async def create_session(self, infrastructure_plant: str, session_id: Optional[str] = None,
                           template_name: Optional[str] = None) -> str:
        """Create a new playground session."""
        try:
            client = RithmicPlaygroundClient(infrastructure_plant, session_id)
            session_id = client.session_id
            
            # Store session
            self.active_sessions[session_id] = client
            self.session_metadata[session_id] = {
                "created_at": datetime.now().isoformat(),
                "infrastructure_plant": infrastructure_plant,
                "template_used": template_name,
                "status": "created"
            }
            
            # Initialize metrics
            self.session_metrics[session_id] = SessionMetrics(session_id=session_id)
            
            # Apply template if specified
            if template_name and template_name in self.session_templates:
                template = self.session_templates[template_name]
                self.session_metadata[session_id]["default_requests"] = template.default_requests
                self.session_metadata[session_id]["template_description"] = template.description
            
            # Save session metadata
            await self._save_session_metadata(session_id)
            
            logger.info(f"Created playground session {session_id} for {infrastructure_plant}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    async def connect_session(self, session_id: str) -> bool:
        """Connect and authenticate a session."""
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        try:
            client = self.active_sessions[session_id]
            success = await client.connect_and_authenticate()
            
            if success:
                self.session_metadata[session_id]["status"] = "connected"
                self.session_metadata[session_id]["connected_at"] = datetime.now().isoformat()
            else:
                self.session_metadata[session_id]["status"] = "failed"
            
            await self._save_session_metadata(session_id)
            return success
            
        except Exception as e:
            logger.error(f"Error connecting session {session_id}: {e}")
            self.session_metadata[session_id]["status"] = "error"
            self.session_metadata[session_id]["error"] = str(e)
            await self._save_session_metadata(session_id)
            return False
    
    async def disconnect_session(self, session_id: str):
        """Disconnect a session."""
        if session_id not in self.active_sessions:
            return
        
        try:
            client = self.active_sessions[session_id]
            await client.disconnect()
            
            self.session_metadata[session_id]["status"] = "disconnected"
            self.session_metadata[session_id]["disconnected_at"] = datetime.now().isoformat()
            
            # Calculate final metrics
            await self._calculate_final_metrics(session_id)
            
            await self._save_session_metadata(session_id)
            
        except Exception as e:
            logger.error(f"Error disconnecting session {session_id}: {e}")
    
    async def remove_session(self, session_id: str):
        """Remove a session completely."""
        if session_id in self.active_sessions:
            await self.disconnect_session(session_id)
            del self.active_sessions[session_id]
        
        if session_id in self.session_metadata:
            del self.session_metadata[session_id]
        
        if session_id in self.session_metrics:
            del self.session_metrics[session_id]
    
    async def get_session_client(self, session_id: str) -> Optional[RithmicPlaygroundClient]:
        """Get the client for a session."""
        return self.active_sessions.get(session_id)
    
    async def list_sessions(self) -> List[Dict[str, Any]]:
        """List all active sessions."""
        sessions = []
        
        for session_id, client in self.active_sessions.items():
            session_info = await client.get_session_info()
            metadata = self.session_metadata.get(session_id, {})
            metrics = self.session_metrics.get(session_id)
            
            session_data = {
                **session_info,
                **metadata,
                "metrics": asdict(metrics) if metrics else None
            }
            sessions.append(session_data)
        
        return sessions
    
    async def get_session_templates(self) -> List[Dict[str, Any]]:
        """Get all available session templates."""
        return [asdict(template) for template in self.session_templates.values()]
    
    async def create_session_from_template(self, template_name: str, 
                                         session_id: Optional[str] = None) -> str:
        """Create a session from a template."""
        if template_name not in self.session_templates:
            raise ValueError(f"Template {template_name} not found")
        
        template = self.session_templates[template_name]
        return await self.create_session(
            infrastructure_plant=template.infrastructure_plant,
            session_id=session_id,
            template_name=template_name
        )
    
    async def run_template_workflow(self, session_id: str) -> List[Dict[str, Any]]:
        """Run the default workflow for a session template."""
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        if session_id not in self.session_metadata:
            raise ValueError(f"Session metadata not found for {session_id}")
        
        metadata = self.session_metadata[session_id]
        default_requests = metadata.get("default_requests", [])
        
        if not default_requests:
            return []
        
        client = self.active_sessions[session_id]
        results = []
        
        for request_config in default_requests:
            try:
                result = await client.send_request(
                    request_config["template_id"],
                    request_config["template_name"],
                    request_config["request_data"]
                )
                results.append({
                    "request": request_config,
                    "response": result,
                    "success": True
                })
                
                # Small delay between requests
                await asyncio.sleep(0.5)
                
            except Exception as e:
                results.append({
                    "request": request_config,
                    "response": None,
                    "success": False,
                    "error": str(e)
                })
        
        return results
    
    async def _save_session_metadata(self, session_id: str):
        """Save session metadata to disk."""
        try:
            session_file = self.storage_dir / f"session_{session_id}.json"
            metadata = self.session_metadata.get(session_id, {})
            
            with open(session_file, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Error saving session metadata for {session_id}: {e}")
    
    async def load_session_metadata(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Load session metadata from disk."""
        try:
            session_file = self.storage_dir / f"session_{session_id}.json"
            if not session_file.exists():
                return None
            
            with open(session_file, 'r') as f:
                return json.load(f)
                
        except Exception as e:
            logger.warning(f"Error loading session metadata for {session_id}: {e}")
            return None
    
    async def _calculate_final_metrics(self, session_id: str):
        """Calculate final session metrics."""
        if session_id not in self.active_sessions:
            return
        
        client = self.active_sessions[session_id]
        metrics = self.session_metrics.get(session_id)
        
        if not metrics:
            return
        
        # Get request history
        history = await client.get_request_history(limit=1000)
        
        if history:
            metrics.total_requests = len(history)
            metrics.successful_requests = len([h for h in history if h.get("success", False)])
            metrics.failed_requests = metrics.total_requests - metrics.successful_requests
            
            if metrics.total_requests > 0:
                metrics.error_rate = (metrics.failed_requests / metrics.total_requests) * 100
            
            # Calculate average response time
            response_times = [h.get("execution_time_ms", 0) for h in history if h.get("success", False)]
            if response_times:
                metrics.average_response_time = sum(response_times) / len(response_times)
            
            # Calculate unique templates used
            unique_templates = set(h.get("template_id") for h in history)
            metrics.unique_templates_used = len(unique_templates)
        
        # Calculate session duration
        created_at = self.session_metadata.get(session_id, {}).get("created_at")
        if created_at:
            created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            duration = datetime.now() - created_time
            metrics.session_duration_minutes = duration.total_seconds() / 60
    
    async def get_session_analytics(self) -> Dict[str, Any]:
        """Get comprehensive session analytics."""
        total_sessions = len(self.session_metadata)
        active_sessions = len([s for s in self.active_sessions.values() if s.connected])
        
        # Plant distribution
        plant_distribution = defaultdict(int)
        for metadata in self.session_metadata.values():
            plant = metadata.get("infrastructure_plant", "Unknown")
            plant_distribution[plant] += 1
        
        # Template usage
        template_usage = defaultdict(int)
        for metadata in self.session_metadata.values():
            template = metadata.get("template_used")
            if template:
                template_usage[template] += 1
        
        # Aggregate metrics
        all_metrics = list(self.session_metrics.values())
        total_requests = sum(m.total_requests for m in all_metrics)
        total_successful = sum(m.successful_requests for m in all_metrics)
        
        avg_response_time = 0
        if all_metrics:
            response_times = [m.average_response_time for m in all_metrics if m.average_response_time > 0]
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
        
        return {
            "summary": {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "total_requests": total_requests,
                "successful_requests": total_successful,
                "overall_success_rate": (total_successful / total_requests * 100) if total_requests > 0 else 0,
                "average_response_time_ms": avg_response_time
            },
            "distribution": {
                "by_plant": dict(plant_distribution),
                "by_template": dict(template_usage)
            },
            "available_templates": len(self.session_templates)
        }
    
    async def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Clean up old disconnected sessions."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        sessions_to_remove = []
        
        for session_id, metadata in self.session_metadata.items():
            created_at = metadata.get("created_at")
            status = metadata.get("status", "unknown")
            
            if created_at and status in ["disconnected", "failed", "error"]:
                created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                if created_time < cutoff_time:
                    sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            await self.remove_session(session_id)
            
            # Remove session file
            try:
                session_file = self.storage_dir / f"session_{session_id}.json"
                if session_file.exists():
                    session_file.unlink()
            except Exception as e:
                logger.warning(f"Error removing session file for {session_id}: {e}")
        
        logger.info(f"Cleaned up {len(sessions_to_remove)} old sessions")
        return len(sessions_to_remove)