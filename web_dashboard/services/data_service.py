"""
Data Service
============

Service layer for database operations and data management.
"""

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.database.database_manager import DatabaseManager

class DataService:
    """Service for database operations."""
    
    def __init__(self, config: dict):
        """Initialize with database configuration."""
        self.db_manager = DatabaseManager(config)
        
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            return self.db_manager.test_connection()
        except Exception:
            return False
            
    def get_tables(self) -> List[Dict[str, Any]]:
        """Get list of all tables in the database."""
        query = """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            ORDER BY table_name
        """
        return self.db_manager.execute_query(query)
        
    def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a table."""
        query = """
            SELECT column_name, data_type, is_nullable, column_default, column_key
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() AND table_name = %s
            ORDER BY ordinal_position
        """
        return self.db_manager.execute_query(query, (table_name,))
        
    def get_table_row_count(self, table_name: str, where_clause: Optional[str] = None) -> int:
        """Get row count for a table, optionally with WHERE clause."""
        try:
            if where_clause:
                query = f"SELECT COUNT(*) as count FROM `{table_name}` WHERE {where_clause}"
                result = self.db_manager.execute_query(query)
            else:
                result = self.db_manager.get_table_row_count(table_name)
                return result if isinstance(result, int) else result[0]['count']
            
            return result[0]['count'] if result else 0
        except Exception:
            return 0
            
    def get_table_data(
        self, 
        table_name: str, 
        limit: Optional[int] = None,
        offset: int = 0,
        where_clause: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get table data with pagination and filtering."""
        base_query = f"SELECT * FROM `{table_name}`"
        
        if where_clause:
            query = f"{base_query} WHERE {where_clause}"
        else:
            query = base_query
            
        if limit is not None:
            query += f" LIMIT {limit} OFFSET {offset}"
            
        return self.db_manager.execute_query(query)
        
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a custom query."""
        return self.db_manager.execute_query(query, params)
        
    def close(self):
        """Close database connection."""
        if self.db_manager:
            self.db_manager.close()