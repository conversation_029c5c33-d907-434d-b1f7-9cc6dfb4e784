#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Protocol Parser Service
=======================

Parses Rithmic Protocol Buffer files to extract template definitions, field schemas,
and documentation for the API playground interface.

Features:
- Automatic proto file discovery and parsing
- Template categorization and organization
- Field validation and type checking
- Code generation for multiple languages
- Example generation and best practices
"""

import os
import re
import json
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

class ValidationResult(NamedTuple):
    """Result of request validation."""
    valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]

@dataclass
class FieldDefinition:
    """Protocol Buffer field definition."""
    name: str
    type: str
    number: int
    label: str  # required, optional, repeated
    default_value: Any = None
    description: str = ""
    enum_values: List[str] = None

@dataclass
class MessageDefinition:
    """Protocol Buffer message definition."""
    name: str
    fields: List[FieldDefinition]
    description: str = ""
    package: str = ""

class ProtocolParser:
    """Parses Protocol Buffer files for the API playground."""
    
    def __init__(self, proto_dir: Optional[str] = None):
        """Initialize the protocol parser."""
        self.proto_dir = proto_dir or self._find_proto_directory()
        self.templates: Dict[int, Dict[str, Any]] = {}
        self.messages: Dict[str, MessageDefinition] = {}
        self.categories: Dict[str, List[int]] = defaultdict(list)
        self.template_id_mapping: Dict[str, int] = {}
        self.initialized = False
        
    def _find_proto_directory(self) -> str:
        """Find the proto directory relative to the current file."""
        current_file = Path(__file__)
        
        # Try different relative paths
        possible_paths = [
            current_file.parent.parent.parent / "proto",
            current_file.parent.parent / "proto", 
            current_file.parent / "proto",
            Path("proto"),
            Path("../proto"),
            Path("../../proto")
        ]
        
        for path in possible_paths:
            if path.exists() and path.is_dir():
                return str(path.absolute())
        
        raise FileNotFoundError("Could not find proto directory")
    
    async def initialize(self):
        """Initialize the parser by discovering and parsing all proto files."""
        if self.initialized:
            return
        
        logger.info(f"Initializing Protocol Parser with proto directory: {self.proto_dir}")
        
        try:
            # Discover all proto files
            proto_files = list(Path(self.proto_dir).glob("*.proto"))
            logger.info(f"Found {len(proto_files)} proto files")
            
            # Parse each proto file
            for proto_file in proto_files:
                await self._parse_proto_file(proto_file)
            
            # Build template mappings and categorization
            await self._build_template_mappings()
            await self._categorize_templates()
            
            self.initialized = True
            logger.info(f"Protocol Parser initialized with {len(self.templates)} templates")
            
        except Exception as e:
            logger.error(f"Failed to initialize Protocol Parser: {e}")
            raise
    
    async def _parse_proto_file(self, proto_file: Path):
        """Parse a single proto file."""
        try:
            with open(proto_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract message definitions
            messages = self._extract_messages(content, proto_file.stem)
            for message in messages:
                self.messages[message.name] = message
            
            # Extract template IDs if this is a request/response file
            template_id = self._extract_template_id(content, proto_file.stem)
            if template_id:
                self.template_id_mapping[proto_file.stem] = template_id
                
        except Exception as e:
            logger.warning(f"Failed to parse {proto_file}: {e}")
    
    def _extract_messages(self, content: str, filename: str) -> List[MessageDefinition]:
        """Extract message definitions from proto file content."""
        messages = []
        
        # Regex to match message definitions
        message_pattern = r'message\s+(\w+)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
        
        for match in re.finditer(message_pattern, content, re.DOTALL):
            message_name = match.group(1)
            message_body = match.group(2)
            
            # Extract fields from message body
            fields = self._extract_fields(message_body)
            
            # Extract description from comments above the message
            description = self._extract_description(content, match.start())
            
            message = MessageDefinition(
                name=message_name,
                fields=fields,
                description=description,
                package=filename
            )
            messages.append(message)
        
        return messages
    
    def _extract_fields(self, message_body: str) -> List[FieldDefinition]:
        """Extract field definitions from message body."""
        fields = []
        
        # Regex to match field definitions
        field_pattern = r'(optional|required|repeated)?\s*(\w+)\s+(\w+)\s*=\s*(\d+)(?:\s*\[([^\]]*)\])?;'
        
        for match in re.finditer(field_pattern, message_body):
            label = match.group(1) or 'optional'
            field_type = match.group(2)
            field_name = match.group(3)
            field_number = int(match.group(4))
            options = match.group(5) or ""
            
            # Extract default value from options
            default_value = None
            if 'default' in options:
                default_match = re.search(r'default\s*=\s*([^,\]]+)', options)
                if default_match:
                    default_value = default_match.group(1).strip('"\'')
            
            # Extract enum values if this is an enum type
            enum_values = self._extract_enum_values(message_body, field_type)
            
            # Extract field description from comments
            field_description = self._extract_field_description(message_body, field_name)
            
            field = FieldDefinition(
                name=field_name,
                type=field_type,
                number=field_number,
                label=label,
                default_value=default_value,
                description=field_description,
                enum_values=enum_values
            )
            fields.append(field)
        
        return fields
    
    def _extract_enum_values(self, message_body: str, enum_type: str) -> Optional[List[str]]:
        """Extract enum values if the field type is an enum."""
        # Look for enum definitions in the message body
        enum_pattern = rf'enum\s+{enum_type}\s*\{{([^}}]*)\}}'
        enum_match = re.search(enum_pattern, message_body, re.DOTALL)
        
        if not enum_match:
            return None
        
        enum_body = enum_match.group(1)
        enum_values = []
        
        # Extract enum value definitions
        value_pattern = r'(\w+)\s*=\s*(\d+);'
        for value_match in re.finditer(value_pattern, enum_body):
            enum_name = value_match.group(1)
            enum_values.append(enum_name)
        
        return enum_values if enum_values else None
    
    def _extract_field_description(self, message_body: str, field_name: str) -> str:
        """Extract field description from comments."""
        # Look for comments before the field definition
        field_pattern = rf'//.*\n\s*(?:optional|required|repeated)?\s*\w+\s+{field_name}\s*='
        match = re.search(field_pattern, message_body)
        
        if match:
            comment_line = match.group(0).split('\n')[0]
            if comment_line.strip().startswith('//'):
                return comment_line.strip()[2:].strip()
        
        return ""
    
    def _extract_template_id(self, content: str, filename: str) -> Optional[int]:
        """Extract template ID from proto file content."""
        # Look for template_id field definitions
        template_id_pattern = r'int32\s+template_id\s*=\s*\d+\s*\[default\s*=\s*(\d+)\]'
        match = re.search(template_id_pattern, content)
        if match:
            return int(match.group(1))
        
        # Look for template ID in comments
        comment_pattern = r'//.*[Tt]emplate\s*(?:ID)?:?\s*(\d+)'
        match = re.search(comment_pattern, content)
        if match:
            return int(match.group(1))
        
        # Infer template ID from filename patterns
        template_id = self._infer_template_id_from_filename(filename)
        return template_id
    
    def _infer_template_id_from_filename(self, filename: str) -> Optional[int]:
        """Infer template ID from filename using known patterns."""
        # Define comprehensive template ID mappings based on Rithmic API documentation
        template_mappings = {
            # System and Authentication (10-99)
            'request_login': 10,
            'response_login': 11,
            'request_logout': 12,
            'response_logout': 13,
            'request_login_info': 14,
            'response_login_info': 15,
            'request_rithmic_system_info': 16,
            'response_rithmic_system_info': 17,
            'request_heartbeat': 18,
            'response_heartbeat': 19,
            'request_rithmic_system_gateway_info': 20,
            'response_rithmic_system_gateway_info': 21,
            'forced_logout': 25,
            'reject': 30,
            
            # Market Data and Ticker Plant (100-199)
            'request_market_data_update': 100,
            'response_market_data_update': 101,
            'request_market_data_update_by_underlying': 102,
            'response_market_data_update_by_underlying': 103,
            'request_front_month_contract': 113,
            'response_front_month_contract': 114,
            'request_depth_by_order_snapshot': 115,
            'response_depth_by_order_snapshot': 116,
            'request_depth_by_order_updates': 117,
            'response_depth_by_order_updates': 118,
            'request_product_codes': 119,
            'response_product_codes': 120,
            'request_reference_data': 125,
            'response_reference_data': 126,
            'request_auxilliary_reference_data': 127,
            'response_auxilliary_reference_data': 128,
            'request_search_symbols': 129,
            'response_search_symbols': 130,
            'last_trade': 150,
            'best_bid_offer': 151,
            'market_mode': 152,
            'depth_by_order': 160,
            'depth_by_order_end_event': 161,
            'open_interest': 162,
            'quote_statistics': 163,
            'trade_statistics': 164,
            'indicator_prices': 165,
            'end_of_day_prices': 166,
            'front_month_contract_update': 170,
            
            # Historical Data and History Plant (200-299)
            'request_time_bar_update': 200,
            'response_time_bar_update': 201,
            'request_time_bar_replay': 202,
            'response_time_bar_replay': 203,
            'request_tick_bar_update': 204,
            'response_tick_bar_update': 205,
            'request_tick_bar_replay': 206,
            'response_tick_bar_replay': 207,
            'request_volume_profile_minute_bars': 208,
            'response_volume_profile_minute_bars': 209,
            'time_bar': 210,
            'tick_bar': 211,
            'request_resume_bars': 215,
            'response_resume_bars': 216,
            'request_get_volume_at_price': 220,
            'response_get_volume_at_price': 221,
            
            # Order Management and Order Plant (300-399)
            'request_new_order': 312,
            'response_new_order': 313,
            'request_cancel_order': 314,
            'response_cancel_order': 315,
            'request_modify_order': 316,
            'response_modify_order': 317,
            'request_cancel_all_orders': 318,
            'response_cancel_all_orders': 319,
            'request_account_list': 320,
            'response_account_list': 321,
            'request_account_rms_info': 322,
            'response_account_rms_info': 323,
            'request_product_rms_info': 324,
            'response_product_rms_info': 325,
            'request_order_session_config': 326,
            'response_order_session_config': 327,
            'request_show_orders': 328,
            'response_show_orders': 329,
            'request_show_order_history': 330,
            'response_show_order_history': 331,
            'request_show_order_history_dates': 332,
            'response_show_order_history_dates': 333,
            'request_show_order_history_detail': 334,
            'response_show_order_history_detail': 335,
            'request_show_order_history_summary': 336,
            'response_show_order_history_summary': 337,
            'request_subscribe_for_order_updates': 338,
            'response_subscribe_for_order_updates': 339,
            'request_trade_routes': 340,
            'response_trade_routes': 341,
            'request_list_exchange_permissions': 342,
            'response_list_exchange_permissions': 343,
            'request_bracket_order': 350,
            'response_bracket_order': 351,
            'request_oco_order': 352,
            'response_oco_order': 353,
            'request_show_brackets': 354,
            'response_show_brackets': 355,
            'request_show_bracket_stops': 356,
            'response_show_bracket_stops': 357,
            'request_update_stop_bracket_level': 358,
            'response_update_stop_bracket_level': 359,
            'request_update_target_bracket_level': 360,
            'response_update_target_bracket_level': 361,
            'request_subscribe_to_bracket_updates': 362,
            'response_subscribe_to_bracket_updates': 363,
            'request_link_orders': 364,
            'response_link_orders': 365,
            'request_exit_position': 366,
            'response_exit_position': 367,
            'request_modify_order_reference_data': 368,
            'response_modify_order_reference_data': 369,
            'request_replay_executions': 370,
            'response_replay_executions': 371,
            'rithmic_order_notification': 380,
            'exchange_order_notification': 381,
            'bracket_updates': 382,
            'account_list_updates': 383,
            'order_book': 384,
            'order_price_limits': 385,
            'trade_route': 386,
            'symbol_margin_rate': 387,
            
            # PnL Plant (400-499)
            'request_pnl_position_snapshot': 400,
            'response_pnl_position_snapshot': 401,
            'request_pnl_position_updates': 402,
            'response_pnl_position_updates': 403,
            'account_pnl_position_update': 410,
            'instrument_pnl_position_update': 411,
            'account_rms_updates': 415,
            'user_account_update': 416,
            
            # Repository Plant (500-599)
            'request_list_accepted_agreements': 500,
            'response_list_accepted_agreements': 501,
            'request_list_unaccepted_agreements': 502,
            'response_list_unaccepted_agreements': 503,
            'request_show_agreement': 504,
            'response_show_agreement': 505,
            'request_accept_agreement': 506,
            'response_accept_agreement': 507,
            'request_easy_to_borrow_list': 508,
            'response_easy_to_borrow_list': 509,
            'update_easy_to_borrow_list': 510,
            'request_give_tick_size_type_table': 515,
            'response_give_tick_size_type_table': 516,
            'request_get_instrument_by_underlying': 520,
            'response_get_instrument_by_underlying': 521,
            'response_get_instrument_by_underlying_keys': 522,
            'request_set_rithmic_mrkt_data_self_cert_status': 525,
            'response_set_rithmic_mrkt_data_self_cert_status': 526,
        }
        
        return template_mappings.get(filename)
    
    def _extract_description(self, content: str, position: int) -> str:
        """Extract description from comments before a message definition."""
        lines = content[:position].split('\n')
        description_lines = []
        
        # Look backwards for comment lines
        for line in reversed(lines[-10:]):  # Check last 10 lines
            line = line.strip()
            if line.startswith('//'):
                description_lines.insert(0, line[2:].strip())
            elif line.startswith('/*'):
                # Multi-line comment
                comment_content = line[2:]
                if '*/' in comment_content:
                    comment_content = comment_content[:comment_content.index('*/')]
                description_lines.insert(0, comment_content.strip())
            elif line and not line.startswith('*'):
                break
        
        return ' '.join(description_lines)
    
    async def _build_template_mappings(self):
        """Build template mappings from parsed data."""
        for filename, template_id in self.template_id_mapping.items():
            message_name = self._filename_to_message_name(filename)
            message = self.messages.get(message_name)
            
            if message:
                # Determine infrastructure plant
                infrastructure_plant = self._determine_infrastructure_plant(filename, template_id)
                
                # Determine if this is a request or response
                is_request = filename.startswith('request_')
                is_response = filename.startswith('response_')
                
                # Find corresponding request/response
                corresponding_template = None
                if is_request:
                    corresponding_filename = filename.replace('request_', 'response_')
                    corresponding_template = self.template_id_mapping.get(corresponding_filename)
                elif is_response:
                    corresponding_filename = filename.replace('response_', 'request_')
                    corresponding_template = self.template_id_mapping.get(corresponding_filename)
                
                template_info = {
                    'template_id': template_id,
                    'template_name': filename,
                    'message_name': message_name,
                    'description': message.description or self._generate_description(filename),
                    'category': self._determine_category(filename),
                    'infrastructure_plant': infrastructure_plant,
                    'is_request': is_request,
                    'is_response': is_response,
                    'corresponding_template': corresponding_template,
                    'message': message,
                    'required_fields': [f.name for f in message.fields if f.label == 'required'],
                    'optional_fields': [f.name for f in message.fields if f.label == 'optional'],
                    'examples': self._generate_examples(message, filename)
                }
                
                self.templates[template_id] = template_info
    
    def _filename_to_message_name(self, filename: str) -> str:
        """Convert filename to expected message name."""
        # Convert snake_case to PascalCase
        parts = filename.split('_')
        return ''.join(word.capitalize() for word in parts)
    
    def _determine_infrastructure_plant(self, filename: str, template_id: int) -> str:
        """Determine which infrastructure plant a template belongs to."""
        # Template ID ranges for different plants
        if 10 <= template_id <= 99:
            return "System"
        elif 100 <= template_id <= 199:
            return "Ticker Plant"
        elif 200 <= template_id <= 299:
            return "History Plant"
        elif 300 <= template_id <= 399:
            return "Order Plant"
        elif 400 <= template_id <= 499:
            return "PnL Plant"
        elif 500 <= template_id <= 599:
            return "Repository Plant"
        else:
            return "Unknown"
    
    def _determine_category(self, filename: str) -> str:
        """Determine the category of a template based on its filename."""
        if 'login' in filename or 'logout' in filename or 'heartbeat' in filename:
            return "Authentication"
        elif 'market_data' in filename or 'depth' in filename or 'trade' in filename or 'bid_offer' in filename:
            return "Market Data"
        elif 'order' in filename or 'cancel' in filename or 'modify' in filename:
            return "Order Management"
        elif 'bar' in filename or 'tick' in filename or 'time' in filename:
            return "Historical Data"
        elif 'account' in filename or 'rms' in filename or 'position' in filename:
            return "Account Management"
        elif 'system' in filename or 'info' in filename:
            return "System Information"
        elif 'symbol' in filename or 'search' in filename or 'reference' in filename:
            return "Symbol Discovery"
        else:
            return "Other"
    
    def _generate_description(self, filename: str) -> str:
        """Generate a description for a template based on its filename."""
        filename_clean = filename.replace('request_', '').replace('response_', '').replace('_', ' ')
        if filename.startswith('request_'):
            return f"Request for {filename_clean}"
        elif filename.startswith('response_'):
            return f"Response for {filename_clean}"
        else:
            return f"Message for {filename_clean}"
    
    def _generate_examples(self, message: MessageDefinition, filename: str) -> List[Dict[str, Any]]:
        """Generate example requests for a message."""
        examples = []
        
        # Generate basic example
        basic_example = {}
        for field in message.fields:
            if field.label == 'required' or field.name in ['template_id', 'user_msg']:
                basic_example[field.name] = self._generate_example_value(field, filename)
        
        if basic_example:
            examples.append({
                'name': 'Basic Example',
                'description': 'Minimal required fields',
                'data': basic_example
            })
        
        # Generate complete example
        complete_example = {}
        for field in message.fields:
            complete_example[field.name] = self._generate_example_value(field, filename)
        
        if complete_example != basic_example:
            examples.append({
                'name': 'Complete Example',
                'description': 'All available fields',
                'data': complete_example
            })
        
        return examples
    
    def _generate_example_value(self, field: FieldDefinition, filename: str) -> Any:
        """Generate an example value for a field."""
        if field.default_value is not None:
            return field.default_value
        
        if field.name == 'template_id':
            return self.template_id_mapping.get(filename, 0)
        elif field.name == 'user_msg':
            return [f"Example request from API playground"]
        elif field.type == 'string':
            if 'symbol' in field.name.lower():
                return "ES"
            elif 'exchange' in field.name.lower():
                return "CME"
            elif 'user' in field.name.lower():
                return "test_user"
            else:
                return f"example_{field.name}"
        elif field.type in ['int32', 'int64', 'uint32', 'uint64']:
            return 1
        elif field.type in ['float', 'double']:
            return 1.0
        elif field.type == 'bool':
            return True
        else:
            return None
    
    async def _categorize_templates(self):
        """Categorize templates by functionality."""
        for template_id, template_info in self.templates.items():
            category = template_info['category']
            self.categories[category].append(template_id)
    
    # Public API methods
    
    async def get_all_templates(self) -> List[Dict[str, Any]]:
        """Get all available templates."""
        if not self.initialized:
            await self.initialize()
        
        templates = []
        for template_id, template_info in self.templates.items():
            templates.append({
                'template_id': template_id,
                'template_name': template_info['template_name'],
                'description': template_info['description'],
                'category': template_info['category'],
                'infrastructure_plant': template_info['infrastructure_plant'],
                'required_fields': template_info['required_fields'],
                'optional_fields': template_info['optional_fields']
            })
        
        return sorted(templates, key=lambda x: x['template_id'])
    
    async def get_template_categories(self) -> Dict[str, int]:
        """Get template categories with counts."""
        if not self.initialized:
            await self.initialize()
        
        return {category: len(template_ids) for category, template_ids in self.categories.items()}
    
    async def get_template_info(self, template_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed information for a specific template."""
        if not self.initialized:
            await self.initialize()
        
        template_info = self.templates.get(template_id)
        if not template_info:
            return None
        
        # Build detailed field information
        fields = []
        message = template_info['message']
        for field in message.fields:
            fields.append({
                'name': field.name,
                'type': field.type,
                'number': field.number,
                'label': field.label,
                'default_value': field.default_value,
                'description': field.description
            })
        
        return {
            'template_id': template_id,
            'template_name': template_info['template_name'],
            'description': template_info['description'],
            'category': template_info['category'],
            'infrastructure_plant': template_info['infrastructure_plant'],
            'is_request': template_info['is_request'],
            'is_response': template_info['is_response'],
            'corresponding_template': template_info['corresponding_template'],
            'fields': fields,
            'required_fields': template_info['required_fields'],
            'optional_fields': template_info['optional_fields'],
            'examples': template_info['examples']
        }
    
    async def get_templates_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Get all templates in a specific category."""
        if not self.initialized:
            await self.initialize()
        
        template_ids = self.categories.get(category, [])
        templates = []
        
        for template_id in template_ids:
            template_info = await self.get_template_info(template_id)
            if template_info:
                templates.append(template_info)
        
        return sorted(templates, key=lambda x: x['template_id'])
    
    async def validate_request(self, template_id: int, request_data: Dict[str, Any]) -> ValidationResult:
        """Validate a request against the template schema."""
        if not self.initialized:
            await self.initialize()
        
        template_info = self.templates.get(template_id)
        if not template_info:
            return ValidationResult(
                valid=False,
                errors=[f"Template {template_id} not found"],
                warnings=[],
                suggestions=[]
            )
        
        errors = []
        warnings = []
        suggestions = []
        
        message = template_info['message']
        
        # Check required fields
        for field in message.fields:
            if field.label == 'required' and field.name not in request_data:
                errors.append(f"Required field '{field.name}' is missing")
        
        # Check field types and values
        for field_name, field_value in request_data.items():
            field_def = next((f for f in message.fields if f.name == field_name), None)
            if not field_def:
                warnings.append(f"Unknown field '{field_name}' will be ignored")
                continue
            
            # Type validation
            type_valid, type_error = self._validate_field_type(field_def, field_value)
            if not type_valid:
                errors.append(f"Field '{field_name}': {type_error}")
        
        # Generate suggestions
        if not request_data.get('template_id'):
            suggestions.append(f"Consider setting template_id to {template_id}")
        
        if not request_data.get('user_msg'):
            suggestions.append("Consider adding user_msg field for request identification")
        
        return ValidationResult(
            valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )
    
    def _validate_field_type(self, field_def: FieldDefinition, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate that a field value matches the expected type."""
        if value is None:
            return True, None  # Allow None values
        
        if field_def.type == 'string':
            if not isinstance(value, str):
                return False, f"Expected string, got {type(value).__name__}"
        elif field_def.type in ['int32', 'int64', 'uint32', 'uint64']:
            if not isinstance(value, int):
                return False, f"Expected integer, got {type(value).__name__}"
        elif field_def.type in ['float', 'double']:
            if not isinstance(value, (int, float)):
                return False, f"Expected number, got {type(value).__name__}"
        elif field_def.type == 'bool':
            if not isinstance(value, bool):
                return False, f"Expected boolean, got {type(value).__name__}"
        elif field_def.label == 'repeated':
            if not isinstance(value, list):
                return False, f"Expected array for repeated field, got {type(value).__name__}"
        
        return True, None
    
    async def generate_python_code(self, template_id: int, template_name: str, 
                                 infrastructure_plant: str, request_data: Dict[str, Any]) -> str:
        """Generate Python code for a request."""
        template_info = self.templates.get(template_id)
        if not template_info:
            raise ValueError(f"Template {template_id} not found")
        
        # Generate Python code
        code_lines = [
            "#!/usr/bin/env python3",
            '"""',
            f"Generated code for Rithmic API Template {template_id} ({template_name})",
            f"Infrastructure Plant: {infrastructure_plant}",
            f"Generated by API Playground at {datetime.now().isoformat()}",
            '"""',
            "",
            "import asyncio",
            "import sys",
            "import os",
            "from pathlib import Path",
            "",
            "# Add required paths",
            "project_root = Path(__file__).parent.parent",
            "sys.path.append(str(project_root / 'proto_generated'))",
            "sys.path.append(str(project_root / 'endpoints' / 'shared'))",
            "",
            f"import {template_name}_pb2",
            "from config import get_config, InfraType",
            "from auth import create_authenticated_connection",
            "",
            "async def main():",
            '    """Execute the API request."""',
            "    print(f'Testing Rithmic API Template {template_id} ({template_name})')",
            "    ",
            "    try:",
            "        # Create authenticated connection",
            f"        infrastructure_type = InfraType.{self._get_infra_type_name(infrastructure_plant)}",
            "        connection, authenticator = await create_authenticated_connection(",
            "            infrastructure_type, 'api_playground_test')",
            "        ",
            "        if not connection or not authenticator:",
            "            print('❌ Failed to establish authenticated connection')",
            "            return False",
            "        ",
            "        print('✅ Authentication successful')",
            "        ",
            "        # Create request message",
            f"        request = {template_name}_pb2.{self._snake_to_pascal(template_name)}()",
        ]
        
        # Add request field assignments
        for field_name, field_value in request_data.items():
            if isinstance(field_value, str):
                code_lines.append(f"        request.{field_name} = '{field_value}'")
            elif isinstance(field_value, list):
                if field_value and isinstance(field_value[0], str):
                    for item in field_value:
                        code_lines.append(f"        request.{field_name}.append('{item}')")
                else:
                    code_lines.append(f"        request.{field_name}[:] = {field_value}")
            else:
                code_lines.append(f"        request.{field_name} = {field_value}")
        
        code_lines.extend([
            "        ",
            "        # Send request",
            "        serialized_request = request.SerializeToString()",
            "        print(f'📤 Sending request ({len(serialized_request)} bytes)')",
            "        ",
            "        if not await connection.send_message(serialized_request):",
            "            print('❌ Failed to send request')",
            "            return False",
            "        ",
            "        # Wait for response",
            "        config = get_config()",
            "        response_bytes = await connection.receive_message(timeout=config.message_timeout)",
            "        ",
            "        if not response_bytes:",
            "            print('❌ No response received')",
            "            return False",
            "        ",
            "        print(f'📥 Received response ({len(response_bytes)} bytes)')",
            "        print('✅ Request completed successfully')",
            "        ",
            "        # Cleanup",
            "        await authenticator.logout()",
            "        await connection.disconnect()",
            "        ",
            "        return True",
            "        ",
            "    except Exception as e:",
            "        print(f'❌ Error: {e}')",
            "        return False",
            "",
            "if __name__ == '__main__':",
            "    import asyncio",
            "    success = asyncio.run(main())",
            "    sys.exit(0 if success else 1)"
        ])
        
        return '\n'.join(code_lines)
    
    async def generate_javascript_code(self, template_id: int, template_name: str,
                                     infrastructure_plant: str, request_data: Dict[str, Any]) -> str:
        """Generate JavaScript code for a request."""
        # JavaScript/Node.js code generation
        code_lines = [
            "#!/usr/bin/env node",
            "/**",
            f" * Generated code for Rithmic API Template {template_id} ({template_name})",
            f" * Infrastructure Plant: {infrastructure_plant}",
            f" * Generated by API Playground at {datetime.now().isoformat()}",
            " */",
            "",
            "const WebSocket = require('ws');",
            "const protobuf = require('protobufjs');",
            "const path = require('path');",
            "",
            "// Configuration",
            "const CONFIG = {",
            "    // Add your Rithmic API configuration here",
            "    user: 'your_username',",
            "    password: 'your_password',",
            "    system: 'your_system',",
            "    uri: 'wss://your-rithmic-uri',",
            "    templateId: " + str(template_id),
            "};",
            "",
            "async function main() {",
            f"    console.log('Testing Rithmic API Template {template_id} ({template_name})');",
            "    ",
            "    try {",
            "        // Load protocol buffer definitions",
            f"        const protoPath = path.join(__dirname, 'proto', '{template_name}.proto');",
            "        const root = await protobuf.load(protoPath);",
            f"        const RequestMessage = root.lookupType('{self._snake_to_pascal(template_name)}');",
            "        ",
            "        // Create request message",
            "        const requestData = " + json.dumps(request_data, indent=8) + ";",
            "        ",
            "        // Validate and create message",
            "        const errMsg = RequestMessage.verify(requestData);",
            "        if (errMsg) {",
            "            throw new Error('Request validation failed: ' + errMsg);",
            "        }",
            "        ",
            "        const message = RequestMessage.create(requestData);",
            "        const buffer = RequestMessage.encode(message).finish();",
            "        ",
            "        console.log(`📤 Sending request (${buffer.length} bytes)`);",
            "        ",
            "        // Here you would establish WebSocket connection and send the message",
            "        // Implementation depends on your specific Rithmic client setup",
            "        ",
            "        console.log('✅ Request completed successfully');",
            "        return true;",
            "        ",
            "    } catch (error) {",
            "        console.error('❌ Error:', error.message);",
            "        return false;",
            "    }",
            "}",
            "",
            "// Run the main function",
            "main().then(success => {",
            "    process.exit(success ? 0 : 1);",
            "}).catch(error => {",
            "    console.error('Unexpected error:', error);",
            "    process.exit(1);",
            "});"
        ]
        
        return '\n'.join(code_lines)
    
    def _get_infra_type_name(self, infrastructure_plant: str) -> str:
        """Get InfraType enum name for infrastructure plant."""
        mapping = {
            "System": "SYSTEM",
            "Ticker Plant": "TICKER_PLANT", 
            "History Plant": "HISTORY_PLANT",
            "Order Plant": "ORDER_PLANT",
            "PnL Plant": "PNL_PLANT",
            "Repository Plant": "REPOSITORY_PLANT"
        }
        return mapping.get(infrastructure_plant, "SYSTEM")
    
    def _snake_to_pascal(self, snake_str: str) -> str:
        """Convert snake_case to PascalCase."""
        return ''.join(word.capitalize() for word in snake_str.split('_'))