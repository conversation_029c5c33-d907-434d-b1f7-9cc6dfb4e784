#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional Futures Trading Web Dashboard
==========================================

Web-based dashboard for monitoring, configuring, and controlling the Rithmic API project.
Replaces the native GUI with a modern web interface served on localhost:8000.

Features:
- Real-time market data monitoring  
- Database management and querying
- Process control for data collection scripts
- Configuration management
- Professional trading interface
"""

import os
import sys
from pathlib import Path
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import API modules 
from api import database, processes, configuration, market_data, code_generation, symbol_search, playground
from websockets import market_data_ws, logs_ws, processes_ws
from config import Settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize settings
settings = Settings()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    logger.info("Starting Professional Futures Trading Dashboard")
    logger.info(f"Dashboard will be available at http://localhost:8000")
    yield
    logger.info("Shutting down dashboard")

# Create FastAPI application
app = FastAPI(
    title="Professional Futures Trading Dashboard",
    description="Web dashboard for Rithmic API project management", 
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(database.router, prefix="/api/database", tags=["database"])
app.include_router(processes.router, prefix="/api/processes", tags=["processes"])
app.include_router(configuration.router, prefix="/api/config", tags=["configuration"])
app.include_router(market_data.router, prefix="/api/market_data", tags=["market_data"])
app.include_router(code_generation.router, prefix="/api/code-generation", tags=["code_generation"])
app.include_router(symbol_search.router, prefix="/api/symbol-search", tags=["symbol_search"])
app.include_router(playground.router, prefix="/api/playground", tags=["playground"])

# WebSocket endpoints
@app.websocket("/ws/market_data")
async def websocket_market_data(websocket: WebSocket):
    """WebSocket endpoint for real-time market data."""
    await market_data_ws.handle_websocket(websocket)

@app.websocket("/ws/logs")
async def websocket_logs(websocket: WebSocket):
    """WebSocket endpoint for real-time logs."""
    await logs_ws.handle_websocket(websocket)

@app.websocket("/ws/processes")
async def websocket_processes(websocket: WebSocket):
    """WebSocket endpoint for process updates."""
    await processes_ws.handle_websocket(websocket)

@app.websocket("/ws/playground")
async def websocket_playground(websocket: WebSocket):
    """WebSocket endpoint for API playground updates."""
    await playground.playground_websocket(websocket)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "Professional Futures Trading Dashboard",
        "version": "1.0.0"
    }

# Server time endpoint
@app.get("/api/server/time")
async def get_server_time():
    """Get server current time and timezone."""
    import datetime
    import time
    
    now = datetime.datetime.now()
    timezone_name = time.tzname[time.daylight]
    
    return {
        "timestamp": now.isoformat(),
        "timezone": timezone_name,
        "formatted_time": now.strftime("%Y-%m-%d %H:%M:%S"),
        "time_only": now.strftime("%H:%M:%S"),
        "unix_timestamp": int(now.timestamp())
    }

# Serve static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Serve main page
@app.get("/", response_class=HTMLResponse)
async def read_index():
    """Serve the main dashboard page."""
    return FileResponse('static/index.html')

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0", 
        port=8000,
        reload=False,
        log_level="info",
        ws="none"  # Temporarily disable WebSocket protocol due to compatibility issue
    )