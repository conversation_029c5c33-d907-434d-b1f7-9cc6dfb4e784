/* Professional Futures Trading Dashboard Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #1a1a1a;
    --secondary-color: #2d2d2d;
    --accent-color: #0066cc;
    --success-color: #00a651;
    --warning-color: #ff9500;
    --danger-color: #ff3b30;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-color: #404040;
    --hover-color: #3a3a3a;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--primary-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Layout Structure */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-icon {
    font-size: 1.8rem;
}

.header-status {
    display: flex;
    align-items: center;
    gap: 20px;
}

.status-indicator {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
}

.server-time {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Sidebar Navigation */
.sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    width: 250px;
    height: calc(100vh - 60px);
    background-color: var(--secondary-color);
    border-right: 1px solid var(--border-color);
    z-index: 999;
    overflow-y: auto;
}

.nav-menu {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background-color: var(--hover-color);
    border-left-color: var(--accent-color);
}

.nav-item.active {
    background-color: var(--accent-color);
    border-left-color: var(--accent-color);
}

.nav-icon {
    font-size: 1.3rem;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.nav-text {
    font-weight: 500;
}

/* Main Content */
.main-content {
    margin-left: 250px;
    margin-top: 60px;
    padding: 30px;
    min-height: calc(100vh - 60px);
    background-color: var(--primary-color);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.content-section h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 10px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, var(--secondary-color), var(--hover-color));
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    box-shadow: 0 4px 15px var(--shadow-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--shadow-color);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 5px;
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px var(--shadow-color);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: var(--text-primary);
    text-align: center;
}

.chart-container canvas {
    max-height: 300px;
}

/* Controls Panel */
.controls-panel, .database-controls, .process-controls, .logs-controls {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group label {
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 80px;
}

/* Form Elements */
input[type="text"],
input[type="number"],
input[type="password"],
select,
textarea {
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px 12px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition);
    min-width: 150px;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

/* Buttons */
.btn {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    background-color: var(--hover-color);
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: #0052a3;
    border-color: #0052a3;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #008a43;
}

.btn-secondary {
    background-color: var(--text-muted);
    border-color: var(--text-muted);
}

.btn-secondary:hover {
    background-color: #777777;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Data Grids */
.market-data-grid,
.database-grid,
.processes-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
}

.market-data-grid {
    grid-template-columns: 1fr 1fr;
}

.database-grid {
    grid-template-columns: 300px 1fr;
}

.data-panel,
.tables-panel {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.data-panel h3,
.tables-panel h3 {
    margin-bottom: 15px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

/* Data Tables */
.data-table,
.table-data {
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
}

.data-table table,
.table-data table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td,
.table-data th,
.table-data td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.data-table th,
.table-data th {
    background-color: var(--secondary-color);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
}

.data-table td,
.table-data td {
    color: var(--text-secondary);
}

/* Tables List */
.tables-list {
    max-height: 400px;
    overflow-y: auto;
}

.table-item {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: var(--transition);
    background-color: var(--primary-color);
}

.table-item:hover {
    background-color: var(--hover-color);
    border-color: var(--accent-color);
}

.table-item.selected {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.table-name {
    font-weight: 500;
    color: var(--text-primary);
}

.table-rows {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 4px;
}

/* Processes List */
.processes-list {
    display: grid;
    gap: 15px;
}

.process-item {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    transition: var(--transition);
}

.process-item:hover {
    border-color: var(--accent-color);
}

.process-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.process-name {
    font-weight: 600;
    color: var(--text-primary);
}

.process-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.process-status.running {
    background-color: var(--success-color);
    color: white;
}

.process-status.stopped {
    background-color: var(--text-muted);
    color: white;
}

.process-status.error {
    background-color: var(--danger-color);
    color: white;
}

.process-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.process-controls {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

/* Configuration Tabs */
.config-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.tab {
    padding: 12px 24px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
    color: var(--text-secondary);
}

.tab:hover {
    color: var(--text-primary);
}

.tab.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
}

.config-panel {
    display: none;
}

.config-panel.active {
    display: block;
}

.config-form {
    display: grid;
    gap: 20px;
    max-width: 500px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

/* Environment Variables */
.env-vars-list {
    display: grid;
    gap: 15px;
}

.env-var-item {
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 15px;
}

.env-var-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.env-var-key {
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
}

.env-var-value {
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.env-var-description {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-top: 5px;
}

/* Logs */
.logs-container {
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    height: 500px;
    overflow-y: auto;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.log-entry {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid var(--border-color);
}

.log-entry.info {
    border-left-color: var(--accent-color);
}

.log-entry.warning {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 149, 0, 0.1);
}

.log-entry.error {
    border-left-color: var(--danger-color);
    background-color: rgba(255, 59, 48, 0.1);
}

.log-timestamp {
    color: var(--text-muted);
    margin-right: 10px;
}

.log-level {
    font-weight: 600;
    margin-right: 10px;
}

.log-message {
    color: var(--text-secondary);
}

/* Checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    min-width: auto;
    width: 16px;
    height: 16px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.pagination button {
    padding: 8px 16px;
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
}

.pagination button:hover:not(:disabled) {
    background-color: var(--hover-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination .current-page {
    color: var(--accent-color);
    font-weight: 600;
}

/* Status Bar */
.status-bar {
    position: fixed;
    bottom: 0;
    left: 250px;
    right: 0;
    height: 30px;
    background-color: var(--secondary-color);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-size: 0.8rem;
    color: var(--text-muted);
    z-index: 999;
}

.status-item {
    margin-right: 30px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(26, 26, 26, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .status-bar {
        left: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .market-data-grid {
        grid-template-columns: 1fr;
    }
    
    .database-grid {
        grid-template-columns: 1fr;
    }
    
    .controls-panel,
    .database-controls,
    .process-controls,
    .logs-controls {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--primary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Utilities */
.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Code Generation Modal Styles */
.code-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.code-modal {
    background: var(--secondary-color);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.code-modal.loading {
    max-width: 300px;
    max-height: 200px;
    text-align: center;
    padding: 40px;
}

.parameter-dialog {
    max-width: 600px;
    max-height: 80vh;
}

@keyframes slideIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 24px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.close-btn:hover {
    background: var(--hover-color);
    color: var(--text-primary);
}

.modal-body {
    flex: 1;
    padding: 24px;
    overflow: auto;
}

.modal-body pre {
    margin: 0;
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    max-height: 60vh;
    overflow: auto;
}

.modal-body code {
    background: none !important;
    color: var(--text-primary);
    font-family: inherit;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-info {
    display: flex;
    gap: 16px;
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Parameter Dialog Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    margin: 0;
    font-weight: normal;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
    margin-bottom: 0;
}

.form-help {
    display: block;
    margin-top: 4px;
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Show Code Button Styles */
.show-code-btn {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.show-code-btn:hover {
    background: linear-gradient(135deg, #357abd, #2968a3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.show-code-btn:active {
    transform: translateY(0);
}

.show-code-btn::before {
    content: '📄';
    font-size: 1rem;
}

/* Section Headers with Show Code */
.section-header-with-code {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header-with-code h2 {
    margin: 0;
}

/* Button Group for Multiple Code Options */
.code-button-group {
    display: flex;
    gap: 8px;
}

.code-button-group .show-code-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Responsive Design for Modals */
@media (max-width: 768px) {
    .code-modal {
        max-width: 95vw;
        max-height: 95vh;
        margin: 10px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }
    
    .modal-body pre {
        font-size: 12px;
        padding: 16px;
    }
    
    .modal-actions {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .code-info {
        flex-direction: column;
        gap: 8px;
        font-size: 0.8rem;
    }
    
    .parameter-dialog {
        max-width: 95vw;
    }
}

/* Contracts Modal Styles */
.contracts-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.contracts-modal {
    background: var(--secondary-color);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    max-width: 90vw;
    max-height: 90vh;
    width: 900px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.contracts-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--primary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.selection-buttons {
    display: flex;
    gap: 8px;
}

.selection-buttons .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.contracts-hierarchy {
    max-height: 50vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
}

.contract-category {
    margin-bottom: 16px;
}

.category-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: var(--hover-color);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.category-header:hover {
    background: var(--border-color);
}

.category-icon {
    font-size: 1.2rem;
}

.category-name {
    flex: 1;
    color: var(--text-primary);
}

.category-count {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.category-toggle {
    color: var(--text-muted);
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

.category-contracts {
    margin-top: 8px;
    padding-left: 20px;
}

.contract-item {
    margin-bottom: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
}

.contract-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition);
}

.contract-header:hover {
    background: var(--hover-color);
}

.contract-checkbox {
    width: 16px;
    height: 16px;
}

.contract-symbol {
    font-weight: 700;
    color: var(--accent-color);
    min-width: 60px;
}

.contract-name {
    flex: 1;
    color: var(--text-primary);
}

.contract-exchange {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
}

.contract-toggle {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.contract-months {
    padding: 16px;
    background: var(--primary-color);
    border-top: 1px solid var(--border-color);
    display: none;
}

.months-header {
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.months-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
}

.month-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.month-item:hover {
    background: var(--hover-color);
    border-color: var(--accent-color);
}

.month-item.front-month {
    border-color: var(--success-color);
    background: rgba(0, 166, 81, 0.1);
}

.month-checkbox {
    width: 14px;
    height: 14px;
}

.month-code {
    font-weight: 600;
    color: var(--text-primary);
}

.front-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--success-color);
    color: white;
    font-size: 0.6rem;
    padding: 1px 4px;
    border-radius: 2px;
    font-weight: 700;
}

.selection-summary {
    display: flex;
    align-items: center;
    gap: 16px;
    color: var(--text-secondary);
}

/* Smart Selection Styles */
.smart-selection-buttons {
    display: flex;
    gap: 4px;
    margin-bottom: 16px;
}

.smart-btn {
    padding: 4px 8px;
    font-size: 0.75rem;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: var(--transition);
}

.smart-btn:hover {
    background: var(--success-color);
}

.smart-btn.active {
    background: var(--success-color);
    box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.3);
}

/* Contract Search */
.contracts-search {
    margin-bottom: 16px;
}

.contracts-search input {
    width: 100%;
    padding: 8px 12px;
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
}

.contracts-search input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

/* Enhanced Market Data Section */
.market-data-selector {
    margin-bottom: 20px;
}

.contract-selector-button {
    background: linear-gradient(135deg, var(--accent-color), #0052a3);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.contract-selector-button:hover {
    background: linear-gradient(135deg, #0052a3, #003d7a);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3);
}

.contract-selector-button::before {
    content: '🏛️';
    font-size: 1rem;
}

.selected-contracts-display {
    margin-top: 12px;
    padding: 12px;
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-family: monospace;
    font-size: 0.9rem;
}

.selected-contracts-display.empty {
    color: var(--text-muted);
    font-style: italic;
}

/* Responsive Design for Contracts */
@media (max-width: 768px) {
    .contracts-modal {
        max-width: 95vw;
        max-height: 95vh;
        margin: 10px;
    }
    
    .contracts-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .selection-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .months-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .contract-header {
        font-size: 0.9rem;
    }
    
    .category-header {
        font-size: 0.9rem;
    }
}

/* Symbol Search Styles */
.symbol-search-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    height: calc(100vh - 200px);
    min-height: 600px;
}

.search-controls-panel {
    background: var(--secondary-color);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
    overflow-y: auto;
}

.search-results-panel {
    background: var(--secondary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.search-filters h3 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group {
    margin-bottom: 16px;
}

.filter-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-primary);
}

.filter-group select,
.filter-group input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-group input[type="text"]:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.filter-group input[type="text"].valid {
    border-color: var(--success-color);
}

.filter-group input[type="text"].invalid {
    border-color: var(--danger-color);
}

.search-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.search-actions .btn {
    flex: 1;
    min-width: 120px;
}

.pattern-examples {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.pattern-examples h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.examples-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.example-item {
    padding: 8px 12px;
    background: var(--primary-color);
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 0.85rem;
}

.example-item code {
    color: var(--accent-color);
    font-weight: 600;
    background: none;
    padding: 0;
}

.results-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.results-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.results-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.results-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.results-table-container {
    flex: 1;
    overflow: auto;
    padding: 0;
}

#symbols-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--secondary-color);
}

#symbols-table th {
    background: var(--primary-color);
    color: var(--text-primary);
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

#symbols-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.symbol-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.symbol-row:hover {
    background: var(--hover-color);
}

.symbol-row.selected {
    background: rgba(0, 102, 204, 0.15);
    border-left: 3px solid var(--accent-color);
}

.symbol-cell {
    font-family: monospace;
    font-weight: 600;
    color: var(--accent-color);
}

.description-cell {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.numeric-cell {
    text-align: right;
    font-family: monospace;
}

.status-cell {
    text-align: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-discovered {
    background: rgba(0, 168, 81, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-active {
    background: rgba(0, 102, 204, 0.2);
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

.status-inactive {
    background: rgba(153, 153, 153, 0.2);
    color: var(--text-muted);
    border: 1px solid var(--text-muted);
}

.no-results td {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 40px 16px;
}

/* Symbol Details Modal */
.symbol-details-modal {
    max-width: 800px;
    max-height: 90vh;
}

.symbol-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.detail-section {
    background: var(--primary-color);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

.detail-section h4 {
    margin: 0 0 16px 0;
    color: var(--accent-color);
    font-size: 1rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item label {
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 120px;
    margin-right: 12px;
}

.detail-item span {
    color: var(--text-primary);
    text-align: right;
    font-family: monospace;
}

/* Responsive Design for Symbol Search */
@media (max-width: 1200px) {
    .symbol-search-layout {
        grid-template-columns: 300px 1fr;
        gap: 16px;
    }
    
    .search-controls-panel {
        padding: 16px;
    }
    
    .results-header {
        padding: 16px;
    }
}

@media (max-width: 768px) {
    .symbol-search-layout {
        grid-template-columns: 1fr;
        gap: 12px;
        height: auto;
        min-height: auto;
    }
    
    .search-controls-panel {
        order: 2;
        height: auto;
        max-height: 400px;
    }
    
    .search-results-panel {
        order: 1;
        min-height: 500px;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .results-controls {
        justify-content: space-between;
    }
    
    .results-actions {
        justify-content: center;
    }
    
    .search-actions {
        flex-direction: column;
    }
    
    .search-actions .btn {
        min-width: auto;
    }
    
    #symbols-table th,
    #symbols-table td {
        padding: 8px;
        font-size: 0.85rem;
    }
    
    .description-cell {
        max-width: 150px;
    }
    
    .symbol-details-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: stretch;
        text-align: left;
    }
    
    .detail-item label {
        min-width: auto;
        margin-right: 0;
        margin-bottom: 4px;
    }
    
    .detail-item span {
        text-align: left;
    }
}

/* ==============================================
   API PLAYGROUND STYLES
   ============================================== */

/* Playground Layout */
.playground-layout {
    display: grid;
    grid-template-columns: 350px 1fr 400px;
    gap: 20px;
    height: calc(100vh - 180px);
    min-height: 600px;
}

.playground-panel {
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.playground-panel h3 {
    color: var(--text-primary);
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    font-size: 1.1rem;
    font-weight: 600;
}

/* Template Browser Panel */
.template-browser-panel {
    overflow-y: auto;
}

.template-filters {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--primary-color);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.templates-list {
    flex: 1;
    overflow-y: auto;
}

.template-category {
    margin-bottom: 16px;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--hover-color);
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
}

.category-name {
    font-weight: 600;
    color: var(--text-primary);
}

.category-count {
    background: var(--accent-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.category-templates {
    padding-left: 12px;
}

.template-item {
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
}

.template-item:hover {
    background: var(--hover-color);
    border-color: var(--accent-color);
}

.template-item.selected {
    background: rgba(0, 102, 204, 0.1);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.template-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.template-id {
    background: var(--text-muted);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
}

.template-description {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-bottom: 8px;
    line-height: 1.4;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.template-plant {
    background: var(--secondary-color);
    padding: 2px 6px;
    border-radius: 3px;
}

.template-fields {
    font-style: italic;
}

/* Request Builder Panel */
.request-builder-panel {
    overflow: hidden;
}

.request-builder-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.selected-template-info {
    background: var(--primary-color);
    padding: 16px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.no-template-selected {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.template-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.template-info-header h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    margin: 0;
}

.template-id-badge {
    background: var(--accent-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.template-info-details {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.template-plant,
.template-category,
.template-type {
    background: var(--hover-color);
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.request-builder-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.template-documentation {
    flex: 1;
    overflow-y: auto;
}

.welcome-message {
    padding: 20px;
    text-align: center;
}

.welcome-message h4 {
    color: var(--accent-color);
    margin-bottom: 16px;
}

.welcome-message ul {
    text-align: left;
    color: var(--text-secondary);
    line-height: 1.8;
}

.request-form-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.form-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 16px;
}

.form-tabs .tab {
    padding: 10px 16px;
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-bottom: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.form-tabs .tab:first-child {
    border-top-left-radius: 4px;
}

.form-tabs .tab:last-child {
    border-top-right-radius: 4px;
}

.form-tabs .tab.active {
    background: var(--secondary-color);
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.form-tabs .tab:hover:not(.active) {
    background: var(--hover-color);
}

.form-content {
    flex: 1;
    overflow: hidden;
}

.form-panel {
    height: 100%;
    overflow-y: auto;
    display: none;
}

.form-panel.active {
    display: block;
}

.form-fields {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-field label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.required {
    color: var(--danger-color);
    font-weight: bold;
}

.field-type {
    background: var(--text-muted);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
}

.form-input {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--primary-color);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.field-description {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 4px;
}

/* Field Validation Styles */
.form-field.field-valid .form-input {
    border-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.2);
}

.form-field.field-invalid .form-input {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.2);
}

.field-error {
    font-size: 0.75rem;
    color: var(--danger-color);
    margin-top: 4px;
    padding: 4px 8px;
    background: rgba(255, 59, 48, 0.1);
    border-radius: 3px;
    border: 1px solid var(--danger-color);
}

.request-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
    margin-top: 16px;
}

.examples-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.example-item {
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
}

.example-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.example-header h5 {
    color: var(--text-primary);
    margin: 0;
}

.load-example-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.load-example-btn:hover {
    background: #0055aa;
}

.example-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 12px;
}

.example-data {
    background: var(--hover-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    font-size: 0.8rem;
    color: var(--text-primary);
    overflow-x: auto;
}

/* Results Panel */
.results-panel {
    overflow: hidden;
}

.session-management {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.session-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.session-selector label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.session-selector select {
    flex: 1;
    min-width: 150px;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--primary-color);
    color: var(--text-primary);
    font-size: 0.8rem;
}

.session-status {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-indicator {
    font-size: 0.9rem;
    font-weight: 500;
}

.status-indicator.connected {
    color: var(--success-color);
}

.status-indicator.disconnected {
    color: var(--danger-color);
}

.session-details {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.results-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 16px;
    overflow-x: auto;
}

.results-tabs .tab {
    padding: 8px 12px;
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-bottom: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.8rem;
    white-space: nowrap;
}

.results-tabs .tab.active {
    background: var(--secondary-color);
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.results-tabs .tab:hover:not(.active) {
    background: var(--hover-color);
}

.results-content {
    flex: 1;
    overflow: hidden;
}

.results-panel-content {
    height: 100%;
    overflow-y: auto;
    display: none;
}

.results-panel-content.active {
    display: block;
}

.response-container,
.validation-container {
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
}

.response-result,
.validation-result {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.response-status {
    font-weight: 600;
}

.response-result.success .response-status {
    color: var(--success-color);
}

.response-result.error .response-status {
    color: var(--danger-color);
}

.response-timing {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-family: 'Courier New', monospace;
}

.response-details h5 {
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.json-display {
    background: var(--hover-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    font-size: 0.8rem;
    color: var(--text-primary);
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
}

.error-message {
    background: rgba(255, 59, 48, 0.1);
    border: 1px solid var(--danger-color);
    border-radius: 4px;
    padding: 12px;
    color: var(--danger-color);
    font-size: 0.9rem;
}

.validation-result.valid .validation-status {
    color: var(--success-color);
}

.validation-result.invalid .validation-status {
    color: var(--danger-color);
}

.validation-errors,
.validation-warnings,
.validation-suggestions {
    margin-top: 12px;
}

.validation-errors h5 {
    color: var(--danger-color);
}

.validation-warnings h5 {
    color: var(--warning-color);
}

.validation-suggestions h5 {
    color: var(--accent-color);
}

.validation-errors ul,
.validation-warnings ul,
.validation-suggestions ul {
    margin-top: 8px;
    padding-left: 20px;
}

.validation-errors li {
    color: var(--danger-color);
}

.validation-warnings li {
    color: var(--warning-color);
}

.validation-suggestions li {
    color: var(--text-secondary);
}

.history-controls,
.streaming-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.history-list,
.streaming-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.history-item {
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.history-item:hover {
    background: var(--hover-color);
}

.history-item.success {
    border-left: 4px solid var(--success-color);
}

.history-item.error {
    border-left: 4px solid var(--danger-color);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.history-template {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.history-time {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-family: 'Courier New', monospace;
}

.history-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.history-status {
    font-size: 1rem;
}

.history-timing {
    font-family: 'Courier New', monospace;
}

.no-response,
.no-history,
.no-streaming,
.no-validation {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 40px 20px;
}

.loading-message {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 20px;
}

/* Code Modal */
.code-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.code-modal {
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.code-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.code-modal-header h3 {
    color: var(--text-primary);
    margin: 0;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 4px;
    transition: var(--transition);
}

.close-modal-btn:hover {
    color: var(--text-primary);
}

.code-modal-content {
    flex: 1;
    overflow: auto;
    padding: 20px;
}

.generated-code {
    background: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 16px;
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    overflow-x: auto;
}

.code-modal-actions {
    display: flex;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    justify-content: flex-end;
}

/* Responsive Design for Playground */
@media (max-width: 1400px) {
    .playground-layout {
        grid-template-columns: 300px 1fr 350px;
    }
}

@media (max-width: 1200px) {
    .playground-layout {
        grid-template-columns: 280px 1fr 320px;
        gap: 16px;
    }
    
    .playground-panel {
        padding: 16px;
    }
}

@media (max-width: 900px) {
    .playground-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: 16px;
        height: auto;
    }
    
    .template-browser-panel {
        max-height: 400px;
    }
    
    .request-builder-panel {
        max-height: 500px;
    }
    
    .results-panel {
        max-height: 400px;
    }
    
    .session-selector {
        flex-direction: column;
        align-items: stretch;
    }
    
    .session-selector select {
        min-width: auto;
    }
    
    .template-meta {
        flex-direction: column;
        gap: 4px;
    }
}