<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Futures Trading Dashboard</title>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">
                <span class="header-icon">📊</span>
                Professional Futures Trading Dashboard
            </h1>
            <div class="header-status">
                <span id="connection-status" class="status-indicator">🔴 Disconnected</span>
                <span id="server-time" class="server-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="sidebar">
        <div class="nav-menu">
            <div class="nav-item active" data-section="overview">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">Overview</span>
            </div>
            <div class="nav-item" data-section="market-data">
                <span class="nav-icon">📈</span>
                <span class="nav-text">Market Data</span>
            </div>
            <div class="nav-item" data-section="database">
                <span class="nav-icon">🗄️</span>
                <span class="nav-text">Database</span>
            </div>
            <div class="nav-item" data-section="processes">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">Processes</span>
            </div>
            <div class="nav-item" data-section="symbol-search">
                <span class="nav-icon">🔍</span>
                <span class="nav-text">Symbol Search</span>
            </div>
            <div class="nav-item" data-section="api-playground">
                <span class="nav-icon">🧪</span>
                <span class="nav-text">API Playground</span>
            </div>
            <div class="nav-item" data-section="configuration">
                <span class="nav-icon">🔧</span>
                <span class="nav-text">Configuration</span>
            </div>
            <div class="nav-item" data-section="logs">
                <span class="nav-icon">📝</span>
                <span class="nav-text">Logs</span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        
        <!-- Overview Section -->
        <section id="overview-section" class="content-section active">
            <h2>System Overview</h2>
            
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3 id="total-symbols">0</h3>
                        <p>Total Symbols</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-content">
                        <h3 id="active-symbols">0</h3>
                        <p>Active Symbols</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🔄</div>
                    <div class="stat-content">
                        <h3 id="running-processes">0</h3>
                        <p>Running Processes</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🗄️</div>
                    <div class="stat-content">
                        <h3 id="database-status">Unknown</h3>
                        <p>Database Status</p>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="charts-grid">
                <div class="chart-container">
                    <h3>Market Activity</h3>
                    <canvas id="market-activity-chart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Process Status</h3>
                    <canvas id="process-status-chart"></canvas>
                </div>
            </div>
        </section>

        <!-- Market Data Section -->
        <section id="market-data-section" class="content-section">
            <div class="section-header-with-code">
                <h2>Real-time Market Data</h2>
                <div class="code-button-group">
                    <button class="show-code-btn" onclick="codeGenerator.showDataCollectionDialog()">Show Collection Code</button>
                </div>
            </div>
            
            <div class="market-data-selector">
                <button class="contract-selector-button" onclick="contractsManager.showContractsModal(updateSelectedContracts)">
                    Select Contracts
                </button>
                <div id="selected-contracts-display" class="selected-contracts-display empty">
                    No contracts selected
                </div>
            </div>
            
            <div class="controls-panel">
                <div class="control-group">
                    <label for="symbol-input">Symbol:</label>
                    <input type="text" id="symbol-input" placeholder="ES" />
                </div>
                <div class="control-group">
                    <label for="exchange-input">Exchange:</label>
                    <select id="exchange-input">
                        <option value="CME">CME</option>
                        <option value="CBOT">CBOT</option>
                        <option value="NYMEX">NYMEX</option>
                        <option value="COMEX">COMEX</option>
                    </select>
                </div>
                <div class="control-group">
                    <button id="subscribe-btn" class="btn btn-primary">Subscribe</button>
                    <button id="unsubscribe-btn" class="btn btn-secondary">Unsubscribe</button>
                </div>
            </div>

            <div class="market-data-grid">
                <div class="data-panel">
                    <h3>Last Trades</h3>
                    <div id="trades-data" class="data-table"></div>
                </div>
                <div class="data-panel">
                    <h3>Best Bid/Offer</h3>
                    <div id="quotes-data" class="data-table"></div>
                </div>
            </div>
        </section>

        <!-- Database Section -->
        <section id="database-section" class="content-section">
            <div class="section-header-with-code">
                <h2>Database Management</h2>
                <div class="code-button-group">
                    <button class="show-code-btn" onclick="codeGenerator.showDatabaseQueryDialog()">Show Query Code</button>
                </div>
            </div>
            
            <div class="database-controls">
                <button id="refresh-tables-btn" class="btn btn-primary">Refresh Tables</button>
                <button id="export-data-btn" class="btn btn-secondary">Export Data</button>
                <button id="test-db-connection-btn" class="btn btn-secondary">Test Connection</button>
                <div class="search-box">
                    <input type="text" id="table-search" placeholder="Search tables..." />
                </div>
            </div>

            <div class="database-grid">
                <div class="tables-panel">
                    <h3>Tables</h3>
                    <div id="tables-list" class="tables-list"></div>
                </div>
                <div class="data-panel">
                    <h3>Table Data</h3>
                    <div class="table-controls">
                        <select id="page-size-select">
                            <option value="50">50 rows</option>
                            <option value="100" selected>100 rows</option>
                            <option value="500">500 rows</option>
                            <option value="1000">1000 rows</option>
                        </select>
                        <input type="text" id="filter-input" placeholder="WHERE clause..." />
                        <button id="apply-filter-btn" class="btn btn-sm">Apply Filter</button>
                    </div>
                    <div id="table-data" class="table-data"></div>
                    <div id="pagination" class="pagination"></div>
                </div>
            </div>
        </section>

        <!-- Processes Section -->
        <section id="processes-section" class="content-section">
            <div class="section-header-with-code">
                <h2>Process Management</h2>
                <div class="code-button-group">
                    <button class="show-code-btn" onclick="codeGenerator.showDataCollectionDialog()">Show Collection Code</button>
                </div>
            </div>
            
            <div class="process-controls">
                <select id="script-select">
                    <option value="">Select script to start...</option>
                </select>
                <button id="start-process-btn" class="btn btn-success">Start Process</button>
                <button id="refresh-processes-btn" class="btn btn-primary">Refresh</button>
            </div>

            <div class="processes-grid">
                <div id="processes-list" class="processes-list"></div>
            </div>
        </section>

        <!-- Configuration Section -->
        <section id="configuration-section" class="content-section">
            <div class="section-header-with-code">
                <h2>Configuration Management</h2>
                <div class="code-button-group">
                    <button class="show-code-btn" onclick="codeGenerator.showConfigurationDialog()">Show Config Code</button>
                </div>
            </div>
            
            <div class="config-tabs">
                <div class="tab active" data-tab="database-config">Database</div>
                <div class="tab" data-tab="rithmic-config">Rithmic API</div>
                <div class="tab" data-tab="environment-config">Environment</div>
            </div>

            <div class="config-content">
                <div id="database-config" class="config-panel active">
                    <h3>Database Configuration</h3>
                    <div class="config-form">
                        <div class="form-group">
                            <label for="db-host">Host:</label>
                            <input type="text" id="db-host" />
                        </div>
                        <div class="form-group">
                            <label for="db-port">Port:</label>
                            <input type="number" id="db-port" />
                        </div>
                        <div class="form-group">
                            <label for="db-user">User:</label>
                            <input type="text" id="db-user" />
                        </div>
                        <div class="form-group">
                            <label for="db-database">Database:</label>
                            <input type="text" id="db-database" />
                        </div>
                        <button id="test-db-connection-btn" class="btn btn-primary">Test Connection</button>
                    </div>
                </div>
                
                <div id="rithmic-config" class="config-panel">
                    <h3>Rithmic API Configuration</h3>
                    <div class="config-form">
                        <div class="form-group">
                            <label for="rithmic-user">User:</label>
                            <input type="text" id="rithmic-user" />
                        </div>
                        <div class="form-group">
                            <label for="rithmic-system">System:</label>
                            <input type="text" id="rithmic-system" />
                        </div>
                        <div class="form-group">
                            <label for="rithmic-gateway">Gateway:</label>
                            <input type="text" id="rithmic-gateway" />
                        </div>
                        <div class="form-group">
                            <label for="rithmic-uri">URI:</label>
                            <input type="text" id="rithmic-uri" />
                        </div>
                        <button id="validate-rithmic-btn" class="btn btn-primary">Validate Configuration</button>
                    </div>
                </div>
                
                <div id="environment-config" class="config-panel">
                    <h3>Environment Variables</h3>
                    <div id="env-vars-list" class="env-vars-list"></div>
                </div>
            </div>
        </section>

        <!-- Symbol Search Section -->
        <section id="symbol-search-section" class="content-section">
            <div class="section-header-with-code">
                <h2>Symbol Search & Discovery</h2>
                <div class="code-button-group">
                    <button class="show-code-btn" onclick="symbolSearchManager.showSearchCodeDialog()">Show Search Code</button>
                </div>
            </div>
            
            <div class="symbol-search-layout">
                <!-- Left Panel - Search & Filters -->
                <div class="search-controls-panel">
                    <div class="search-filters">
                        <h3>🔍 Search & Filters</h3>
                        
                        <div class="filter-group">
                            <label for="exchange-filter">Exchange:</label>
                            <select id="exchange-filter">
                                <option value="All">All</option>
                                <option value="CME">CME</option>
                                <option value="CBOT">CBOT</option>
                                <option value="NYMEX">NYMEX</option>
                                <option value="COMEX">COMEX</option>
                                <option value="ICE">ICE</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="symbol-pattern">Symbol Pattern:</label>
                            <input type="text" id="symbol-pattern" placeholder="ES*, *Z25, NQ*" />
                        </div>
                        
                        <div class="filter-group">
                            <label for="product-code">Product Code:</label>
                            <select id="product-code">
                                <option value="All">All</option>
                                <option value="ES">ES</option>
                                <option value="NQ">NQ</option>
                                <option value="YM">YM</option>
                                <option value="RTY">RTY</option>
                                <option value="ZN">ZN</option>
                                <option value="ZB">ZB</option>
                                <option value="ZF">ZF</option>
                                <option value="ZT">ZT</option>
                                <option value="CL">CL</option>
                                <option value="NG">NG</option>
                                <option value="GC">GC</option>
                                <option value="SI">SI</option>
                                <option value="HG">HG</option>
                                <option value="PA">PA</option>
                            </select>
                        </div>
                        
                        <div class="search-actions">
                            <button id="discover-symbols-btn" class="btn btn-primary">🔍 Discover Symbols</button>
                            <button id="clear-search-btn" class="btn btn-secondary">🧹 Clear</button>
                        </div>
                    </div>
                    
                    <div class="pattern-examples">
                        <h4>Pattern Examples</h4>
                        <div class="examples-list">
                            <div class="example-item">
                                <code>ES*</code> - All E-mini S&P 500 contracts
                            </div>
                            <div class="example-item">
                                <code>NQ*</code> - All E-mini NASDAQ contracts
                            </div>
                            <div class="example-item">
                                <code>*Z25</code> - All December 2025 contracts
                            </div>
                            <div class="example-item">
                                <code>ES*24</code> - All E-mini S&P 500 2024 contracts
                            </div>
                            <div class="example-item">
                                <code>*H*</code> - All March contracts
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Panel - Results -->
                <div class="search-results-panel">
                    <div class="results-header">
                        <h3>📊 Symbol Results</h3>
                        <div class="results-controls">
                            <span id="results-count">Results: 0</span>
                            <div class="results-actions">
                                <button id="export-csv-btn" class="btn btn-sm btn-secondary">📤 Export CSV</button>
                                <button id="save-database-btn" class="btn btn-sm btn-secondary">💾 Save to Database</button>
                                <button id="show-details-btn" class="btn btn-sm btn-secondary">📊 Show Details</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="results-table-container">
                        <table id="symbols-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>Symbol</th>
                                    <th>Exchange</th>
                                    <th>Product</th>
                                    <th>Description</th>
                                    <th>Tick Size</th>
                                    <th>Contract Size</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="symbols-table-body">
                                <tr class="no-results">
                                    <td colspan="7">No symbols found. Use the search filters to discover symbols.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- API Playground Section -->
        <section id="api-playground-section" class="content-section">
            <div class="section-header-with-code">
                <h2>🧪 Rithmic API Playground</h2>
                <div class="code-button-group">
                    <button class="show-code-btn" onclick="playgroundManager.showGeneratedCode()">Show Generated Code</button>
                    <button class="show-code-btn" onclick="playgroundManager.exportSession()">Export Session</button>
                </div>
            </div>
            
            <!-- Playground Layout -->
            <div class="playground-layout">
                
                <!-- Left Panel - Template Browser -->
                <div class="playground-panel template-browser-panel">
                    <h3>📚 Template Browser</h3>
                    
                    <div class="template-filters">
                        <div class="filter-group">
                            <label for="category-filter">Category:</label>
                            <select id="category-filter">
                                <option value="">All Categories</option>
                                <option value="Authentication">Authentication</option>
                                <option value="Market Data">Market Data</option>
                                <option value="Order Management">Order Management</option>
                                <option value="Historical Data">Historical Data</option>
                                <option value="Account Management">Account Management</option>
                                <option value="System Information">System Information</option>
                                <option value="Symbol Discovery">Symbol Discovery</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="plant-filter">Infrastructure Plant:</label>
                            <select id="plant-filter">
                                <option value="">All Plants</option>
                                <option value="System">System</option>
                                <option value="Ticker Plant">Ticker Plant</option>
                                <option value="History Plant">History Plant</option>
                                <option value="Order Plant">Order Plant</option>
                                <option value="PnL Plant">PnL Plant</option>
                                <option value="Repository Plant">Repository Plant</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <input type="text" id="template-search" placeholder="Search templates..." />
                            <button id="refresh-templates-btn" class="btn btn-sm">🔄 Refresh</button>
                        </div>
                    </div>
                    
                    <div class="templates-list" id="templates-list">
                        <div class="loading-message">Loading templates...</div>
                    </div>
                </div>
                
                <!-- Center Panel - Request Builder -->
                <div class="playground-panel request-builder-panel">
                    <h3>🛠️ Request Builder</h3>
                    
                    <div class="request-builder-header">
                        <div class="selected-template-info" id="selected-template-info">
                            <div class="no-template-selected">Select a template to get started</div>
                        </div>
                    </div>
                    
                    <div class="request-builder-content" id="request-builder-content">
                        <div class="template-documentation" id="template-documentation">
                            <div class="welcome-message">
                                <h4>🚀 Welcome to the Rithmic API Playground!</h4>
                                <p>Select a template from the browser on the left to start building API requests.</p>
                                <ul>
                                    <li>Browse 100+ Rithmic Protocol Buffer templates</li>
                                    <li>Build and test requests with real-time validation</li>
                                    <li>Connect to live Rithmic infrastructure plants</li>
                                    <li>Generate production-ready code in Python & JavaScript</li>
                                    <li>Track request/response history and debug sessions</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="request-form-container" id="request-form-container" style="display: none;">
                            <div class="form-tabs">
                                <div class="tab active" data-tab="basic-fields">Basic</div>
                                <div class="tab" data-tab="advanced-fields">Advanced</div>
                                <div class="tab" data-tab="examples">Examples</div>
                            </div>
                            
                            <div class="form-content">
                                <div id="basic-fields" class="form-panel active">
                                    <div class="form-fields" id="basic-form-fields">
                                        <!-- Dynamic form fields will be generated here -->
                                    </div>
                                </div>
                                
                                <div id="advanced-fields" class="form-panel">
                                    <div class="form-fields" id="advanced-form-fields">
                                        <!-- Advanced form fields will be generated here -->
                                    </div>
                                </div>
                                
                                <div id="examples" class="form-panel">
                                    <div class="examples-list" id="examples-list">
                                        <!-- Template examples will be loaded here -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="request-actions">
                                <button id="validate-request-btn" class="btn btn-secondary">✓ Validate</button>
                                <button id="test-request-btn" class="btn btn-primary">🚀 Test Request</button>
                                <button id="generate-code-btn" class="btn btn-secondary">💻 Generate Code</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Panel - Session & Results -->
                <div class="playground-panel results-panel">
                    <h3>📊 Session & Results</h3>
                    
                    <div class="session-management">
                        <div class="session-selector">
                            <label for="active-session">Active Session:</label>
                            <select id="active-session">
                                <option value="">No Session</option>
                            </select>
                            <button id="new-session-btn" class="btn btn-sm btn-success">+ New</button>
                            <button id="connect-session-btn" class="btn btn-sm btn-primary">🔗 Connect</button>
                        </div>
                        
                        <div class="session-status" id="session-status">
                            <div class="status-indicator disconnected">🔴 Disconnected</div>
                            <div class="session-details">No active session</div>
                        </div>
                    </div>
                    
                    <div class="results-tabs">
                        <div class="tab active" data-tab="response-data">Response</div>
                        <div class="tab" data-tab="request-history">History</div>
                        <div class="tab" data-tab="streaming-data">Streaming</div>
                        <div class="tab" data-tab="validation-results">Validation</div>
                    </div>
                    
                    <div class="results-content">
                        <div id="response-data" class="results-panel-content active">
                            <div class="response-container">
                                <div class="no-response">No response data yet. Send a request to see results.</div>
                            </div>
                        </div>
                        
                        <div id="request-history" class="results-panel-content">
                            <div class="history-controls">
                                <button id="clear-history-btn" class="btn btn-sm btn-secondary">🗑️ Clear</button>
                                <button id="export-history-btn" class="btn btn-sm btn-secondary">📤 Export</button>
                            </div>
                            <div class="history-list" id="history-list">
                                <div class="no-history">No request history yet.</div>
                            </div>
                        </div>
                        
                        <div id="streaming-data" class="results-panel-content">
                            <div class="streaming-controls">
                                <button id="start-streaming-btn" class="btn btn-sm btn-success">▶️ Start</button>
                                <button id="stop-streaming-btn" class="btn btn-sm btn-danger">⏹️ Stop</button>
                                <button id="clear-streaming-btn" class="btn btn-sm btn-secondary">🗑️ Clear</button>
                            </div>
                            <div class="streaming-list" id="streaming-list">
                                <div class="no-streaming">No streaming data yet.</div>
                            </div>
                        </div>
                        
                        <div id="validation-results" class="results-panel-content">
                            <div class="validation-container" id="validation-container">
                                <div class="no-validation">No validation results yet.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Logs Section -->
        <section id="logs-section" class="content-section">
            <h2>System Logs</h2>
            
            <div class="logs-controls">
                <select id="log-level-filter">
                    <option value="">All Levels</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                </select>
                <select id="log-component-filter">
                    <option value="">All Components</option>
                </select>
                <button id="clear-logs-btn" class="btn btn-secondary">Clear</button>
                <label class="checkbox-label">
                    <input type="checkbox" id="auto-scroll-logs" checked />
                    Auto-scroll
                </label>
            </div>

            <div id="logs-container" class="logs-container"></div>
        </section>

    </main>

    <!-- Status Bar -->
    <footer class="status-bar">
        <div class="status-item">
            <span id="websocket-status">WebSocket: Disconnected</span>
        </div>
        <div class="status-item">
            <span id="last-update">Last Update: Never</span>
        </div>
        <div class="status-item">
            <span id="data-count">Data Points: 0</span>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading...</div>
    </div>

    <!-- JavaScript Modules -->
    <script src="/static/js/websockets.js"></script>
    <script src="/static/js/dashboard.js"></script>
    <script src="/static/js/database.js"></script>
    <script src="/static/js/processes.js"></script>
    <script src="/static/js/charts.js"></script>
    <script src="/static/js/code-generation.js"></script>
    <script src="/static/js/contracts.js"></script>
    <script src="/static/js/symbol-search.js"></script>
    <script src="/static/js/playground.js"></script>
</body>
</html>