/**
 * Contracts Management JavaScript Module
 * =====================================
 * 
 * Manages futures contract hierarchy and selection for the web dashboard.
 * Provides TreeView-style contract selection with smart filtering.
 */

class ContractsManager {
    constructor() {
        this.contractHierarchy = {};
        this.selectedContracts = new Set();
        this.init();
    }
    
    init() {
        this.loadContractHierarchy();
        this.setupEventListeners();
    }
    
    /**
     * Load contract hierarchy from the original GUI structure
     */
    loadContractHierarchy() {
        // Contract hierarchy based on the original GUI implementation
        this.contractHierarchy = {
            "E-mini S&P 500": {
                symbol: "ES",
                exchange: "CME",
                category: "Index Futures",
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25",
                description: "E-mini S&P 500 futures contract"
            },
            "Micro E-mini S&P 500": {
                symbol: "MES",
                exchange: "CME", 
                category: "Micro Index Futures",
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25",
                description: "Micro E-mini S&P 500 futures contract"
            },
            "E-mini NASDAQ-100": {
                symbol: "NQ",
                exchange: "CME",
                category: "Index Futures",
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25", 
                description: "E-mini NASDAQ-100 futures contract"
            },
            "Micro E-mini NASDAQ-100": {
                symbol: "MNQ",
                exchange: "CME",
                category: "Micro Index Futures", 
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25",
                description: "Micro E-mini NASDAQ-100 futures contract"
            },
            "E-mini Dow Jones": {
                symbol: "YM",
                exchange: "CBOT",
                category: "Index Futures",
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25",
                description: "E-mini Dow Jones Industrial Average futures"
            },
            "Micro E-mini Dow": {
                symbol: "MYM",
                exchange: "CBOT",
                category: "Micro Index Futures",
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25",
                description: "Micro E-mini Dow Jones futures contract"
            },
            "E-mini Russell 2000": {
                symbol: "RTY",
                exchange: "CME",
                category: "Index Futures",
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25",
                description: "E-mini Russell 2000 futures contract"
            },
            "Micro E-mini Russell 2000": {
                symbol: "M2K",
                exchange: "CME",
                category: "Micro Index Futures",
                months: ["H25", "M25", "U25", "Z25", "H26", "M26"],
                front_month: "H25",
                description: "Micro E-mini Russell 2000 futures contract"
            },
            "Crude Oil": {
                symbol: "CL",
                exchange: "NYMEX",
                category: "Energy Futures",
                months: ["F25", "G25", "H25", "J25", "K25", "M25"],
                front_month: "F25",
                description: "Light Sweet Crude Oil futures"
            },
            "Natural Gas": {
                symbol: "NG",
                exchange: "NYMEX",
                category: "Energy Futures",
                months: ["F25", "G25", "H25", "J25", "K25", "M25"],
                front_month: "F25",
                description: "Henry Hub Natural Gas futures"
            },
            "Gold": {
                symbol: "GC",
                exchange: "COMEX",
                category: "Metals Futures",
                months: ["G25", "J25", "M25", "Q25", "V25", "Z25"],
                front_month: "G25",
                description: "Gold futures contract"
            },
            "Silver": {
                symbol: "SI",
                exchange: "COMEX",
                category: "Metals Futures",
                months: ["H25", "K25", "N25", "U25", "Z25", "H26"],
                front_month: "H25",
                description: "Silver futures contract"
            },
            "Corn": {
                symbol: "C",
                exchange: "CBOT",
                category: "Agricultural Futures",
                months: ["H25", "K25", "N25", "U25", "Z25", "H26"],
                front_month: "H25",
                description: "Corn futures contract"
            },
            "Soybeans": {
                symbol: "S",
                exchange: "CBOT",
                category: "Agricultural Futures",
                months: ["H25", "K25", "N25", "X25", "F26", "H26"],
                front_month: "H25",
                description: "Soybeans futures contract"
            },
            "Wheat": {
                symbol: "W",
                exchange: "CBOT",
                category: "Agricultural Futures",
                months: ["H25", "K25", "N25", "U25", "Z25", "H26"],
                front_month: "H25",
                description: "Wheat futures contract"
            }
        };
    }
    
    setupEventListeners() {
        // Contract selection modal close
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('contracts-modal-overlay')) {
                this.hideContractsModal();
            }
        });
        
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideContractsModal();
            }
        });
    }
    
    /**
     * Show contract selection modal
     */
    showContractsModal(callback) {
        this.selectionCallback = callback;
        
        const modal = document.createElement('div');
        modal.className = 'contracts-modal-overlay';
        modal.innerHTML = this.createContractsModalHTML();
        
        document.body.appendChild(modal);
        
        // Setup modal event listeners
        this.setupModalEventListeners(modal);
        
        // Load current selection
        this.updateContractSelection();
    }
    
    /**
     * Create HTML for contracts selection modal
     */
    createContractsModalHTML() {
        return `
            <div class="contracts-modal">
                <div class="modal-header">
                    <h3>🏛️ Select Futures Contracts</h3>
                    <button type="button" class="close-btn" onclick="contractsManager.hideContractsModal()">×</button>
                </div>
                
                <div class="modal-body">
                    <div class="contracts-toolbar">
                        <div class="selection-buttons">
                            <button class="btn btn-sm" onclick="contractsManager.selectFrontMonth()">Front Month</button>
                            <button class="btn btn-sm" onclick="contractsManager.selectFrontTwo()">Front 2 Months</button>
                            <button class="btn btn-sm" onclick="contractsManager.selectAllActive()">All Active</button>
                            <button class="btn btn-sm btn-secondary" onclick="contractsManager.clearSelection()">Clear All</button>
                        </div>
                        
                        <div class="search-box">
                            <input type="text" id="contracts-search" placeholder="Search contracts..." 
                                   onkeyup="contractsManager.filterContracts(this.value)">
                        </div>
                    </div>
                    
                    <div class="contracts-hierarchy">
                        ${this.createHierarchyHTML()}
                    </div>
                </div>
                
                <div class="modal-footer">
                    <div class="selection-summary">
                        <span id="selection-count">0 contracts selected</span>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="contractsManager.hideContractsModal()">Cancel</button>
                        <button class="btn btn-primary" onclick="contractsManager.confirmSelection()">Apply Selection</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Create hierarchical contract tree HTML
     */
    createHierarchyHTML() {
        const categories = {};
        
        // Group contracts by category
        Object.entries(this.contractHierarchy).forEach(([name, contract]) => {
            if (!categories[contract.category]) {
                categories[contract.category] = [];
            }
            categories[contract.category].push({ name, ...contract });
        });
        
        let html = '';
        
        Object.entries(categories).forEach(([category, contracts]) => {
            html += `
                <div class="contract-category">
                    <div class="category-header" onclick="contractsManager.toggleCategory('${category}')">
                        <span class="category-icon">📊</span>
                        <span class="category-name">${category}</span>
                        <span class="category-count">(${contracts.length})</span>
                        <span class="category-toggle">▼</span>
                    </div>
                    
                    <div class="category-contracts" id="category-${category.replace(/\s+/g, '-')}">
                        ${contracts.map(contract => this.createContractHTML(contract)).join('')}
                    </div>
                </div>
            `;
        });
        
        return html;
    }
    
    /**
     * Create HTML for individual contract
     */
    createContractHTML(contract) {
        return `
            <div class="contract-item" data-symbol="${contract.symbol}">
                <div class="contract-header" onclick="contractsManager.toggleContract('${contract.symbol}')">
                    <input type="checkbox" class="contract-checkbox" 
                           onchange="contractsManager.handleContractToggle('${contract.symbol}', this.checked)">
                    <span class="contract-symbol">${contract.symbol}</span>
                    <span class="contract-name">${contract.name}</span>
                    <span class="contract-exchange">[${contract.exchange}]</span>
                    <span class="contract-toggle">▼</span>
                </div>
                
                <div class="contract-months" id="months-${contract.symbol}">
                    <div class="months-header">Available Months:</div>
                    <div class="months-grid">
                        ${contract.months.map(month => `
                            <div class="month-item ${month === contract.front_month ? 'front-month' : ''}"
                                 onclick="contractsManager.selectContractMonth('${contract.symbol}', '${month}')">
                                <input type="checkbox" class="month-checkbox"
                                       data-contract="${contract.symbol}" 
                                       data-month="${month}"
                                       onchange="contractsManager.handleMonthToggle('${contract.symbol}', '${month}', this.checked)">
                                <span class="month-code">${month}</span>
                                ${month === contract.front_month ? '<span class="front-badge">FRONT</span>' : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Setup modal event listeners
     */
    setupModalEventListeners(modal) {
        // Category toggle
        modal.querySelectorAll('.category-header').forEach(header => {
            header.addEventListener('click', (e) => {
                if (e.target.classList.contains('category-header')) {
                    const category = e.target.querySelector('.category-name').textContent;
                    this.toggleCategory(category);
                }
            });
        });
    }
    
    /**
     * Toggle category visibility
     */
    toggleCategory(category) {
        const categoryId = `category-${category.replace(/\s+/g, '-')}`;
        const categoryElement = document.getElementById(categoryId);
        const toggleIcon = document.querySelector(`[onclick*="${category}"] .category-toggle`);
        
        if (categoryElement.style.display === 'none') {
            categoryElement.style.display = 'block';
            toggleIcon.textContent = '▼';
        } else {
            categoryElement.style.display = 'none';
            toggleIcon.textContent = '▶';
        }
    }
    
    /**
     * Toggle contract expansion
     */
    toggleContract(symbol) {
        const monthsElement = document.getElementById(`months-${symbol}`);
        const toggleIcon = document.querySelector(`[onclick*="${symbol}"] .contract-toggle`);
        
        if (monthsElement.style.display === 'none') {
            monthsElement.style.display = 'block';
            toggleIcon.textContent = '▼';
        } else {
            monthsElement.style.display = 'none';
            toggleIcon.textContent = '▶';
        }
    }
    
    /**
     * Handle contract checkbox toggle
     */
    handleContractToggle(symbol, checked) {
        const contract = Object.values(this.contractHierarchy).find(c => c.symbol === symbol);
        if (!contract) return;
        
        if (checked) {
            // Add front month by default
            this.selectedContracts.add(`${symbol}_${contract.front_month}`);
        } else {
            // Remove all months for this contract
            contract.months.forEach(month => {
                this.selectedContracts.delete(`${symbol}_${month}`);
            });
        }
        
        this.updateContractSelection();
    }
    
    /**
     * Handle month checkbox toggle
     */
    handleMonthToggle(symbol, month, checked) {
        const contractMonth = `${symbol}_${month}`;
        
        if (checked) {
            this.selectedContracts.add(contractMonth);
        } else {
            this.selectedContracts.delete(contractMonth);
        }
        
        this.updateContractSelection();
    }
    
    /**
     * Select front month for all contracts
     */
    selectFrontMonth() {
        this.selectedContracts.clear();
        
        Object.values(this.contractHierarchy).forEach(contract => {
            this.selectedContracts.add(`${contract.symbol}_${contract.front_month}`);
        });
        
        this.updateContractSelection();
    }
    
    /**
     * Select front 2 months for all contracts
     */
    selectFrontTwo() {
        this.selectedContracts.clear();
        
        Object.values(this.contractHierarchy).forEach(contract => {
            // Add first two months
            contract.months.slice(0, 2).forEach(month => {
                this.selectedContracts.add(`${contract.symbol}_${month}`);
            });
        });
        
        this.updateContractSelection();
    }
    
    /**
     * Select all active contracts
     */
    selectAllActive() {
        this.selectedContracts.clear();
        
        Object.values(this.contractHierarchy).forEach(contract => {
            contract.months.forEach(month => {
                this.selectedContracts.add(`${contract.symbol}_${month}`);
            });
        });
        
        this.updateContractSelection();
    }
    
    /**
     * Clear all selections
     */
    clearSelection() {
        this.selectedContracts.clear();
        this.updateContractSelection();
    }
    
    /**
     * Filter contracts by search term
     */
    filterContracts(searchTerm) {
        const term = searchTerm.toLowerCase();
        
        document.querySelectorAll('.contract-item').forEach(item => {
            const symbol = item.dataset.symbol;
            const contract = Object.values(this.contractHierarchy).find(c => c.symbol === symbol);
            
            const matchesSearch = !term || 
                symbol.toLowerCase().includes(term) ||
                contract.name.toLowerCase().includes(term) ||
                contract.description.toLowerCase().includes(term) ||
                contract.exchange.toLowerCase().includes(term);
            
            item.style.display = matchesSearch ? 'block' : 'none';
        });
    }
    
    /**
     * Update contract selection UI
     */
    updateContractSelection() {
        // Update checkboxes
        document.querySelectorAll('.contract-checkbox').forEach(checkbox => {
            const symbol = checkbox.closest('.contract-item').dataset.symbol;
            const contract = Object.values(this.contractHierarchy).find(c => c.symbol === symbol);
            
            // Check if any month is selected for this contract
            const hasSelectedMonths = contract.months.some(month => 
                this.selectedContracts.has(`${symbol}_${month}`)
            );
            
            checkbox.checked = hasSelectedMonths;
        });
        
        // Update month checkboxes
        document.querySelectorAll('.month-checkbox').forEach(checkbox => {
            const symbol = checkbox.dataset.contract;
            const month = checkbox.dataset.month;
            checkbox.checked = this.selectedContracts.has(`${symbol}_${month}`);
        });
        
        // Update selection count
        const countElement = document.getElementById('selection-count');
        if (countElement) {
            countElement.textContent = `${this.selectedContracts.size} contracts selected`;
        }
    }
    
    /**
     * Confirm selection and callback
     */
    confirmSelection() {
        const contracts = Array.from(this.selectedContracts);
        
        if (this.selectionCallback) {
            this.selectionCallback(contracts);
        }
        
        this.hideContractsModal();
    }
    
    /**
     * Hide contracts modal
     */
    hideContractsModal() {
        const modal = document.querySelector('.contracts-modal-overlay');
        if (modal) {
            modal.remove();
        }
        this.selectionCallback = null;
    }
    
    /**
     * Get selected contracts in various formats
     */
    getSelectedContracts() {
        return {
            full: Array.from(this.selectedContracts),
            symbols: [...new Set(Array.from(this.selectedContracts).map(c => c.split('_')[0]))],
            grouped: this.groupContractsBySymbol()
        };
    }
    
    /**
     * Group contracts by symbol
     */
    groupContractsBySymbol() {
        const grouped = {};
        
        this.selectedContracts.forEach(contract => {
            const [symbol, month] = contract.split('_');
            if (!grouped[symbol]) {
                grouped[symbol] = [];
            }
            grouped[symbol].push(month);
        });
        
        return grouped;
    }
}

// Initialize global contracts manager instance
window.contractsManager = new ContractsManager();