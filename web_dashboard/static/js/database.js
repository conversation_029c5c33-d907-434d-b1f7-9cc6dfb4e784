/**
 * Database Management JavaScript Module
 * Handles database operations and table management
 */

class DatabaseManager {
    constructor(dashboard) {
        this.dashboard = dashboard;
        this.currentTable = null;
        this.currentPage = 0;
        this.pageSize = 100;
        this.totalPages = 0;
        this.filters = {};
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadTables();
    }
    
    setupEventListeners() {
        // Table search
        const searchInput = document.getElementById('table-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterTables(e.target.value);
            });
        }
        
        // Page size change
        const pageSizeSelect = document.getElementById('page-size-select');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 0;
                if (this.currentTable) {
                    this.loadTableData(this.currentTable);
                }
            });
        }
        
        // Filter application
        const applyFilterBtn = document.getElementById('apply-filter-btn');
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', () => {
                this.applyFilter();
            });
        }
        
        // Enter key for filter input
        const filterInput = document.getElementById('filter-input');
        if (filterInput) {
            filterInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilter();
                }
            });
        }
        
        // Export data
        const exportBtn = document.getElementById('export-data-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportCurrentData();
            });
        }
    }
    
    async loadTables() {
        try {
            this.dashboard.showLoading('Loading database tables...');
            
            const response = await fetch('/api/database/tables');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const tables = await response.json();
            this.displayTables(tables);
            
        } catch (error) {
            console.error('Error loading tables:', error);
            this.dashboard.showError('Failed to load database tables');
        } finally {
            this.dashboard.hideLoading();
        }
    }
    
    displayTables(tables) {
        const tablesList = document.getElementById('tables-list');
        if (!tablesList) return;
        
        tablesList.innerHTML = '';
        
        tables.forEach(table => {
            const tableItem = document.createElement('div');
            tableItem.className = 'table-item';
            tableItem.dataset.tableName = table.name;
            
            tableItem.innerHTML = `
                <div class="table-name">${table.name}</div>
                <div class="table-rows">${table.row_count.toLocaleString()} rows</div>
                <div class="table-size">${this.formatBytes(table.size_bytes || 0)}</div>
            `;
            
            tableItem.addEventListener('click', () => {
                this.selectTable(table.name);
            });
            
            tablesList.appendChild(tableItem);
        });
    }
    
    filterTables(searchTerm) {
        const tableItems = document.querySelectorAll('.table-item');
        const term = searchTerm.toLowerCase();
        
        tableItems.forEach(item => {
            const tableName = item.querySelector('.table-name').textContent.toLowerCase();
            const matches = tableName.includes(term);
            item.style.display = matches ? 'block' : 'none';
        });
    }
    
    async selectTable(tableName) {
        try {
            // Update UI selection
            document.querySelectorAll('.table-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            const selectedItem = document.querySelector(`[data-table-name="${tableName}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
            
            this.currentTable = tableName;
            this.currentPage = 0;
            
            // Load table schema first
            await this.loadTableSchema(tableName);
            
            // Load table data
            await this.loadTableData(tableName);
            
        } catch (error) {
            console.error('Error selecting table:', error);
            this.dashboard.showError('Failed to select table');
        }
    }
    
    async loadTableSchema(tableName) {
        try {
            const response = await fetch(`/api/database/tables/${tableName}/schema`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const schema = await response.json();
            this.displayTableSchema(schema);
            
        } catch (error) {
            console.error('Error loading table schema:', error);
        }
    }
    
    displayTableSchema(schema) {
        // This could be expanded to show table schema information
        console.log('Table schema:', schema);
    }
    
    async loadTableData(tableName) {
        try {
            this.dashboard.showLoading('Loading table data...');
            
            const filterInput = document.getElementById('filter-input');
            const whereClause = filterInput ? filterInput.value.trim() : '';
            
            const requestData = {
                table: tableName,
                page: this.currentPage,
                page_size: this.pageSize,
                where_clause: whereClause || null
            };
            
            const response = await fetch(`/api/database/tables/${tableName}/data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            this.displayTableData(result.data, result.pagination);
            
        } catch (error) {
            console.error('Error loading table data:', error);
            this.dashboard.showError('Failed to load table data');
        } finally {
            this.dashboard.hideLoading();
        }
    }
    
    displayTableData(data, pagination) {
        const tableDataDiv = document.getElementById('table-data');
        if (!tableDataDiv) return;
        
        this.totalPages = pagination.total_pages;
        
        if (!data || data.length === 0) {
            tableDataDiv.innerHTML = '<div class="no-data">No data available</div>';
            this.updatePagination(pagination);
            return;
        }
        
        // Create table
        const table = document.createElement('table');
        table.className = 'data-table';
        
        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        Object.keys(data[0]).forEach(key => {
            const th = document.createElement('th');
            th.textContent = key;
            th.style.cursor = 'pointer';
            th.addEventListener('click', () => {
                this.sortTableData(key);
            });
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // Create body
        const tbody = document.createElement('tbody');
        
        data.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.className = index % 2 === 0 ? 'even' : 'odd';
            
            Object.values(row).forEach(value => {
                const td = document.createElement('td');
                td.textContent = this.formatCellValue(value);
                td.title = value; // Show full value on hover
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        tableDataDiv.innerHTML = '';
        tableDataDiv.appendChild(table);
        
        // Update pagination
        this.updatePagination(pagination);
    }
    
    updatePagination(pagination) {
        const paginationDiv = document.getElementById('pagination');
        if (!paginationDiv) return;
        
        const prevDisabled = pagination.page === 0;
        const nextDisabled = pagination.page >= pagination.total_pages - 1;
        
        paginationDiv.innerHTML = `
            <button class="btn btn-sm" ${prevDisabled ? 'disabled' : ''} onclick="databaseManager.previousPage()">
                Previous
            </button>
            <span class="current-page">
                Page ${pagination.page + 1} of ${pagination.total_pages} 
                (${pagination.total_rows.toLocaleString()} total rows)
            </span>
            <button class="btn btn-sm" ${nextDisabled ? 'disabled' : ''} onclick="databaseManager.nextPage()">
                Next
            </button>
        `;
    }
    
    async previousPage() {
        if (this.currentPage > 0) {
            this.currentPage--;
            await this.loadTableData(this.currentTable);
        }
    }
    
    async nextPage() {
        if (this.currentPage < this.totalPages - 1) {
            this.currentPage++;
            await this.loadTableData(this.currentTable);
        }
    }
    
    async applyFilter() {
        this.currentPage = 0;
        if (this.currentTable) {
            await this.loadTableData(this.currentTable);
        }
    }
    
    async exportCurrentData() {
        if (!this.currentTable) {
            this.dashboard.showError('Please select a table first');
            return;
        }
        
        try {
            this.dashboard.showLoading('Exporting data...');
            
            const filterInput = document.getElementById('filter-input');
            const whereClause = filterInput ? filterInput.value.trim() : '';
            
            const params = new URLSearchParams({
                table: this.currentTable,
                format: 'csv'
            });
            
            if (whereClause) {
                params.append('where_clause', whereClause);
            }
            
            const response = await fetch(`/api/database/export?${params}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentTable}_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            this.dashboard.showSuccess('Data exported successfully');
            
        } catch (error) {
            console.error('Error exporting data:', error);
            this.dashboard.showError('Failed to export data');
        } finally {
            this.dashboard.hideLoading();
        }
    }
    
    sortTableData(column) {
        // This would implement client-side sorting
        // For now, we'll just show a message
        console.log(`Sorting by column: ${column}`);
        this.dashboard.showSuccess(`Sorting by ${column} (feature coming soon)`);
    }
    
    formatCellValue(value) {
        if (value === null || value === undefined) {
            return '';
        }
        
        if (typeof value === 'string' && value.length > 50) {
            return value.substring(0, 50) + '...';
        }
        
        if (typeof value === 'number') {
            return value.toLocaleString();
        }
        
        return String(value);
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    async testConnection() {
        try {
            const response = await fetch('/api/database/status');
            const status = await response.json();
            
            if (status.connected) {
                this.dashboard.showSuccess(`Database connected: ${status.connection_info}`);
            } else {
                this.dashboard.showError('Database connection failed');
            }
            
        } catch (error) {
            console.error('Error testing database connection:', error);
            this.dashboard.showError('Failed to test database connection');
        }
    }
    
    refresh() {
        this.loadTables();
        if (this.currentTable) {
            this.loadTableData(this.currentTable);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.dashboard) {
        window.databaseManager = new DatabaseManager(window.dashboard);
    }
});