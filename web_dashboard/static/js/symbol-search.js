/**
 * Symbol Search JavaScript Module
 * ===============================
 * 
 * Handles symbol search and discovery functionality for the web dashboard.
 * Provides comprehensive symbol search with filtering, pattern matching, and results management.
 */

class SymbolSearchManager {
    constructor() {
        this.currentResults = [];
        this.selectedSymbol = null;
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadPatternExamples();
    }
    
    /**
     * Setup event listeners for symbol search interface
     */
    setupEventListeners() {
        // Discover symbols button
        const discoverBtn = document.getElementById('discover-symbols-btn');
        if (discoverBtn) {
            discoverBtn.addEventListener('click', () => this.discoverSymbols());
        }
        
        // Clear search button
        const clearBtn = document.getElementById('clear-search-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearSearch());
        }
        
        // Export CSV button
        const exportBtn = document.getElementById('export-csv-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportToCsv());
        }
        
        // Save to database button
        const saveBtn = document.getElementById('save-database-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveToDatabase());
        }
        
        // Show details button
        const detailsBtn = document.getElementById('show-details-btn');
        if (detailsBtn) {
            detailsBtn.addEventListener('click', () => this.showSelectedSymbolDetails());
        }
        
        // Symbol pattern input - trigger search on Enter
        const patternInput = document.getElementById('symbol-pattern');
        if (patternInput) {
            patternInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.discoverSymbols();
                }
            });
            
            // Add pattern validation
            patternInput.addEventListener('input', (e) => {
                this.validatePattern(e.target.value);
            });
        }
        
        // Table row selection
        const symbolsTable = document.getElementById('symbols-table');
        if (symbolsTable) {
            symbolsTable.addEventListener('click', (e) => {
                const row = e.target.closest('tr');
                if (row && !row.classList.contains('no-results')) {
                    this.selectSymbolRow(row);
                }
            });
            
            // Double-click for details
            symbolsTable.addEventListener('dblclick', (e) => {
                const row = e.target.closest('tr');
                if (row && !row.classList.contains('no-results')) {
                    const symbol = row.cells[0].textContent;
                    this.showSymbolDetails(symbol);
                }
            });
        }
    }
    
    /**
     * Load pattern examples from API
     */
    async loadPatternExamples() {
        try {
            const response = await fetch('/api/symbol-search/pattern-examples');
            const data = await response.json();
            
            // Examples are already included in HTML, but we could update them dynamically
            console.log('Pattern examples loaded:', data);
            
        } catch (error) {
            console.warn('Could not load pattern examples:', error);
        }
    }
    
    /**
     * Validate symbol pattern input
     */
    validatePattern(pattern) {
        const patternInput = document.getElementById('symbol-pattern');
        if (!patternInput) return;
        
        // Remove invalid characters and provide feedback
        const validPattern = pattern.replace(/[^A-Za-z0-9*]/g, '');
        if (validPattern !== pattern) {
            patternInput.value = validPattern;
        }
        
        // Visual feedback for pattern validity
        if (pattern.length > 0) {
            patternInput.classList.remove('invalid');
            patternInput.classList.add('valid');
        } else {
            patternInput.classList.remove('valid', 'invalid');
        }
    }
    
    /**
     * Discover symbols based on current filters
     */
    async discoverSymbols() {
        try {
            this.showLoading('Discovering symbols...');
            
            // Get filter values
            const exchangeFilter = document.getElementById('exchange-filter').value;
            const symbolPattern = document.getElementById('symbol-pattern').value.trim();
            const productCode = document.getElementById('product-code').value;
            
            // Prepare request
            const request = {
                exchange_filter: exchangeFilter,
                symbol_pattern: symbolPattern,
                product_code: productCode
            };
            
            // Call discovery API
            const response = await fetch('/api/symbol-search/discover', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(request)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Update results
            this.currentResults = data.symbols;
            this.displayResults(data.symbols);
            this.updateResultsCount(data.total_count);
            
            this.hideLoading();
            this.showSuccess(`Discovered ${data.total_count} symbols`);
            
        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to discover symbols: ${error.message}`);
            console.error('Symbol discovery error:', error);
        }
    }
    
    /**
     * Display search results in the table
     */
    displayResults(symbols) {
        const tableBody = document.getElementById('symbols-table-body');
        if (!tableBody) return;
        
        // Clear existing results
        tableBody.innerHTML = '';
        
        if (symbols.length === 0) {
            tableBody.innerHTML = `
                <tr class="no-results">
                    <td colspan="7">No symbols found matching the search criteria.</td>
                </tr>
            `;
            return;
        }
        
        // Add symbol rows
        symbols.forEach((symbol, index) => {
            const row = document.createElement('tr');
            row.className = 'symbol-row';
            row.dataset.symbol = symbol.symbol;
            row.innerHTML = `
                <td class="symbol-cell">${symbol.symbol}</td>
                <td>${symbol.exchange}</td>
                <td>${symbol.product_code}</td>
                <td class="description-cell">${symbol.description}</td>
                <td class="numeric-cell">${this.formatTickSize(symbol.tick_size)}</td>
                <td class="numeric-cell">${symbol.contract_size.toLocaleString()}</td>
                <td class="status-cell">
                    <span class="status-badge status-${symbol.status.toLowerCase()}">${symbol.status}</span>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }
    
    /**
     * Format tick size for display
     */
    formatTickSize(tickSize) {
        if (tickSize >= 1) {
            return tickSize.toString();
        } else if (tickSize >= 0.01) {
            return tickSize.toFixed(6).replace(/\.?0+$/, '');
        } else {
            return tickSize.toFixed(8).replace(/\.?0+$/, '');
        }
    }
    
    /**
     * Update results count display
     */
    updateResultsCount(count) {
        const countElement = document.getElementById('results-count');
        if (countElement) {
            countElement.textContent = `Results: ${count}`;
        }
    }
    
    /**
     * Select a symbol row
     */
    selectSymbolRow(row) {
        // Remove existing selection
        document.querySelectorAll('.symbol-row.selected').forEach(r => {
            r.classList.remove('selected');
        });
        
        // Select new row
        row.classList.add('selected');
        this.selectedSymbol = row.dataset.symbol;
        
        // Enable detail buttons
        this.updateActionButtons(true);
    }
    
    /**
     * Update action button states
     */
    updateActionButtons(hasSelection) {
        const detailsBtn = document.getElementById('show-details-btn');
        const exportBtn = document.getElementById('export-csv-btn');
        const saveBtn = document.getElementById('save-database-btn');
        
        if (detailsBtn) {
            detailsBtn.disabled = !hasSelection;
        }
        
        const hasResults = this.currentResults.length > 0;
        if (exportBtn) exportBtn.disabled = !hasResults;
        if (saveBtn) saveBtn.disabled = !hasResults;
    }
    
    /**
     * Clear search results and filters
     */
    clearSearch() {
        // Clear filters
        document.getElementById('exchange-filter').value = 'All';
        document.getElementById('symbol-pattern').value = '';
        document.getElementById('product-code').value = 'All';
        
        // Clear results
        this.currentResults = [];
        this.selectedSymbol = null;
        this.displayResults([]);
        this.updateResultsCount(0);
        this.updateActionButtons(false);
        
        // Remove pattern validation styling
        const patternInput = document.getElementById('symbol-pattern');
        if (patternInput) {
            patternInput.classList.remove('valid', 'invalid');
        }
        
        this.showSuccess('Search cleared');
    }
    
    /**
     * Export results to CSV
     */
    async exportToCsv() {
        if (this.currentResults.length === 0) {
            this.showError('No results to export');
            return;
        }
        
        try {
            this.showLoading('Exporting to CSV...');
            
            const response = await fetch('/api/symbol-search/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    symbols: this.currentResults,
                    filename: `symbol_search_results_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.csv`
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // Download the file
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `symbol_search_results_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            this.hideLoading();
            this.showSuccess(`Exported ${this.currentResults.length} symbols to CSV`);
            
        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to export CSV: ${error.message}`);
            console.error('CSV export error:', error);
        }
    }
    
    /**
     * Save results to database
     */
    async saveToDatabase() {
        if (this.currentResults.length === 0) {
            this.showError('No results to save');
            return;
        }
        
        try {
            this.showLoading('Saving symbols to database...');
            
            const response = await fetch('/api/symbol-search/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    symbols: this.currentResults
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            this.hideLoading();
            this.showSuccess(`Saved ${result.saved_count} symbols to database (${result.skipped_count} skipped)`);
            
        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to save to database: ${error.message}`);
            console.error('Database save error:', error);
        }
    }
    
    /**
     * Show details for selected symbol
     */
    async showSelectedSymbolDetails() {
        if (!this.selectedSymbol) {
            this.showError('Please select a symbol first');
            return;
        }
        
        await this.showSymbolDetails(this.selectedSymbol);
    }
    
    /**
     * Show detailed information for a symbol
     */
    async showSymbolDetails(symbol) {
        try {
            this.showLoading('Loading symbol details...');
            
            const response = await fetch(`/api/symbol-search/details/${symbol}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const details = await response.json();
            
            this.hideLoading();
            this.showSymbolDetailsModal(symbol, details);
            
        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to load symbol details: ${error.message}`);
            console.error('Symbol details error:', error);
        }
    }
    
    /**
     * Show symbol details modal
     */
    showSymbolDetailsModal(symbol, details) {
        const modalHtml = `
            <div class="code-modal-overlay">
                <div class="code-modal symbol-details-modal">
                    <div class="modal-header">
                        <h3>📊 Symbol Details - ${symbol}</h3>
                        <button type="button" class="close-btn" onclick="symbolSearchManager.hideDetailsModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="symbol-details-grid">
                            <div class="detail-section">
                                <h4>Basic Information</h4>
                                <div class="detail-item">
                                    <label>Symbol:</label>
                                    <span>${details.basic_info.symbol}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Exchange:</label>
                                    <span>${details.basic_info.exchange}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Product Code:</label>
                                    <span>${details.basic_info.product_code}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Description:</label>
                                    <span>${details.basic_info.description}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Tick Size:</label>
                                    <span>${this.formatTickSize(details.basic_info.tick_size)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Contract Size:</label>
                                    <span>${details.basic_info.contract_size.toLocaleString()}</span>
                                </div>
                            </div>
                            
                            <div class="detail-section">
                                <h4>Trading Details</h4>
                                <div class="detail-item">
                                    <label>Market Hours:</label>
                                    <span>${details.trading_details.market_hours}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Settlement:</label>
                                    <span>${details.trading_details.settlement}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Margin Info:</label>
                                    <span>${details.trading_details.margin_info}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Expiration:</label>
                                    <span>${details.trading_details.expiration}</span>
                                </div>
                            </div>
                            
                            <div class="detail-section">
                                <h4>Risk Metrics</h4>
                                <div class="detail-item">
                                    <label>Point Value:</label>
                                    <span>${details.risk_metrics.point_value}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Daily Limit:</label>
                                    <span>${details.risk_metrics.daily_limit}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Volatility:</label>
                                    <span>${details.risk_metrics.volatility}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="symbolSearchManager.hideDetailsModal()">Close</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }
    
    /**
     * Hide symbol details modal
     */
    hideDetailsModal() {
        const modal = document.querySelector('.symbol-details-modal');
        if (modal) {
            modal.closest('.code-modal-overlay').remove();
        }
    }
    
    /**
     * Show symbol search code generation dialog
     */
    async showSearchCodeDialog() {
        // Get current filters for context-aware code generation
        const filters = {
            exchange_filter: document.getElementById('exchange-filter').value,
            symbol_pattern: document.getElementById('symbol-pattern').value.trim(),
            product_code: document.getElementById('product-code').value
        };
        
        // Use the existing code generation system
        if (window.codeGenerator) {
            window.codeGenerator.showSymbolSearchDialog(filters);
        } else {
            this.showError('Code generation not available');
        }
    }
    
    /**
     * Show loading state
     */
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        const text = overlay.querySelector('.loading-text');
        if (text) text.textContent = message;
        if (overlay) overlay.classList.add('show');
    }
    
    /**
     * Hide loading state
     */
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) overlay.classList.remove('show');
    }
    
    /**
     * Show success notification
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    /**
     * Show error notification
     */
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    /**
     * Show notification
     */
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            z-index: 2001;
            background-color: ${type === 'success' ? 'var(--success-color)' : 'var(--danger-color)'};
            box-shadow: 0 4px 15px var(--shadow-color);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
}

// Initialize global symbol search manager instance
window.symbolSearchManager = new SymbolSearchManager();