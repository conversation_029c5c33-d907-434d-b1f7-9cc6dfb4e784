/**
 * WebSocket Manager for real-time data streaming
 * Handles connections to multiple WebSocket endpoints
 */

class WebSocketManager {
    constructor() {
        this.connections = new Map();
        this.subscriptions = new Map();
        this.reconnectAttempts = new Map();
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.heartbeatInterval = 30000;
        this.heartbeatTimers = new Map();
        
        // Event handlers
        this.onConnectionChange = null;
        this.onMessage = null;
        this.onError = null;
    }
    
    connect() {
        this.connectToEndpoint('market_data', '/ws/market_data');
        this.connectToEndpoint('logs', '/ws/logs');
        this.connectToEndpoint('processes', '/ws/processes');
    }
    
    connectToEndpoint(name, path) {
        if (this.connections.has(name)) {
            this.connections.get(name).close();
        }
        
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}${path}`;
        
        try {
            const ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                console.log(`WebSocket connected: ${name}`);
                this.connections.set(name, ws);
                this.reconnectAttempts.set(name, 0);
                this.startHeartbeat(name);
                
                if (this.onConnectionChange) {
                    this.onConnectionChange(true);
                }
                
                // Send any pending subscriptions
                const pendingSubs = this.subscriptions.get(name) || [];
                pendingSubs.forEach(sub => {
                    this.sendMessage(name, sub);
                });
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(name, data);
                } catch (error) {
                    console.error(`Error parsing WebSocket message from ${name}:`, error);
                }
            };
            
            ws.onclose = (event) => {
                console.log(`WebSocket disconnected: ${name}`, event.code, event.reason);
                this.connections.delete(name);
                this.stopHeartbeat(name);
                
                if (this.onConnectionChange) {
                    this.onConnectionChange(false);
                }
                
                // Attempt to reconnect
                this.scheduleReconnect(name, path);
            };
            
            ws.onerror = (error) => {
                console.error(`WebSocket error on ${name}:`, error);
                if (this.onError) {
                    this.onError(name, error);
                }
            };
            
        } catch (error) {
            console.error(`Failed to create WebSocket connection to ${name}:`, error);
            this.scheduleReconnect(name, path);
        }
    }
    
    scheduleReconnect(name, path) {
        const attempts = this.reconnectAttempts.get(name) || 0;
        
        if (attempts < this.maxReconnectAttempts) {
            const delay = this.reconnectDelay * Math.pow(2, attempts);
            console.log(`Reconnecting to ${name} in ${delay}ms (attempt ${attempts + 1})`);
            
            setTimeout(() => {
                this.reconnectAttempts.set(name, attempts + 1);
                this.connectToEndpoint(name, path);
            }, delay);
        } else {
            console.error(`Max reconnection attempts reached for ${name}`);
        }
    }
    
    startHeartbeat(name) {
        const timer = setInterval(() => {
            if (this.isConnected(name)) {
                this.sendMessage(name, { type: 'ping' });
            }
        }, this.heartbeatInterval);
        
        this.heartbeatTimers.set(name, timer);
    }
    
    stopHeartbeat(name) {
        const timer = this.heartbeatTimers.get(name);
        if (timer) {
            clearInterval(timer);
            this.heartbeatTimers.delete(name);
        }
    }
    
    handleMessage(endpoint, data) {
        // Handle heartbeat responses
        if (data.type === 'pong') {
            return;
        }
        
        // Add endpoint information to message
        const enrichedData = {
            ...data,
            endpoint: endpoint
        };
        
        if (this.onMessage) {
            this.onMessage(enrichedData);
        }
    }
    
    sendMessage(endpoint, message) {
        const ws = this.connections.get(endpoint);
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
            return true;
        } else {
            // Queue message for when connection is established
            if (!this.subscriptions.has(endpoint)) {
                this.subscriptions.set(endpoint, []);
            }
            this.subscriptions.get(endpoint).push(message);
            return false;
        }
    }
    
    isConnected(endpoint) {
        const ws = this.connections.get(endpoint);
        return ws && ws.readyState === WebSocket.OPEN;
    }
    
    // Market Data Methods
    subscribeToMarketData(symbol, exchange = 'CME') {
        const message = {
            type: 'subscribe',
            symbol: symbol,
            exchange: exchange,
            data_types: ['trades', 'quotes', 'depth']
        };
        
        return this.sendMessage('market_data', message);
    }
    
    unsubscribeFromMarketData(symbol, exchange = 'CME') {
        const message = {
            type: 'unsubscribe',
            symbol: symbol,
            exchange: exchange
        };
        
        return this.sendMessage('market_data', message);
    }
    
    // Logs Methods
    subscribeToLogs(level = null, component = null) {
        const message = {
            type: 'subscribe',
            level: level,
            component: component
        };
        
        return this.sendMessage('logs', message);
    }
    
    // Process Methods
    subscribeToProcessUpdates() {
        const message = {
            type: 'subscribe'
        };
        
        return this.sendMessage('processes', message);
    }
    
    // Utility Methods
    getConnectionStatus() {
        const status = {};
        ['market_data', 'logs', 'processes'].forEach(endpoint => {
            status[endpoint] = this.isConnected(endpoint);
        });
        return status;
    }
    
    disconnect() {
        // Stop all heartbeats
        this.heartbeatTimers.forEach((timer, name) => {
            clearInterval(timer);
        });
        this.heartbeatTimers.clear();
        
        // Close all connections
        this.connections.forEach((ws, name) => {
            ws.close();
        });
        this.connections.clear();
        
        // Clear subscriptions
        this.subscriptions.clear();
        this.reconnectAttempts.clear();
    }
    
    // Health check
    checkHealth() {
        const health = {
            connected_endpoints: 0,
            total_endpoints: 3,
            status: {}
        };
        
        ['market_data', 'logs', 'processes'].forEach(endpoint => {
            const connected = this.isConnected(endpoint);
            health.status[endpoint] = connected;
            if (connected) {
                health.connected_endpoints++;
            }
        });
        
        health.healthy = health.connected_endpoints === health.total_endpoints;
        return health;
    }
}

// Global WebSocket manager instance
window.WebSocketManager = WebSocketManager;