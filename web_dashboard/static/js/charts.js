/**
 * Charts Manager for Dashboard Visualizations
 * Handles real-time charts using Chart.js
 */

class ChartsManager {
    constructor(dashboard) {
        this.dashboard = dashboard;
        this.charts = new Map();
        this.chartData = new Map();
        this.updateInterval = null;
        this.maxDataPoints = 50;
        
        this.init();
    }
    
    init() {
        this.initializeCharts();
        this.startDataCollection();
    }
    
    initializeCharts() {
        // Initialize market activity chart
        this.createMarketActivityChart();
        
        // Initialize process status chart
        this.createProcessStatusChart();
        
        // Initialize system performance chart
        this.createSystemPerformanceChart();
    }
    
    createMarketActivityChart() {
        const canvas = document.getElementById('market-activity-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Market Data Updates/min',
                        data: [],
                        borderColor: '#0066cc',
                        backgroundColor: 'rgba(0, 102, 204, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Active Symbols',
                        data: [],
                        borderColor: '#00a651',
                        backgroundColor: 'rgba(0, 166, 81, 0.1)',
                        tension: 0.4,
                        fill: true,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#cccccc',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        ticks: {
                            color: '#cccccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        title: {
                            display: true,
                            text: 'Updates/min',
                            color: '#cccccc'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        ticks: {
                            color: '#cccccc'
                        },
                        grid: {
                            drawOnChartArea: false,
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        title: {
                            display: true,
                            text: 'Symbols',
                            color: '#cccccc'
                        }
                    }
                },
                animation: {
                    duration: 750
                }
            }
        });
        
        this.charts.set('market-activity', chart);
    }
    
    createProcessStatusChart() {
        const canvas = document.getElementById('process-status-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Running', 'Stopped', 'Error'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: [
                        '#00a651',
                        '#999999',
                        '#ff3b30'
                    ],
                    borderColor: [
                        '#00a651',
                        '#999999',
                        '#ff3b30'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#ffffff',
                            padding: 20
                        }
                    }
                },
                animation: {
                    duration: 1000
                }
            }
        });
        
        this.charts.set('process-status', chart);
    }
    
    createSystemPerformanceChart() {
        // This chart might be added to the overview section later
        const canvas = document.getElementById('system-performance-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'CPU Usage %',
                        data: [],
                        borderColor: '#ff9500',
                        backgroundColor: 'rgba(255, 149, 0, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Memory Usage %',
                        data: [],
                        borderColor: '#ff3b30',
                        backgroundColor: 'rgba(255, 59, 48, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#cccccc',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        min: 0,
                        max: 100,
                        ticks: {
                            color: '#cccccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        title: {
                            display: true,
                            text: 'Usage %',
                            color: '#cccccc'
                        }
                    }
                },
                animation: {
                    duration: 500
                }
            }
        });
        
        this.charts.set('system-performance', chart);
    }
    
    updateMarketActivityChart(updatesPerMinute, activeSymbols) {
        const chart = this.charts.get('market-activity');
        if (!chart) return;
        
        const now = new Date();
        const timeLabel = now.toLocaleTimeString();
        
        // Add new data point
        chart.data.labels.push(timeLabel);
        chart.data.datasets[0].data.push(updatesPerMinute);
        chart.data.datasets[1].data.push(activeSymbols);
        
        // Keep only last N data points
        if (chart.data.labels.length > this.maxDataPoints) {
            chart.data.labels.shift();
            chart.data.datasets[0].data.shift();
            chart.data.datasets[1].data.shift();
        }
        
        chart.update('none');
    }
    
    updateProcessStatusChart(processStats) {
        const chart = this.charts.get('process-status');
        if (!chart) return;
        
        chart.data.datasets[0].data = [
            processStats.running || 0,
            processStats.stopped || 0,
            processStats.error || 0
        ];
        
        chart.update();
    }
    
    updateSystemPerformanceChart(cpuUsage, memoryUsage) {
        const chart = this.charts.get('system-performance');
        if (!chart) return;
        
        const now = new Date();
        const timeLabel = now.toLocaleTimeString();
        
        // Add new data point
        chart.data.labels.push(timeLabel);
        chart.data.datasets[0].data.push(cpuUsage);
        chart.data.datasets[1].data.push(memoryUsage);
        
        // Keep only last N data points
        if (chart.data.labels.length > this.maxDataPoints) {
            chart.data.labels.shift();
            chart.data.datasets[0].data.shift();
            chart.data.datasets[1].data.shift();
        }
        
        chart.update('none');
    }
    
    async startDataCollection() {
        // Update charts every 5 seconds
        this.updateInterval = setInterval(async () => {
            await this.collectAndUpdateData();
        }, 5000);
        
        // Initial data collection
        await this.collectAndUpdateData();
    }
    
    async collectAndUpdateData() {
        try {
            // Collect market data stats
            await this.updateMarketData();
            
            // Collect process stats
            await this.updateProcessData();
            
            // Collect system stats (if available)
            await this.updateSystemData();
            
        } catch (error) {
            console.error('Error collecting chart data:', error);
        }
    }
    
    async updateMarketData() {
        try {
            const response = await fetch('/api/market_data/stats');
            if (response.ok) {
                const stats = await response.json();
                
                // Calculate updates per minute (mock data for now)
                const updatesPerMinute = Math.floor(Math.random() * 100) + 50;
                
                this.updateMarketActivityChart(
                    updatesPerMinute,
                    stats.active_symbols || 0
                );
            }
        } catch (error) {
            console.error('Error updating market data charts:', error);
        }
    }
    
    async updateProcessData() {
        try {
            const response = await fetch('/api/processes/stats');
            if (response.ok) {
                const stats = await response.json();
                this.updateProcessStatusChart(stats);
            }
        } catch (error) {
            console.error('Error updating process charts:', error);
        }
    }
    
    async updateSystemData() {
        try {
            // This would collect system performance data
            // For now, we'll use mock data
            const cpuUsage = Math.random() * 50 + 10; // 10-60%
            const memoryUsage = Math.random() * 30 + 20; // 20-50%
            
            this.updateSystemPerformanceChart(cpuUsage, memoryUsage);
        } catch (error) {
            console.error('Error updating system charts:', error);
        }
    }
    
    // Market data real-time updates
    addMarketDataPoint(symbol, price, volume) {
        // This would be called from WebSocket updates
        console.log(`Market data update: ${symbol} - Price: ${price}, Volume: ${volume}`);
        
        // Update relevant charts
        this.updateMarketActivityChart(volume, 1);
    }
    
    // Resize charts when window resizes
    resizeCharts() {
        this.charts.forEach(chart => {
            chart.resize();
        });
    }
    
    // Destroy all charts
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.charts.forEach(chart => {
            chart.destroy();
        });
        
        this.charts.clear();
        this.chartData.clear();
    }
    
    // Theme management
    updateTheme(isDark = true) {
        const textColor = isDark ? '#ffffff' : '#000000';
        const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        
        this.charts.forEach(chart => {
            // Update legend colors
            if (chart.options.plugins && chart.options.plugins.legend) {
                chart.options.plugins.legend.labels.color = textColor;
            }
            
            // Update scale colors
            if (chart.options.scales) {
                Object.keys(chart.options.scales).forEach(scaleKey => {
                    const scale = chart.options.scales[scaleKey];
                    if (scale.ticks) {
                        scale.ticks.color = textColor;
                    }
                    if (scale.grid) {
                        scale.grid.color = gridColor;
                    }
                    if (scale.title) {
                        scale.title.color = textColor;
                    }
                });
            }
            
            chart.update();
        });
    }
    
    // Chart visibility management
    showChart(chartName) {
        const chart = this.charts.get(chartName);
        if (chart && chart.canvas) {
            chart.canvas.style.display = 'block';
            chart.resize();
        }
    }
    
    hideChart(chartName) {
        const chart = this.charts.get(chartName);
        if (chart && chart.canvas) {
            chart.canvas.style.display = 'none';
        }
    }
    
    // Export chart as image
    exportChart(chartName, filename) {
        const chart = this.charts.get(chartName);
        if (!chart) return;
        
        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = filename || `${chartName}-${Date.now()}.png`;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Handle window resize
window.addEventListener('resize', () => {
    if (window.chartsManager) {
        window.chartsManager.resizeCharts();
    }
});

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.dashboard) {
        window.chartsManager = new ChartsManager(window.dashboard);
        
        // Make ChartsManager available to dashboard
        window.ChartsManager = ChartsManager;
    }
});