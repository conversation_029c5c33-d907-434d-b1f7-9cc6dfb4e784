/**
 * Professional Futures Trading Dashboard
 * Main JavaScript functionality
 */

class Dashboard {
    constructor() {
        this.currentSection = 'overview';
        this.websocketManager = null;
        this.charts = {};
        this.updateInterval = null;
        
        this.init();
    }
    
    init() {
        this.setupNavigation();
        this.setupWebSockets();
        this.setupEventListeners();
        this.loadInitialData();
        this.startPeriodicUpdates();
        
        // Hide loading overlay after initialization
        setTimeout(() => {
            this.hideLoading();
        }, 1000);
    }
    
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const section = item.dataset.section;
                this.switchSection(section);
            });
        });
    }
    
    switchSection(section) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');
        
        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${section}-section`).classList.add('active');
        
        this.currentSection = section;
        
        // Load section-specific data
        this.loadSectionData(section);
    }
    
    setupWebSockets() {
        this.websocketManager = new WebSocketManager();
        this.websocketManager.onConnectionChange = (connected) => {
            this.updateConnectionStatus(connected);
        };
        this.websocketManager.onMessage = (data) => {
            this.handleWebSocketMessage(data);
        };
        this.websocketManager.connect();
    }
    
    setupEventListeners() {
        // Configuration tabs
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchConfigTab(tab.dataset.tab);
            });
        });
        
        // Database controls
        document.getElementById('refresh-tables-btn')?.addEventListener('click', () => {
            this.refreshTables();
        });
        
        document.getElementById('test-db-connection-btn')?.addEventListener('click', () => {
            this.testDatabaseConnection();
        });
        
        // Process controls
        document.getElementById('start-process-btn')?.addEventListener('click', () => {
            this.startProcess();
        });
        
        document.getElementById('refresh-processes-btn')?.addEventListener('click', () => {
            this.refreshProcesses();
        });
        
        // Market data controls
        document.getElementById('subscribe-btn')?.addEventListener('click', () => {
            this.subscribeToMarketData();
        });
        
        document.getElementById('unsubscribe-btn')?.addEventListener('click', () => {
            this.unsubscribeFromMarketData();
        });
        
        // Logs controls
        document.getElementById('clear-logs-btn')?.addEventListener('click', () => {
            this.clearLogs();
        });
        
        // Update server time
        setInterval(() => {
            this.updateServerTime();
        }, 1000);
    }
    
    async loadInitialData() {
        try {
            await Promise.all([
                this.loadOverviewData(),
                this.loadConfiguration(),
                this.loadAvailableScripts()
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Failed to load initial data');
        }
    }
    
    async loadOverviewData() {
        try {
            // Load market data stats
            const response = await fetch('/api/market_data/stats');
            const stats = await response.json();
            
            document.getElementById('total-symbols').textContent = stats.total_symbols || 0;
            document.getElementById('active-symbols').textContent = stats.active_symbols || 0;
            
            // Load database status
            const dbResponse = await fetch('/api/database/status');
            const dbStatus = await dbResponse.json();
            
            document.getElementById('database-status').textContent = 
                dbStatus.connected ? 'Connected' : 'Disconnected';
            document.getElementById('database-status').className = 
                dbStatus.connected ? 'text-success' : 'text-danger';
            
            // Load process stats
            const processResponse = await fetch('/api/processes/stats');
            const processStats = await processResponse.json();
            
            document.getElementById('running-processes').textContent = processStats.running_processes || 0;
            
        } catch (error) {
            console.error('Error loading overview data:', error);
        }
    }
    
    async loadConfiguration() {
        try {
            // Load database configuration
            const dbResponse = await fetch('/api/config/database');
            const dbConfig = await dbResponse.json();
            
            document.getElementById('db-host').value = dbConfig.host || '';
            document.getElementById('db-port').value = dbConfig.port || '';
            document.getElementById('db-user').value = dbConfig.user || '';
            document.getElementById('db-database').value = dbConfig.database || '';
            
            // Load Rithmic configuration
            const rithmicResponse = await fetch('/api/config/rithmic');
            const rithmicConfig = await rithmicResponse.json();
            
            document.getElementById('rithmic-user').value = rithmicConfig.user || '';
            document.getElementById('rithmic-system').value = rithmicConfig.system || '';
            document.getElementById('rithmic-gateway').value = rithmicConfig.gateway || '';
            document.getElementById('rithmic-uri').value = rithmicConfig.uri || '';
            
            // Load environment variables
            const envResponse = await fetch('/api/config/environment');
            const envData = await envResponse.json();
            
            this.displayEnvironmentVariables(envData.environment_variables);
            
        } catch (error) {
            console.error('Error loading configuration:', error);
        }
    }
    
    async loadAvailableScripts() {
        try {
            const response = await fetch('/api/processes/scripts/available');
            const data = await response.json();
            
            const select = document.getElementById('script-select');
            select.innerHTML = '<option value="">Select script to start...</option>';
            
            data.scripts.forEach(script => {
                const option = document.createElement('option');
                option.value = script.name;
                option.textContent = `${script.name} (${script.category})`;
                select.appendChild(option);
            });
            
        } catch (error) {
            console.error('Error loading available scripts:', error);
        }
    }
    
    loadSectionData(section) {
        switch (section) {
            case 'overview':
                this.loadOverviewData();
                this.initializeCharts();
                break;
            case 'database':
                this.refreshTables();
                break;
            case 'processes':
                this.refreshProcesses();
                break;
            case 'market-data':
                this.loadMarketDataStatus();
                break;
            case 'logs':
                this.loadRecentLogs();
                break;
        }
    }
    
    async refreshTables() {
        try {
            this.showLoading('Loading tables...');
            
            const response = await fetch('/api/database/tables');
            const tables = await response.json();
            
            const tablesList = document.getElementById('tables-list');
            tablesList.innerHTML = '';
            
            tables.forEach(table => {
                const tableItem = document.createElement('div');
                tableItem.className = 'table-item';
                tableItem.innerHTML = `
                    <div class="table-name">${table.name}</div>
                    <div class="table-rows">${table.row_count.toLocaleString()} rows</div>
                `;
                tableItem.addEventListener('click', () => {
                    this.selectTable(table.name);
                });
                tablesList.appendChild(tableItem);
            });
            
        } catch (error) {
            console.error('Error refreshing tables:', error);
            this.showError('Failed to load tables');
        } finally {
            this.hideLoading();
        }
    }
    
    async selectTable(tableName) {
        try {
            // Store current table for code generation
            this.currentTable = tableName;
            
            // Update UI
            document.querySelectorAll('.table-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.table-item').classList.add('selected');
            
            // Load table data
            const response = await fetch(`/api/database/tables/${tableName}/data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    table: tableName,
                    page: 0,
                    page_size: 100
                })
            });
            
            const data = await response.json();
            this.displayTableData(data.data, data.pagination);
            
            // Update Show Code button to be context-aware
            this.updateShowCodeButton(tableName);
            
        } catch (error) {
            console.error('Error loading table data:', error);
            this.showError('Failed to load table data');
        }
    }
    
    updateShowCodeButton(tableName) {
        // Update the Show Code button in database section to be context-aware
        const showCodeBtn = document.querySelector('#database-section .show-code-btn');
        if (showCodeBtn) {
            showCodeBtn.textContent = `Show Query Code (${tableName})`;
            showCodeBtn.onclick = () => {
                // Get current filter if any
                const currentFilter = this.getCurrentFilter();
                window.codeGenerator.showDatabaseQueryDialog(tableName, currentFilter);
            };
        }
    }
    
    getCurrentFilter() {
        // Get current WHERE clause if any from filters or search
        // This would need to be implemented based on your filtering logic
        // For now, return empty string
        return '';
    }
    
    displayTableData(data, pagination) {
        const tableDataDiv = document.getElementById('table-data');
        
        if (!data || data.length === 0) {
            tableDataDiv.innerHTML = '<p>No data available</p>';
            return;
        }
        
        // Create table
        const table = document.createElement('table');
        table.className = 'data-table';
        
        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        Object.keys(data[0]).forEach(key => {
            const th = document.createElement('th');
            th.textContent = key;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // Create body
        const tbody = document.createElement('tbody');
        
        data.forEach(row => {
            const tr = document.createElement('tr');
            
            Object.values(row).forEach(value => {
                const td = document.createElement('td');
                td.textContent = value !== null ? value : '';
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        tableDataDiv.innerHTML = '';
        tableDataDiv.appendChild(table);
        
        // Update pagination
        this.updatePagination(pagination);
    }
    
    updatePagination(pagination) {
        const paginationDiv = document.getElementById('pagination');
        paginationDiv.innerHTML = `
            <button ${pagination.page === 0 ? 'disabled' : ''}>Previous</button>
            <span class="current-page">Page ${pagination.page + 1} of ${pagination.total_pages}</span>
            <button ${pagination.page >= pagination.total_pages - 1 ? 'disabled' : ''}>Next</button>
        `;
    }
    
    async refreshProcesses() {
        try {
            const response = await fetch('/api/processes/');
            const processes = await response.json();
            
            const processesList = document.getElementById('processes-list');
            processesList.innerHTML = '';
            
            processes.forEach(process => {
                const processItem = document.createElement('div');
                processItem.className = 'process-item';
                processItem.innerHTML = `
                    <div class="process-header">
                        <div class="process-name">${process.name}</div>
                        <div class="process-status ${process.status}">${process.status}</div>
                    </div>
                    <div class="process-details">
                        <div>PID: ${process.pid || 'N/A'}</div>
                        <div>Uptime: ${process.uptime || 'N/A'}</div>
                        <div>CPU: ${process.cpu_percent ? process.cpu_percent.toFixed(1) + '%' : 'N/A'}</div>
                        <div>Memory: ${process.memory_mb ? process.memory_mb.toFixed(1) + 'MB' : 'N/A'}</div>
                    </div>
                    <div class="process-controls">
                        <button class="btn btn-sm btn-success" onclick="dashboard.startProcess('${process.id}')">Start</button>
                        <button class="btn btn-sm btn-secondary" onclick="dashboard.stopProcess('${process.id}')">Stop</button>
                        <button class="btn btn-sm" onclick="dashboard.showProcessLogs('${process.id}')">Logs</button>
                    </div>
                `;
                processesList.appendChild(processItem);
            });
            
        } catch (error) {
            console.error('Error refreshing processes:', error);
            this.showError('Failed to load processes');
        }
    }
    
    async startProcess() {
        const scriptSelect = document.getElementById('script-select');
        const scriptName = scriptSelect.value;
        
        if (!scriptName) {
            this.showError('Please select a script to start');
            return;
        }
        
        try {
            const response = await fetch('/api/processes/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    script_name: scriptName,
                    args: []
                })
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showSuccess('Process started successfully');
                this.refreshProcesses();
            } else {
                this.showError(result.detail || 'Failed to start process');
            }
            
        } catch (error) {
            console.error('Error starting process:', error);
            this.showError('Failed to start process');
        }
    }
    
    async stopProcess(processId) {
        try {
            const response = await fetch(`/api/processes/${processId}/stop`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showSuccess('Process stopped successfully');
                this.refreshProcesses();
            } else {
                this.showError(result.detail || 'Failed to stop process');
            }
            
        } catch (error) {
            console.error('Error stopping process:', error);
            this.showError('Failed to stop process');
        }
    }
    
    async testDatabaseConnection() {
        try {
            const response = await fetch('/api/database/status');
            const status = await response.json();
            
            if (status.connected) {
                this.showSuccess(`Connected to ${status.connection_info}`);
            } else {
                this.showError('Database connection failed');
            }
            
        } catch (error) {
            console.error('Error testing database connection:', error);
            this.showError('Failed to test database connection');
        }
    }
    
    subscribeToMarketData() {
        const symbol = document.getElementById('symbol-input').value;
        const exchange = document.getElementById('exchange-input').value;
        
        if (!symbol) {
            this.showError('Please enter a symbol');
            return;
        }
        
        this.websocketManager.subscribeToMarketData(symbol, exchange);
        this.showSuccess(`Subscribed to ${symbol}@${exchange}`);
    }
    
    unsubscribeFromMarketData() {
        const symbol = document.getElementById('symbol-input').value;
        const exchange = document.getElementById('exchange-input').value;
        
        if (!symbol) {
            this.showError('Please enter a symbol');
            return;
        }
        
        this.websocketManager.unsubscribeFromMarketData(symbol, exchange);
        this.showSuccess(`Unsubscribed from ${symbol}@${exchange}`);
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'market_data':
                this.handleMarketDataUpdate(data);
                break;
            case 'log':
                this.handleLogUpdate(data);
                break;
            case 'process_update':
                this.handleProcessUpdate(data);
                break;
        }
    }
    
    handleMarketDataUpdate(data) {
        if (data.data_type === 'trades') {
            this.updateTradesDisplay(data.data);
        } else if (data.data_type === 'quotes') {
            this.updateQuotesDisplay(data.data);
        }
    }
    
    updateTradesDisplay(trades) {
        const tradesData = document.getElementById('trades-data');
        // Implementation for displaying trades data
        if (Array.isArray(trades) && trades.length > 0) {
            const table = this.createDataTable(trades);
            tradesData.innerHTML = '';
            tradesData.appendChild(table);
        }
    }
    
    updateQuotesDisplay(quotes) {
        const quotesData = document.getElementById('quotes-data');
        // Implementation for displaying quotes data
        if (Array.isArray(quotes) && quotes.length > 0) {
            const table = this.createDataTable(quotes);
            quotesData.innerHTML = '';
            quotesData.appendChild(table);
        }
    }
    
    createDataTable(data) {
        const table = document.createElement('table');
        
        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        Object.keys(data[0]).forEach(key => {
            const th = document.createElement('th');
            th.textContent = key;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // Create body
        const tbody = document.createElement('tbody');
        
        data.slice(0, 10).forEach(row => { // Show only last 10 rows
            const tr = document.createElement('tr');
            
            Object.values(row).forEach(value => {
                const td = document.createElement('td');
                td.textContent = value !== null ? value : '';
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        return table;
    }
    
    handleLogUpdate(data) {
        const logEntry = data.data;
        this.addLogEntry(logEntry);
    }
    
    addLogEntry(logEntry) {
        const logsContainer = document.getElementById('logs-container');
        const logDiv = document.createElement('div');
        logDiv.className = `log-entry ${logEntry.level.toLowerCase()}`;
        
        logDiv.innerHTML = `
            <span class="log-timestamp">${new Date(logEntry.timestamp).toLocaleTimeString()}</span>
            <span class="log-level">[${logEntry.level}]</span>
            <span class="log-message">${logEntry.message}</span>
        `;
        
        logsContainer.appendChild(logDiv);
        
        // Auto-scroll if enabled
        const autoScroll = document.getElementById('auto-scroll-logs');
        if (autoScroll && autoScroll.checked) {
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        // Limit log entries
        const logEntries = logsContainer.querySelectorAll('.log-entry');
        if (logEntries.length > 1000) {
            logEntries[0].remove();
        }
    }
    
    clearLogs() {
        document.getElementById('logs-container').innerHTML = '';
    }
    
    switchConfigTab(tabName) {
        // Update tabs
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update panels
        document.querySelectorAll('.config-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    }
    
    displayEnvironmentVariables(envVars) {
        const envVarsList = document.getElementById('env-vars-list');
        envVarsList.innerHTML = '';
        
        envVars.forEach(envVar => {
            const envVarItem = document.createElement('div');
            envVarItem.className = 'env-var-item';
            envVarItem.innerHTML = `
                <div class="env-var-header">
                    <div class="env-var-key">${envVar.key}</div>
                </div>
                <div class="env-var-value">${envVar.value}</div>
                <div class="env-var-description">${envVar.description}</div>
            `;
            envVarsList.appendChild(envVarItem);
        });
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const wsStatusElement = document.getElementById('websocket-status');
        
        if (connected) {
            statusElement.textContent = '🟢 Connected';
            statusElement.className = 'status-indicator text-success';
            wsStatusElement.textContent = 'WebSocket: Connected';
        } else {
            statusElement.textContent = '🔴 Disconnected';
            statusElement.className = 'status-indicator text-danger';
            wsStatusElement.textContent = 'WebSocket: Disconnected';
        }
    }
    
    async updateServerTime() {
        const serverTimeElement = document.getElementById('server-time');
        try {
            const response = await fetch('/api/server/time');
            const timeData = await response.json();
            
            // Display server time with timezone
            serverTimeElement.textContent = `${timeData.time_only} ${timeData.timezone}`;
        } catch (error) {
            // Fallback to client time if server time API fails
            console.warn('Failed to get server time, using client time:', error);
            const now = new Date();
            const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            serverTimeElement.textContent = `${now.toLocaleTimeString()} (${timeZone})`;
        }
    }
    
    startPeriodicUpdates() {
        this.updateInterval = setInterval(() => {
            if (this.currentSection === 'overview') {
                this.loadOverviewData();
            }
        }, 30000); // Update every 30 seconds
    }
    
    initializeCharts() {
        // Initialize charts when charts.js is loaded
        if (window.ChartsManager) {
            this.charts = new ChartsManager();
            this.charts.initializeCharts();
        }
    }
    
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        const text = overlay.querySelector('.loading-text');
        text.textContent = message;
        overlay.classList.add('show');
    }
    
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        overlay.classList.remove('show');
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type) {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            z-index: 2001;
            background-color: ${type === 'success' ? 'var(--success-color)' : 'var(--danger-color)'};
            box-shadow: 0 4px 15px var(--shadow-color);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 5000);
    }
}

// Global function for updating selected contracts from contracts modal
function updateSelectedContracts(contracts) {
    const display = document.getElementById('selected-contracts-display');
    
    if (contracts.length === 0) {
        display.textContent = 'No contracts selected';
        display.className = 'selected-contracts-display empty';
    } else {
        // Group by symbol for display
        const grouped = {};
        contracts.forEach(contract => {
            const [symbol, month] = contract.split('_');
            if (!grouped[symbol]) {
                grouped[symbol] = [];
            }
            grouped[symbol].push(month);
        });
        
        // Create display text
        const displayText = Object.entries(grouped)
            .map(([symbol, months]) => `${symbol}: ${months.join(', ')}`)
            .join(' | ');
        
        display.textContent = displayText;
        display.className = 'selected-contracts-display';
    }
    
    // Store for code generation
    window.selectedContracts = contracts;
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
    window.selectedContracts = [];
});