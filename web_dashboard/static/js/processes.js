/**
 * Process Management JavaScript Module
 * Handles process control and monitoring
 */

class ProcessManager {
    constructor(dashboard) {
        this.dashboard = dashboard;
        this.processes = new Map();
        this.updateInterval = null;
        this.autoRefresh = true;
        this.refreshRate = 5000; // 5 seconds
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadAvailableScripts();
        this.startAutoRefresh();
    }
    
    setupEventListeners() {
        // Start process button
        const startBtn = document.getElementById('start-process-btn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startSelectedProcess();
            });
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-processes-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshProcesses();
            });
        }
        
        // Script selection
        const scriptSelect = document.getElementById('script-select');
        if (scriptSelect) {
            scriptSelect.addEventListener('change', (e) => {
                this.onScriptSelectionChange(e.target.value);
            });
        }
    }
    
    async loadAvailableScripts() {
        try {
            const response = await fetch('/api/processes/scripts/available');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.displayAvailableScripts(data.scripts);
            
        } catch (error) {
            console.error('Error loading available scripts:', error);
            this.dashboard.showError('Failed to load available scripts');
        }
    }
    
    displayAvailableScripts(scripts) {
        const select = document.getElementById('script-select');
        if (!select) return;
        
        select.innerHTML = '<option value="">Select script to start...</option>';
        
        // Group scripts by category
        const categories = {};
        scripts.forEach(script => {
            if (!categories[script.category]) {
                categories[script.category] = [];
            }
            categories[script.category].push(script);
        });
        
        // Add options grouped by category
        Object.keys(categories).sort().forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category;
            
            categories[category].forEach(script => {
                const option = document.createElement('option');
                option.value = script.name;
                option.textContent = `${script.name}`;
                option.title = script.description || '';
                optgroup.appendChild(option);
            });
            
            select.appendChild(optgroup);
        });
    }
    
    onScriptSelectionChange(scriptName) {
        // Could show script details or parameters here
        if (scriptName) {
            console.log(`Selected script: ${scriptName}`);
        }
    }
    
    async startSelectedProcess() {
        const scriptSelect = document.getElementById('script-select');
        const scriptName = scriptSelect ? scriptSelect.value : '';
        
        if (!scriptName) {
            this.dashboard.showError('Please select a script to start');
            return;
        }
        
        await this.startProcess(scriptName);
    }
    
    async startProcess(scriptName, args = []) {
        try {
            this.dashboard.showLoading('Starting process...');
            
            const response = await fetch('/api/processes/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    script_name: scriptName,
                    args: args
                })
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.dashboard.showSuccess(`Process "${scriptName}" started successfully`);
                this.refreshProcesses();
                
                // Reset script selection
                const scriptSelect = document.getElementById('script-select');
                if (scriptSelect) {
                    scriptSelect.value = '';
                }
            } else {
                this.dashboard.showError(result.detail || 'Failed to start process');
            }
            
        } catch (error) {
            console.error('Error starting process:', error);
            this.dashboard.showError('Failed to start process');
        } finally {
            this.dashboard.hideLoading();
        }
    }
    
    async stopProcess(processId) {
        try {
            const response = await fetch(`/api/processes/${processId}/stop`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.dashboard.showSuccess('Process stopped successfully');
                this.refreshProcesses();
            } else {
                this.dashboard.showError(result.detail || 'Failed to stop process');
            }
            
        } catch (error) {
            console.error('Error stopping process:', error);
            this.dashboard.showError('Failed to stop process');
        }
    }
    
    async restartProcess(processId) {
        try {
            const response = await fetch(`/api/processes/${processId}/restart`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.dashboard.showSuccess('Process restarted successfully');
                this.refreshProcesses();
            } else {
                this.dashboard.showError(result.detail || 'Failed to restart process');
            }
            
        } catch (error) {
            console.error('Error restarting process:', error);
            this.dashboard.showError('Failed to restart process');
        }
    }
    
    async refreshProcesses() {
        try {
            const response = await fetch('/api/processes/');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const processes = await response.json();
            this.updateProcessList(processes);
            
        } catch (error) {
            console.error('Error refreshing processes:', error);
            this.dashboard.showError('Failed to refresh processes');
        }
    }
    
    updateProcessList(processes) {
        const processesList = document.getElementById('processes-list');
        if (!processesList) return;
        
        processesList.innerHTML = '';
        
        // Update internal process map
        this.processes.clear();
        processes.forEach(process => {
            this.processes.set(process.id, process);
        });
        
        if (processes.length === 0) {
            processesList.innerHTML = '<div class="no-processes">No processes running</div>';
            return;
        }
        
        processes.forEach(process => {
            const processItem = this.createProcessItem(process);
            processesList.appendChild(processItem);
        });
    }
    
    createProcessItem(process) {
        const processItem = document.createElement('div');
        processItem.className = 'process-item';
        processItem.dataset.processId = process.id;
        
        const statusClass = this.getStatusClass(process.status);
        const uptime = this.formatUptime(process.uptime);
        const memoryUsage = process.memory_mb ? `${process.memory_mb.toFixed(1)} MB` : 'N/A';
        const cpuUsage = process.cpu_percent !== null ? `${process.cpu_percent.toFixed(1)}%` : 'N/A';
        
        processItem.innerHTML = `
            <div class="process-header">
                <div class="process-name">${process.name}</div>
                <div class="process-status ${statusClass}">${process.status}</div>
            </div>
            <div class="process-details">
                <div class="detail-item">
                    <span class="detail-label">PID:</span>
                    <span class="detail-value">${process.pid || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Uptime:</span>
                    <span class="detail-value">${uptime}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">CPU:</span>
                    <span class="detail-value">${cpuUsage}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Memory:</span>
                    <span class="detail-value">${memoryUsage}</span>
                </div>
            </div>
            <div class="process-controls">
                ${this.createProcessControls(process)}
            </div>
            <div class="process-logs" id="process-logs-${process.id}" style="display: none;">
                <div class="logs-header">
                    <h4>Recent Logs</h4>
                    <button class="btn btn-sm" onclick="processManager.hideProcessLogs('${process.id}')">Hide</button>
                </div>
                <div class="logs-content" id="logs-content-${process.id}"></div>
            </div>
        `;
        
        return processItem;
    }
    
    createProcessControls(process) {
        let controls = '';
        
        if (process.status === 'running') {
            controls += `
                <button class="btn btn-sm btn-secondary" onclick="processManager.stopProcess('${process.id}')">
                    Stop
                </button>
                <button class="btn btn-sm" onclick="processManager.restartProcess('${process.id}')">
                    Restart
                </button>
            `;
        } else {
            controls += `
                <button class="btn btn-sm btn-success" onclick="processManager.startProcess('${process.name}')">
                    Start
                </button>
            `;
        }
        
        controls += `
            <button class="btn btn-sm" onclick="processManager.showProcessLogs('${process.id}')">
                Logs
            </button>
            <button class="btn btn-sm btn-secondary" onclick="processManager.removeProcess('${process.id}')">
                Remove
            </button>
        `;
        
        return controls;
    }
    
    getStatusClass(status) {
        switch (status.toLowerCase()) {
            case 'running':
                return 'running';
            case 'stopped':
                return 'stopped';
            case 'error':
            case 'failed':
                return 'error';
            default:
                return '';
        }
    }
    
    formatUptime(uptimeSeconds) {
        if (!uptimeSeconds || uptimeSeconds < 0) {
            return 'N/A';
        }
        
        const hours = Math.floor(uptimeSeconds / 3600);
        const minutes = Math.floor((uptimeSeconds % 3600) / 60);
        const seconds = Math.floor(uptimeSeconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    }
    
    async showProcessLogs(processId) {
        try {
            const logsDiv = document.getElementById(`process-logs-${processId}`);
            const logsContent = document.getElementById(`logs-content-${processId}`);
            
            if (!logsDiv || !logsContent) return;
            
            // Show loading
            logsContent.innerHTML = '<div class="loading">Loading logs...</div>';
            logsDiv.style.display = 'block';
            
            // Fetch logs
            const response = await fetch(`/api/processes/${processId}/logs`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const logs = await response.json();
            this.displayProcessLogs(processId, logs);
            
        } catch (error) {
            console.error('Error loading process logs:', error);
            this.dashboard.showError('Failed to load process logs');
        }
    }
    
    displayProcessLogs(processId, logs) {
        const logsContent = document.getElementById(`logs-content-${processId}`);
        if (!logsContent) return;
        
        if (!logs || logs.length === 0) {
            logsContent.innerHTML = '<div class="no-logs">No logs available</div>';
            return;
        }
        
        const logsHtml = logs.map(log => {
            const timestamp = new Date(log.timestamp).toLocaleTimeString();
            const levelClass = log.level.toLowerCase();
            return `
                <div class="log-entry ${levelClass}">
                    <span class="log-timestamp">${timestamp}</span>
                    <span class="log-level">[${log.level}]</span>
                    <span class="log-message">${log.message}</span>
                </div>
            `;
        }).join('');
        
        logsContent.innerHTML = `<div class="logs-list">${logsHtml}</div>`;
        
        // Auto-scroll to bottom
        logsContent.scrollTop = logsContent.scrollHeight;
    }
    
    hideProcessLogs(processId) {
        const logsDiv = document.getElementById(`process-logs-${processId}`);
        if (logsDiv) {
            logsDiv.style.display = 'none';
        }
    }
    
    async removeProcess(processId) {
        if (!confirm('Are you sure you want to remove this process?')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/processes/${processId}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.dashboard.showSuccess('Process removed successfully');
                this.refreshProcesses();
            } else {
                this.dashboard.showError(result.detail || 'Failed to remove process');
            }
            
        } catch (error) {
            console.error('Error removing process:', error);
            this.dashboard.showError('Failed to remove process');
        }
    }
    
    startAutoRefresh() {
        if (this.autoRefresh) {
            this.updateInterval = setInterval(() => {
                // Only refresh if we're on the processes section
                if (this.dashboard.currentSection === 'processes') {
                    this.refreshProcesses();
                }
            }, this.refreshRate);
        }
    }
    
    stopAutoRefresh() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    setAutoRefresh(enabled) {
        this.autoRefresh = enabled;
        if (enabled) {
            this.startAutoRefresh();
        } else {
            this.stopAutoRefresh();
        }
    }
    
    getProcessStats() {
        const stats = {
            total: this.processes.size,
            running: 0,
            stopped: 0,
            error: 0
        };
        
        this.processes.forEach(process => {
            switch (process.status.toLowerCase()) {
                case 'running':
                    stats.running++;
                    break;
                case 'stopped':
                    stats.stopped++;
                    break;
                case 'error':
                case 'failed':
                    stats.error++;
                    break;
            }
        });
        
        return stats;
    }
    
    // Cleanup method
    destroy() {
        this.stopAutoRefresh();
        this.processes.clear();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.dashboard) {
        window.processManager = new ProcessManager(window.dashboard);
    }
});