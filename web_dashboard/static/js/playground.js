/**
 * Rithmic API Playground JavaScript Module
 * ======================================
 * 
 * Provides frontend functionality for the Rithmic API playground interface.
 * Handles template browsing, request building, session management, and real-time testing.
 */

class PlaygroundManager {
    constructor() {
        this.templates = [];
        this.selectedTemplate = null;
        this.activeSessions = [];
        this.currentSession = null;
        this.websocket = null;
        this.requestHistory = [];
        this.streamingData = [];
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }
    
    async initialize() {
        console.log('🧪 Initializing API Playground...');
        
        try {
            await this.loadTemplates();
            this.setupEventListeners();
            this.setupWebSocket();
            this.setupTabs();
            
            console.log('✅ API Playground initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing API Playground:', error);
        }
    }
    
    async loadTemplates() {
        try {
            console.log('📚 Loading Rithmic Protocol templates...');
            
            const response = await fetch('/api/playground/templates');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            this.templates = await response.json();
            console.log(`📊 Loaded ${this.templates.length} templates`);
            
            this.renderTemplatesList();
            this.populateFilterDropdowns();
            
        } catch (error) {
            console.error('❌ Error loading templates:', error);
            this.showError('Failed to load templates. Please check your connection.');
        }
    }
    
    renderTemplatesList() {
        const container = document.getElementById('templates-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (this.templates.length === 0) {
            container.innerHTML = '<div class="no-templates">No templates found</div>';
            return;
        }
        
        // Apply filters
        const filteredTemplates = this.applyTemplateFilters();
        
        // Group templates by category
        const templatesByCategory = {};
        filteredTemplates.forEach(template => {
            const category = template.category || 'Other';
            if (!templatesByCategory[category]) {
                templatesByCategory[category] = [];
            }
            templatesByCategory[category].push(template);
        });
        
        // Render grouped templates
        Object.keys(templatesByCategory).sort().forEach(category => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'template-category';
            
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'category-header';
            categoryHeader.innerHTML = `
                <span class="category-name">${category}</span>
                <span class="category-count">${templatesByCategory[category].length}</span>
            `;
            categoryDiv.appendChild(categoryHeader);
            
            const templatesContainer = document.createElement('div');
            templatesContainer.className = 'category-templates';
            
            templatesByCategory[category].forEach(template => {
                const templateDiv = document.createElement('div');
                templateDiv.className = 'template-item';
                templateDiv.dataset.templateId = template.template_id;
                
                templateDiv.innerHTML = `
                    <div class="template-header">
                        <span class="template-name">${template.template_name}</span>
                        <span class="template-id">#${template.template_id}</span>
                    </div>
                    <div class="template-description">${template.description}</div>
                    <div class="template-meta">
                        <span class="template-plant">${template.infrastructure_plant}</span>
                        <span class="template-fields">${template.required_fields.length} required</span>
                    </div>
                `;
                
                templateDiv.addEventListener('click', () => this.selectTemplate(template));
                templatesContainer.appendChild(templateDiv);
            });
            
            categoryDiv.appendChild(templatesContainer);
            container.appendChild(categoryDiv);
        });
    }
    
    applyTemplateFilters() {
        const categoryFilter = document.getElementById('category-filter')?.value || '';
        const plantFilter = document.getElementById('plant-filter')?.value || '';
        const searchFilter = document.getElementById('template-search')?.value.toLowerCase() || '';
        
        return this.templates.filter(template => {
            if (categoryFilter && template.category !== categoryFilter) return false;
            if (plantFilter && template.infrastructure_plant !== plantFilter) return false;
            if (searchFilter) {
                const searchText = `${template.template_name} ${template.description}`.toLowerCase();
                if (!searchText.includes(searchFilter)) return false;
            }
            return true;
        });
    }
    
    populateFilterDropdowns() {
        // Populate category filter
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            const categories = [...new Set(this.templates.map(t => t.category))].sort();
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categoryFilter.appendChild(option);
            });
        }
        
        // Populate plant filter
        const plantFilter = document.getElementById('plant-filter');
        if (plantFilter) {
            const plants = [...new Set(this.templates.map(t => t.infrastructure_plant))].sort();
            plants.forEach(plant => {
                const option = document.createElement('option');
                option.value = plant;
                option.textContent = plant;
                plantFilter.appendChild(option);
            });
        }
    }
    
    async selectTemplate(template) {
        console.log(`🎯 Selected template: ${template.template_name} (${template.template_id})`);
        
        try {
            // Highlight selected template
            document.querySelectorAll('.template-item').forEach(item => {
                item.classList.remove('selected');
            });
            document.querySelector(`[data-template-id="${template.template_id}"]`)?.classList.add('selected');
            
            // Load detailed template information
            const response = await fetch(`/api/playground/templates/${template.template_id}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            this.selectedTemplate = await response.json();
            this.renderRequestBuilder();
            
        } catch (error) {
            console.error('❌ Error selecting template:', error);
            this.showError('Failed to load template details.');
        }
    }
    
    renderRequestBuilder() {
        if (!this.selectedTemplate) return;
        
        // Update selected template info
        const infoContainer = document.getElementById('selected-template-info');
        if (infoContainer) {
            infoContainer.innerHTML = `
                <div class="template-info-header">
                    <h4>${this.selectedTemplate.template_name}</h4>
                    <span class="template-id-badge">#${this.selectedTemplate.template_id}</span>
                </div>
                <div class="template-info-details">
                    <span class="template-plant">${this.selectedTemplate.infrastructure_plant}</span>
                    <span class="template-category">${this.selectedTemplate.category}</span>
                    <span class="template-type">${this.selectedTemplate.is_request ? 'Request' : 'Response'}</span>
                </div>
                <div class="template-description">${this.selectedTemplate.description}</div>
            `;
        }
        
        // Show request form
        const formContainer = document.getElementById('request-form-container');
        const docContainer = document.getElementById('template-documentation');
        if (formContainer && docContainer) {
            formContainer.style.display = 'block';
            docContainer.style.display = 'none';
        }
        
        // Generate form fields
        this.generateFormFields();
        this.loadTemplateExamples();
    }
    
    generateFormFields() {
        if (!this.selectedTemplate || !this.selectedTemplate.fields) return;
        
        const basicContainer = document.getElementById('basic-form-fields');
        const advancedContainer = document.getElementById('advanced-form-fields');
        
        if (!basicContainer || !advancedContainer) return;
        
        // Clear existing fields
        basicContainer.innerHTML = '';
        advancedContainer.innerHTML = '';
        
        // Separate required and optional fields
        const requiredFields = this.selectedTemplate.fields.filter(f => f.label === 'required');
        const optionalFields = this.selectedTemplate.fields.filter(f => f.label === 'optional');
        
        // Generate required fields (basic tab)
        requiredFields.forEach(field => {
            const fieldElement = this.createFormField(field, true);
            basicContainer.appendChild(fieldElement);
        });
        
        // Generate optional fields (advanced tab)
        optionalFields.forEach(field => {
            const fieldElement = this.createFormField(field, false);
            advancedContainer.appendChild(fieldElement);
        });
        
        // Add template_id field if not present
        if (!requiredFields.some(f => f.name === 'template_id')) {
            const templateIdField = this.createFormField({
                name: 'template_id',
                type: 'int32',
                label: 'required',
                default_value: this.selectedTemplate.template_id,
                description: 'Template ID for this request'
            }, true);
            basicContainer.insertBefore(templateIdField, basicContainer.firstChild);
        }
    }
    
    createFormField(field, isRequired) {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'form-field';
        
        const label = document.createElement('label');
        label.htmlFor = `field_${field.name}`;
        label.innerHTML = `
            ${field.name}
            ${isRequired ? '<span class="required">*</span>' : ''}
            <span class="field-type">${field.type}</span>
        `;
        
        let input;
        
        // Create appropriate input based on field type and properties
        if (field.enum_values && field.enum_values.length > 0) {
            // Enum field - create select dropdown
            input = document.createElement('select');
            
            // Add empty option if not required
            if (!isRequired) {
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = 'Select...';
                input.appendChild(emptyOption);
            }
            
            // Add enum options
            field.enum_values.forEach(enumValue => {
                const option = document.createElement('option');
                option.value = enumValue;
                option.textContent = this.formatEnumName(enumValue);
                input.appendChild(option);
            });
            
            // Set default value if available
            if (field.default_value) {
                input.value = field.default_value;
            }
            
        } else if (field.type === 'bool') {
            input = document.createElement('input');
            input.type = 'checkbox';
            input.checked = field.default_value === 'true' || field.default_value === true;
            
        } else if (field.label === 'repeated') {
            // Repeated field - create textarea for multiple values
            input = document.createElement('textarea');
            input.rows = 3;
            input.placeholder = this.getRepeatedFieldPlaceholder(field);
            
            if (field.default_value) {
                input.value = Array.isArray(field.default_value) 
                    ? field.default_value.join('\n')
                    : field.default_value;
            }
            
        } else if (field.type === 'string') {
            // Check if this is a special string field that should be a select
            const specialSelects = this.getSpecialSelectOptions(field.name);
            if (specialSelects) {
                input = document.createElement('select');
                
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = 'Select...';
                input.appendChild(emptyOption);
                
                specialSelects.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.label;
                    input.appendChild(optionElement);
                });
                
                if (field.default_value) {
                    input.value = field.default_value;
                }
            } else {
                input = document.createElement('input');
                input.type = 'text';
                input.value = field.default_value || '';
                input.placeholder = this.getFieldPlaceholder(field.name);
                
                // Add input validation for specific string patterns
                this.addStringValidation(input, field.name);
            }
            
        } else if (field.type.includes('int') || field.type.includes('uint')) {
            input = document.createElement('input');
            input.type = 'number';
            input.step = '1';
            input.value = field.default_value || '';
            
            // Add min/max validation for integer types
            if (field.type.includes('uint')) {
                input.min = '0';
            }
            if (field.type === 'int32' || field.type === 'uint32') {
                input.max = field.type.includes('uint') ? '4294967295' : '2147483647';
            }
            
        } else if (field.type.includes('float') || field.type.includes('double')) {
            input = document.createElement('input');
            input.type = 'number';
            input.step = 'any';
            input.value = field.default_value || '';
            input.placeholder = 'Enter decimal number';
            
        } else if (field.type === 'bytes') {
            input = document.createElement('input');
            input.type = 'text';
            input.value = field.default_value || '';
            input.placeholder = 'Enter base64 encoded data';
            
        } else {
            // Default to text input
            input = document.createElement('input');
            input.type = 'text';
            input.value = field.default_value || '';
            input.placeholder = this.getFieldPlaceholder(field.name);
        }
        
        input.id = `field_${field.name}`;
        input.name = field.name;
        input.className = 'form-input';
        
        // Add required attribute for validation
        if (isRequired && input.type !== 'checkbox') {
            input.required = true;
        }
        
        // Add data attributes for validation
        input.setAttribute('data-field-type', field.type);
        input.setAttribute('data-field-label', field.label);
        
        fieldDiv.appendChild(label);
        
        // Add description if available
        if (field.description) {
            const description = document.createElement('div');
            description.className = 'field-description';
            description.textContent = field.description;
            fieldDiv.appendChild(description);
        }
        
        fieldDiv.appendChild(input);
        
        // Add real-time validation
        this.addFieldValidation(input, field);
        
        return fieldDiv;
    }
    
    formatEnumName(enumValue) {
        // Convert UPPER_CASE_ENUM to Title Case Enum
        return enumValue
            .toLowerCase()
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
    
    getRepeatedFieldPlaceholder(field) {
        if (field.type === 'string') {
            return 'Enter values, one per line\nExample:\nvalue1\nvalue2\nvalue3';
        } else if (field.type.includes('int') || field.type.includes('float')) {
            return 'Enter numbers, one per line\nExample:\n123\n456\n789';
        } else {
            return 'Enter values, one per line';
        }
    }
    
    getSpecialSelectOptions(fieldName) {
        const specialSelects = {
            'exchange': [
                { value: 'CME', label: 'CME - Chicago Mercantile Exchange' },
                { value: 'CBOT', label: 'CBOT - Chicago Board of Trade' },
                { value: 'NYMEX', label: 'NYMEX - New York Mercantile Exchange' },
                { value: 'COMEX', label: 'COMEX - Commodity Exchange' },
                { value: 'ICE', label: 'ICE - Intercontinental Exchange' },
                { value: 'EUREX', label: 'EUREX - European Exchange' }
            ],
            'system': [
                { value: 'Rithmic Paper Trading', label: 'Rithmic Paper Trading' },
                { value: 'Rithmic Live Trading', label: 'Rithmic Live Trading' },
                { value: 'Rithmic Test', label: 'Rithmic Test' }
            ],
            'gateway': [
                { value: 'Chicago Area', label: 'Chicago Area' },
                { value: 'New York Area', label: 'New York Area' },
                { value: 'London Area', label: 'London Area' },
                { value: 'Tokyo Area', label: 'Tokyo Area' }
            ],
            'symbol': [
                { value: 'ES', label: 'ES - E-mini S&P 500' },
                { value: 'NQ', label: 'NQ - E-mini NASDAQ' },
                { value: 'YM', label: 'YM - E-mini Dow Jones' },
                { value: 'RTY', label: 'RTY - E-mini Russell 2000' },
                { value: 'CL', label: 'CL - Crude Oil' },
                { value: 'GC', label: 'GC - Gold' },
                { value: 'SI', label: 'SI - Silver' },
                { value: 'ZN', label: 'ZN - 10-Year Treasury Note' },
                { value: 'ZB', label: 'ZB - 30-Year Treasury Bond' }
            ]
        };
        
        return specialSelects[fieldName.toLowerCase()];
    }
    
    addStringValidation(input, fieldName) {
        const patterns = {
            'symbol': /^[A-Z]{1,6}[A-Z0-9]*$/,
            'exchange': /^[A-Z]{3,6}$/,
            'account_id': /^[A-Za-z0-9_-]+$/,
            'user': /^[A-Za-z0-9_.-]+$/,
            'fcm_id': /^[A-Za-z0-9_-]+$/,
            'ib_id': /^[A-Za-z0-9_-]+$/
        };
        
        const pattern = patterns[fieldName.toLowerCase()];
        if (pattern) {
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                if (value && !pattern.test(value)) {
                    e.target.setCustomValidity(`Invalid format for ${fieldName}`);
                } else {
                    e.target.setCustomValidity('');
                }
            });
        }
    }
    
    addFieldValidation(input, field) {
        // Add real-time validation based on field type
        input.addEventListener('input', (e) => {
            this.validateField(e.target, field);
        });
        
        input.addEventListener('blur', (e) => {
            this.validateField(e.target, field);
        });
    }
    
    validateField(input, field) {
        let isValid = true;
        let errorMessage = '';
        
        const value = input.value.trim();
        
        // Skip validation for empty optional fields
        if (!value && field.label !== 'required') {
            input.setCustomValidity('');
            this.updateFieldValidationUI(input, true);
            return;
        }
        
        // Required field validation
        if (field.label === 'required' && !value && input.type !== 'checkbox') {
            isValid = false;
            errorMessage = `${field.name} is required`;
        }
        
        // Type-specific validation
        if (value && isValid) {
            if (field.type.includes('int') && !field.enum_values) {
                const num = parseInt(value);
                if (isNaN(num)) {
                    isValid = false;
                    errorMessage = 'Must be a valid integer';
                } else if (field.type.includes('uint') && num < 0) {
                    isValid = false;
                    errorMessage = 'Must be a positive integer';
                }
            } else if (field.type.includes('float') || field.type.includes('double')) {
                const num = parseFloat(value);
                if (isNaN(num)) {
                    isValid = false;
                    errorMessage = 'Must be a valid number';
                }
            }
        }
        
        // Set validation state
        input.setCustomValidity(isValid ? '' : errorMessage);
        this.updateFieldValidationUI(input, isValid, errorMessage);
    }
    
    updateFieldValidationUI(input, isValid, errorMessage = '') {
        const fieldDiv = input.closest('.form-field');
        if (!fieldDiv) return;
        
        // Remove existing validation classes and messages
        fieldDiv.classList.remove('field-valid', 'field-invalid');
        const existingError = fieldDiv.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        if (input.value.trim()) {
            if (isValid) {
                fieldDiv.classList.add('field-valid');
            } else {
                fieldDiv.classList.add('field-invalid');
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = errorMessage;
                fieldDiv.appendChild(errorDiv);
            }
        }
    }
    
    getFieldPlaceholder(fieldName) {
        const placeholders = {
            'symbol': 'ES',
            'exchange': 'CME',
            'user': 'test_user',
            'password': 'password',
            'system': 'Rithmic Paper Trading',
            'gateway': 'Chicago Area',
            'fcm_id': 'test_fcm',
            'ib_id': 'test_ib',
            'account_id': 'test_account'
        };
        
        return placeholders[fieldName.toLowerCase()] || `Enter ${fieldName}`;
    }
    
    async loadTemplateExamples() {
        if (!this.selectedTemplate || !this.selectedTemplate.examples) return;
        
        const examplesContainer = document.getElementById('examples-list');
        if (!examplesContainer) return;
        
        examplesContainer.innerHTML = '';
        
        this.selectedTemplate.examples.forEach(example => {
            const exampleDiv = document.createElement('div');
            exampleDiv.className = 'example-item';
            
            exampleDiv.innerHTML = `
                <div class="example-header">
                    <h5>${example.name}</h5>
                    <button class="btn btn-sm load-example-btn">Load Example</button>
                </div>
                <div class="example-description">${example.description}</div>
                <pre class="example-data">${JSON.stringify(example.data, null, 2)}</pre>
            `;
            
            exampleDiv.querySelector('.load-example-btn').addEventListener('click', () => {
                this.loadExampleData(example.data);
            });
            
            examplesContainer.appendChild(exampleDiv);
        });
    }
    
    loadExampleData(exampleData) {
        Object.keys(exampleData).forEach(fieldName => {
            const input = document.querySelector(`[name="${fieldName}"]`);
            if (input) {
                const value = exampleData[fieldName];
                if (input.type === 'checkbox') {
                    input.checked = Boolean(value);
                } else if (input.tagName === 'TEXTAREA') {
                    input.value = Array.isArray(value) ? value.join('\n') : String(value);
                } else {
                    input.value = Array.isArray(value) ? value.join(', ') : String(value);
                }
            }
        });
        
        this.showSuccess('Example data loaded into form');
    }
    
    setupEventListeners() {
        // Template filters
        document.getElementById('category-filter')?.addEventListener('change', () => this.renderTemplatesList());
        document.getElementById('plant-filter')?.addEventListener('change', () => this.renderTemplatesList());
        document.getElementById('template-search')?.addEventListener('input', () => this.renderTemplatesList());
        document.getElementById('refresh-templates-btn')?.addEventListener('click', () => this.loadTemplates());
        
        // Request actions
        document.getElementById('validate-request-btn')?.addEventListener('click', () => this.validateRequest());
        document.getElementById('test-request-btn')?.addEventListener('click', () => this.testRequest());
        document.getElementById('generate-code-btn')?.addEventListener('click', () => this.generateCode());
        
        // Session management
        document.getElementById('new-session-btn')?.addEventListener('click', () => this.createSession());
        document.getElementById('connect-session-btn')?.addEventListener('click', () => this.connectSession());
        
        // History management
        document.getElementById('clear-history-btn')?.addEventListener('click', () => this.clearHistory());
        document.getElementById('export-history-btn')?.addEventListener('click', () => this.exportHistory());
        
        // Streaming controls
        document.getElementById('start-streaming-btn')?.addEventListener('click', () => this.startStreaming());
        document.getElementById('stop-streaming-btn')?.addEventListener('click', () => this.stopStreaming());
        document.getElementById('clear-streaming-btn')?.addEventListener('click', () => this.clearStreaming());
    }
    
    setupTabs() {
        // Form tabs
        document.querySelectorAll('.form-tabs .tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.dataset.tab;
                
                // Update active tab
                document.querySelectorAll('.form-tabs .tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Update active panel
                document.querySelectorAll('.form-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                document.getElementById(tabId)?.classList.add('active');
            });
        });
        
        // Results tabs
        document.querySelectorAll('.results-tabs .tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.dataset.tab;
                
                // Update active tab
                document.querySelectorAll('.results-tabs .tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Update active panel
                document.querySelectorAll('.results-panel-content').forEach(panel => {
                    panel.classList.remove('active');
                });
                document.getElementById(tabId)?.classList.add('active');
            });
        });
    }
    
    setupWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }
        
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/playground`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('🔗 Playground WebSocket connected');
        };
        
        this.websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleWebSocketMessage(message);
            } catch (error) {
                console.error('❌ Error parsing WebSocket message:', error);
            }
        };
        
        this.websocket.onclose = () => {
            console.log('🔌 Playground WebSocket disconnected');
            // Attempt reconnection after 5 seconds
            setTimeout(() => this.setupWebSocket(), 5000);
        };
        
        this.websocket.onerror = (error) => {
            console.error('❌ Playground WebSocket error:', error);
        };
    }
    
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'session_status':
                this.updateSessionStatus(message.session_id, message.status);
                break;
            case 'streaming_data':
                this.addStreamingData(message.data);
                break;
            case 'error':
                this.showError(message.message);
                break;
        }
    }
    
    async validateRequest() {
        if (!this.selectedTemplate) {
            this.showError('Please select a template first');
            return;
        }
        
        try {
            const requestData = this.collectFormData();
            
            const response = await fetch('/api/playground/validate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    template_id: this.selectedTemplate.template_id,
                    request_data: requestData
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const validationResult = await response.json();
            this.displayValidationResults(validationResult);
            
        } catch (error) {
            console.error('❌ Error validating request:', error);
            this.showError('Failed to validate request');
        }
    }
    
    async testRequest() {
        if (!this.selectedTemplate) {
            this.showError('Please select a template first');
            return;
        }
        
        try {
            const requestData = this.collectFormData();
            
            const playgroundRequest = {
                template_id: this.selectedTemplate.template_id,
                template_name: this.selectedTemplate.template_name,
                infrastructure_plant: this.selectedTemplate.infrastructure_plant,
                request_data: requestData,
                session_id: this.currentSession?.session_id
            };
            
            // Show loading state
            const testButton = document.getElementById('test-request-btn');
            const originalText = testButton.textContent;
            testButton.textContent = '🔄 Testing...';
            testButton.disabled = true;
            
            const response = await fetch('/api/playground/test', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(playgroundRequest)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            this.displayTestResults(result);
            this.addToHistory(result);
            
            // Restore button state
            testButton.textContent = originalText;
            testButton.disabled = false;
            
        } catch (error) {
            console.error('❌ Error testing request:', error);
            this.showError('Failed to test request');
            
            // Restore button state
            const testButton = document.getElementById('test-request-btn');
            testButton.textContent = '🚀 Test Request';
            testButton.disabled = false;
        }
    }
    
    collectFormData() {
        const formData = {};
        
        document.querySelectorAll('.form-input').forEach(input => {
            const fieldName = input.name;
            let value;
            
            if (input.type === 'checkbox') {
                value = input.checked;
            } else if (input.tagName === 'TEXTAREA') {
                // Handle repeated fields (one value per line)
                const lines = input.value.split('\n').filter(line => line.trim());
                value = lines.length > 1 ? lines : (lines[0] || '');
            } else if (input.type === 'number') {
                value = input.value ? parseFloat(input.value) : undefined;
            } else {
                value = input.value || undefined;
            }
            
            if (value !== undefined && value !== '') {
                formData[fieldName] = value;
            }
        });
        
        return formData;
    }
    
    displayValidationResults(validationResult) {
        const container = document.getElementById('validation-container');
        if (!container) return;
        
        container.innerHTML = `
            <div class="validation-result ${validationResult.valid ? 'valid' : 'invalid'}">
                <div class="validation-status">
                    ${validationResult.valid ? '✅ Valid' : '❌ Invalid'}
                </div>
                
                ${validationResult.errors.length > 0 ? `
                    <div class="validation-errors">
                        <h5>Errors:</h5>
                        <ul>
                            ${validationResult.errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                ${validationResult.warnings.length > 0 ? `
                    <div class="validation-warnings">
                        <h5>Warnings:</h5>
                        <ul>
                            ${validationResult.warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                ${validationResult.suggestions.length > 0 ? `
                    <div class="validation-suggestions">
                        <h5>Suggestions:</h5>
                        <ul>
                            ${validationResult.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
        
        // Switch to validation tab
        document.querySelector('[data-tab="validation-results"]')?.click();
    }
    
    displayTestResults(result) {
        const container = document.getElementById('response-data');
        if (!container) return;
        
        container.innerHTML = `
            <div class="response-result ${result.success ? 'success' : 'error'}">
                <div class="response-header">
                    <div class="response-status">
                        ${result.success ? '✅ Success' : '❌ Error'}
                    </div>
                    <div class="response-timing">
                        ${result.execution_time_ms.toFixed(2)}ms
                    </div>
                </div>
                
                <div class="response-details">
                    <h5>Request:</h5>
                    <pre class="json-display">${JSON.stringify(result.request_data, null, 2)}</pre>
                    
                    ${result.success && result.response_data ? `
                        <h5>Response:</h5>
                        <pre class="json-display">${JSON.stringify(result.response_data, null, 2)}</pre>
                    ` : ''}
                    
                    ${!result.success && result.error_message ? `
                        <h5>Error:</h5>
                        <div class="error-message">${result.error_message}</div>
                    ` : ''}
                </div>
            </div>
        `;
        
        // Switch to response tab
        document.querySelector('[data-tab="response-data"]')?.click();
    }
    
    addToHistory(result) {
        this.requestHistory.unshift(result);
        this.updateHistoryDisplay();
    }
    
    updateHistoryDisplay() {
        const container = document.getElementById('history-list');
        if (!container) return;
        
        if (this.requestHistory.length === 0) {
            container.innerHTML = '<div class="no-history">No request history yet.</div>';
            return;
        }
        
        container.innerHTML = this.requestHistory.map(item => `
            <div class="history-item ${item.success ? 'success' : 'error'}">
                <div class="history-header">
                    <span class="history-template">${item.template_name}</span>
                    <span class="history-time">${new Date(item.timestamp).toLocaleTimeString()}</span>
                </div>
                <div class="history-details">
                    <span class="history-status">${item.success ? '✅' : '❌'}</span>
                    <span class="history-timing">${item.execution_time_ms.toFixed(2)}ms</span>
                </div>
            </div>
        `).join('');
    }
    
    async generateCode() {
        if (!this.selectedTemplate) {
            this.showError('Please select a template first');
            return;
        }
        
        try {
            const requestData = this.collectFormData();
            
            const playgroundRequest = {
                template_id: this.selectedTemplate.template_id,
                template_name: this.selectedTemplate.template_name,
                infrastructure_plant: this.selectedTemplate.infrastructure_plant,
                request_data: requestData
            };
            
            // Generate Python code
            const pythonResponse = await fetch('/api/playground/generate/python', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(playgroundRequest)
            });
            
            if (!pythonResponse.ok) {
                throw new Error(`HTTP ${pythonResponse.status}: ${pythonResponse.statusText}`);
            }
            
            const pythonResult = await pythonResponse.json();
            this.showGeneratedCode(pythonResult.code, 'python');
            
        } catch (error) {
            console.error('❌ Error generating code:', error);
            this.showError('Failed to generate code');
        }
    }
    
    showGeneratedCode(code, language) {
        // Create modal or new window to display generated code
        const modal = document.createElement('div');
        modal.className = 'code-modal-overlay';
        modal.innerHTML = `
            <div class="code-modal">
                <div class="code-modal-header">
                    <h3>Generated ${language.toUpperCase()} Code</h3>
                    <button class="close-modal-btn">×</button>
                </div>
                <div class="code-modal-content">
                    <pre class="generated-code"><code>${this.escapeHtml(code)}</code></pre>
                </div>
                <div class="code-modal-actions">
                    <button class="btn btn-primary copy-code-btn">📋 Copy Code</button>
                    <button class="btn btn-secondary download-code-btn">💾 Download</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Setup modal events
        modal.querySelector('.close-modal-btn').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('.copy-code-btn').addEventListener('click', () => {
            navigator.clipboard.writeText(code);
            this.showSuccess('Code copied to clipboard');
        });
        
        modal.querySelector('.download-code-btn').addEventListener('click', () => {
            const blob = new Blob([code], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.selectedTemplate.template_name}.py`;
            a.click();
            URL.revokeObjectURL(url);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showSuccess(message) {
        console.log('✅', message);
        // Implement toast notification
    }
    
    showError(message) {
        console.error('❌', message);
        // Implement toast notification
    }
    
    async createSession() {
        if (!this.selectedTemplate) {
            this.showError('Please select a template first');
            return;
        }
        
        try {
            const response = await fetch('/api/playground/sessions/create', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    infrastructure_plant: this.selectedTemplate.infrastructure_plant
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            this.addSession(result.session);
            this.showSuccess(`Session created: ${result.session.session_id}`);
            
        } catch (error) {
            console.error('❌ Error creating session:', error);
            this.showError('Failed to create session');
        }
    }
    
    addSession(session) {
        this.activeSessions.push(session);
        this.updateSessionSelector();
    }
    
    updateSessionSelector() {
        const selector = document.getElementById('active-session');
        if (!selector) return;
        
        selector.innerHTML = '<option value="">No Session</option>';
        
        this.activeSessions.forEach(session => {
            const option = document.createElement('option');
            option.value = session.session_id;
            option.textContent = `${session.session_id} (${session.infrastructure_plant})`;
            selector.appendChild(option);
        });
    }
    
    async connectSession() {
        const selector = document.getElementById('active-session');
        const sessionId = selector?.value;
        
        if (!sessionId) {
            this.showError('Please select a session first');
            return;
        }
        
        try {
            const response = await fetch(`/api/playground/sessions/${sessionId}/connect`, {
                method: 'POST'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            if (result.authenticated) {
                this.currentSession = this.activeSessions.find(s => s.session_id === sessionId);
                this.updateSessionStatus(sessionId, { connection_status: 'connected', authenticated: true });
                this.showSuccess('Session connected successfully');
            } else {
                this.showError('Failed to authenticate session');
            }
            
        } catch (error) {
            console.error('❌ Error connecting session:', error);
            this.showError('Failed to connect session');
        }
    }
    
    updateSessionStatus(sessionId, status) {
        const statusContainer = document.getElementById('session-status');
        if (!statusContainer) return;
        
        const isConnected = status.connection_status === 'connected' && status.authenticated;
        
        statusContainer.innerHTML = `
            <div class="status-indicator ${isConnected ? 'connected' : 'disconnected'}">
                ${isConnected ? '🟢 Connected' : '🔴 Disconnected'}
            </div>
            <div class="session-details">
                ${sessionId ? `Session: ${sessionId}` : 'No active session'}
                ${status.request_count !== undefined ? `<br>Requests: ${status.request_count}` : ''}
            </div>
        `;
    }
    
    clearHistory() {
        this.requestHistory = [];
        this.updateHistoryDisplay();
        this.showSuccess('Request history cleared');
    }
    
    exportHistory() {
        if (this.requestHistory.length === 0) {
            this.showError('No history to export');
            return;
        }
        
        const data = JSON.stringify(this.requestHistory, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `playground_history_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.showSuccess('History exported successfully');
    }
    
    startStreaming() {
        // Implement streaming functionality
        this.showSuccess('Streaming started (not implemented yet)');
    }
    
    stopStreaming() {
        // Implement streaming functionality
        this.showSuccess('Streaming stopped (not implemented yet)');
    }
    
    clearStreaming() {
        this.streamingData = [];
        this.showSuccess('Streaming data cleared');
    }
}

// Initialize playground manager
const playgroundManager = new PlaygroundManager();