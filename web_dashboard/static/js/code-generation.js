/**
 * Code Generation JavaScript Module
 * =================================
 * 
 * Handles "Show Code" functionality for generating Python scripts from web dashboard operations.
 * Provides modals, syntax highlighting, copy/download features.
 */

class CodeGenerationManager {
    constructor() {
        this.currentCode = null;
        this.currentFilename = null;
        this.setupEventListeners();
        this.loadSyntaxHighlighter();
    }
    
    /**
     * Load syntax highlighting library (Prism.js)
     */
    loadSyntaxHighlighter() {
        // Load Prism.js CSS
        const prismCSS = document.createElement('link');
        prismCSS.rel = 'stylesheet';
        prismCSS.href = 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css';
        document.head.appendChild(prismCSS);
        
        // Load Prism.js JavaScript
        const prismJS = document.createElement('script');
        prismJS.src = 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js';
        prismJS.onload = () => {
            // Load Python syntax highlighting
            const prismPython = document.createElement('script');
            prismPython.src = 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js';
            document.head.appendChild(prismPython);
        };
        document.head.appendChild(prismJS);
    }
    
    /**
     * Setup event listeners for code generation UI
     */
    setupEventListeners() {
        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('code-modal-overlay')) {
                this.hideCodeModal();
            }
        });
        
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideCodeModal();
            }
        });
    }
    
    /**
     * Show database query code generation dialog
     */
    async showDatabaseQueryDialog(tableName = '', whereClause = '') {
        const dialog = this.createParameterDialog('Database Query Script Generator', {
            table_name: {
                label: 'Table Name',
                type: 'text',
                value: tableName,
                required: true,
                description: 'Database table to query'
            },
            where_clause: {
                label: 'WHERE Clause (optional)',
                type: 'text',
                value: whereClause,
                placeholder: 'e.g., symbol = "ES" AND timestamp > "2023-01-01"',
                description: 'SQL WHERE clause for filtering'
            },
            chunk_size: {
                label: 'Chunk Size',
                type: 'number',
                value: 10000,
                min: 1000,
                max: 1000000,
                description: 'Rows per thread/chunk'
            },
            max_workers: {
                label: 'Max Workers',
                type: 'select',
                value: 'os.cpu_count()',
                options: ['os.cpu_count()', '1', '2', '4', '8', '16'],
                description: 'Maximum number of worker threads'
            },
            threading_strategy: {
                label: 'Threading Strategy',
                type: 'select',
                value: 'ThreadPoolExecutor',
                options: ['ThreadPoolExecutor', 'ProcessPoolExecutor', 'asyncio'],
                description: 'Threading approach for parallel processing'
            },
            export_csv: {
                label: 'Export to CSV',
                type: 'checkbox',
                value: true,
                description: 'Include CSV export functionality'
            },
            simple_mode: {
                label: 'Simple Mode',
                type: 'checkbox',
                value: false,
                description: 'Generate single-threaded script'
            }
        });
        
        const result = await this.showParameterDialog(dialog);
        if (result) {
            await this.generateDatabaseQueryScript(result);
        }
    }
    
    /**
     * Show configuration script generation dialog
     */
    async showConfigurationDialog() {
        const dialog = this.createParameterDialog('Configuration Script Generator', {
            include_database: {
                label: 'Include Database Config',
                type: 'checkbox',
                value: true,
                description: 'Include database configuration'
            },
            include_rithmic: {
                label: 'Include Rithmic API Config',
                type: 'checkbox',
                value: true,
                description: 'Include Rithmic API configuration'
            },
            use_env_vars: {
                label: 'Use Environment Variables',
                type: 'checkbox',
                value: true,
                description: 'Use environment variables instead of hardcoded values'
            }
        });
        
        const result = await this.showParameterDialog(dialog);
        if (result) {
            await this.generateConfigurationScript(result);
        }
    }
    
    /**
     * Show data collection script generation dialog
     */
    async showDataCollectionDialog(contracts = [], dataTypes = []) {
        // Use selected contracts from the dashboard if available
        if (window.selectedContracts && window.selectedContracts.length > 0) {
            contracts = window.selectedContracts.map(c => c.split('_')[0]); // Get just symbols
        }
        const dialog = this.createParameterDialog('Data Collection Script Generator', {
            contracts: {
                label: 'Contracts',
                type: 'text',
                value: contracts.join(', '),
                placeholder: 'ES, NQ, YM, RTY',
                description: 'Comma-separated list of futures contracts'
            },
            data_types: {
                label: 'Data Types',
                type: 'multiselect',
                value: dataTypes,
                options: ['level1', 'level3', 'trades', 'quotes', 'historical'],
                description: 'Types of market data to collect'
            },
            collection_mode: {
                label: 'Collection Mode',
                type: 'select',
                value: 'continuous',
                options: ['continuous', 'historical', 'sample'],
                description: 'Data collection mode'
            }
        });
        
        const result = await this.showParameterDialog(dialog);
        if (result) {
            // Process contracts
            result.contracts = result.contracts.split(',').map(c => c.trim()).filter(c => c);
            await this.generateDataCollectionScript(result);
        }
    }
    
    /**
     * Generate database query script
     */
    async generateDatabaseQueryScript(params) {
        try {
            this.showLoadingModal('Generating database query script...');
            
            const response = await fetch('/api/code-generation/database-query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const code = await response.text();
            const filename = `query_${params.table_name}_${params.simple_mode ? 'simple' : 'multithreaded'}.py`;
            
            this.hideLoadingModal();
            this.showCodeModal(code, `Database Query Script - ${params.table_name}`, filename);
            
        } catch (error) {
            this.hideLoadingModal();
            this.showError(`Failed to generate database query script: ${error.message}`);
        }
    }
    
    /**
     * Generate configuration script
     */
    async generateConfigurationScript(params) {
        try {
            this.showLoadingModal('Generating configuration script...');
            
            const response = await fetch('/api/code-generation/configuration', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const code = await response.text();
            const filename = 'configuration_setup.py';
            
            this.hideLoadingModal();
            this.showCodeModal(code, 'Configuration Setup Script', filename);
            
        } catch (error) {
            this.hideLoadingModal();
            this.showError(`Failed to generate configuration script: ${error.message}`);
        }
    }
    
    /**
     * Generate data collection script
     */
    async generateDataCollectionScript(params) {
        try {
            this.showLoadingModal('Generating data collection script...');
            
            const response = await fetch('/api/code-generation/data-collection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const code = await response.text();
            const filename = `data_collection_${params.collection_mode}.py`;
            
            this.hideLoadingModal();
            this.showCodeModal(code, 'Data Collection Script', filename);
            
        } catch (error) {
            this.hideLoadingModal();
            this.showError(`Failed to generate data collection script: ${error.message}`);
        }
    }
    
    /**
     * Create parameter dialog
     */
    createParameterDialog(title, parameters) {
        const dialog = document.createElement('div');
        dialog.className = 'code-modal-overlay';
        
        const content = document.createElement('div');
        content.className = 'code-modal parameter-dialog';
        
        content.innerHTML = `
            <div class="modal-header">
                <h3>${title}</h3>
                <button type="button" class="close-btn" onclick="codeGenerator.hideParameterDialog()">×</button>
            </div>
            <div class="modal-body">
                <form id="parameter-form">
                    ${this.createParameterFields(parameters)}
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="codeGenerator.hideParameterDialog()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="codeGenerator.submitParameterDialog()">Generate Script</button>
            </div>
        `;
        
        dialog.appendChild(content);
        return dialog;
    }
    
    /**
     * Create parameter form fields
     */
    createParameterFields(parameters) {
        return Object.entries(parameters).map(([key, config]) => {
            let field = '';
            
            switch (config.type) {
                case 'text':
                case 'number':
                    field = `
                        <input type="${config.type}" 
                               id="${key}" 
                               name="${key}" 
                               value="${config.value || ''}"
                               placeholder="${config.placeholder || ''}"
                               ${config.required ? 'required' : ''}
                               ${config.min ? `min="${config.min}"` : ''}
                               ${config.max ? `max="${config.max}"` : ''}>
                    `;
                    break;
                    
                case 'select':
                    const options = config.options.map(opt => 
                        `<option value="${opt}" ${opt === config.value ? 'selected' : ''}>${opt}</option>`
                    ).join('');
                    field = `<select id="${key}" name="${key}">${options}</select>`;
                    break;
                    
                case 'multiselect':
                    const checkboxes = config.options.map(opt => 
                        `<label><input type="checkbox" name="${key}" value="${opt}" 
                         ${config.value.includes(opt) ? 'checked' : ''}> ${opt}</label>`
                    ).join('<br>');
                    field = `<div class="checkbox-group">${checkboxes}</div>`;
                    break;
                    
                case 'checkbox':
                    field = `
                        <input type="checkbox" 
                               id="${key}" 
                               name="${key}" 
                               ${config.value ? 'checked' : ''}>
                    `;
                    break;
            }
            
            return `
                <div class="form-group">
                    <label for="${key}">${config.label}</label>
                    ${field}
                    ${config.description ? `<small class="form-help">${config.description}</small>` : ''}
                </div>
            `;
        }).join('');
    }
    
    /**
     * Show parameter dialog
     */
    showParameterDialog(dialog) {
        return new Promise((resolve) => {
            this.currentParameterResolve = resolve;
            document.body.appendChild(dialog);
        });
    }
    
    /**
     * Hide parameter dialog
     */
    hideParameterDialog() {
        const dialog = document.querySelector('.parameter-dialog');
        if (dialog) {
            dialog.closest('.code-modal-overlay').remove();
        }
        if (this.currentParameterResolve) {
            this.currentParameterResolve(null);
            this.currentParameterResolve = null;
        }
    }
    
    /**
     * Submit parameter dialog
     */
    submitParameterDialog() {
        const form = document.getElementById('parameter-form');
        const formData = new FormData(form);
        const params = {};
        
        // Process regular form fields
        for (let [key, value] of formData.entries()) {
            if (params[key]) {
                // Handle multi-select
                if (!Array.isArray(params[key])) {
                    params[key] = [params[key]];
                }
                params[key].push(value);
            } else {
                params[key] = value;
            }
        }
        
        // Process checkboxes (including unchecked ones)
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.name && !params.hasOwnProperty(checkbox.name)) {
                params[checkbox.name] = checkbox.checked;
            }
        });
        
        // Convert numeric fields
        const numberInputs = form.querySelectorAll('input[type="number"]');
        numberInputs.forEach(input => {
            if (params[input.name]) {
                params[input.name] = parseInt(params[input.name]);
            }
        });
        
        this.hideParameterDialog();
        if (this.currentParameterResolve) {
            this.currentParameterResolve(params);
            this.currentParameterResolve = null;
        }
    }
    
    /**
     * Show code modal with syntax highlighting
     */
    showCodeModal(code, title, filename) {
        this.currentCode = code;
        this.currentFilename = filename;
        
        // Remove existing modal
        const existingModal = document.querySelector('.code-modal-overlay');
        if (existingModal) {
            existingModal.remove();
        }
        
        const modal = document.createElement('div');
        modal.className = 'code-modal-overlay';
        
        modal.innerHTML = `
            <div class="code-modal">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-sm" onclick="codeGenerator.copyToClipboard()">📋 Copy</button>
                        <button type="button" class="btn btn-sm" onclick="codeGenerator.downloadCode()">💾 Download</button>
                        <button type="button" class="close-btn" onclick="codeGenerator.hideCodeModal()">×</button>
                    </div>
                </div>
                <div class="modal-body">
                    <pre><code class="language-python" id="generated-code">${this.escapeHtml(code)}</code></pre>
                </div>
                <div class="modal-footer">
                    <div class="code-info">
                        <span>📄 ${filename}</span>
                        <span>📏 ${code.split('\n').length} lines</span>
                        <span>📊 ${code.length} characters</span>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Apply syntax highlighting if Prism is loaded
        if (window.Prism) {
            window.Prism.highlightAll();
        }
    }
    
    /**
     * Hide code modal
     */
    hideCodeModal() {
        const modal = document.querySelector('.code-modal-overlay');
        if (modal) {
            modal.remove();
        }
        this.currentCode = null;
        this.currentFilename = null;
    }
    
    /**
     * Show loading modal
     */
    showLoadingModal(message) {
        const modal = document.createElement('div');
        modal.className = 'code-modal-overlay loading-modal';
        modal.innerHTML = `
            <div class="code-modal loading">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    /**
     * Hide loading modal
     */
    hideLoadingModal() {
        const modal = document.querySelector('.loading-modal');
        if (modal) {
            modal.remove();
        }
    }
    
    /**
     * Copy code to clipboard
     */
    async copyToClipboard() {
        if (!this.currentCode) return;
        
        try {
            await navigator.clipboard.writeText(this.currentCode);
            this.showSuccess('Code copied to clipboard!');
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = this.currentCode;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showSuccess('Code copied to clipboard!');
        }
    }
    
    /**
     * Download code as file
     */
    downloadCode() {
        if (!this.currentCode || !this.currentFilename) return;
        
        const blob = new Blob([this.currentCode], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = this.currentFilename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        this.showSuccess(`Downloaded ${this.currentFilename}`);
    }
    
    /**
     * Escape HTML characters
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * Show success notification
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    /**
     * Show error notification
     */
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    /**
     * Show notification
     */
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            z-index: 2001;
            background-color: ${type === 'success' ? 'var(--success-color)' : 'var(--danger-color)'};
            box-shadow: 0 4px 15px var(--shadow-color);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
}

// Initialize global code generator instance
window.codeGenerator = new CodeGenerationManager();