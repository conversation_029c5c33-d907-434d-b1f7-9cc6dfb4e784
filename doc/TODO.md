## Enhanced Rithmic API Implementation Plan

### Phase 1: Setup and Protocol Buffer Compilation
1. **Study the API Documentation**: Think hard and carefully review the Rithmic API specification document located at `doc/Reference_Guide.md` to understand message formats, endpoints, and data structures.

2. **Compile Protocol Buffers**: Generate Python classes from all `.proto` files in the `proto/` directory using the Protocol Buffer compiler (`protoc`). Ensure all generated Python files are properly organized and importable.

### Phase 2: WebSocket Client Infrastructure
3. **Create WebSocket Client Class**: Implement a robust WebSocket client class (`RithmicWebSocketClient`) with the following capabilities:
   - Connection management (connect, disconnect, reconnect on failure)
   - Message serialization/deserialization using the compiled protobuf classes
   - Error handling and logging
   - Asynchronous message handling

4. **Implement Authentication Flow**: Create a two-step authentication process:

   **Step A - System Discovery:**
   - Open WebSocket connection to Rithmic API
   - Send `RequestRithmicSystemInfo` message immediately upon connection
   - Parse `ResponseRithmicSystemInfo` to extract available system names
   - Close the WebSocket connection
   - Store system information for use in login

   **Step B - Login Authentication:**
   - Open a new WebSocket connection
   - Send `RequestLogin` message with these exact credentials:
     - `user`: "PP-013155"
     - `password`: "b7neA8k6JA"
     - `system_name`: "Rithmic Paper Trading"
     - `gateway`: "Chicago Area"
     - `app_name`: Generate a generic application name (e.g., "PythonClient")
     - `version`: Generate a version string (e.g., "1.0.0")
   - Handle login response and maintain authenticated session

### Phase 3: Data Collection Framework
5. **Data Collection Standards**: For all subsequent data collection scripts, implement these requirements:
   - Request ALL available fields from each API endpoint (do not filter or limit fields)
   - Write complete raw responses to timestamped files in a `data/` directory for later analysis
   - Include proper error handling and retry logic
   - Log all requests and responses with timestamps

### Phase 4: Contract Discovery
6. **ES Futures Front Month Contract Discovery**: Create `find_front_month_contract.py` with dual approach:
   - **Primary Method**: Use `RequestFrontMonthContract` message to directly request the nearest ES futures contract
   - **Fallback Method**: If the direct endpoint fails or returns no data:
     - Use `RequestSearchSymbols` to search for ES futures contracts
     - Parse expiration dates from search results
     - Select the contract with the nearest expiration date
   - Save the identified contract symbol for use in subsequent scripts

   **Required Protocol Files:**
   - `proto/request_front_month_contract.proto`
   - `proto/response_front_month_contract.proto`
   - `proto/request_search_symbols.proto`
   - `proto/response_search_symbols.proto`

### Phase 5: Market Data Subscription
7. **Level 1 Market Data**: Create `subscribe_level1_data.py` to:
   - Subscribe to real-time Level 1 market data for the identified ES contract
   - Request ALL available fields, specifically ensuring inclusion of:
     - `LAST_TRADE` (last trade price and size)
     - `BBO` (Best Bid/Offer prices and sizes)
     - Any other available Level 1 fields
   - Continuously write incoming data to timestamped files

   **Required Protocol Files:**
   - `proto/request_market_data_update.proto`
   - `proto/response_market_data_update.proto`

8. **Level 3 Depth by Order**: Create `subscribe_depth_by_order.py` to:
   - Subscribe to real-time depth-by-order updates (Level 3 data)
   - Handle all order book change events
   - Process end-of-event markers properly
   - Write all order book updates to timestamped files

   **Required Protocol Files:**
   - `proto/request_depth_by_order_updates.proto`
   - `proto/response_depth_by_order_updates.proto`
   - `proto/depth_by_order.proto`
   - `proto/depth_by_order_end_event.proto`

9. **Order Book Snapshot**: Create `request_orderbook_snapshot.py` to:
   - Request a complete snapshot of the current order book state
   - Execute this IMMEDIATELY after subscribing to depth-by-order updates to prevent data gaps
   - Use this snapshot as the baseline state before processing incremental updates
   - Save snapshot data to timestamped files

   **Required Protocol Files:**
   - `proto/request_depth_by_order_snapshot.proto`
   - `proto/response_depth_by_order_snapshot.proto`

### Implementation Notes:
- Implement comprehensive logging throughout all components
- Create a configuration file for credentials and connection parameters
- Include proper exception handling and graceful shutdown procedures