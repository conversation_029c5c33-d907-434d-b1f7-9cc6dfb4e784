#!/usr/bin/env python3
"""
Advanced Rithmic API Usage Example

This example demonstrates advanced features of the Rithmic API:
- Multi-contract management and resolution
- Concurrent market data subscriptions
- Database integration and data persistence
- Performance optimization techniques
- Error handling and recovery

Suitable for production-ready applications.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict, deque

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
    from src.rithmic_api.config import config
    from src.utils.contract_cache import ContractCache
    from src.utils.contract_resolver import ContractResolver
    from src.utils.multi_contract_manager import MultiContractManager
    RITHMIC_AVAILABLE = True
except ImportError as e:
    print(f"❌ Rithmic components not available: {e}")
    print("💡 Make sure you're running from the project root directory")
    RITHMIC_AVAILABLE = False

try:
    from database.database_manager import get_database_manager
    from database.models import <PERSON>ym<PERSON>, BestBidOffer, LastTrade
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    print("⚠️ Database components not available - continuing without persistence")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AdvancedRithmicExample:
    """Advanced example showcasing multi-contract operations."""
    
    def __init__(self):
        self.client = None
        self.cache = None
        self.resolver = None
        self.manager = None
        self.db_manager = None
        
        # Data storage
        self.market_data = defaultdict(lambda: {
            'bbo': deque(maxlen=100),  # Keep last 100 BBO updates
            'trades': deque(maxlen=100),  # Keep last 100 trades
            'stats': {'messages': 0, 'last_update': None}
        })
        
        # Performance tracking
        self.performance_stats = {
            'start_time': None,
            'messages_received': 0,
            'contracts_resolved': 0,
            'database_writes': 0
        }
    
    async def run_example(self):
        """Run the complete advanced example."""
        if not RITHMIC_AVAILABLE:
            print("❌ Cannot run example - components not available")
            return
        
        print("🚀 Starting Advanced Rithmic API Example")
        print("=" * 60)
        
        self.performance_stats['start_time'] = datetime.now()
        
        try:
            # Step 1: Initialize all components
            await self._initialize_components()
            
            # Step 2: Authenticate and setup
            await self._authenticate()
            
            # Step 3: Resolve multiple contracts
            contracts = await self._resolve_contracts()
            
            # Step 4: Setup concurrent subscriptions
            if contracts:
                await self._setup_subscriptions(contracts)
            
            # Step 5: Collect data with analytics
            await self._collect_data_with_analytics()
            
            # Step 6: Show comprehensive results
            self._show_comprehensive_results()
            
        except Exception as e:
            print(f"❌ Advanced example failed: {e}")
            logger.error(f"Example error: {e}", exc_info=True)
        
        finally:
            await self._cleanup()
        
        print("\n✅ Advanced example completed!")
    
    async def _initialize_components(self):
        """Initialize all components with advanced configuration."""
        print("\n🔧 Step 1: Initializing advanced components...")
        
        # Initialize WebSocket client with optimizations
        self.client = RithmicWebSocketClient()
        print("  ✅ WebSocket client initialized")
        
        # Initialize contract cache with database support
        self.cache = ContractCache(use_database=DATABASE_AVAILABLE)
        cache_status = self.cache.get_cache_stats()
        print(f"  ✅ Contract cache initialized ({cache_status['total_contracts']} contracts)")
        
        # Initialize contract resolver
        self.resolver = ContractResolver(self.cache)
        print("  ✅ Contract resolver initialized")
        
        # Initialize multi-contract manager
        self.manager = MultiContractManager(self.client)
        print("  ✅ Multi-contract manager initialized")
        
        # Initialize database if available
        if DATABASE_AVAILABLE:
            try:
                self.db_manager = get_database_manager()
                if self.db_manager.test_connection():
                    print("  ✅ Database connection established")
                else:
                    print("  ⚠️ Database connection failed - continuing without persistence")
                    DATABASE_AVAILABLE = False
            except Exception as e:
                print(f"  ⚠️ Database initialization failed: {e}")
                DATABASE_AVAILABLE = False
        
        print(f"  📊 Initialization complete! Database: {'✅' if DATABASE_AVAILABLE else '❌'}")
    
    async def _authenticate(self):
        """Authenticate with advanced error handling."""
        print("\n🔐 Step 2: Advanced authentication...")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"  🔄 Authentication attempt {attempt + 1}/{max_retries}")
                
                # System discovery with timeout
                systems = await asyncio.wait_for(
                    self.client.discover_systems(), 
                    timeout=30
                )
                
                if not systems:
                    raise Exception("No systems available")
                
                print(f"  📋 Found {len(systems)} systems")
                
                # Prefer paper trading, fallback to first available
                system_name = "Rithmic Paper Trading"
                if system_name not in systems:
                    system_name = systems[0]
                
                print(f"  🎯 Using system: {system_name}")
                
                # Login with timeout
                await asyncio.wait_for(
                    self.client.login(system_name, infra_type="TICKER_PLANT"),
                    timeout=30
                )
                
                print("  ✅ Authentication successful!")
                return
                
            except asyncio.TimeoutError:
                print(f"  ⏱️ Attempt {attempt + 1} timed out")
            except Exception as e:
                print(f"  ❌ Attempt {attempt + 1} failed: {e}")
            
            if attempt < max_retries - 1:
                print(f"  ⏳ Retrying in 5 seconds...")
                await asyncio.sleep(5)
        
        raise Exception("Authentication failed after all retries")
    
    async def _resolve_contracts(self):
        """Resolve multiple contracts using different methods."""
        print("\n🔍 Step 3: Resolving multiple contracts...")
        
        # Define contract specifications
        contract_specs = [
            {'symbol': 'ES', 'exchange': 'CME', 'type': 'front_month'},
            {'symbol': 'NQ', 'exchange': 'CME', 'type': 'front_month'},
            {'symbol': 'YM', 'exchange': 'CME', 'type': 'front_month'},
            {'symbol': 'CL', 'exchange': 'NYMEX', 'type': 'front_month'},
            {'symbol': 'GC', 'exchange': 'CME', 'type': 'front_month'}
        ]
        
        resolved_contracts = []
        
        for spec in contract_specs:
            try:
                print(f"  🔍 Resolving {spec['symbol']}@{spec['exchange']}...")
                
                if spec['type'] == 'front_month':
                    # Get front month contract
                    contract_info = await self.client.find_front_month_contract(
                        product=spec['symbol'],
                        exchange=spec['exchange']
                    )
                    
                    if contract_info:
                        resolved_contracts.append(contract_info)
                        print(f"    ✅ Resolved to: {contract_info.get('symbol', 'N/A')}")
                        self.performance_stats['contracts_resolved'] += 1
                    else:
                        print(f"    ❌ Could not resolve {spec['symbol']}")
                
            except Exception as e:
                print(f"    ❌ Error resolving {spec['symbol']}: {e}")
        
        print(f"  📊 Successfully resolved {len(resolved_contracts)} contracts")
        return resolved_contracts
    
    async def _setup_subscriptions(self, contracts):
        """Setup concurrent market data subscriptions."""
        print("\n📡 Step 4: Setting up concurrent subscriptions...")
        
        subscription_tasks = []
        
        for contract in contracts:
            symbol = contract.get('symbol')
            exchange = contract.get('exchange')
            
            print(f"  📊 Subscribing to {symbol}@{exchange}...")
            
            try:
                # Create subscription task
                task = asyncio.create_task(
                    self.client.subscribe_to_market_data(
                        symbol=symbol,
                        exchange=exchange,
                        update_type='LEVEL_1'
                    )
                )
                subscription_tasks.append(task)
                
                # Register with manager
                self.manager.add_subscription(f"{symbol}@{exchange}", contract)
                
            except Exception as e:
                print(f"    ❌ Subscription failed for {symbol}: {e}")
        
        # Wait for all subscriptions to complete
        if subscription_tasks:
            print(f"  ⏳ Waiting for {len(subscription_tasks)} subscriptions...")
            
            results = await asyncio.gather(*subscription_tasks, return_exceptions=True)
            
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            error_count = len(results) - success_count
            
            print(f"  📊 Subscriptions: {success_count} successful, {error_count} failed")
    
    async def _collect_data_with_analytics(self):
        """Collect data with real-time analytics and persistence."""
        print("\n📈 Step 5: Collecting data with analytics...")
        
        # Data collection configuration
        collection_duration = 15  # seconds
        analytics_interval = 5   # seconds
        
        print(f"  ⏱️ Collecting data for {collection_duration} seconds...")
        print(f"  📊 Analytics updates every {analytics_interval} seconds")
        
        # Setup data callback with persistence
        def data_callback(template_id, message, raw_data):
            """Advanced callback with analytics and persistence."""
            try:
                self.performance_stats['messages_received'] += 1
                
                timestamp = datetime.now()
                symbol = getattr(message, 'symbol', 'Unknown')
                
                # Update per-symbol statistics
                if symbol != 'Unknown':
                    self.market_data[symbol]['stats']['messages'] += 1
                    self.market_data[symbol]['stats']['last_update'] = timestamp
                
                # Handle different message types
                if template_id == 151:  # Best Bid Offer
                    self._process_bbo_message(symbol, message, timestamp)
                    
                elif template_id == 150:  # Last Trade
                    self._process_trade_message(symbol, message, timestamp)
                
                # Persist to database if available
                if DATABASE_AVAILABLE and self.db_manager:
                    asyncio.create_task(self._persist_message(template_id, message, timestamp))
                
            except Exception as e:
                logger.error(f"Error in data callback: {e}")
        
        # Start listening for updates
        listen_task = asyncio.create_task(
            self.client.listen_for_updates(callback=data_callback)
        )
        
        # Start analytics reporting
        analytics_task = asyncio.create_task(
            self._run_analytics(analytics_interval, collection_duration)
        )
        
        # Wait for collection period
        await asyncio.sleep(collection_duration)
        
        # Stop tasks
        listen_task.cancel()
        analytics_task.cancel()
        
        try:
            await listen_task
        except asyncio.CancelledError:
            pass
        
        try:
            await analytics_task
        except asyncio.CancelledError:
            pass
        
        print(f"  ✅ Data collection completed!")
    
    def _process_bbo_message(self, symbol, message, timestamp):
        """Process Best Bid Offer message."""
        try:
            bbo_data = {
                'timestamp': timestamp,
                'bid_price': getattr(message, 'bid_price', 0),
                'ask_price': getattr(message, 'ask_price', 0),
                'bid_size': getattr(message, 'bid_size', 0),
                'ask_size': getattr(message, 'ask_size', 0)
            }
            
            self.market_data[symbol]['bbo'].append(bbo_data)
            
        except Exception as e:
            logger.error(f"Error processing BBO for {symbol}: {e}")
    
    def _process_trade_message(self, symbol, message, timestamp):
        """Process Last Trade message."""
        try:
            trade_data = {
                'timestamp': timestamp,
                'price': getattr(message, 'trade_price', 0),
                'size': getattr(message, 'trade_size', 0),
                'condition': getattr(message, 'trade_condition', '')
            }
            
            self.market_data[symbol]['trades'].append(trade_data)
            
        except Exception as e:
            logger.error(f"Error processing trade for {symbol}: {e}")
    
    async def _persist_message(self, template_id, message, timestamp):
        """Persist message to database."""
        try:
            if template_id == 151:  # Best Bid Offer
                # Create BBO record (simplified)
                # In practice, you'd use the actual DAO methods
                self.performance_stats['database_writes'] += 1
                
            elif template_id == 150:  # Last Trade
                # Create trade record (simplified)
                # In practice, you'd use the actual DAO methods
                self.performance_stats['database_writes'] += 1
                
        except Exception as e:
            logger.error(f"Error persisting message: {e}")
    
    async def _run_analytics(self, interval, total_duration):
        """Run real-time analytics reporting."""
        try:
            elapsed = 0
            
            while elapsed < total_duration:
                await asyncio.sleep(interval)
                elapsed += interval
                
                print(f"\n    📊 Analytics Update ({elapsed}s / {total_duration}s):")
                
                # Overall statistics
                msg_rate = self.performance_stats['messages_received'] / elapsed
                print(f"      📈 Message Rate: {msg_rate:.1f} msg/sec")
                print(f"      📊 Total Messages: {self.performance_stats['messages_received']}")
                
                # Per-symbol statistics
                active_symbols = [s for s in self.market_data.keys() if self.market_data[s]['stats']['messages'] > 0]
                print(f"      🎯 Active Symbols: {len(active_symbols)}")
                
                for symbol in sorted(active_symbols):
                    stats = self.market_data[symbol]['stats']
                    bbo_count = len(self.market_data[symbol]['bbo'])
                    trade_count = len(self.market_data[symbol]['trades'])
                    
                    print(f"        {symbol}: {stats['messages']} msg, {bbo_count} BBO, {trade_count} trades")
                
                if DATABASE_AVAILABLE:
                    print(f"      💾 Database Writes: {self.performance_stats['database_writes']}")
                
        except asyncio.CancelledError:
            pass
    
    def _show_comprehensive_results(self):
        """Show comprehensive results and analytics."""
        print("\n📊 Step 6: Comprehensive Results Analysis")
        print("=" * 50)
        
        # Performance summary
        end_time = datetime.now()
        duration = (end_time - self.performance_stats['start_time']).total_seconds()
        
        print(f"📈 Performance Summary:")
        print(f"  ⏱️ Total Duration: {duration:.1f} seconds")
        print(f"  📊 Messages Received: {self.performance_stats['messages_received']:,}")
        print(f"  🔍 Contracts Resolved: {self.performance_stats['contracts_resolved']}")
        print(f"  📈 Average Rate: {self.performance_stats['messages_received']/duration:.1f} msg/sec")
        
        if DATABASE_AVAILABLE:
            print(f"  💾 Database Writes: {self.performance_stats['database_writes']}")
        
        # Per-symbol analysis
        print(f"\n📋 Per-Symbol Analysis:")
        
        for symbol, data in sorted(self.market_data.items()):
            if data['stats']['messages'] == 0:
                continue
            
            print(f"\n  📊 {symbol}:")
            print(f"    Total Messages: {data['stats']['messages']}")
            print(f"    BBO Updates: {len(data['bbo'])}")
            print(f"    Trade Updates: {len(data['trades'])}")
            
            if data['stats']['last_update']:
                last_update = data['stats']['last_update'].strftime("%H:%M:%S")
                print(f"    Last Update: {last_update}")
            
            # Show latest BBO if available
            if data['bbo']:
                latest_bbo = data['bbo'][-1]
                print(f"    Latest BBO: {latest_bbo['bid_price']} x {latest_bbo['ask_price']}")
            
            # Show latest trade if available
            if data['trades']:
                latest_trade = data['trades'][-1]
                print(f"    Latest Trade: {latest_trade['price']} ({latest_trade['size']})")
        
        # System health check
        print(f"\n🏥 System Health:")
        print(f"  🔗 WebSocket: {'✅ Connected' if self.client.is_connected else '❌ Disconnected'}")
        print(f"  🔐 Authenticated: {'✅ Yes' if self.client.is_authenticated else '❌ No'}")
        print(f"  💾 Database: {'✅ Available' if DATABASE_AVAILABLE else '❌ Unavailable'}")
        print(f"  📦 Cache: {self.cache.get_cache_stats()['total_contracts']} contracts")
    
    async def _cleanup(self):
        """Advanced cleanup with proper resource management."""
        print("\n🧹 Advanced cleanup...")
        
        # Stop multi-contract manager
        if self.manager:
            self.manager.remove_all_subscriptions()
            print("  ✅ Multi-contract manager cleaned up")
        
        # Disconnect WebSocket client
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            print("  ✅ WebSocket client disconnected")
        
        # Close database connections
        if self.db_manager:
            # Database manager handles its own cleanup
            print("  ✅ Database connections released")
        
        print("  ✅ All resources cleaned up")


async def main():
    """Main entry point."""
    example = AdvancedRithmicExample()
    await example.run_example()


if __name__ == "__main__":
    print(__doc__)
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Advanced example interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)