#!/usr/bin/env python3
"""
Basic Rithmic API Usage Example

This example demonstrates the fundamental operations of the Rithmic API:
- System discovery and authentication
- Symbol searching and contract resolution
- Basic market data subscription
- Simple database integration

Perfect for getting started with the API.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
    from src.rithmic_api.config import config
    from src.utils.contract_cache import ContractCache
    RITHMIC_AVAILABLE = True
except ImportError as e:
    print(f"❌ Rithmic components not available: {e}")
    print("💡 Make sure you're running from the project root directory")
    RITHMIC_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BasicRithmicExample:
    """Basic example of Rithmic API usage."""
    
    def __init__(self):
        self.client = None
        self.cache = None
        self.received_data = []
    
    async def run_example(self):
        """Run the complete basic example."""
        if not RITHMIC_AVAILABLE:
            print("❌ Cannot run example - components not available")
            return
        
        print("🚀 Starting Basic Rithmic API Example")
        print("=" * 50)
        
        try:
            # Step 1: Initialize components
            await self._initialize()
            
            # Step 2: Authenticate with API
            await self._authenticate()
            
            # Step 3: Search for a symbol
            symbol_info = await self._search_symbol()
            
            # Step 4: Subscribe to market data
            if symbol_info:
                await self._subscribe_to_data(symbol_info)
            
            # Step 5: Collect data for a short period
            await self._collect_data()
            
            # Step 6: Show results
            self._show_results()
            
        except Exception as e:
            print(f"❌ Example failed: {e}")
            logger.error(f"Example error: {e}", exc_info=True)
        
        finally:
            await self._cleanup()
        
        print("\n✅ Basic example completed!")
    
    async def _initialize(self):
        """Initialize client and cache."""
        print("\n📋 Step 1: Initializing components...")
        
        # Initialize WebSocket client
        self.client = RithmicWebSocketClient()
        print("  ✅ WebSocket client initialized")
        
        # Initialize contract cache
        self.cache = ContractCache(use_database=True)
        print("  ✅ Contract cache initialized")
        
        print("  📊 Cache status:", self.cache.get_cache_stats()['total_contracts'], "contracts")
    
    async def _authenticate(self):
        """Authenticate with the Rithmic API."""
        print("\n🔐 Step 2: Authenticating with API...")
        
        # Discover available systems
        print("  🔍 Discovering available systems...")
        systems = await self.client.discover_systems()
        
        if not systems:
            raise Exception("No systems available")
        
        print(f"  📋 Found {len(systems)} systems: {', '.join(systems[:3])}...")
        
        # Choose paper trading system
        system_name = "Rithmic Paper Trading"
        if system_name not in systems:
            system_name = systems[0]
        
        print(f"  🎯 Using system: {system_name}")
        
        # Login to ticker plant
        print("  🔗 Logging in to ticker plant...")
        await self.client.login(system_name, infra_type="TICKER_PLANT")
        
        print("  ✅ Authentication successful!")
    
    async def _search_symbol(self):
        """Search for a symbol to use in the example."""
        print("\n🔍 Step 3: Searching for symbols...")
        
        # Search for ES futures (S&P 500 E-mini)
        print("  📈 Searching for ES futures...")
        
        try:
            results = await self.client.search_symbols(
                text="ES*",
                exchange="CME",
                type="FUTURE"
            )
            
            if results:
                symbol_info = results[0]  # Use first result
                print(f"  ✅ Found symbol: {symbol_info.get('symbol', 'N/A')} on {symbol_info.get('exchange', 'N/A')}")
                print(f"     Product: {symbol_info.get('product_code', 'N/A')}")
                print(f"     Type: {symbol_info.get('instrument_type', 'N/A')}")
                return symbol_info
            else:
                print("  ⚠️ No ES symbols found, trying NQ...")
                
                # Fallback to NQ futures
                results = await self.client.search_symbols(
                    text="NQ*",
                    exchange="CME", 
                    type="FUTURE"
                )
                
                if results:
                    symbol_info = results[0]
                    print(f"  ✅ Found fallback symbol: {symbol_info.get('symbol', 'N/A')}")
                    return symbol_info
                else:
                    print("  ❌ No symbols found")
                    return None
        
        except Exception as e:
            print(f"  ❌ Symbol search failed: {e}")
            return None
    
    async def _subscribe_to_data(self, symbol_info):
        """Subscribe to market data for the symbol."""
        print("\n📡 Step 4: Subscribing to market data...")
        
        symbol = symbol_info.get('symbol')
        exchange = symbol_info.get('exchange')
        
        print(f"  📊 Subscribing to {symbol}@{exchange}...")
        
        try:
            # Subscribe to market data updates
            await self.client.subscribe_to_market_data(
                symbol=symbol,
                exchange=exchange,
                update_type='LEVEL_1'
            )
            
            print("  ✅ Market data subscription successful!")
            
        except Exception as e:
            print(f"  ❌ Subscription failed: {e}")
            raise
    
    async def _collect_data(self):
        """Collect market data for a short period."""
        print("\n📈 Step 5: Collecting market data...")
        
        print("  ⏱️ Collecting data for 10 seconds...")
        
        # Set up data collection callback
        def data_callback(template_id, message, raw_data):
            """Callback for received market data."""
            try:
                timestamp = datetime.now()
                
                # Determine message type
                if template_id == 151:  # Best Bid Offer
                    symbol = getattr(message, 'symbol', 'N/A')
                    bid_price = getattr(message, 'bid_price', 0)
                    ask_price = getattr(message, 'ask_price', 0)
                    
                    data_entry = {
                        'timestamp': timestamp,
                        'type': 'BBO',
                        'symbol': symbol,
                        'bid_price': bid_price,
                        'ask_price': ask_price
                    }
                    
                elif template_id == 150:  # Last Trade
                    symbol = getattr(message, 'symbol', 'N/A')
                    trade_price = getattr(message, 'trade_price', 0)
                    trade_size = getattr(message, 'trade_size', 0)
                    
                    data_entry = {
                        'timestamp': timestamp,
                        'type': 'Trade',
                        'symbol': symbol,
                        'trade_price': trade_price,
                        'trade_size': trade_size
                    }
                    
                else:
                    # Other message types
                    data_entry = {
                        'timestamp': timestamp,
                        'type': f'Template_{template_id}',
                        'data': str(message)[:100] + '...' if len(str(message)) > 100 else str(message)
                    }
                
                self.received_data.append(data_entry)
                
                # Print live updates (limited to avoid spam)
                if len(self.received_data) <= 10 or len(self.received_data) % 10 == 0:
                    print(f"    📊 Received {data_entry['type']}: {data_entry.get('symbol', 'N/A')}")
                
            except Exception as e:
                logger.error(f"Error in data callback: {e}")
        
        # Start listening for updates
        listen_task = asyncio.create_task(
            self.client.listen_for_updates(callback=data_callback)
        )
        
        # Wait for 10 seconds
        await asyncio.sleep(10)
        
        # Stop listening
        listen_task.cancel()
        try:
            await listen_task
        except asyncio.CancelledError:
            pass
        
        print(f"  ✅ Data collection completed! Received {len(self.received_data)} messages")
    
    def _show_results(self):
        """Show the collected results."""
        print("\n📊 Step 6: Results Summary")
        print("=" * 30)
        
        if not self.received_data:
            print("  ℹ️ No data collected")
            return
        
        # Count by message type
        type_counts = {}
        for entry in self.received_data:
            msg_type = entry['type']
            type_counts[msg_type] = type_counts.get(msg_type, 0) + 1
        
        print(f"  📈 Total Messages: {len(self.received_data)}")
        print("  📋 Message Types:")
        
        for msg_type, count in sorted(type_counts.items()):
            print(f"    {msg_type}: {count} messages")
        
        # Show sample data
        print("\n  🔍 Sample Data (last 3 messages):")
        for entry in self.received_data[-3:]:
            timestamp = entry['timestamp'].strftime("%H:%M:%S.%f")[:-3]
            print(f"    [{timestamp}] {entry['type']}: {entry.get('symbol', 'N/A')}")
            
            if 'bid_price' in entry:
                print(f"      Bid: {entry['bid_price']}, Ask: {entry['ask_price']}")
            elif 'trade_price' in entry:
                print(f"      Price: {entry['trade_price']}, Size: {entry['trade_size']}")
    
    async def _cleanup(self):
        """Clean up resources."""
        print("\n🧹 Cleaning up...")
        
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            print("  ✅ WebSocket client disconnected")


async def main():
    """Main entry point."""
    example = BasicRithmicExample()
    await example.run_example()


if __name__ == "__main__":
    print(__doc__)
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Example interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)