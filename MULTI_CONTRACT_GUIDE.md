# Multi-Contract Subscription Guide

## 🚀 Overview

The Rithmic API SDK now supports subscribing to multiple contracts simultaneously with advanced contract resolution, wildcard patterns, and comprehensive subscription management.

## ✨ Key Features

- **Multiple Contract Types**: Specific contracts, continuous contracts (@ES), wildcard patterns (ES*, *.CME)
- **Automatic Resolution**: Resolves contract specifications to actual tradeable symbols
- **Smart Caching**: Caches contract information for fast lookups and offline resolution
- **Batched Subscriptions**: Rate-limited subscription processing for optimal performance
- **Error Handling**: Robust error handling with configurable continuation policies
- **Subscription Limits**: Automatic detection and enforcement of API limits

## 📝 Contract Specification Formats

### Specific Contracts
```bash
ESU5.CME NQU5.CME YMU5.CME    # Specific expiration contracts
```

### Continuous Contracts (Front Month)
```bash
@ES @NQ @YM @RTY              # Resolves to current front month
```

### Wildcard Patterns
```bash
ES*                           # All ES contracts
*.CME                        # All CME contracts
*                            # All contracts (use with caution!)
NQ* YM*                      # Multiple product families
```

### Mixed Specifications
```bash
@ES NQU5.CME YM*             # Mix of continuous, specific, and wildcard
```

## ⚙️ Configuration

### Environment Variables (.env file)

```bash
# Contract Subscription List (space-separated)
CONTRACTS_TO_SUBSCRIBE=@ES @NQ

# Maximum concurrent subscriptions (recommended: 10-50)
MAX_CONCURRENT_CONTRACTS=10

# Contract resolution settings
CACHE_CONTRACTS=true
CACHE_TTL_HOURS=24
AUTO_REFRESH_CACHE=true

# Subscription behavior
SUBSCRIBE_ON_STARTUP=true
VALIDATE_CONTRACTS_BEFORE_SUBSCRIBE=true
CONTINUE_ON_INVALID_CONTRACT=true

# Performance settings
BATCH_SUBSCRIPTION_SIZE=5
SUBSCRIPTION_DELAY_MS=100
```

### Configuration Examples

#### Conservative Setup (Recommended)
```bash
CONTRACTS_TO_SUBSCRIBE=@ES @NQ
MAX_CONCURRENT_CONTRACTS=10
BATCH_SUBSCRIPTION_SIZE=5
```

#### Active Trading Setup
```bash
CONTRACTS_TO_SUBSCRIBE=@ES @NQ @YM @RTY @GC @CL
MAX_CONCURRENT_CONTRACTS=20
BATCH_SUBSCRIPTION_SIZE=10
```

#### Market Scanner Setup
```bash
CONTRACTS_TO_SUBSCRIBE=ES* NQ* YM* RTY*
MAX_CONCURRENT_CONTRACTS=50
BATCH_SUBSCRIPTION_SIZE=20
```

## 🏃‍♂️ Quick Start

### 1. Configure Your Contracts
Edit `.env` file:
```bash
CONTRACTS_TO_SUBSCRIBE=@ES @NQ @YM
```

### 2. Run Multi-Contract Data Collection
```bash
# Use configured contracts
python3 run_multi_contract_level1.py

# Override with specific contracts
python3 run_multi_contract_level1.py -c "@ES @NQ YM*"

# Run for specific duration
python3 run_multi_contract_level1.py -d 300
```

### 3. Enhanced Single/Multi Mode
```bash
# Auto-detect mode from configuration
python3 run_level1_enhanced.py

# Force single-contract mode
python3 run_level1_enhanced.py --single

# Multi-contract with duration
python3 run_level1_enhanced.py -d 600
```

## 🧪 Testing and Optimization

### Test Subscription Limits
```bash
python3 run_test_limits.py
```

This script will:
- Progressively test increasing numbers of subscriptions
- Identify the maximum concurrent subscriptions supported
- Recommend optimal `MAX_CONCURRENT_CONTRACTS` setting

### Populate Contract Cache
```bash
python3 run_populate_cache.py
```

Benefits:
- Faster contract resolution
- Offline wildcard pattern matching
- Reduced API calls during subscription

## 📊 Available Scripts

| Script | Description | Usage |
|--------|-------------|-------|
| `run_multi_contract_level1.py` | Multi-contract Level 1 data | `python3 run_multi_contract_level1.py -c "@ES @NQ"` |
| `run_level1_enhanced.py` | Enhanced Level 1 (auto-mode) | `python3 run_level1_enhanced.py --single` |
| `run_populate_cache.py` | Contract cache population | `python3 run_populate_cache.py` |
| `run_test_limits.py` | Subscription limits testing | `python3 run_test_limits.py` |

## 🔧 Advanced Usage

### Programmatic API

```python
from src.utils import MultiContractManager, ContractResolver
from src.rithmic_api import RithmicWebSocketClient

# Initialize client and manager
client = RithmicWebSocketClient()
manager = MultiContractManager(client)

# Subscribe to contracts
contract_specs = ["@ES", "@NQ", "YM*"]
results = await manager.subscribe_to_contracts(contract_specs)

# Check subscription status
stats = manager.get_subscription_stats()
print(f"Active subscriptions: {stats['successful_subscriptions']}")
```

### Contract Resolution

```python
from src.utils import ContractResolver

resolver = ContractResolver(client)

# Resolve continuous contracts
resolved = await resolver.resolve_single_contract("@ES")
print(f"@ES resolves to: {resolved[0].resolved_symbol}")

# Resolve wildcard patterns
resolved = await resolver.resolve_single_contract("ES*")
print(f"ES* matches {len(resolved)} contracts")
```

### Contract Cache Management

```python
from src.utils import contract_cache

# Get cache statistics
stats = contract_cache.get_cache_stats()
print(f"Cached contracts: {stats['total_contracts']}")

# Search contracts
es_contracts = contract_cache.get_contracts_by_product("ES")
cme_contracts = contract_cache.get_contracts_by_exchange("CME")
```

## 🎯 Best Practices

### Performance
- Start with conservative `MAX_CONCURRENT_CONTRACTS` (10-20)
- Use `run_test_limits.py` to find optimal limits
- Enable contract caching for frequently used patterns
- Use batch processing for large contract lists

### Reliability
- Enable `VALIDATE_CONTRACTS_BEFORE_SUBSCRIBE=true`
- Set `CONTINUE_ON_INVALID_CONTRACT=true` for robust operation
- Use continuous contracts (@ES) instead of specific expiries
- Monitor subscription success rates

### Resource Management
- Limit wildcard patterns in production
- Use reasonable `CACHE_TTL_HOURS` (24 hours recommended)
- Clear old data with `CLEAR_DATA_ON_START=true`
- Monitor disk space for data collection

## 🚨 Troubleshooting

### Common Issues

#### "No contracts resolved"
- Check contract specifications in `.env`
- Verify cache is populated (`run_populate_cache.py`)
- Ensure market is open for live contract discovery

#### "Subscription failures"
- Reduce `MAX_CONCURRENT_CONTRACTS`
- Increase `SUBSCRIPTION_DELAY_MS`
- Check API connection and authentication

#### "Cache expired/empty"
- Run `python3 run_populate_cache.py`
- Check `CACHE_TTL_HOURS` setting
- Verify API connectivity for cache refresh

### Debug Mode
Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📋 Migration from Single-Contract

### Existing Scripts
Existing single-contract scripts continue to work:
```bash
python3 run_level1_data.py      # Still works
python3 run_find_contract.py    # Still works
```

### Enhanced Versions
Use enhanced versions for new features:
```bash
python3 run_level1_enhanced.py  # Auto single/multi mode
python3 run_multi_contract_level1.py  # Pure multi-contract
```

### Configuration Migration
Add to existing `.env`:
```bash
# Add these lines to existing .env
CONTRACTS_TO_SUBSCRIBE=@ES
MAX_CONCURRENT_CONTRACTS=10
CACHE_CONTRACTS=true
```

## 🔮 Future Enhancements

Planned features:
- Order management with multiple contracts
- Portfolio-level position tracking
- Risk management across contract families
- Historical data collection for multiple contracts
- Real-time analytics and alerting

## 💡 Tips

1. **Start Small**: Begin with 2-3 contracts and scale up
2. **Test First**: Always run `run_test_limits.py` in your environment
3. **Cache Wisely**: Populate cache during off-hours for faster operation
4. **Monitor Performance**: Track subscription success rates and data flow
5. **Use Continuous**: Prefer `@ES` over specific `ESU5.CME` for dynamic contracts

## 📞 Support

For issues or questions:
1. Check logs in `data/logs/` directory
2. Run diagnostic scripts (`run_test_limits.py`)
3. Verify configuration in `.env` file
4. Review subscription statistics and error messages