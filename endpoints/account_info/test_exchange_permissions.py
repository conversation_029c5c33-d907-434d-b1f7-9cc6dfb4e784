#!/usr/bin/env python3

"""
Test Rithmic List Exchange Permissions endpoints (Templates 342/343).

This script demonstrates how to retrieve exchange permissions and entitlements
from the Rithmic API, showing which exchanges and products are accessible.

SAFETY: This script only performs READ-ONLY exchange permissions requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_list_exchange_permissions_pb2
import response_list_exchange_permissions_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message
from timeout_manager import TimeoutManager

logger = logging.getLogger(__name__)

async def test_exchange_permissions(account_id: Optional[str] = None, fcm_id: Optional[str] = None,
                                   ib_id: Optional[str] = None, output_format: str = "human") -> bool:
    """
    Test exchange permissions functionality.
    
    Args:
        account_id: Optional specific account ID to query
        fcm_id: Optional FCM ID filter
        ib_id: Optional IB ID filter
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🏛️  TESTING EXCHANGE PERMISSIONS ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.LIST_EXCHANGE_PERMISSIONS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.LIST_EXCHANGE_PERMISSIONS_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"FCM ID: {fcm_id or 'All FCMs'}")
    print(f"IB ID: {ib_id or 'All IBs'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "exchange_permissions_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create exchange permissions request
        request = request_list_exchange_permissions_pb2.RequestListExchangePermissions()
        request.template_id = TemplateIDs.LIST_EXCHANGE_PERMISSIONS_REQUEST
        request.user_msg.append("Exchange permissions request from endpoint tester")
        
        # Set filters - FCM ID and IB ID are required by server even if empty
        # Account ID is also required by the server
        request.account_id = account_id if account_id else "demo"
        
        # FCM ID is required by the server (error 1039 if missing)
        # Use default paper trading FCM ID if none provided
        request.fcm_id = fcm_id if fcm_id else "demo"
            
        # IB ID is also required by the server  
        request.ib_id = ib_id if ib_id else "demo"
        
        # Send exchange permissions request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending exchange permissions request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send exchange permissions request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response with advanced timeout management
        print("⏳ Waiting for exchange permissions response...")
        
        timeout_manager = TimeoutManager("exchange_permissions", InfraType.ORDER_PLANT)
        
        async def receive_response():
            return await connection.receive_message(timeout=30)  # Base timeout, will be managed by timeout_manager
        
        timeout_result = await timeout_manager.execute_with_progressive_timeout(
            receive_response, "exchange permissions response"
        )
        
        if not timeout_result.success:
            print(f"❌ No response received: {timeout_result.error_message}")
            return False
            
        response_bytes = timeout_result.result
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_list_exchange_permissions_pb2.ResponseListExchangePermissions()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 EXCHANGE PERMISSIONS ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Exchange permissions request successful!")
            
            # Display exchange permissions information
            permissions = []
            if hasattr(response, 'exchange') and response.exchange:
                print(f"\n🏛️  EXCHANGE PERMISSIONS ({len(response.exchange)} exchanges):")
                print("-" * 100)
                
                for i, exchange in enumerate(response.exchange):
                    permission_info = {
                        "exchange": exchange,
                        "account_id": "",
                        "fcm_id": "",
                        "ib_id": "",
                        "permission_type": "",
                        "permission_level": "",
                        "product_type": "",
                        "symbol": "",
                        "enabled": True,
                        "market_data": False,
                        "trading": False,
                        "historical_data": False,
                        "entitlement_id": "",
                        "start_date": "",
                        "end_date": "",
                        "fee_schedule": "",
                        "data_vendor": ""
                    }
                    
                    # Get corresponding permission data
                    if hasattr(response, 'account_id') and i < len(response.account_id):
                        permission_info["account_id"] = response.account_id[i]
                    
                    if hasattr(response, 'fcm_id') and i < len(response.fcm_id):
                        permission_info["fcm_id"] = response.fcm_id[i]
                    
                    if hasattr(response, 'ib_id') and i < len(response.ib_id):
                        permission_info["ib_id"] = response.ib_id[i]
                    
                    if hasattr(response, 'permission_type') and i < len(response.permission_type):
                        permission_info["permission_type"] = response.permission_type[i]
                    
                    if hasattr(response, 'permission_level') and i < len(response.permission_level):
                        permission_info["permission_level"] = response.permission_level[i]
                    
                    if hasattr(response, 'product_type') and i < len(response.product_type):
                        permission_info["product_type"] = response.product_type[i]
                    
                    if hasattr(response, 'symbol') and i < len(response.symbol):
                        permission_info["symbol"] = response.symbol[i]
                    
                    if hasattr(response, 'enabled') and i < len(response.enabled):
                        permission_info["enabled"] = response.enabled[i]
                    
                    if hasattr(response, 'market_data') and i < len(response.market_data):
                        permission_info["market_data"] = response.market_data[i]
                    
                    if hasattr(response, 'trading') and i < len(response.trading):
                        permission_info["trading"] = response.trading[i]
                    
                    if hasattr(response, 'historical_data') and i < len(response.historical_data):
                        permission_info["historical_data"] = response.historical_data[i]
                    
                    if hasattr(response, 'entitlement_id') and i < len(response.entitlement_id):
                        permission_info["entitlement_id"] = response.entitlement_id[i]
                    
                    if hasattr(response, 'start_date') and i < len(response.start_date):
                        permission_info["start_date"] = response.start_date[i]
                    
                    if hasattr(response, 'end_date') and i < len(response.end_date):
                        permission_info["end_date"] = response.end_date[i]
                    
                    if hasattr(response, 'fee_schedule') and i < len(response.fee_schedule):
                        permission_info["fee_schedule"] = response.fee_schedule[i]
                    
                    if hasattr(response, 'data_vendor') and i < len(response.data_vendor):
                        permission_info["data_vendor"] = response.data_vendor[i]
                    
                    permissions.append(permission_info)
                    
                    # Display permission information
                    print(f"{i+1:3d}. Exchange: {permission_info['exchange']}")
                    
                    # Account hierarchy
                    if permission_info['account_id'] or permission_info['fcm_id'] or permission_info['ib_id']:
                        print(f"     📍 ACCOUNT HIERARCHY:")
                        if permission_info['fcm_id']:
                            print(f"       FCM ID: {permission_info['fcm_id']}")
                        if permission_info['ib_id']:
                            print(f"       IB ID: {permission_info['ib_id']}")
                        if permission_info['account_id']:
                            print(f"       Account ID: {permission_info['account_id']}")
                    
                    # Permission details
                    print(f"     🔐 PERMISSION DETAILS:")
                    if permission_info['permission_type']:
                        print(f"       Type: {permission_info['permission_type']}")
                    if permission_info['permission_level']:
                        print(f"       Level: {permission_info['permission_level']}")
                    if permission_info['entitlement_id']:
                        print(f"       Entitlement ID: {permission_info['entitlement_id']}")
                    
                    # Access rights
                    print(f"     ✅ ACCESS RIGHTS:")
                    access_types = []
                    if permission_info['market_data']:
                        access_types.append("Market Data")
                    if permission_info['trading']:
                        access_types.append("Trading")
                    if permission_info['historical_data']:
                        access_types.append("Historical Data")
                    
                    if access_types:
                        print(f"       Granted: {', '.join(access_types)}")
                    else:
                        print(f"       Granted: None specified")
                    
                    status = "✅ ENABLED" if permission_info['enabled'] else "❌ DISABLED"
                    print(f"       Status: {status}")
                    
                    # Product scope
                    if permission_info['product_type'] or permission_info['symbol']:
                        print(f"     📋 PRODUCT SCOPE:")
                        if permission_info['product_type']:
                            print(f"       Product Type: {permission_info['product_type']}")
                        if permission_info['symbol']:
                            print(f"       Symbol: {permission_info['symbol']}")
                    
                    # Validity period
                    if permission_info['start_date'] or permission_info['end_date']:
                        print(f"     📅 VALIDITY PERIOD:")
                        if permission_info['start_date']:
                            print(f"       Start Date: {permission_info['start_date']}")
                        if permission_info['end_date']:
                            print(f"       End Date: {permission_info['end_date']}")
                    
                    # Commercial terms
                    if permission_info['fee_schedule'] or permission_info['data_vendor']:
                        print(f"     💰 COMMERCIAL TERMS:")
                        if permission_info['fee_schedule']:
                            print(f"       Fee Schedule: {permission_info['fee_schedule']}")
                        if permission_info['data_vendor']:
                            print(f"       Data Vendor: {permission_info['data_vendor']}")
                    
                    print()
                
                # Exchange permissions analysis
                print(f"\n📈 EXCHANGE PERMISSIONS SUMMARY:")
                print(f"Total Permission Records: {len(permissions)}")
                
                # Exchange breakdown
                exchange_counts = {}
                permission_type_counts = {}
                product_type_counts = {}
                data_vendor_counts = {}
                
                for perm in permissions:
                    exchange = perm['exchange'] or 'Unknown'
                    exchange_counts[exchange] = exchange_counts.get(exchange, 0) + 1
                    
                    if perm['permission_type']:
                        ptype = perm['permission_type']
                        permission_type_counts[ptype] = permission_type_counts.get(ptype, 0) + 1
                    
                    if perm['product_type']:
                        prod_type = perm['product_type']
                        product_type_counts[prod_type] = product_type_counts.get(prod_type, 0) + 1
                    
                    if perm['data_vendor']:
                        vendor = perm['data_vendor']
                        data_vendor_counts[vendor] = data_vendor_counts.get(vendor, 0) + 1
                
                if exchange_counts:
                    print(f"\nExchange Access:")
                    for exchange, count in sorted(exchange_counts.items()):
                        enabled_count = len([p for p in permissions if p['exchange'] == exchange and p['enabled']])
                        print(f"  • {exchange}: {count} permissions ({enabled_count} enabled)")
                
                if permission_type_counts:
                    print(f"\nPermission Types:")
                    for ptype, count in sorted(permission_type_counts.items()):
                        print(f"  • {ptype}: {count}")
                
                if product_type_counts:
                    print(f"\nProduct Type Coverage:")
                    for prod_type, count in sorted(product_type_counts.items()):
                        print(f"  • {prod_type}: {count}")
                
                if data_vendor_counts:
                    print(f"\nData Vendors:")
                    for vendor, count in sorted(data_vendor_counts.items()):
                        print(f"  • {vendor}: {count}")
                
                # Access rights analysis
                market_data_perms = [p for p in permissions if p['market_data']]
                trading_perms = [p for p in permissions if p['trading']]
                historical_perms = [p for p in permissions if p['historical_data']]
                enabled_perms = [p for p in permissions if p['enabled']]
                
                print(f"\nAccess Rights Summary:")
                print(f"  • Market Data Access: {len(market_data_perms)} permissions")
                print(f"  • Trading Access: {len(trading_perms)} permissions")
                print(f"  • Historical Data Access: {len(historical_perms)} permissions")
                print(f"  • Total Enabled: {len(enabled_perms)} ({len(enabled_perms)/len(permissions)*100:.1f}%)")
                
                # Coverage by exchange
                unique_exchanges = set(p['exchange'] for p in permissions if p['exchange'])
                print(f"\nExchange Coverage:")
                print(f"  • Total Exchanges: {len(unique_exchanges)}")
                
                # Major exchanges check
                major_exchanges = ['CME', 'CBOT', 'NYMEX', 'COMEX', 'NYSE', 'NASDAQ', 'ICE', 'EUREX']
                covered_major = [ex for ex in major_exchanges if ex in unique_exchanges]
                
                if covered_major:
                    print(f"  • Major Exchanges Covered: {', '.join(covered_major)}")
                
                # Entitlement analysis
                unique_entitlements = set(p['entitlement_id'] for p in permissions if p['entitlement_id'])
                if unique_entitlements:
                    print(f"  • Unique Entitlements: {len(unique_entitlements)}")
            
            else:
                print(f"\n📋 No exchange permissions found")
                print("   This could indicate:")
                print("   • No exchange permissions configured for the account")
                print("   • Insufficient permissions to view permission details")
                print("   • Account/FCM/IB filter may be too restrictive")
                print("   • No active exchange entitlements")
            
            # CSV output
            if output_format == "csv" and permissions:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Exchange", "Account_ID", "FCM_ID", "IB_ID", "Permission_Type", 
                              "Permission_Level", "Product_Type", "Market_Data", "Trading", 
                              "Historical_Data", "Enabled", "Start_Date", "End_Date", "Data_Vendor"]
                print(",".join(csv_headers))
                
                for perm in permissions:
                    values = [
                        perm['exchange'],
                        perm['account_id'],
                        perm['fcm_id'],
                        perm['ib_id'],
                        perm['permission_type'],
                        perm['permission_level'],
                        perm['product_type'],
                        str(perm['market_data']),
                        str(perm['trading']),
                        str(perm['historical_data']),
                        str(perm['enabled']),
                        perm['start_date'],
                        perm['end_date'],
                        perm['data_vendor']
                    ]
                    print(",".join(values))
            
            # Permission optimization insights
            if permissions:
                print(f"\n💡 EXCHANGE PERMISSION INSIGHTS:")
                print(f"✅ Found {len(permissions)} exchange permission records")
                
                # Coverage assessment
                if len(unique_exchanges) > 10:
                    print("• Excellent exchange coverage for diversified trading")
                elif len(unique_exchanges) > 5:
                    print("• Good exchange coverage")
                else:
                    print("• Limited exchange coverage")
                    print("  Consider additional exchange entitlements for diversification")
                
                # Permission completeness
                full_access_perms = [p for p in permissions if p['market_data'] and p['trading'] and p['historical_data']]
                if len(full_access_perms) > len(permissions) * 0.8:
                    print("• Comprehensive access rights across exchanges")
                elif len(full_access_perms) > len(permissions) * 0.5:
                    print("• Moderate access rights coverage")
                else:
                    print("• Limited access rights")
                    print("  Review permission levels for required functionality")
                
                # Enabled status
                if len(enabled_perms) == len(permissions):
                    print("• All permissions are active and enabled")
                elif len(enabled_perms) > len(permissions) * 0.9:
                    print("• Most permissions are enabled")
                else:
                    print(f"• {len(permissions) - len(enabled_perms)} permissions are disabled")
                    print("  Check with administrator to enable required permissions")
                
                # Trading capability assessment
                trading_exchanges = set(p['exchange'] for p in trading_perms if p['exchange'])
                market_data_exchanges = set(p['exchange'] for p in market_data_perms if p['exchange'])
                
                if len(trading_exchanges) == len(market_data_exchanges):
                    print("• Consistent trading and market data access")
                elif len(trading_exchanges) < len(market_data_exchanges):
                    print("• More market data access than trading access")
                    print("  Consider trading permissions for additional exchanges")
                else:
                    print("• Trading access exceeds market data access")
                    print("  Consider market data subscriptions for complete coverage")
                
                # Data vendor diversity
                if len(data_vendor_counts) > 1:
                    print("• Multiple data vendors for redundancy")
                elif len(data_vendor_counts) == 1:
                    vendor = list(data_vendor_counts.keys())[0]
                    print(f"• Single data vendor: {vendor}")
                else:
                    print("• No specific data vendor information available")
        
        else:
            print("❌ Exchange permissions request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions to view exchange entitlements")
            print("   • Check if account/FCM/IB IDs are valid")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without filters to see all permissions")
            print("   • Verify exchange permissions are properly configured")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing exchange permissions: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for exchange permissions testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Exchange Permissions endpoint (Templates 342/343)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_exchange_permissions.py
  python test_exchange_permissions.py --account-id "123456"
  python test_exchange_permissions.py --fcm-id "FCM01" --ib-id "IB01"
  python test_exchange_permissions.py --format csv

Safety:
  This script performs READ-ONLY exchange permissions requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays exchange permissions including:
  - Exchange access and entitlements
  - Permission types and levels
  - Access rights (market data, trading, historical)
  - Product type coverage and symbol access
  - Validity periods and commercial terms
  - Comprehensive permission analysis and optimization insights
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--fcm-id",
        type=str,
        help="FCM ID filter (optional)"
    )
    
    parser.add_argument(
        "--ib-id",
        type=str,
        help="IB ID filter (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC EXCHANGE PERMISSIONS ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY EXCHANGE PERMISSIONS OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_exchange_permissions(
        account_id=args.account_id,
        fcm_id=args.fcm_id,
        ib_id=args.ib_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Exchange permissions test completed successfully!")
        return 0
    else:
        print("\n❌ Exchange permissions test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)