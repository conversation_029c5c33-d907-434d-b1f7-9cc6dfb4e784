#!/usr/bin/env python3

"""
Test Rithmic Account List endpoints (Templates 302/303).

This script demonstrates how to retrieve and display account information
from the Rithmic API, including account details and permissions.

SAFETY: This script only performs READ-ONLY account information retrieval.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_account_list_pb2
import response_account_list_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_account_list(output_format: str = "human") -> bool:
    """
    Test account list retrieval functionality.
    
    Args:
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("👥 TESTING ACCOUNT LIST ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.ACCOUNT_LIST_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.ACCOUNT_LIST_RESPONSE} (Response)")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "account_list_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create account list request
        request = request_account_list_pb2.RequestAccountList()
        request.template_id = TemplateIDs.ACCOUNT_LIST_REQUEST
        request.user_msg.append("Account list request from endpoint tester")
        
        # Set user type (required field) - TRADER type for paper trading
        request.user_type = request_account_list_pb2.RequestAccountList.UserType.USER_TYPE_TRADER
        
        # Send account list request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending account list request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send account list request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for account list response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_account_list_pb2.ResponseAccountList()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 ACCOUNT LIST ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        # Empty response codes might indicate success for some account endpoints
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Account list request successful!")
            
            # Display account information
            accounts = []
            if hasattr(response, 'account') and response.account:
                print(f"\n💼 AVAILABLE ACCOUNTS ({len(response.account)}):")
                print("-" * 80)
                
                for i, account_id in enumerate(response.account):
                    account_info = {"account_id": account_id}
                    
                    # Get corresponding account details
                    if hasattr(response, 'fcm_id') and i < len(response.fcm_id):
                        account_info["fcm_id"] = response.fcm_id[i]
                    
                    if hasattr(response, 'ib_id') and i < len(response.ib_id):
                        account_info["ib_id"] = response.ib_id[i]
                    
                    if hasattr(response, 'account_name') and i < len(response.account_name):
                        account_info["account_name"] = response.account_name[i]
                    
                    if hasattr(response, 'account_type') and i < len(response.account_type):
                        account_info["account_type"] = response.account_type[i]
                    
                    if hasattr(response, 'currency') and i < len(response.currency):
                        account_info["currency"] = response.currency[i]
                    
                    accounts.append(account_info)
                    
                    # Display account details
                    if output_format == "human":
                        print(f"{i+1:2d}. Account ID: {account_id}")
                        for key, value in account_info.items():
                            if key != "account_id" and value:
                                print(f"    {key.replace('_', ' ').title()}: {value}")
                        print()
                    
                    elif output_format == "csv" and i == 0:
                        # Print CSV header
                        headers = list(account_info.keys())
                        print(",".join(headers))
                    
                    if output_format == "csv":
                        values = [str(account_info.get(h, "")) for h in account_info.keys()]
                        print(",".join(values))
                
                # Summary statistics
                print(f"\n📈 ACCOUNT SUMMARY:")
                print(f"Total Accounts: {len(accounts)}")
                
                # Analyze account types
                if accounts:
                    account_types = set()
                    currencies = set()
                    fcm_ids = set()
                    
                    for acc in accounts:
                        if acc.get("account_type"):
                            account_types.add(acc["account_type"])
                        if acc.get("currency"):
                            currencies.add(acc["currency"])
                        if acc.get("fcm_id"):
                            fcm_ids.add(acc["fcm_id"])
                    
                    if account_types:
                        print(f"Account Types: {', '.join(account_types)}")
                    if currencies:
                        print(f"Currencies: {', '.join(currencies)}")
                    if fcm_ids:
                        print(f"FCM IDs: {', '.join(fcm_ids)}")
                
                # Check for paper trading accounts
                paper_accounts = [acc for acc in accounts 
                                if "paper" in str(acc.get("account_id", "")).lower() or
                                   "test" in str(acc.get("account_id", "")).lower() or
                                   "demo" in str(acc.get("account_id", "")).lower()]
                
                if paper_accounts:
                    print(f"\n📄 PAPER/TEST ACCOUNTS FOUND: {len(paper_accounts)}")
                    for acc in paper_accounts:
                        print(f"   • {acc['account_id']}")
                
                # Check configured account
                configured_account = config_obj.username
                account_ids = [acc["account_id"] for acc in accounts]
                if configured_account in account_ids:
                    print(f"\n✅ Configured account found: {configured_account}")
                else:
                    print(f"\n⚠️  Configured account not found: {configured_account}")
                    if account_ids:
                        print(f"   Available accounts: {', '.join(account_ids[:5])}")
                        if len(account_ids) > 5:
                            print(f"   ... and {len(account_ids) - 5} more")
            
            else:
                print("\n⚠️  No accounts found in response")
                print("   This may indicate:")
                print("   • No accounts are accessible with current credentials")
                print("   • Account permissions may be restricted")
                print("   • Connection to wrong system or environment")
        
        else:
            print("❌ Account list request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify credentials are correct")
            print("   • Check account permissions")
            print("   • Ensure connection to correct system")
            print("   • Try connecting to different infrastructure plant")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing account list: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for account list testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Account List endpoint (Templates 302/303)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_account_list.py
  python test_account_list.py --format json
  python test_account_list.py --format csv
  python test_account_list.py --log-level DEBUG

Safety:
  This script performs READ-ONLY account information retrieval only.
  Requires authentication but no account modifications performed.
  
Output:
  The script will display all accessible accounts with their details
  including account IDs, FCM IDs, account types, and currencies.
        """
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC ACCOUNT LIST ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY ACCOUNT INFORMATION RETRIEVAL ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_account_list(output_format=args.format)
    
    if success:
        print("\n✅ Account list test completed successfully!")
        return 0
    else:
        print("\n❌ Account list test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)