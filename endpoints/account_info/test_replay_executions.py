#!/usr/bin/env python3

"""
Test Rithmic Replay Executions endpoints (Templates 3506/3507).

This script demonstrates how to replay historical execution data
from the Rithmic API, showing detailed fill and execution information.

SAFETY: This script only performs READ-ONLY execution replay requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_replay_executions_pb2
import response_replay_executions_pb2
import exchange_order_notification_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class ExecutionAnalyzer:
    """Analyzes execution replay data for trading performance insights."""
    
    def __init__(self):
        self.executions = []
        self.symbols_seen = set()
        self.accounts_seen = set()
        self.execution_venues = set()
        self.fill_data = []
        
    def add_execution(self, execution_data: Dict[str, Any]):
        """Add an execution to the analysis."""
        self.executions.append(execution_data)
        
        symbol = execution_data.get('symbol', '')
        if symbol:
            self.symbols_seen.add(symbol)
        
        account = execution_data.get('account_id', '')
        if account:
            self.accounts_seen.add(account)
        
        exchange = execution_data.get('exchange', '')
        if exchange:
            self.execution_venues.add(exchange)
        
        # Track fill data for analysis
        if execution_data.get('fill_size', 0) > 0:
            self.fill_data.append({
                'symbol': symbol,
                'fill_size': execution_data.get('fill_size', 0),
                'fill_price': execution_data.get('fill_price', 0),
                'transaction_type': execution_data.get('transaction_type', ''),
                'fill_time': execution_data.get('fill_time', ''),
                'fill_date': execution_data.get('fill_date', '')
            })
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive execution analysis."""
        total_executions = len(self.executions)
        total_fills = len(self.fill_data)
        
        summary = {
            "total_executions": total_executions,
            "total_fills": total_fills,
            "unique_symbols": len(self.symbols_seen),
            "unique_accounts": len(self.accounts_seen),
            "execution_venues": len(self.execution_venues),
            "symbols": list(self.symbols_seen),
            "accounts": list(self.accounts_seen),
            "venues": list(self.execution_venues),
            "fill_analysis": {},
            "volume_analysis": {},
            "price_analysis": {}
        }
        
        if self.fill_data:
            # Volume analysis
            total_volume = sum(fill['fill_size'] for fill in self.fill_data)
            avg_fill_size = total_volume / len(self.fill_data) if self.fill_data else 0
            max_fill = max(fill['fill_size'] for fill in self.fill_data)
            min_fill = min(fill['fill_size'] for fill in self.fill_data)
            
            summary["volume_analysis"] = {
                "total_volume": total_volume,
                "average_fill_size": avg_fill_size,
                "max_fill_size": max_fill,
                "min_fill_size": min_fill
            }
            
            # Price analysis
            prices = [fill['fill_price'] for fill in self.fill_data if fill['fill_price'] > 0]
            if prices:
                avg_price = sum(prices) / len(prices)
                max_price = max(prices)
                min_price = min(prices)
                
                summary["price_analysis"] = {
                    "average_fill_price": avg_price,
                    "max_fill_price": max_price,
                    "min_fill_price": min_price,
                    "price_range": max_price - min_price
                }
            
            # Fill size distribution
            small_fills = len([f for f in self.fill_data if f['fill_size'] <= 100])
            medium_fills = len([f for f in self.fill_data if 100 < f['fill_size'] <= 1000])
            large_fills = len([f for f in self.fill_data if f['fill_size'] > 1000])
            
            summary["fill_analysis"] = {
                "small_fills": small_fills,
                "medium_fills": medium_fills,
                "large_fills": large_fills,
                "small_fill_pct": (small_fills / total_fills * 100) if total_fills > 0 else 0,
                "medium_fill_pct": (medium_fills / total_fills * 100) if total_fills > 0 else 0,
                "large_fill_pct": (large_fills / total_fills * 100) if total_fills > 0 else 0
            }
        
        return summary

async def test_replay_executions(account_id: Optional[str] = None, fcm_id: Optional[str] = None,
                                ib_id: Optional[str] = None, start_index: Optional[int] = None,
                                finish_index: Optional[int] = None, output_format: str = "human") -> bool:
    """
    Test replay executions functionality.
    
    Args:
        account_id: Optional specific account ID to query
        fcm_id: Optional FCM ID filter
        ib_id: Optional IB ID filter
        start_index: Optional starting index for execution replay
        finish_index: Optional ending index for execution replay
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    analyzer = ExecutionAnalyzer()
    
    print("=" * 60)
    print("🔄 TESTING REPLAY EXECUTIONS ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.REPLAY_EXECUTIONS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.REPLAY_EXECUTIONS_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"FCM ID: {fcm_id or 'All FCMs'}")
    print(f"IB ID: {ib_id or 'All IBs'}")
    print(f"Start Index: {start_index or 'Beginning'}")
    print(f"Finish Index: {finish_index or 'End'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "replay_executions_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create replay executions request
        request = request_replay_executions_pb2.RequestReplayExecutions()
        request.template_id = TemplateIDs.REPLAY_EXECUTIONS_REQUEST
        request.user_msg.append("Replay executions request from endpoint tester")
        
        # Set filters - FCM ID and IB ID are required by server even if empty
        # Account ID is also required by the server
        request.account_id = account_id if account_id else "demo"
        
        # FCM ID is required by the server (error 1039 if missing)
        # Use default paper trading FCM ID if none provided
        request.fcm_id = fcm_id if fcm_id else "demo"
            
        # IB ID is also required by the server  
        request.ib_id = ib_id if ib_id else "demo"
            
        if start_index is not None:
            request.start_index = start_index
            
        if finish_index is not None:
            request.finish_index = finish_index
        
        # Send replay executions request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending replay executions request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send replay executions request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for replay executions response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_replay_executions_pb2.ResponseReplayExecutions()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 REPLAY EXECUTIONS ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Replay executions request successful!")
            
            # Listen for execution notifications
            print("\n🔄 LISTENING FOR EXECUTION REPLAY NOTIFICATIONS...")
            print("(Waiting up to 20 seconds for execution data...)")
            
            execution_count = 0
            start_time = asyncio.get_event_loop().time()
            timeout = 20.0  # Wait up to 20 seconds for execution data
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                try:
                    # Wait for execution notifications
                    exec_bytes = await connection.receive_message(timeout=3.0)
                    
                    if not exec_bytes:
                        continue  # Timeout, keep listening
                    
                    # Parse notification to determine type
                    exec_info = parse_message(exec_bytes)
                    if not exec_info:
                        continue
                    
                    template_id = exec_info.template_id
                    
                    # Check if this is an exchange order notification (execution data)
                    if template_id == TemplateIDs.EXCHANGE_ORDER_NOTIFICATION:
                        # Parse execution notification
                        exec_notification = exchange_order_notification_pb2.ExchangeOrderNotification()
                        exec_notification.ParseFromString(exec_bytes)
                        
                        execution_count += 1
                        
                        # Extract execution data
                        execution_data = {
                            "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                            "account_id": getattr(exec_notification, 'account_id', ''),
                            "symbol": getattr(exec_notification, 'symbol', ''),
                            "exchange": getattr(exec_notification, 'exchange', ''),
                            "order_num": getattr(exec_notification, 'order_num', ''),
                            "exchange_order_id": getattr(exec_notification, 'exchange_order_id', ''),
                            "transaction_type": getattr(exec_notification, 'transaction_type', ''),
                            "fill_id": getattr(exec_notification, 'fill_id', ''),
                            "fill_price": getattr(exec_notification, 'fill_price', 0),
                            "fill_size": getattr(exec_notification, 'fill_size', 0),
                            "fill_time": getattr(exec_notification, 'fill_time', ''),
                            "fill_date": getattr(exec_notification, 'fill_date', ''),
                            "confirmation": getattr(exec_notification, 'confirmation', ''),
                            "currency": getattr(exec_notification, 'currency', ''),
                            "modify_date": getattr(exec_notification, 'modify_date', ''),
                            "modify_time": getattr(exec_notification, 'modify_time', ''),
                            "cancel_date": getattr(exec_notification, 'cancel_date', ''),
                            "cancel_time": getattr(exec_notification, 'cancel_time', ''),
                            "fcm_id": getattr(exec_notification, 'fcm_id', ''),
                            "ib_id": getattr(exec_notification, 'ib_id', ''),
                            "user_id": getattr(exec_notification, 'user_id', ''),
                            "notification_type": getattr(exec_notification, 'notification_type', '')
                        }
                        
                        # Add to analyzer
                        analyzer.add_execution(execution_data)
                        
                        # Display execution
                        print(f"🔄 EXECUTION #{execution_count} [{execution_data['timestamp']}]")
                        print(f"    Symbol: {execution_data['symbol']}@{execution_data['exchange']}")
                        print(f"    Account: {execution_data['account_id']}")
                        
                        if execution_data['order_num']:
                            print(f"    Order #: {execution_data['order_num']}")
                        
                        if execution_data['exchange_order_id']:
                            print(f"    Exchange Order ID: {execution_data['exchange_order_id']}")
                        
                        if execution_data['fill_id']:
                            print(f"    Fill ID: {execution_data['fill_id']}")
                        
                        # Fill details
                        if execution_data['fill_size'] > 0:
                            print(f"    💎 FILL DETAILS:")
                            print(f"      Size: {execution_data['fill_size']:,}")
                            
                            if execution_data['fill_price'] > 0:
                                print(f"      Price: ${execution_data['fill_price']:,.4f}")
                                
                                # Calculate notional value
                                notional = execution_data['fill_size'] * execution_data['fill_price']
                                print(f"      Notional: ${notional:,.2f}")
                            
                            if execution_data['fill_date'] and execution_data['fill_time']:
                                print(f"      Fill Time: {execution_data['fill_date']} {execution_data['fill_time']}")
                            
                            print(f"      Transaction: {execution_data['transaction_type']}")
                            
                            if execution_data['currency']:
                                print(f"      Currency: {execution_data['currency']}")
                        
                        # Confirmation and status
                        if execution_data['confirmation']:
                            print(f"    Confirmation: {execution_data['confirmation']}")
                        
                        if execution_data['notification_type']:
                            print(f"    Type: {execution_data['notification_type']}")
                        
                        # Account details
                        if execution_data['fcm_id'] or execution_data['ib_id'] or execution_data['user_id']:
                            print(f"    📋 ROUTING:")
                            if execution_data['fcm_id']:
                                print(f"      FCM: {execution_data['fcm_id']}")
                            if execution_data['ib_id']:
                                print(f"      IB: {execution_data['ib_id']}")
                            if execution_data['user_id']:
                                print(f"      User: {execution_data['user_id']}")
                        
                        # Modification/cancellation info
                        if (execution_data['modify_date'] or execution_data['cancel_date']):
                            print(f"    📅 MODIFICATIONS:")
                            if execution_data['modify_date'] and execution_data['modify_time']:
                                print(f"      Modified: {execution_data['modify_date']} {execution_data['modify_time']}")
                            if execution_data['cancel_date'] and execution_data['cancel_time']:
                                print(f"      Cancelled: {execution_data['cancel_date']} {execution_data['cancel_time']}")
                        
                        # Display detailed message if requested
                        if output_format != "human":
                            print(f"\n📋 EXECUTION DETAILS:")
                            print(message_handler_obj.format_message(exec_info, output_format))
                            print("-" * 40)
                        
                        print()  # Blank line for readability
                    
                    else:
                        # Other notification types
                        print(f"📡 Other notification: Template {template_id}")
                
                except asyncio.TimeoutError:
                    # No more execution notifications
                    break
                except Exception as e:
                    logger.warning(f"Error processing execution notification: {e}")
                    continue
            
            # Get analysis summary
            summary = analyzer.get_summary()
            
            print(f"\n🔄 EXECUTION REPLAY SUMMARY:")
            print("=" * 60)
            print(f"Index Range: {start_index or 'Beginning'} - {finish_index or 'End'}")
            print(f"Total Executions: {summary['total_executions']}")
            print(f"Total Fills: {summary['total_fills']}")
            print(f"Unique Symbols: {summary['unique_symbols']}")
            print(f"Unique Accounts: {summary['unique_accounts']}")
            print(f"Execution Venues: {summary['execution_venues']}")
            
            if execution_count > 0:
                avg_frequency = execution_count / timeout
                print(f"Average Replay Rate: {avg_frequency:.2f} executions/second")
            
            # Volume analysis
            if summary['volume_analysis']:
                vol = summary['volume_analysis']
                print(f"\n📊 VOLUME ANALYSIS:")
                print(f"Total Volume Executed: {vol['total_volume']:,}")
                print(f"Average Fill Size: {vol['average_fill_size']:,.0f}")
                print(f"Fill Size Range: {vol['min_fill_size']:,} - {vol['max_fill_size']:,}")
            
            # Price analysis
            if summary['price_analysis']:
                price = summary['price_analysis']
                print(f"\n💰 PRICE ANALYSIS:")
                print(f"Average Fill Price: ${price['average_fill_price']:,.4f}")
                print(f"Price Range: ${price['min_fill_price']:,.4f} - ${price['max_fill_price']:,.4f}")
                print(f"Price Spread: ${price['price_range']:,.4f}")
            
            # Fill size distribution
            if summary['fill_analysis']:
                fill = summary['fill_analysis']
                print(f"\n📈 FILL SIZE DISTRIBUTION:")
                print(f"Small Fills (≤100): {fill['small_fills']} ({fill['small_fill_pct']:.1f}%)")
                print(f"Medium Fills (100-1000): {fill['medium_fills']} ({fill['medium_fill_pct']:.1f}%)")
                print(f"Large Fills (>1000): {fill['large_fills']} ({fill['large_fill_pct']:.1f}%)")
            
            # Symbol and venue breakdown
            if summary['symbols']:
                print(f"\nSymbols: {', '.join(summary['symbols'][:10])}")
                if len(summary['symbols']) > 10:
                    print(f"... and {len(summary['symbols']) - 10} more")
            
            if summary['venues']:
                print(f"Execution Venues: {', '.join(summary['venues'])}")
            
            if summary['accounts']:
                print(f"Accounts: {', '.join(summary['accounts'][:5])}")
                if len(summary['accounts']) > 5:
                    print(f"... and {len(summary['accounts']) - 5} more")
            
            # CSV output
            if output_format == "csv" and analyzer.executions:
                print(f"\n📊 CSV FORMAT (EXECUTIONS):")
                csv_headers = ["Timestamp", "Order_Num", "Account_ID", "Symbol", "Exchange", 
                              "Fill_ID", "Fill_Size", "Fill_Price", "Transaction_Type", 
                              "Fill_Date", "Fill_Time", "Confirmation"]
                print(",".join(csv_headers))
                
                for execution in analyzer.executions[-50:]:  # Last 50 executions
                    values = [
                        execution['timestamp'],
                        execution['order_num'],
                        execution['account_id'],
                        execution['symbol'],
                        execution['exchange'],
                        execution['fill_id'],
                        str(execution['fill_size']),
                        str(execution['fill_price']),
                        execution['transaction_type'],
                        execution['fill_date'],
                        execution['fill_time'],
                        execution['confirmation']
                    ]
                    print(",".join(values))
            
            # Execution performance insights
            print(f"\n💡 EXECUTION PERFORMANCE INSIGHTS:")
            if summary['total_executions'] > 0:
                print(f"✅ Successfully replayed {summary['total_executions']} execution records")
                
                # Execution efficiency assessment
                if summary['total_fills'] > 0:
                    fill_rate = (summary['total_fills'] / summary['total_executions']) * 100
                    print(f"• Fill Rate: {fill_rate:.1f}% of execution records contained fills")
                    
                    if fill_rate > 80:
                        print("• High execution efficiency - most records represent actual fills")
                    elif fill_rate > 50:
                        print("• Moderate execution efficiency")
                    else:
                        print("• Lower fill rate - many non-fill execution records")
                
                # Market impact assessment
                if summary.get('volume_analysis', {}).get('average_fill_size', 0) > 1000:
                    print("• Large average fill sizes may indicate institutional trading")
                elif summary.get('volume_analysis', {}).get('average_fill_size', 0) > 100:
                    print("• Moderate fill sizes suggest retail/small institutional trading")
                else:
                    print("• Small fill sizes indicate retail trading patterns")
                
                # Price consistency
                if summary.get('price_analysis', {}).get('price_range', 0) > 0:
                    avg_price = summary['price_analysis']['average_fill_price']
                    price_range = summary['price_analysis']['price_range']
                    volatility_pct = (price_range / avg_price * 100) if avg_price > 0 else 0
                    
                    if volatility_pct > 10:
                        print(f"• High price volatility: {volatility_pct:.1f}% range")
                    elif volatility_pct > 5:
                        print(f"• Moderate price volatility: {volatility_pct:.1f}% range")
                    else:
                        print(f"• Low price volatility: {volatility_pct:.1f}% range")
            else:
                print("⚠️  No execution records received in replay")
                print("• Check if execution history exists for specified criteria")
                print("• Verify index range and account filters")
                print("• Consider extending timeout for larger datasets")
        
        else:
            print("❌ Replay executions request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions for execution replay access")
            print("   • Check if account/FCM/IB IDs are valid")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without filters to replay all executions")
            print("   • Verify start/finish index range is valid")
            print("   • Check if execution history is available")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing replay executions: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for replay executions testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Replay Executions endpoint (Templates 3506/3507)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_replay_executions.py
  python test_replay_executions.py --account-id "123456"
  python test_replay_executions.py --start-index 0 --finish-index 100
  python test_replay_executions.py --fcm-id "FCM01" --format csv

Safety:
  This script performs READ-ONLY execution replay requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays historical execution data including:
  - Detailed fill information (size, price, time)
  - Exchange order IDs and fill confirmations
  - Account and routing information
  - Comprehensive execution performance analysis
  - Volume and price distribution statistics
  - Trading pattern insights and efficiency metrics
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--fcm-id",
        type=str,
        help="FCM ID filter (optional)"
    )
    
    parser.add_argument(
        "--ib-id",
        type=str,
        help="IB ID filter (optional)"
    )
    
    parser.add_argument(
        "--start-index",
        type=int,
        help="Starting index for execution replay (optional)"
    )
    
    parser.add_argument(
        "--finish-index",
        type=int,
        help="Ending index for execution replay (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC REPLAY EXECUTIONS ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY EXECUTION REPLAY OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_replay_executions(
        account_id=args.account_id,
        fcm_id=args.fcm_id,
        ib_id=args.ib_id,
        start_index=args.start_index,
        finish_index=args.finish_index,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Replay executions test completed successfully!")
        return 0
    else:
        print("\n❌ Replay executions test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)