#!/usr/bin/env python3

"""
Test Rithmic Show Order History endpoints (Templates 322/323).

This script demonstrates how to retrieve historical order data
from the Rithmic API, showing past orders and their execution details.

SAFETY: This script only performs READ-ONLY order history requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_show_order_history_pb2
import response_show_order_history_pb2
import rithmic_order_notification_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_order_history(account_id: Optional[str] = None, fcm_id: Optional[str] = None,
                           ib_id: Optional[str] = None, basket_id: Optional[str] = None,
                           output_format: str = "human") -> bool:
    """
    Test order history functionality.
    
    Args:
        account_id: Optional specific account ID to query
        fcm_id: Optional FCM ID filter
        ib_id: Optional IB ID filter
        basket_id: Optional basket ID filter
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📚 TESTING ORDER HISTORY ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.SHOW_ORDER_HISTORY_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.SHOW_ORDER_HISTORY_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"FCM ID: {fcm_id or 'All FCMs'}")
    print(f"IB ID: {ib_id or 'All IBs'}")
    print(f"Basket ID: {basket_id or 'All baskets'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "order_history_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create order history request
        request = request_show_order_history_pb2.RequestShowOrderHistory()
        request.template_id = TemplateIDs.SHOW_ORDER_HISTORY_REQUEST
        request.user_msg.append("Order history request from endpoint tester")
        
        # Set filters - FCM ID and IB ID are required by server even if empty
        # Account ID is also required by the server
        request.account_id = account_id if account_id else "demo"
        
        # FCM ID is required by the server (error 1039 if missing)
        # Use default paper trading FCM ID if none provided
        request.fcm_id = fcm_id if fcm_id else "demo"
            
        # IB ID is also required by the server  
        request.ib_id = ib_id if ib_id else "demo"
            
        if basket_id:
            request.basket_id = basket_id
        
        # Send order history request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending order history request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send order history request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for order history response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_show_order_history_pb2.ResponseShowOrderHistory()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 ORDER HISTORY ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Order history request successful!")
            
            # Listen for historical order notifications
            print("\n📚 LISTENING FOR HISTORICAL ORDER NOTIFICATIONS...")
            print("(Waiting up to 15 seconds for historical order data...)")
            
            orders = []
            start_time = asyncio.get_event_loop().time()
            timeout = 15.0  # Wait up to 15 seconds for historical data
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                try:
                    # Wait for order notifications
                    order_bytes = await connection.receive_message(timeout=3.0)
                    
                    if not order_bytes:
                        continue  # Timeout, keep listening
                    
                    # Parse notification to determine type
                    order_info = parse_message(order_bytes)
                    if not order_info:
                        continue
                    
                    template_id = order_info.template_id
                    
                    # Check if this is an order notification
                    if template_id == TemplateIDs.RITHMIC_ORDER_NOTIFICATION:
                        # Parse order notification
                        order_notification = rithmic_order_notification_pb2.RithmicOrderNotification()
                        order_notification.ParseFromString(order_bytes)
                        
                        # Extract historical order data
                        order_data = {
                            "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                            "account_id": getattr(order_notification, 'account_id', ''),
                            "symbol": getattr(order_notification, 'symbol', ''),
                            "exchange": getattr(order_notification, 'exchange', ''),
                            "order_tag": getattr(order_notification, 'order_tag', ''),
                            "basket_id": getattr(order_notification, 'basket_id', ''),
                            "transaction_type": getattr(order_notification, 'transaction_type', ''),
                            "duration": getattr(order_notification, 'duration', ''),
                            "price_type": getattr(order_notification, 'price_type', ''),
                            "quantity": getattr(order_notification, 'quantity', 0),
                            "price": getattr(order_notification, 'price', 0),
                            "trigger_price": getattr(order_notification, 'trigger_price', 0),
                            "status": getattr(order_notification, 'status', ''),
                            "order_num": getattr(order_notification, 'order_num', ''),
                            "currency": getattr(order_notification, 'currency', ''),
                            "fill_quantity": getattr(order_notification, 'fill_quantity', 0),
                            "remaining_quantity": getattr(order_notification, 'remaining_quantity', 0),
                            "avg_fill_price": getattr(order_notification, 'avg_fill_price', 0),
                            "total_fill_size": getattr(order_notification, 'total_fill_size', 0),
                            "order_date": getattr(order_notification, 'order_date', ''),
                            "order_time": getattr(order_notification, 'order_time', ''),
                            "modify_date": getattr(order_notification, 'modify_date', ''),
                            "modify_time": getattr(order_notification, 'modify_time', ''),
                            "cancel_date": getattr(order_notification, 'cancel_date', ''),
                            "cancel_time": getattr(order_notification, 'cancel_time', ''),
                            "completion_reason": getattr(order_notification, 'completion_reason', ''),
                            "exchange_order_id": getattr(order_notification, 'exchange_order_id', ''),
                            "fcm_id": getattr(order_notification, 'fcm_id', ''),
                            "ib_id": getattr(order_notification, 'ib_id', ''),
                            "user_id": getattr(order_notification, 'user_id', '')
                        }
                        
                        orders.append(order_data)
                        print(f"📚 Received historical order: {order_data['symbol']} {order_data['transaction_type']} - {order_data['status']}")
                    
                    else:
                        # Other notification types
                        print(f"📡 Other notification: Template {template_id}")
                
                except asyncio.TimeoutError:
                    # No more order notifications
                    break
                except Exception as e:
                    logger.warning(f"Error processing order notification: {e}")
                    continue
            
            # Display historical order results
            if orders:
                print(f"\n📚 ORDER HISTORY ({len(orders)} orders):")
                print("-" * 100)
                
                for i, order in enumerate(orders):
                    print(f"{i+1:3d}. Order: {order['symbol']}@{order['exchange']}")
                    print(f"     Order #: {order['order_num']}")
                    print(f"     Account: {order['account_id']}")
                    
                    if order['fcm_id']:
                        print(f"     FCM: {order['fcm_id']}")
                    if order['ib_id']:
                        print(f"     IB: {order['ib_id']}")
                    if order['user_id']:
                        print(f"     User: {order['user_id']}")
                    
                    if order['order_tag']:
                        print(f"     Tag: {order['order_tag']}")
                    if order['basket_id']:
                        print(f"     Basket: {order['basket_id']}")
                    if order['exchange_order_id']:
                        print(f"     Exchange Order ID: {order['exchange_order_id']}")
                    
                    # Order type and details
                    print(f"     Type: {order['transaction_type']} {order['quantity']:,} @ {order['price_type']}")
                    
                    if order['price'] > 0:
                        print(f"     Price: ${order['price']:,.4f}")
                    
                    if order['trigger_price'] > 0:
                        print(f"     Trigger: ${order['trigger_price']:,.4f}")
                    
                    print(f"     Duration: {order['duration']}")
                    print(f"     Status: {order['status']}")
                    
                    if order['completion_reason']:
                        print(f"     Completion: {order['completion_reason']}")
                    
                    # Fill information
                    if order['fill_quantity'] > 0:
                        print(f"     🎯 FILL INFORMATION:")
                        print(f"       Filled: {order['fill_quantity']:,} / {order['quantity']:,}")
                        fill_pct = (order['fill_quantity'] / order['quantity']) * 100
                        print(f"       Fill %: {fill_pct:.1f}%")
                        
                        if order['avg_fill_price'] > 0:
                            print(f"       Avg Fill Price: ${order['avg_fill_price']:,.4f}")
                        
                        if order['total_fill_size'] > 0:
                            print(f"       Total Fill Size: {order['total_fill_size']:,}")
                    
                    if order['remaining_quantity'] > 0:
                        print(f"     Unfilled: {order['remaining_quantity']:,}")
                    
                    # Timestamps
                    print(f"     📅 TIMING:")
                    if order['order_date'] and order['order_time']:
                        print(f"       Ordered: {order['order_date']} {order['order_time']}")
                    
                    if order['modify_date'] and order['modify_time']:
                        print(f"       Modified: {order['modify_date']} {order['modify_time']}")
                    
                    if order['cancel_date'] and order['cancel_time']:
                        print(f"       Cancelled: {order['cancel_date']} {order['cancel_time']}")
                    
                    print()
                
                # Comprehensive historical order analysis
                print(f"\n📈 HISTORICAL ORDER ANALYSIS:")
                print(f"Total Historical Orders: {len(orders)}")
                
                # Status breakdown
                status_counts = {}
                transaction_counts = {}
                symbol_counts = {}
                completion_counts = {}
                
                for order in orders:
                    status = order['status'] or 'Unknown'
                    status_counts[status] = status_counts.get(status, 0) + 1
                    
                    transaction = order['transaction_type'] or 'Unknown'
                    transaction_counts[transaction] = transaction_counts.get(transaction, 0) + 1
                    
                    symbol = order['symbol'] or 'Unknown'
                    symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
                    
                    completion = order['completion_reason'] or 'Active'
                    completion_counts[completion] = completion_counts.get(completion, 0) + 1
                
                if status_counts:
                    print(f"\nOrder Status Distribution:")
                    for status, count in sorted(status_counts.items()):
                        pct = (count / len(orders)) * 100
                        print(f"  • {status}: {count} ({pct:.1f}%)")
                
                if transaction_counts:
                    print(f"\nTransaction Types:")
                    for trans_type, count in sorted(transaction_counts.items()):
                        pct = (count / len(orders)) * 100
                        print(f"  • {trans_type}: {count} ({pct:.1f}%)")
                
                if completion_counts:
                    print(f"\nCompletion Reasons:")
                    for reason, count in sorted(completion_counts.items()):
                        pct = (count / len(orders)) * 100
                        print(f"  • {reason}: {count} ({pct:.1f}%)")
                
                if symbol_counts:
                    print(f"\nMost Active Symbols:")
                    sorted_symbols = sorted(symbol_counts.items(), key=lambda x: x[1], reverse=True)
                    for symbol, count in sorted_symbols[:10]:  # Top 10 symbols
                        pct = (count / len(orders)) * 100
                        print(f"  • {symbol}: {count} orders ({pct:.1f}%)")
                
                # Fill analysis
                filled_orders = [o for o in orders if o['fill_quantity'] > 0]
                fully_filled = [o for o in orders if o['fill_quantity'] >= o['quantity'] and o['quantity'] > 0]
                partially_filled = [o for o in orders if 0 < o['fill_quantity'] < o['quantity']]
                unfilled_orders = [o for o in orders if o['fill_quantity'] == 0]
                
                print(f"\nFill Analysis:")
                print(f"  • Total Orders: {len(orders)}")
                print(f"  • Filled Orders: {len(filled_orders)} ({len(filled_orders)/len(orders)*100:.1f}%)")
                print(f"  • Fully Filled: {len(fully_filled)} ({len(fully_filled)/len(orders)*100:.1f}%)")
                print(f"  • Partially Filled: {len(partially_filled)} ({len(partially_filled)/len(orders)*100:.1f}%)")
                print(f"  • Unfilled: {len(unfilled_orders)} ({len(unfilled_orders)/len(orders)*100:.1f}%)")
                
                if filled_orders:
                    total_filled_qty = sum(o['fill_quantity'] for o in filled_orders)
                    total_ordered_qty = sum(o['quantity'] for o in orders if o['quantity'] > 0)
                    overall_fill_rate = (total_filled_qty / total_ordered_qty * 100) if total_ordered_qty > 0 else 0
                    
                    print(f"  • Total Filled Quantity: {total_filled_qty:,}")
                    print(f"  • Overall Fill Rate: {overall_fill_rate:.1f}%")
                    
                    # Average fill price analysis
                    fill_prices = [o['avg_fill_price'] for o in filled_orders if o['avg_fill_price'] > 0]
                    if fill_prices:
                        avg_fill = sum(fill_prices) / len(fill_prices)
                        min_fill = min(fill_prices)
                        max_fill = max(fill_prices)
                        print(f"  • Avg Fill Price Range: ${min_fill:.4f} - ${max_fill:.4f}")
                        print(f"  • Overall Avg Fill: ${avg_fill:.4f}")
                
                # Price type analysis
                price_types = {}
                for order in orders:
                    price_type = order['price_type'] or 'Unknown'
                    price_types[price_type] = price_types.get(price_type, 0) + 1
                
                if price_types:
                    print(f"\nPrice Type Distribution:")
                    for price_type, count in sorted(price_types.items()):
                        pct = (count / len(orders)) * 100
                        print(f"  • {price_type}: {count} ({pct:.1f}%)")
                
                # Duration analysis
                durations = {}
                for order in orders:
                    duration = order['duration'] or 'Unknown'
                    durations[duration] = durations.get(duration, 0) + 1
                
                if durations:
                    print(f"\nDuration Distribution:")
                    for duration, count in sorted(durations.items()):
                        pct = (count / len(orders)) * 100
                        print(f"  • {duration}: {count} ({pct:.1f}%)")
            
            else:
                print(f"\n📋 No historical orders found")
                print("   This could indicate:")
                print("   • No historical orders in the specified criteria")
                print("   • Account has no trading history")
                print("   • Filters may be too restrictive")
                print("   • Order history retention period may have expired")
                print("   • Different FCM/IB may have the order history")
            
            # CSV output
            if output_format == "csv" and orders:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Order_Num", "Account_ID", "Symbol", "Exchange", "Transaction_Type", 
                              "Quantity", "Price", "Status", "Fill_Quantity", "Avg_Fill_Price",
                              "Duration", "Price_Type", "Order_Date", "Order_Time", "Completion_Reason"]
                print(",".join(csv_headers))
                
                for order in orders:
                    values = [
                        order['order_num'],
                        order['account_id'],
                        order['symbol'],
                        order['exchange'],
                        order['transaction_type'],
                        str(order['quantity']),
                        str(order['price']),
                        order['status'],
                        str(order['fill_quantity']),
                        str(order['avg_fill_price']),
                        order['duration'],
                        order['price_type'],
                        order['order_date'],
                        order['order_time'],
                        order['completion_reason']
                    ]
                    print(",".join(values))
            
            # Trading pattern insights
            if orders:
                print(f"\n💡 TRADING PATTERN INSIGHTS:")
                print(f"✅ Successfully retrieved {len(orders)} historical orders")
                
                # Trading activity assessment
                if len(filled_orders) > len(orders) * 0.8:
                    print("• High execution rate - efficient order management")
                elif len(filled_orders) > len(orders) * 0.5:
                    print("• Moderate execution rate - room for improvement")
                else:
                    print("• Low execution rate - review order strategy")
                
                # Order size analysis
                quantities = [o['quantity'] for o in orders if o['quantity'] > 0]
                if quantities:
                    avg_qty = sum(quantities) / len(quantities)
                    max_qty = max(quantities)
                    min_qty = min(quantities)
                    print(f"• Order size range: {min_qty:,} - {max_qty:,} (avg: {avg_qty:,.0f})")
                    
                    if max_qty > avg_qty * 10:
                        print("• Wide variation in order sizes detected")
                
                # Market vs limit preference
                market_pct = price_types.get('MARKET', 0) / len(orders) * 100
                limit_pct = price_types.get('LIMIT', 0) / len(orders) * 100
                
                if market_pct > 50:
                    print("• Preference for market orders (immediate execution)")
                elif limit_pct > 50:
                    print("• Preference for limit orders (price control)")
                else:
                    print("• Balanced use of market and limit orders")
        
        else:
            print("❌ Order history request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions for order history access")
            print("   • Check if account/FCM/IB/basket IDs are valid")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without filters to see all order history")
            print("   • Verify order history retention settings")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing order history: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for order history testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Order History endpoint (Templates 322/323)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_order_history.py
  python test_order_history.py --account-id "123456"
  python test_order_history.py --fcm-id "FCM01" --ib-id "IB01"
  python test_order_history.py --basket-id "BASKET01"
  python test_order_history.py --format csv

Safety:
  This script performs READ-ONLY order history requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays historical orders including:
  - Complete order details (symbol, quantity, price, type, status)
  - Fill information and execution analysis
  - Account and routing information
  - Timing information (order, modify, cancel dates/times)
  - Comprehensive trading pattern analysis
  - Statistical breakdowns by status, type, symbol, etc.
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--fcm-id",
        type=str,
        help="FCM ID filter (optional)"
    )
    
    parser.add_argument(
        "--ib-id",
        type=str,
        help="IB ID filter (optional)"
    )
    
    parser.add_argument(
        "--basket-id",
        type=str,
        help="Basket ID filter (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC ORDER HISTORY ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY ORDER HISTORY OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_order_history(
        account_id=args.account_id,
        fcm_id=args.fcm_id,
        ib_id=args.ib_id,
        basket_id=args.basket_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Order history test completed successfully!")
        return 0
    else:
        print("\n❌ Order history test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)