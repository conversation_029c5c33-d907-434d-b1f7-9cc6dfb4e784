#!/usr/bin/env python3

"""
Test Rithmic Account <PERSON><PERSON> Info endpoints (Templates 304/305).

This script demonstrates how to retrieve account risk management system (RMS) information
from the Rithmic API, providing account-specific risk parameters and limits.

SAFETY: This script only performs READ-ONLY account RMS info requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_account_rms_info_pb2
import response_account_rms_info_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_account_rms_info(account_id: Optional[str] = None,
                              output_format: str = "human") -> bool:
    """
    Test account RMS info retrieval functionality.
    
    Args:
        account_id: Optional specific account ID to query
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("⚖️ TESTING ACCOUNT RMS INFO ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.ACCOUNT_RMS_INFO_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.ACCOUNT_RMS_INFO_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "account_rms_info_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create account RMS info request
        request = request_account_rms_info_pb2.RequestAccountRmsInfo()
        request.template_id = TemplateIDs.ACCOUNT_RMS_INFO_REQUEST
        request.user_msg.append("Account RMS info request from endpoint tester")
        
        # Set user type (required field) - TRADER type for paper trading
        request.user_type = request_account_rms_info_pb2.RequestAccountRmsInfo.UserType.USER_TYPE_TRADER
        
        # Set account ID if provided
        if account_id:
            request.account_id = account_id
        
        # Send account RMS info request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending account RMS info request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send account RMS info request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for account RMS info response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_account_rms_info_pb2.ResponseAccountRmsInfo()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 ACCOUNT RMS INFO ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        # Empty response codes might indicate success for some account endpoints
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Account RMS info request successful!")
            
            # Display account RMS information
            accounts = []
            if hasattr(response, 'account_id') and response.account_id:
                print(f"\n⚖️ ACCOUNT RMS INFORMATION ({len(response.account_id)} accounts):")
                print("-" * 80)
                
                for i, acc_id in enumerate(response.account_id):
                    account_rms = {
                        "account_id": acc_id,
                        "fcm_id": "",
                        "ib_id": "",
                        "account_type": "",
                        "max_order_qty": 0,
                        "max_position_qty": 0,
                        "max_daily_loss": 0,
                        "buying_power": 0,
                        "available_buying_power": 0,
                        "initial_margin": 0,
                        "maintenance_margin": 0,
                        "margin_excess": 0,
                        "day_trading_buying_power": 0,
                        "overnight_buying_power": 0
                    }
                    
                    # Get corresponding RMS data
                    if hasattr(response, 'fcm_id') and i < len(response.fcm_id):
                        account_rms["fcm_id"] = response.fcm_id[i]
                    
                    if hasattr(response, 'ib_id') and i < len(response.ib_id):
                        account_rms["ib_id"] = response.ib_id[i]
                    
                    if hasattr(response, 'account_type') and i < len(response.account_type):
                        account_rms["account_type"] = response.account_type[i]
                    
                    if hasattr(response, 'max_order_qty') and i < len(response.max_order_qty):
                        account_rms["max_order_qty"] = response.max_order_qty[i]
                    
                    if hasattr(response, 'max_position_qty') and i < len(response.max_position_qty):
                        account_rms["max_position_qty"] = response.max_position_qty[i]
                    
                    if hasattr(response, 'max_daily_loss') and i < len(response.max_daily_loss):
                        account_rms["max_daily_loss"] = response.max_daily_loss[i]
                    
                    if hasattr(response, 'buying_power') and i < len(response.buying_power):
                        account_rms["buying_power"] = response.buying_power[i]
                    
                    if hasattr(response, 'available_buying_power') and i < len(response.available_buying_power):
                        account_rms["available_buying_power"] = response.available_buying_power[i]
                    
                    if hasattr(response, 'initial_margin') and i < len(response.initial_margin):
                        account_rms["initial_margin"] = response.initial_margin[i]
                    
                    if hasattr(response, 'maintenance_margin') and i < len(response.maintenance_margin):
                        account_rms["maintenance_margin"] = response.maintenance_margin[i]
                    
                    if hasattr(response, 'margin_excess') and i < len(response.margin_excess):
                        account_rms["margin_excess"] = response.margin_excess[i]
                    
                    if hasattr(response, 'day_trading_buying_power') and i < len(response.day_trading_buying_power):
                        account_rms["day_trading_buying_power"] = response.day_trading_buying_power[i]
                    
                    if hasattr(response, 'overnight_buying_power') and i < len(response.overnight_buying_power):
                        account_rms["overnight_buying_power"] = response.overnight_buying_power[i]
                    
                    accounts.append(account_rms)
                    
                    # Display account RMS info
                    print(f"{i+1:3d}. Account ID: {account_rms['account_id']}")
                    
                    if account_rms['fcm_id']:
                        print(f"     FCM ID: {account_rms['fcm_id']}")
                    
                    if account_rms['ib_id']:
                        print(f"     IB ID: {account_rms['ib_id']}")
                    
                    if account_rms['account_type']:
                        print(f"     Account Type: {account_rms['account_type']}")
                    
                    # Risk limits
                    print(f"     📊 RISK LIMITS:")
                    if account_rms['max_order_qty'] > 0:
                        print(f"       Max Order Qty: {account_rms['max_order_qty']:,}")
                    
                    if account_rms['max_position_qty'] > 0:
                        print(f"       Max Position Qty: {account_rms['max_position_qty']:,}")
                    
                    if account_rms['max_daily_loss'] > 0:
                        print(f"       Max Daily Loss: ${account_rms['max_daily_loss']:,.2f}")
                    
                    # Buying power and margins
                    print(f"     💰 BUYING POWER & MARGINS:")
                    if account_rms['buying_power'] > 0:
                        print(f"       Buying Power: ${account_rms['buying_power']:,.2f}")
                    
                    if account_rms['available_buying_power'] > 0:
                        print(f"       Available BP: ${account_rms['available_buying_power']:,.2f}")
                    
                    if account_rms['initial_margin'] > 0:
                        print(f"       Initial Margin: ${account_rms['initial_margin']:,.2f}")
                    
                    if account_rms['maintenance_margin'] > 0:
                        print(f"       Maintenance Margin: ${account_rms['maintenance_margin']:,.2f}")
                    
                    if account_rms['margin_excess'] != 0:
                        excess_symbol = "+" if account_rms['margin_excess'] > 0 else ""
                        print(f"       Margin Excess: {excess_symbol}${account_rms['margin_excess']:,.2f}")
                    
                    # Day trading specific
                    if account_rms['day_trading_buying_power'] > 0:
                        print(f"       Day Trading BP: ${account_rms['day_trading_buying_power']:,.2f}")
                    
                    if account_rms['overnight_buying_power'] > 0:
                        print(f"       Overnight BP: ${account_rms['overnight_buying_power']:,.2f}")
                    
                    print()
                
                # Summary statistics
                print(f"\n📈 RMS SUMMARY:")
                print(f"Total Accounts: {len(accounts)}")
                
                # Account type breakdown
                account_types = {}
                for account in accounts:
                    acc_type = account['account_type'] or 'Unknown'
                    account_types[acc_type] = account_types.get(acc_type, 0) + 1
                
                if account_types:
                    print(f"Account Types:")
                    for acc_type, count in account_types.items():
                        print(f"  • {acc_type}: {count}")
                
                # Risk assessment
                high_risk_accounts = 0
                total_buying_power = 0
                
                for account in accounts:
                    if account['max_daily_loss'] > 0 and account['max_daily_loss'] < 10000:
                        high_risk_accounts += 1
                    
                    total_buying_power += account['available_buying_power']
                
                if total_buying_power > 0:
                    print(f"Total Available Buying Power: ${total_buying_power:,.2f}")
                
                if high_risk_accounts > 0:
                    print(f"Accounts with Tight Risk Limits: {high_risk_accounts}")
            
            else:
                print(f"\n📋 No account RMS information found")
                print("   This could indicate:")
                print("   • No accounts accessible with current credentials")
                print("   • Insufficient permissions for RMS data")
                print("   • Account ID filter may be too restrictive")
            
            # CSV output
            if output_format == "csv" and accounts:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Account_ID", "FCM_ID", "IB_ID", "Account_Type", 
                              "Max_Order_Qty", "Max_Position_Qty", "Max_Daily_Loss",
                              "Buying_Power", "Available_BP", "Initial_Margin", 
                              "Maintenance_Margin", "Margin_Excess"]
                print(",".join(csv_headers))
                
                for account in accounts:
                    values = [
                        account['account_id'],
                        account['fcm_id'],
                        account['ib_id'],
                        account['account_type'],
                        str(account['max_order_qty']),
                        str(account['max_position_qty']),
                        str(account['max_daily_loss']),
                        str(account['buying_power']),
                        str(account['available_buying_power']),
                        str(account['initial_margin']),
                        str(account['maintenance_margin']),
                        str(account['margin_excess'])
                    ]
                    print(",".join(values))
            
            # Risk analysis
            if accounts:
                print(f"\n🔍 RISK ANALYSIS:")
                for account in accounts:
                    acc_id = account['account_id']
                    
                    # Margin utilization
                    if account['buying_power'] > 0 and account['available_buying_power'] >= 0:
                        utilization = (1 - account['available_buying_power'] / account['buying_power']) * 100
                        print(f"• {acc_id}: Buying power utilization: {utilization:.1f}%")
                        
                        if utilization > 80:
                            print(f"  ⚠️  HIGH UTILIZATION - Consider reducing positions")
                        elif utilization > 50:
                            print(f"  ⚡ MODERATE UTILIZATION - Monitor closely")
                        else:
                            print(f"  ✅ LOW UTILIZATION - Good margin cushion")
                    
                    # Margin health
                    if account['margin_excess'] > 0:
                        print(f"• {acc_id}: Positive margin excess - ${account['margin_excess']:,.2f}")
                    elif account['margin_excess'] < 0:
                        print(f"• {acc_id}: ⚠️  Margin deficit - ${abs(account['margin_excess']):,.2f}")
        
        else:
            print("❌ Account RMS info request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify account permissions for RMS data access")
            print("   • Check if account ID is valid and accessible")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without account ID filter to see all accounts")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing account RMS info: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for account RMS info testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Account RMS Info endpoint (Templates 304/305)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_account_rms_info.py
  python test_account_rms_info.py --account-id "123456"
  python test_account_rms_info.py --format csv
  python test_account_rms_info.py --format json

Safety:
  This script performs READ-ONLY account RMS info requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays comprehensive account RMS information including:
  - Account identification (FCM ID, IB ID, type)
  - Risk limits (max order qty, max position qty, max daily loss)
  - Buying power information (total, available, day trading, overnight)
  - Margin requirements (initial, maintenance, excess/deficit)
  - Risk analysis and utilization metrics
  - Account type breakdown and summary statistics
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional, defaults to all accounts)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC ACCOUNT RMS INFO ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY ACCOUNT RMS INFO OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_account_rms_info(
        account_id=args.account_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Account RMS info test completed successfully!")
        return 0
    else:
        print("\n❌ Account RMS info test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)