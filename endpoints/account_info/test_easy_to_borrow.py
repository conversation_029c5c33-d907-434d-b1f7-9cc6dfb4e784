#!/usr/bin/env python3

"""
Test Rithmic Easy to Borrow List endpoints (Templates 348/349).

This script demonstrates how to retrieve the easy to borrow list
from the Rithmic API, showing available securities for short selling.

SAFETY: This script only performs READ-ONLY easy to borrow list requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_easy_to_borrow_list_pb2
import response_easy_to_borrow_list_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_easy_to_borrow(symbol_filter: Optional[str] = None, exchange_filter: Optional[str] = None,
                             account_id: Optional[str] = None, output_format: str = "human") -> bool:
    """
    Test easy to borrow list functionality.
    
    Args:
        symbol_filter: Optional symbol pattern to filter results
        exchange_filter: Optional exchange filter
        account_id: Optional specific account ID to query
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("💰 TESTING EASY TO BORROW LIST ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.EASY_TO_BORROW_LIST_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.EASY_TO_BORROW_LIST_RESPONSE} (Response)")
    print(f"Symbol Filter: {symbol_filter or 'All symbols'}")
    print(f"Exchange Filter: {exchange_filter or 'All exchanges'}")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "easy_to_borrow_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create easy to borrow list request
        request = request_easy_to_borrow_list_pb2.RequestEasyToBorrowList()
        request.template_id = TemplateIDs.EASY_TO_BORROW_LIST_REQUEST
        request.user_msg.append("Easy to borrow list request from endpoint tester")
        
        # Set filters if provided
        if symbol_filter:
            request.symbol = symbol_filter
        
        if exchange_filter:
            request.exchange = exchange_filter
        
        if account_id:
            request.account_id = account_id
        
        # Send easy to borrow list request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending easy to borrow list request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send easy to borrow list request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for easy to borrow list response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_easy_to_borrow_list_pb2.ResponseEasyToBorrowList()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 EASY TO BORROW LIST ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Easy to borrow list request successful!")
            
            # Display easy to borrow securities
            securities = []
            if hasattr(response, 'symbol') and response.symbol:
                print(f"\n💰 EASY TO BORROW SECURITIES ({len(response.symbol)} securities):")
                print("-" * 100)
                
                for i, symbol in enumerate(response.symbol):
                    security_info = {
                        "symbol": symbol,
                        "exchange": "",
                        "security_type": "",
                        "company_name": "",
                        "available_shares": 0,
                        "borrow_rate": 0.0,
                        "locate_required": False,
                        "hard_to_borrow": False,
                        "minimum_quantity": 0,
                        "maximum_quantity": 0,
                        "last_updated": "",
                        "cusip": "",
                        "isin": "",
                        "sector": "",
                        "market_cap": 0,
                        "price": 0.0,
                        "availability_tier": "",
                        "borrow_fee": 0.0,
                        "currency": "",
                        "country": ""
                    }
                    
                    # Get corresponding security data
                    if hasattr(response, 'exchange') and i < len(response.exchange):
                        security_info["exchange"] = response.exchange[i]
                    
                    if hasattr(response, 'security_type') and i < len(response.security_type):
                        security_info["security_type"] = response.security_type[i]
                    
                    if hasattr(response, 'company_name') and i < len(response.company_name):
                        security_info["company_name"] = response.company_name[i]
                    
                    if hasattr(response, 'available_shares') and i < len(response.available_shares):
                        security_info["available_shares"] = response.available_shares[i]
                    
                    if hasattr(response, 'borrow_rate') and i < len(response.borrow_rate):
                        security_info["borrow_rate"] = response.borrow_rate[i]
                    
                    if hasattr(response, 'locate_required') and i < len(response.locate_required):
                        security_info["locate_required"] = response.locate_required[i]
                    
                    if hasattr(response, 'hard_to_borrow') and i < len(response.hard_to_borrow):
                        security_info["hard_to_borrow"] = response.hard_to_borrow[i]
                    
                    if hasattr(response, 'minimum_quantity') and i < len(response.minimum_quantity):
                        security_info["minimum_quantity"] = response.minimum_quantity[i]
                    
                    if hasattr(response, 'maximum_quantity') and i < len(response.maximum_quantity):
                        security_info["maximum_quantity"] = response.maximum_quantity[i]
                    
                    if hasattr(response, 'last_updated') and i < len(response.last_updated):
                        security_info["last_updated"] = response.last_updated[i]
                    
                    if hasattr(response, 'cusip') and i < len(response.cusip):
                        security_info["cusip"] = response.cusip[i]
                    
                    if hasattr(response, 'isin') and i < len(response.isin):
                        security_info["isin"] = response.isin[i]
                    
                    if hasattr(response, 'sector') and i < len(response.sector):
                        security_info["sector"] = response.sector[i]
                    
                    if hasattr(response, 'market_cap') and i < len(response.market_cap):
                        security_info["market_cap"] = response.market_cap[i]
                    
                    if hasattr(response, 'price') and i < len(response.price):
                        security_info["price"] = response.price[i]
                    
                    if hasattr(response, 'availability_tier') and i < len(response.availability_tier):
                        security_info["availability_tier"] = response.availability_tier[i]
                    
                    if hasattr(response, 'borrow_fee') and i < len(response.borrow_fee):
                        security_info["borrow_fee"] = response.borrow_fee[i]
                    
                    if hasattr(response, 'currency') and i < len(response.currency):
                        security_info["currency"] = response.currency[i]
                    
                    if hasattr(response, 'country') and i < len(response.country):
                        security_info["country"] = response.country[i]
                    
                    securities.append(security_info)
                    
                    # Display security information
                    print(f"{i+1:3d}. Symbol: {security_info['symbol']}@{security_info['exchange']}")
                    
                    if security_info['company_name']:
                        print(f"     Company: {security_info['company_name']}")
                    
                    if security_info['security_type']:
                        print(f"     Type: {security_info['security_type']}")
                    
                    # Identification codes
                    if security_info['cusip'] or security_info['isin']:
                        print(f"     🆔 IDENTIFIERS:")
                        if security_info['cusip']:
                            print(f"       CUSIP: {security_info['cusip']}")
                        if security_info['isin']:
                            print(f"       ISIN: {security_info['isin']}")
                    
                    # Market information
                    if security_info['sector'] or security_info['country'] or security_info['market_cap']:
                        print(f"     🌍 MARKET INFO:")
                        if security_info['sector']:
                            print(f"       Sector: {security_info['sector']}")
                        if security_info['country']:
                            print(f"       Country: {security_info['country']}")
                        if security_info['market_cap'] > 0:
                            print(f"       Market Cap: ${security_info['market_cap']:,.0f}")
                    
                    # Current pricing
                    if security_info['price'] > 0:
                        print(f"     💲 PRICING:")
                        print(f"       Current Price: ${security_info['price']:,.2f}")
                        if security_info['currency']:
                            print(f"       Currency: {security_info['currency']}")
                    
                    # Borrowing details
                    print(f"     📊 BORROWING DETAILS:")
                    if security_info['available_shares'] > 0:
                        print(f"       Available Shares: {security_info['available_shares']:,}")
                    
                    if security_info['borrow_rate'] > 0:
                        print(f"       Borrow Rate: {security_info['borrow_rate']:.4f}%")
                    
                    if security_info['borrow_fee'] > 0:
                        print(f"       Borrow Fee: ${security_info['borrow_fee']:,.2f}")
                    
                    if security_info['availability_tier']:
                        print(f"       Availability Tier: {security_info['availability_tier']}")
                    
                    # Quantity limits
                    if security_info['minimum_quantity'] > 0 or security_info['maximum_quantity'] > 0:
                        print(f"     📏 QUANTITY LIMITS:")
                        if security_info['minimum_quantity'] > 0:
                            print(f"       Minimum: {security_info['minimum_quantity']:,} shares")
                        if security_info['maximum_quantity'] > 0:
                            print(f"       Maximum: {security_info['maximum_quantity']:,} shares")
                    
                    # Special conditions
                    print(f"     ⚠️  CONDITIONS:")
                    locate_status = "✅ REQUIRED" if security_info['locate_required'] else "❌ NOT REQUIRED"
                    print(f"       Locate Required: {locate_status}")
                    
                    htb_status = "⚠️  YES" if security_info['hard_to_borrow'] else "✅ NO"
                    print(f"       Hard to Borrow: {htb_status}")
                    
                    # Last updated
                    if security_info['last_updated']:
                        print(f"     📅 Last Updated: {security_info['last_updated']}")
                    
                    print()
                
                # Easy to borrow analysis
                print(f"\n📈 EASY TO BORROW ANALYSIS:")
                print(f"Total Securities: {len(securities)}")
                
                # Exchange breakdown
                exchange_counts = {}
                security_type_counts = {}
                sector_counts = {}
                availability_tier_counts = {}
                
                for security in securities:
                    exchange = security['exchange'] or 'Unknown'
                    exchange_counts[exchange] = exchange_counts.get(exchange, 0) + 1
                    
                    sec_type = security['security_type'] or 'Unknown'
                    security_type_counts[sec_type] = security_type_counts.get(sec_type, 0) + 1
                    
                    if security['sector']:
                        sector = security['sector']
                        sector_counts[sector] = sector_counts.get(sector, 0) + 1
                    
                    if security['availability_tier']:
                        tier = security['availability_tier']
                        availability_tier_counts[tier] = availability_tier_counts.get(tier, 0) + 1
                
                if exchange_counts:
                    print(f"\nExchange Distribution:")
                    for exchange, count in sorted(exchange_counts.items()):
                        print(f"  • {exchange}: {count} securities")
                
                if security_type_counts:
                    print(f"\nSecurity Types:")
                    for sec_type, count in sorted(security_type_counts.items()):
                        print(f"  • {sec_type}: {count}")
                
                if sector_counts:
                    print(f"\nTop Sectors:")
                    sorted_sectors = sorted(sector_counts.items(), key=lambda x: x[1], reverse=True)
                    for sector, count in sorted_sectors[:10]:  # Top 10 sectors
                        print(f"  • {sector}: {count}")
                
                if availability_tier_counts:
                    print(f"\nAvailability Tiers:")
                    for tier, count in sorted(availability_tier_counts.items()):
                        print(f"  • {tier}: {count}")
                
                # Borrowing cost analysis
                rates = [s['borrow_rate'] for s in securities if s['borrow_rate'] > 0]
                fees = [s['borrow_fee'] for s in securities if s['borrow_fee'] > 0]
                
                if rates:
                    avg_rate = sum(rates) / len(rates)
                    min_rate = min(rates)
                    max_rate = max(rates)
                    
                    print(f"\nBorrowing Rates:")
                    print(f"  • Securities with rates: {len(rates)}")
                    print(f"  • Average rate: {avg_rate:.4f}%")
                    print(f"  • Rate range: {min_rate:.4f}% - {max_rate:.4f}%")
                
                if fees:
                    avg_fee = sum(fees) / len(fees)
                    min_fee = min(fees)
                    max_fee = max(fees)
                    
                    print(f"\nBorrowing Fees:")
                    print(f"  • Securities with fees: {len(fees)}")
                    print(f"  • Average fee: ${avg_fee:.2f}")
                    print(f"  • Fee range: ${min_fee:.2f} - ${max_fee:.2f}")
                
                # Availability analysis
                total_shares = sum(s['available_shares'] for s in securities if s['available_shares'] > 0)
                securities_with_shares = [s for s in securities if s['available_shares'] > 0]
                
                if securities_with_shares:
                    avg_availability = total_shares / len(securities_with_shares)
                    print(f"\nShare Availability:")
                    print(f"  • Securities with share counts: {len(securities_with_shares)}")
                    print(f"  • Total available shares: {total_shares:,}")
                    print(f"  • Average availability: {avg_availability:,.0f} shares")
                
                # Special conditions analysis
                locate_required = len([s for s in securities if s['locate_required']])
                hard_to_borrow = len([s for s in securities if s['hard_to_borrow']])
                
                print(f"\nSpecial Conditions:")
                print(f"  • Locate Required: {locate_required} securities ({locate_required/len(securities)*100:.1f}%)")
                print(f"  • Hard to Borrow: {hard_to_borrow} securities ({hard_to_borrow/len(securities)*100:.1f}%)")
                
                # Market cap analysis
                market_caps = [s['market_cap'] for s in securities if s['market_cap'] > 0]
                if market_caps:
                    large_cap = len([mc for mc in market_caps if mc > 10_000_000_000])  # >$10B
                    mid_cap = len([mc for mc in market_caps if 2_000_000_000 <= mc <= 10_000_000_000])  # $2B-$10B
                    small_cap = len([mc for mc in market_caps if mc < 2_000_000_000])  # <$2B
                    
                    print(f"\nMarket Cap Distribution:")
                    print(f"  • Large Cap (>$10B): {large_cap}")
                    print(f"  • Mid Cap ($2B-$10B): {mid_cap}")
                    print(f"  • Small Cap (<$2B): {small_cap}")
            
            else:
                print(f"\n📋 No easy to borrow securities found")
                print("   This could indicate:")
                print("   • No securities available for borrowing")
                print("   • Insufficient permissions for borrow list access")
                print("   • Filters may be too restrictive")
                print("   • No short selling capabilities configured")
            
            # CSV output
            if output_format == "csv" and securities:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Symbol", "Exchange", "Company_Name", "Security_Type", "Available_Shares", 
                              "Borrow_Rate", "Borrow_Fee", "Locate_Required", "Hard_To_Borrow",
                              "Minimum_Quantity", "Maximum_Quantity", "Sector", "Market_Cap", "Price"]
                print(",".join(csv_headers))
                
                for security in securities:
                    values = [
                        security['symbol'],
                        security['exchange'],
                        security['company_name'],
                        security['security_type'],
                        str(security['available_shares']),
                        str(security['borrow_rate']),
                        str(security['borrow_fee']),
                        str(security['locate_required']),
                        str(security['hard_to_borrow']),
                        str(security['minimum_quantity']),
                        str(security['maximum_quantity']),
                        security['sector'],
                        str(security['market_cap']),
                        str(security['price'])
                    ]
                    print(",".join(values))
            
            # Short selling insights
            if securities:
                print(f"\n💡 SHORT SELLING INSIGHTS:")
                print(f"✅ Found {len(securities)} securities available for short selling")
                
                # Cost efficiency assessment
                low_cost_securities = len([s for s in securities if 0 < s['borrow_rate'] <= 1.0])
                medium_cost_securities = len([s for s in securities if 1.0 < s['borrow_rate'] <= 5.0])
                high_cost_securities = len([s for s in securities if s['borrow_rate'] > 5.0])
                
                if rates:
                    print(f"• Cost Distribution:")
                    print(f"  - Low Cost (≤1%): {low_cost_securities} securities")
                    print(f"  - Medium Cost (1-5%): {medium_cost_securities} securities")
                    print(f"  - High Cost (>5%): {high_cost_securities} securities")
                
                # Accessibility assessment
                easy_access = len([s for s in securities if not s['locate_required'] and not s['hard_to_borrow']])
                moderate_access = len([s for s in securities if s['locate_required'] and not s['hard_to_borrow']])
                difficult_access = len([s for s in securities if s['hard_to_borrow']])
                
                print(f"• Accessibility:")
                print(f"  - Easy Access: {easy_access} securities")
                print(f"  - Moderate Access: {moderate_access} securities (locate required)")
                print(f"  - Difficult Access: {difficult_access} securities (hard to borrow)")
                
                # Liquidity assessment
                if securities_with_shares:
                    high_liquidity = len([s for s in securities if s['available_shares'] > 100_000])
                    medium_liquidity = len([s for s in securities if 10_000 <= s['available_shares'] <= 100_000])
                    low_liquidity = len([s for s in securities if 0 < s['available_shares'] < 10_000])
                    
                    print(f"• Liquidity (by available shares):")
                    print(f"  - High Liquidity (>100K): {high_liquidity} securities")
                    print(f"  - Medium Liquidity (10K-100K): {medium_liquidity} securities")
                    print(f"  - Low Liquidity (<10K): {low_liquidity} securities")
                
                # Sector diversification
                if len(sector_counts) > 10:
                    print(f"• Excellent sector diversification ({len(sector_counts)} sectors)")
                elif len(sector_counts) > 5:
                    print(f"• Good sector diversification ({len(sector_counts)} sectors)")
                else:
                    print(f"• Limited sector diversification ({len(sector_counts)} sectors)")
        
        else:
            print("❌ Easy to borrow list request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions for easy to borrow list access")
            print("   • Check if account has short selling capabilities")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without filters to see complete list")
            print("   • Verify short selling permissions are properly configured")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing easy to borrow: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for easy to borrow testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Easy to Borrow List endpoint (Templates 348/349)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_easy_to_borrow.py
  python test_easy_to_borrow.py --symbol-filter "AAPL"
  python test_easy_to_borrow.py --exchange-filter "NASDAQ"
  python test_easy_to_borrow.py --format csv

Safety:
  This script performs READ-ONLY easy to borrow list requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays easy to borrow securities including:
  - Security identification and company information
  - Borrowing rates, fees, and availability
  - Quantity limits and special conditions
  - Market capitalization and sector analysis
  - Short selling accessibility and cost insights
        """
    )
    
    parser.add_argument(
        "--symbol-filter",
        type=str,
        help="Symbol pattern to filter results (optional)"
    )
    
    parser.add_argument(
        "--exchange-filter",
        type=str,
        help="Exchange filter (optional)"
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC EASY TO BORROW LIST ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY EASY TO BORROW OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_easy_to_borrow(
        symbol_filter=args.symbol_filter,
        exchange_filter=args.exchange_filter,
        account_id=args.account_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Easy to borrow test completed successfully!")
        return 0
    else:
        print("\n❌ Easy to borrow test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)