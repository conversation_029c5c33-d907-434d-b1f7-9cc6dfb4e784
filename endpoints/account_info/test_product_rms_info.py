#!/usr/bin/env python3

"""
Test Rithmic Product RMS Info endpoints (Templates 306/307).

This script demonstrates how to retrieve product-specific risk management system (RMS) information
from the Rithmic API, providing product-level risk parameters and margin requirements.

SAFETY: This script only performs READ-ONLY product RMS info requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_product_rms_info_pb2
import response_product_rms_info_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_product_rms_info(symbol: Optional[str] = None, exchange: Optional[str] = None,
                              output_format: str = "human") -> bool:
    """
    Test product RMS info retrieval functionality.
    
    Args:
        symbol: Optional specific symbol to query
        exchange: Optional exchange name
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🏭 TESTING PRODUCT RMS INFO ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.PRODUCT_RMS_INFO_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.PRODUCT_RMS_INFO_RESPONSE} (Response)")
    print(f"Symbol: {symbol or 'All products'}")
    print(f"Exchange: {exchange or 'All exchanges'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "product_rms_info_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create product RMS info request
        request = request_product_rms_info_pb2.RequestProductRmsInfo()
        request.template_id = TemplateIDs.PRODUCT_RMS_INFO_REQUEST
        request.user_msg.append("Product RMS info request from endpoint tester")
        
        # Set symbol and exchange if provided
        if symbol:
            request.symbol = symbol
        
        if exchange:
            request.exchange = exchange
        
        # Send product RMS info request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending product RMS info request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send product RMS info request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for product RMS info response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_product_rms_info_pb2.ResponseProductRmsInfo()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 PRODUCT RMS INFO ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        # Empty response codes might indicate success for some account endpoints
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Product RMS info request successful!")
            
            # Display product RMS information
            products = []
            if hasattr(response, 'symbol') and response.symbol:
                print(f"\n🏭 PRODUCT RMS INFORMATION ({len(response.symbol)} products):")
                print("-" * 80)
                
                for i, sym in enumerate(response.symbol):
                    product_rms = {
                        "symbol": sym,
                        "exchange": "",
                        "product_type": "",
                        "initial_margin": 0,
                        "maintenance_margin": 0,
                        "margin_multiplier": 0,
                        "max_position_qty": 0,
                        "max_order_qty": 0,
                        "price_increment": 0,
                        "min_price_increment": 0,
                        "contract_size": 0,
                        "currency": "",
                        "risk_discount": 0,
                        "day_margin": 0,
                        "overnight_margin": 0,
                        "intraday_margin": 0
                    }
                    
                    # Get corresponding RMS data
                    if hasattr(response, 'exchange') and i < len(response.exchange):
                        product_rms["exchange"] = response.exchange[i]
                    
                    if hasattr(response, 'product_type') and i < len(response.product_type):
                        product_rms["product_type"] = response.product_type[i]
                    
                    if hasattr(response, 'initial_margin') and i < len(response.initial_margin):
                        product_rms["initial_margin"] = response.initial_margin[i]
                    
                    if hasattr(response, 'maintenance_margin') and i < len(response.maintenance_margin):
                        product_rms["maintenance_margin"] = response.maintenance_margin[i]
                    
                    if hasattr(response, 'margin_multiplier') and i < len(response.margin_multiplier):
                        product_rms["margin_multiplier"] = response.margin_multiplier[i]
                    
                    if hasattr(response, 'max_position_qty') and i < len(response.max_position_qty):
                        product_rms["max_position_qty"] = response.max_position_qty[i]
                    
                    if hasattr(response, 'max_order_qty') and i < len(response.max_order_qty):
                        product_rms["max_order_qty"] = response.max_order_qty[i]
                    
                    if hasattr(response, 'price_increment') and i < len(response.price_increment):
                        product_rms["price_increment"] = response.price_increment[i]
                    
                    if hasattr(response, 'min_price_increment') and i < len(response.min_price_increment):
                        product_rms["min_price_increment"] = response.min_price_increment[i]
                    
                    if hasattr(response, 'contract_size') and i < len(response.contract_size):
                        product_rms["contract_size"] = response.contract_size[i]
                    
                    if hasattr(response, 'currency') and i < len(response.currency):
                        product_rms["currency"] = response.currency[i]
                    
                    if hasattr(response, 'risk_discount') and i < len(response.risk_discount):
                        product_rms["risk_discount"] = response.risk_discount[i]
                    
                    if hasattr(response, 'day_margin') and i < len(response.day_margin):
                        product_rms["day_margin"] = response.day_margin[i]
                    
                    if hasattr(response, 'overnight_margin') and i < len(response.overnight_margin):
                        product_rms["overnight_margin"] = response.overnight_margin[i]
                    
                    if hasattr(response, 'intraday_margin') and i < len(response.intraday_margin):
                        product_rms["intraday_margin"] = response.intraday_margin[i]
                    
                    products.append(product_rms)
                    
                    # Display product RMS info
                    print(f"{i+1:3d}. Symbol: {product_rms['symbol']}")
                    
                    if product_rms['exchange']:
                        print(f"     Exchange: {product_rms['exchange']}")
                    
                    if product_rms['product_type']:
                        print(f"     Product Type: {product_rms['product_type']}")
                    
                    if product_rms['currency']:
                        print(f"     Currency: {product_rms['currency']}")
                    
                    # Margin requirements
                    print(f"     💰 MARGIN REQUIREMENTS:")
                    if product_rms['initial_margin'] > 0:
                        print(f"       Initial Margin: ${product_rms['initial_margin']:,.2f}")
                    
                    if product_rms['maintenance_margin'] > 0:
                        print(f"       Maintenance Margin: ${product_rms['maintenance_margin']:,.2f}")
                    
                    if product_rms['day_margin'] > 0:
                        print(f"       Day Margin: ${product_rms['day_margin']:,.2f}")
                    
                    if product_rms['overnight_margin'] > 0:
                        print(f"       Overnight Margin: ${product_rms['overnight_margin']:,.2f}")
                    
                    if product_rms['intraday_margin'] > 0:
                        print(f"       Intraday Margin: ${product_rms['intraday_margin']:,.2f}")
                    
                    if product_rms['margin_multiplier'] > 0:
                        print(f"       Margin Multiplier: {product_rms['margin_multiplier']:.2f}x")
                    
                    # Position and order limits
                    print(f"     📊 POSITION & ORDER LIMITS:")
                    if product_rms['max_position_qty'] > 0:
                        print(f"       Max Position Qty: {product_rms['max_position_qty']:,}")
                    
                    if product_rms['max_order_qty'] > 0:
                        print(f"       Max Order Qty: {product_rms['max_order_qty']:,}")
                    
                    # Contract specifications
                    print(f"     📋 CONTRACT SPECIFICATIONS:")
                    if product_rms['contract_size'] > 0:
                        print(f"       Contract Size: {product_rms['contract_size']:,}")
                    
                    if product_rms['price_increment'] > 0:
                        print(f"       Price Increment: {product_rms['price_increment']}")
                    
                    if product_rms['min_price_increment'] > 0:
                        print(f"       Min Price Increment: {product_rms['min_price_increment']}")
                    
                    # Risk parameters
                    if product_rms['risk_discount'] > 0:
                        print(f"     ⚖️ Risk Discount: {product_rms['risk_discount']:.2f}%")
                    
                    print()
                
                # Summary statistics
                print(f"\n📈 PRODUCT RMS SUMMARY:")
                print(f"Total Products: {len(products)}")
                
                # Product type breakdown
                product_types = {}
                exchanges = set()
                currencies = set()
                
                for product in products:
                    prod_type = product['product_type'] or 'Unknown'
                    product_types[prod_type] = product_types.get(prod_type, 0) + 1
                    
                    if product['exchange']:
                        exchanges.add(product['exchange'])
                    
                    if product['currency']:
                        currencies.add(product['currency'])
                
                if product_types:
                    print(f"Product Types:")
                    for prod_type, count in product_types.items():
                        print(f"  • {prod_type}: {count}")
                
                if exchanges:
                    print(f"Exchanges: {', '.join(sorted(exchanges))}")
                
                if currencies:
                    print(f"Currencies: {', '.join(sorted(currencies))}")
                
                # Margin analysis
                margin_products = [p for p in products if p['initial_margin'] > 0]
                if margin_products:
                    avg_initial = sum(p['initial_margin'] for p in margin_products) / len(margin_products)
                    min_initial = min(p['initial_margin'] for p in margin_products)
                    max_initial = max(p['initial_margin'] for p in margin_products)
                    
                    print(f"\nMargin Analysis:")
                    print(f"  • Average Initial Margin: ${avg_initial:,.2f}")
                    print(f"  • Margin Range: ${min_initial:,.2f} - ${max_initial:,.2f}")
                
                # Position limit analysis
                position_products = [p for p in products if p['max_position_qty'] > 0]
                if position_products:
                    avg_position = sum(p['max_position_qty'] for p in position_products) / len(position_products)
                    print(f"  • Average Max Position: {avg_position:,.0f}")
            
            else:
                print(f"\n📋 No product RMS information found")
                print("   This could indicate:")
                print("   • No products accessible with current credentials")
                print("   • Insufficient permissions for product RMS data")
                print("   • Symbol/exchange filter may be too restrictive")
                print("   • No products configured in the system")
            
            # CSV output
            if output_format == "csv" and products:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Symbol", "Exchange", "Product_Type", "Currency",
                              "Initial_Margin", "Maintenance_Margin", "Day_Margin", 
                              "Overnight_Margin", "Max_Position_Qty", "Max_Order_Qty",
                              "Contract_Size", "Price_Increment", "Risk_Discount"]
                print(",".join(csv_headers))
                
                for product in products:
                    values = [
                        product['symbol'],
                        product['exchange'],
                        product['product_type'],
                        product['currency'],
                        str(product['initial_margin']),
                        str(product['maintenance_margin']),
                        str(product['day_margin']),
                        str(product['overnight_margin']),
                        str(product['max_position_qty']),
                        str(product['max_order_qty']),
                        str(product['contract_size']),
                        str(product['price_increment']),
                        str(product['risk_discount'])
                    ]
                    print(",".join(values))
            
            # Risk analysis by product category
            if products:
                print(f"\n🔍 PRODUCT RISK ANALYSIS:")
                
                # Categorize by margin requirements
                high_margin = [p for p in products if p['initial_margin'] > 10000]
                medium_margin = [p for p in products if 1000 < p['initial_margin'] <= 10000]
                low_margin = [p for p in products if 0 < p['initial_margin'] <= 1000]
                
                print(f"Margin Categories:")
                print(f"  • High Margin (>$10K): {len(high_margin)} products")
                print(f"  • Medium Margin ($1K-$10K): {len(medium_margin)} products")
                print(f"  • Low Margin (<$1K): {len(low_margin)} products")
                
                if high_margin:
                    print(f"  High Margin Products: {', '.join([p['symbol'] for p in high_margin[:5]])}")
                
                # Position limit categories
                high_qty = [p for p in products if p['max_position_qty'] > 1000]
                medium_qty = [p for p in products if 100 < p['max_position_qty'] <= 1000]
                low_qty = [p for p in products if 0 < p['max_position_qty'] <= 100]
                
                print(f"\nPosition Limit Categories:")
                print(f"  • High Qty (>1000): {len(high_qty)} products")
                print(f"  • Medium Qty (100-1000): {len(medium_qty)} products")
                print(f"  • Low Qty (<100): {len(low_qty)} products")
                
                # Risk assessment
                risky_products = []
                for product in products:
                    if (product['initial_margin'] > 5000 and 
                        product['max_position_qty'] > 500):
                        risky_products.append(product['symbol'])
                
                if risky_products:
                    print(f"\nHigh Risk Products (High Margin + High Position Limits):")
                    print(f"  {', '.join(risky_products[:10])}")
                    if len(risky_products) > 10:
                        print(f"  ... and {len(risky_products) - 10} more")
        
        else:
            print("❌ Product RMS info request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions for product RMS data access")
            print("   • Check if symbol and exchange are valid")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without symbol/exchange filter to see all products")
            print("   • Verify RMS configuration is available")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing product RMS info: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for product RMS info testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Product RMS Info endpoint (Templates 306/307)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_product_rms_info.py
  python test_product_rms_info.py --symbol ESH5 --exchange CME
  python test_product_rms_info.py --exchange CME
  python test_product_rms_info.py --format csv

Safety:
  This script performs READ-ONLY product RMS info requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays comprehensive product RMS information including:
  - Product identification (symbol, exchange, type, currency)
  - Margin requirements (initial, maintenance, day, overnight, intraday)
  - Position and order limits (max position qty, max order qty)
  - Contract specifications (size, price increments)
  - Risk parameters (discount, multipliers)
  - Product categorization and risk analysis
  - Summary statistics by product type and exchange
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        help="Specific symbol to query (optional, defaults to all products)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange filter (optional, defaults to all exchanges)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC PRODUCT RMS INFO ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY PRODUCT RMS INFO OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_product_rms_info(
        symbol=args.symbol,
        exchange=args.exchange,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Product RMS info test completed successfully!")
        return 0
    else:
        print("\n❌ Product RMS info test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)