#!/usr/bin/env python3

"""
Test Rithmic Show Orders endpoints (Templates 320/321).

This script demonstrates how to retrieve current/active orders
from the Rithmic API, showing live order status and details.

SAFETY: This script only performs READ-ONLY show orders requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_show_orders_pb2
import response_show_orders_pb2
import rithmic_order_notification_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_show_orders(account_id: Optional[str] = None, fcm_id: Optional[str] = None,
                          ib_id: Optional[str] = None, output_format: str = "human") -> bool:
    """
    Test show orders functionality.
    
    Args:
        account_id: Optional specific account ID to query
        fcm_id: Optional FCM ID filter
        ib_id: Optional IB ID filter
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📋 TESTING SHOW ORDERS ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.SHOW_ORDERS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.SHOW_ORDERS_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"FCM ID: {fcm_id or 'All FCMs'}")
    print(f"IB ID: {ib_id or 'All IBs'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "show_orders_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create show orders request
        request = request_show_orders_pb2.RequestShowOrders()
        request.template_id = TemplateIDs.SHOW_ORDERS_REQUEST
        request.user_msg.append("Show orders request from endpoint tester")
        
        # Set filters - FCM ID and IB ID are required by server even if empty
        # Account ID is also required by the server
        request.account_id = account_id if account_id else "demo"
        
        # FCM ID is required by the server (error 1039 if missing)
        # Use default paper trading FCM ID if none provided
        request.fcm_id = fcm_id if fcm_id else "demo"
            
        # IB ID is also required by the server  
        request.ib_id = ib_id if ib_id else "demo"
        
        # Send show orders request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending show orders request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send show orders request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for show orders response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_show_orders_pb2.ResponseShowOrders()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 SHOW ORDERS ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Show orders request successful!")
            
            # Listen for order notifications
            print("\n📋 LISTENING FOR ORDER NOTIFICATIONS...")
            print("(Waiting up to 10 seconds for order data...)")
            
            orders = []
            start_time = asyncio.get_event_loop().time()
            timeout = 10.0  # Wait up to 10 seconds for order data
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                try:
                    # Wait for order notifications
                    order_bytes = await connection.receive_message(timeout=2.0)
                    
                    if not order_bytes:
                        continue  # Timeout, keep listening
                    
                    # Parse notification to determine type
                    order_info = parse_message(order_bytes)
                    if not order_info:
                        continue
                    
                    template_id = order_info.template_id
                    
                    # Check if this is an order notification
                    if template_id == TemplateIDs.RITHMIC_ORDER_NOTIFICATION:
                        # Parse order notification
                        order_notification = rithmic_order_notification_pb2.RithmicOrderNotification()
                        order_notification.ParseFromString(order_bytes)
                        
                        # Extract order data
                        order_data = {
                            "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                            "account_id": getattr(order_notification, 'account_id', ''),
                            "symbol": getattr(order_notification, 'symbol', ''),
                            "exchange": getattr(order_notification, 'exchange', ''),
                            "order_tag": getattr(order_notification, 'order_tag', ''),
                            "basket_id": getattr(order_notification, 'basket_id', ''),
                            "transaction_type": getattr(order_notification, 'transaction_type', ''),
                            "duration": getattr(order_notification, 'duration', ''),
                            "price_type": getattr(order_notification, 'price_type', ''),
                            "quantity": getattr(order_notification, 'quantity', 0),
                            "price": getattr(order_notification, 'price', 0),
                            "trigger_price": getattr(order_notification, 'trigger_price', 0),
                            "status": getattr(order_notification, 'status', ''),
                            "order_num": getattr(order_notification, 'order_num', ''),
                            "currency": getattr(order_notification, 'currency', ''),
                            "fill_quantity": getattr(order_notification, 'fill_quantity', 0),
                            "remaining_quantity": getattr(order_notification, 'remaining_quantity', 0),
                            "avg_fill_price": getattr(order_notification, 'avg_fill_price', 0),
                            "total_fill_size": getattr(order_notification, 'total_fill_size', 0),
                            "order_date": getattr(order_notification, 'order_date', ''),
                            "order_time": getattr(order_notification, 'order_time', ''),
                            "modify_date": getattr(order_notification, 'modify_date', ''),
                            "modify_time": getattr(order_notification, 'modify_time', '')
                        }
                        
                        orders.append(order_data)
                        print(f"📋 Received order notification: {order_data['symbol']} {order_data['transaction_type']}")
                    
                    else:
                        # Other notification types
                        print(f"📡 Other notification: Template {template_id}")
                
                except asyncio.TimeoutError:
                    # No more order notifications
                    break
                except Exception as e:
                    logger.warning(f"Error processing order notification: {e}")
                    continue
            
            # Display order results
            if orders:
                print(f"\n📋 CURRENT ORDERS ({len(orders)} orders):")
                print("-" * 80)
                
                for i, order in enumerate(orders):
                    print(f"{i+1:3d}. Order: {order['symbol']}@{order['exchange']}")
                    print(f"     Order #: {order['order_num']}")
                    print(f"     Account: {order['account_id']}")
                    
                    if order['order_tag']:
                        print(f"     Tag: {order['order_tag']}")
                    
                    if order['basket_id']:
                        print(f"     Basket: {order['basket_id']}")
                    
                    # Order type and details
                    print(f"     Type: {order['transaction_type']} {order['quantity']:,} @ {order['price_type']}")
                    
                    if order['price'] > 0:
                        print(f"     Price: ${order['price']:,.2f}")
                    
                    if order['trigger_price'] > 0:
                        print(f"     Trigger: ${order['trigger_price']:,.2f}")
                    
                    print(f"     Duration: {order['duration']}")
                    print(f"     Status: {order['status']}")
                    
                    # Fill information
                    if order['fill_quantity'] > 0:
                        print(f"     Filled: {order['fill_quantity']:,} / {order['quantity']:,}")
                        fill_pct = (order['fill_quantity'] / order['quantity']) * 100
                        print(f"     Fill %: {fill_pct:.1f}%")
                        
                        if order['avg_fill_price'] > 0:
                            print(f"     Avg Fill Price: ${order['avg_fill_price']:,.2f}")
                    
                    if order['remaining_quantity'] > 0:
                        print(f"     Remaining: {order['remaining_quantity']:,}")
                    
                    # Timestamps
                    if order['order_date'] and order['order_time']:
                        print(f"     Order Time: {order['order_date']} {order['order_time']}")
                    
                    if order['modify_date'] and order['modify_time']:
                        print(f"     Modified: {order['modify_date']} {order['modify_time']}")
                    
                    print()
                
                # Order summary statistics
                print(f"\n📈 ORDER SUMMARY:")
                print(f"Total Orders: {len(orders)}")
                
                # Status breakdown
                status_counts = {}
                transaction_counts = {}
                symbol_counts = {}
                
                for order in orders:
                    status = order['status'] or 'Unknown'
                    status_counts[status] = status_counts.get(status, 0) + 1
                    
                    transaction = order['transaction_type'] or 'Unknown'
                    transaction_counts[transaction] = transaction_counts.get(transaction, 0) + 1
                    
                    symbol = order['symbol'] or 'Unknown'
                    symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
                
                if status_counts:
                    print(f"Order Status:")
                    for status, count in status_counts.items():
                        print(f"  • {status}: {count}")
                
                if transaction_counts:
                    print(f"Transaction Types:")
                    for trans_type, count in transaction_counts.items():
                        print(f"  • {trans_type}: {count}")
                
                if symbol_counts:
                    print(f"Symbols:")
                    for symbol, count in symbol_counts.items():
                        print(f"  • {symbol}: {count}")
                
                # Fill analysis
                filled_orders = [o for o in orders if o['fill_quantity'] > 0]
                pending_orders = [o for o in orders if o['remaining_quantity'] > 0]
                
                print(f"\nOrder Activity:")
                print(f"  • Filled Orders: {len(filled_orders)}")
                print(f"  • Pending Orders: {len(pending_orders)}")
                
                if filled_orders:
                    total_filled_qty = sum(o['fill_quantity'] for o in filled_orders)
                    print(f"  • Total Filled Quantity: {total_filled_qty:,}")
                
                if pending_orders:
                    total_pending_qty = sum(o['remaining_quantity'] for o in pending_orders)
                    print(f"  • Total Pending Quantity: {total_pending_qty:,}")
            
            else:
                print(f"\n📋 No current orders found")
                print("   This could indicate:")
                print("   • No active orders in the account")
                print("   • All orders have been filled or cancelled")
                print("   • Account filter may be too restrictive")
                print("   • Orders may be on different FCM/IB")
            
            # CSV output
            if output_format == "csv" and orders:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Timestamp", "Order_Num", "Account_ID", "Symbol", "Exchange", 
                              "Transaction_Type", "Quantity", "Price", "Status", "Fill_Quantity", 
                              "Remaining_Quantity", "Duration", "Order_Date", "Order_Time"]
                print(",".join(csv_headers))
                
                for order in orders:
                    values = [
                        order['timestamp'],
                        order['order_num'],
                        order['account_id'],
                        order['symbol'],
                        order['exchange'],
                        order['transaction_type'],
                        str(order['quantity']),
                        str(order['price']),
                        order['status'],
                        str(order['fill_quantity']),
                        str(order['remaining_quantity']),
                        order['duration'],
                        order['order_date'],
                        order['order_time']
                    ]
                    print(",".join(values))
            
            # Order management insights
            if orders:
                print(f"\n💡 ORDER MANAGEMENT INSIGHTS:")
                print(f"✅ Successfully retrieved {len(orders)} current orders")
                
                # Risk assessment
                high_qty_orders = [o for o in orders if o['quantity'] > 1000]
                if high_qty_orders:
                    print(f"• {len(high_qty_orders)} orders with high quantity (>1000)")
                
                # Market vs limit analysis
                market_orders = [o for o in orders if o['price_type'] == 'MARKET']
                limit_orders = [o for o in orders if o['price_type'] == 'LIMIT']
                
                if market_orders:
                    print(f"• {len(market_orders)} market orders (immediate execution)")
                if limit_orders:
                    print(f"• {len(limit_orders)} limit orders (price-specific)")
                
                # Duration analysis
                day_orders = [o for o in orders if o['duration'] == 'DAY']
                gtc_orders = [o for o in orders if o['duration'] == 'GTC']
                
                if day_orders:
                    print(f"• {len(day_orders)} day orders (expire end of session)")
                if gtc_orders:
                    print(f"• {len(gtc_orders)} GTC orders (good till cancelled)")
        
        else:
            print("❌ Show orders request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions for order viewing")
            print("   • Check if account/FCM/IB IDs are valid")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without filters to see all orders")
            print("   • Verify order visibility permissions")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing show orders: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for show orders testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Show Orders endpoint (Templates 320/321)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_show_orders.py
  python test_show_orders.py --account-id "123456"
  python test_show_orders.py --fcm-id "FCM01" --ib-id "IB01"
  python test_show_orders.py --format csv

Safety:
  This script performs READ-ONLY show orders requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays current/active orders including:
  - Order details (symbol, quantity, price, type)
  - Order status and fill information
  - Account and routing information
  - Order timing and modification history
  - Summary statistics and analysis
  - Risk assessment and insights
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--fcm-id",
        type=str,
        help="FCM ID filter (optional)"
    )
    
    parser.add_argument(
        "--ib-id",
        type=str,
        help="IB ID filter (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC SHOW ORDERS ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY SHOW ORDERS OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_show_orders(
        account_id=args.account_id,
        fcm_id=args.fcm_id,
        ib_id=args.ib_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Show orders test completed successfully!")
        return 0
    else:
        print("\n❌ Show orders test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)