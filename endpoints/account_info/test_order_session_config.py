#!/usr/bin/env python3

"""
Test Rithmic Order Session Config endpoints (Templates 3502/3503).

This script demonstrates how to retrieve order session configuration
from the Rithmic API, showing session parameters and trading settings.

SAFETY: This script only performs READ-ONLY order session config requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_order_session_config_pb2
import response_order_session_config_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message
from timeout_manager import TimeoutManager, managed_timeout

logger = logging.getLogger(__name__)

async def test_order_session_config(account_id: Optional[str] = None, fcm_id: Optional[str] = None,
                                   ib_id: Optional[str] = None, output_format: str = "human") -> bool:
    """
    Test order session configuration functionality.
    
    Args:
        account_id: Optional specific account ID to query
        fcm_id: Optional FCM ID filter
        ib_id: Optional IB ID filter
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("⚙️  TESTING ORDER SESSION CONFIG ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.ORDER_SESSION_CONFIG_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.ORDER_SESSION_CONFIG_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"FCM ID: {fcm_id or 'All FCMs'}")
    print(f"IB ID: {ib_id or 'All IBs'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "order_session_config_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create order session config request
        request = request_order_session_config_pb2.RequestOrderSessionConfig()
        request.template_id = TemplateIDs.ORDER_SESSION_CONFIG_REQUEST
        request.user_msg.append("Order session config request from endpoint tester")
        
        # Set filters - FCM ID and IB ID are required by server even if empty
        # Account ID is also required by the server
        request.account_id = account_id if account_id else "demo"
        
        # FCM ID is required by the server (error 1039 if missing)
        # Use default paper trading FCM ID if none provided
        request.fcm_id = fcm_id if fcm_id else "demo"
            
        # IB ID is also required by the server  
        request.ib_id = ib_id if ib_id else "demo"
        
        # Send order session config request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending order session config request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send order session config request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response with advanced timeout management
        print("⏳ Waiting for order session config response...")
        
        timeout_manager = TimeoutManager("order_session_config", InfraType.ORDER_PLANT)
        
        async def receive_response():
            return await connection.receive_message(timeout=30)  # Base timeout, will be managed by timeout_manager
        
        timeout_result = await timeout_manager.execute_with_progressive_timeout(
            receive_response, "order session config response"
        )
        
        if not timeout_result.success:
            print(f"❌ No response received: {timeout_result.error_message}")
            return False
            
        response_bytes = timeout_result.result
        
        # Response validation handled by timeout manager
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_order_session_config_pb2.ResponseOrderSessionConfig()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 ORDER SESSION CONFIG ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Order session config request successful!")
            
            # Display order session configuration information
            configs = []
            if hasattr(response, 'session_name') and response.session_name:
                print(f"\n⚙️  ORDER SESSION CONFIGURATIONS ({len(response.session_name)} sessions):")
                print("-" * 100)
                
                for i, session_name in enumerate(response.session_name):
                    config_info = {
                        "session_name": session_name,
                        "account_id": "",
                        "fcm_id": "",
                        "ib_id": "",
                        "session_template": "",
                        "exchange": "",
                        "session_type": "",
                        "max_order_qty": 0,
                        "max_position_qty": 0,
                        "max_orders_per_minute": 0,
                        "max_daily_loss": 0,
                        "trading_hours_start": "",
                        "trading_hours_end": "",
                        "risk_check_enabled": False,
                        "pre_trade_risk": False,
                        "post_trade_risk": False,
                        "position_limit_check": False,
                        "order_size_limit": False,
                        "price_band_check": False,
                        "enabled": True,
                        "default_session": False,
                        "session_timeout": 0,
                        "heartbeat_interval": 0,
                        "order_entry_mode": "",
                        "execution_mode": "",
                        "reject_duplicate_orders": False,
                        "allow_market_orders": True,
                        "allow_stop_orders": True,
                        "allow_limit_orders": True,
                        "cancel_on_disconnect": False
                    }
                    
                    # Get corresponding configuration data
                    if hasattr(response, 'account_id') and i < len(response.account_id):
                        config_info["account_id"] = response.account_id[i]
                    
                    if hasattr(response, 'fcm_id') and i < len(response.fcm_id):
                        config_info["fcm_id"] = response.fcm_id[i]
                    
                    if hasattr(response, 'ib_id') and i < len(response.ib_id):
                        config_info["ib_id"] = response.ib_id[i]
                    
                    if hasattr(response, 'session_template') and i < len(response.session_template):
                        config_info["session_template"] = response.session_template[i]
                    
                    if hasattr(response, 'exchange') and i < len(response.exchange):
                        config_info["exchange"] = response.exchange[i]
                    
                    if hasattr(response, 'session_type') and i < len(response.session_type):
                        config_info["session_type"] = response.session_type[i]
                    
                    if hasattr(response, 'max_order_qty') and i < len(response.max_order_qty):
                        config_info["max_order_qty"] = response.max_order_qty[i]
                    
                    if hasattr(response, 'max_position_qty') and i < len(response.max_position_qty):
                        config_info["max_position_qty"] = response.max_position_qty[i]
                    
                    if hasattr(response, 'max_orders_per_minute') and i < len(response.max_orders_per_minute):
                        config_info["max_orders_per_minute"] = response.max_orders_per_minute[i]
                    
                    if hasattr(response, 'max_daily_loss') and i < len(response.max_daily_loss):
                        config_info["max_daily_loss"] = response.max_daily_loss[i]
                    
                    if hasattr(response, 'trading_hours_start') and i < len(response.trading_hours_start):
                        config_info["trading_hours_start"] = response.trading_hours_start[i]
                    
                    if hasattr(response, 'trading_hours_end') and i < len(response.trading_hours_end):
                        config_info["trading_hours_end"] = response.trading_hours_end[i]
                    
                    if hasattr(response, 'risk_check_enabled') and i < len(response.risk_check_enabled):
                        config_info["risk_check_enabled"] = response.risk_check_enabled[i]
                    
                    if hasattr(response, 'pre_trade_risk') and i < len(response.pre_trade_risk):
                        config_info["pre_trade_risk"] = response.pre_trade_risk[i]
                    
                    if hasattr(response, 'post_trade_risk') and i < len(response.post_trade_risk):
                        config_info["post_trade_risk"] = response.post_trade_risk[i]
                    
                    if hasattr(response, 'position_limit_check') and i < len(response.position_limit_check):
                        config_info["position_limit_check"] = response.position_limit_check[i]
                    
                    if hasattr(response, 'order_size_limit') and i < len(response.order_size_limit):
                        config_info["order_size_limit"] = response.order_size_limit[i]
                    
                    if hasattr(response, 'price_band_check') and i < len(response.price_band_check):
                        config_info["price_band_check"] = response.price_band_check[i]
                    
                    if hasattr(response, 'enabled') and i < len(response.enabled):
                        config_info["enabled"] = response.enabled[i]
                    
                    if hasattr(response, 'default_session') and i < len(response.default_session):
                        config_info["default_session"] = response.default_session[i]
                    
                    if hasattr(response, 'session_timeout') and i < len(response.session_timeout):
                        config_info["session_timeout"] = response.session_timeout[i]
                    
                    if hasattr(response, 'heartbeat_interval') and i < len(response.heartbeat_interval):
                        config_info["heartbeat_interval"] = response.heartbeat_interval[i]
                    
                    if hasattr(response, 'order_entry_mode') and i < len(response.order_entry_mode):
                        config_info["order_entry_mode"] = response.order_entry_mode[i]
                    
                    if hasattr(response, 'execution_mode') and i < len(response.execution_mode):
                        config_info["execution_mode"] = response.execution_mode[i]
                    
                    if hasattr(response, 'reject_duplicate_orders') and i < len(response.reject_duplicate_orders):
                        config_info["reject_duplicate_orders"] = response.reject_duplicate_orders[i]
                    
                    if hasattr(response, 'allow_market_orders') and i < len(response.allow_market_orders):
                        config_info["allow_market_orders"] = response.allow_market_orders[i]
                    
                    if hasattr(response, 'allow_stop_orders') and i < len(response.allow_stop_orders):
                        config_info["allow_stop_orders"] = response.allow_stop_orders[i]
                    
                    if hasattr(response, 'allow_limit_orders') and i < len(response.allow_limit_orders):
                        config_info["allow_limit_orders"] = response.allow_limit_orders[i]
                    
                    if hasattr(response, 'cancel_on_disconnect') and i < len(response.cancel_on_disconnect):
                        config_info["cancel_on_disconnect"] = response.cancel_on_disconnect[i]
                    
                    configs.append(config_info)
                    
                    # Display session configuration
                    print(f"{i+1:3d}. Session: {config_info['session_name']}")
                    
                    # Account hierarchy
                    if config_info['account_id'] or config_info['fcm_id'] or config_info['ib_id']:
                        print(f"     📍 ACCOUNT HIERARCHY:")
                        if config_info['fcm_id']:
                            print(f"       FCM ID: {config_info['fcm_id']}")
                        if config_info['ib_id']:
                            print(f"       IB ID: {config_info['ib_id']}")
                        if config_info['account_id']:
                            print(f"       Account ID: {config_info['account_id']}")
                    
                    # Session details
                    print(f"     ⚙️  SESSION DETAILS:")
                    if config_info['session_template']:
                        print(f"       Template: {config_info['session_template']}")
                    if config_info['exchange']:
                        print(f"       Exchange: {config_info['exchange']}")
                    if config_info['session_type']:
                        print(f"       Type: {config_info['session_type']}")
                    
                    # Status
                    status = "✅ ENABLED" if config_info['enabled'] else "❌ DISABLED"
                    print(f"       Status: {status}")
                    
                    if config_info['default_session']:
                        print(f"       Default Session: ⭐ YES")
                    
                    # Trading limits
                    print(f"     📊 TRADING LIMITS:")
                    if config_info['max_order_qty'] > 0:
                        print(f"       Max Order Qty: {config_info['max_order_qty']:,}")
                    
                    if config_info['max_position_qty'] > 0:
                        print(f"       Max Position Qty: {config_info['max_position_qty']:,}")
                    
                    if config_info['max_orders_per_minute'] > 0:
                        print(f"       Max Orders/Minute: {config_info['max_orders_per_minute']}")
                    
                    if config_info['max_daily_loss'] > 0:
                        print(f"       Max Daily Loss: ${config_info['max_daily_loss']:,.2f}")
                    
                    # Trading hours
                    if config_info['trading_hours_start'] or config_info['trading_hours_end']:
                        print(f"     🕐 TRADING HOURS:")
                        if config_info['trading_hours_start']:
                            print(f"       Start: {config_info['trading_hours_start']}")
                        if config_info['trading_hours_end']:
                            print(f"       End: {config_info['trading_hours_end']}")
                    
                    # Risk management
                    print(f"     🛡️  RISK MANAGEMENT:")
                    risk_features = []
                    if config_info['risk_check_enabled']:
                        risk_features.append("Risk Checks")
                    if config_info['pre_trade_risk']:
                        risk_features.append("Pre-Trade Risk")
                    if config_info['post_trade_risk']:
                        risk_features.append("Post-Trade Risk")
                    if config_info['position_limit_check']:
                        risk_features.append("Position Limits")
                    if config_info['order_size_limit']:
                        risk_features.append("Order Size Limits")
                    if config_info['price_band_check']:
                        risk_features.append("Price Band Checks")
                    
                    if risk_features:
                        print(f"       Enabled: {', '.join(risk_features)}")
                    else:
                        print(f"       Enabled: None")
                    
                    # Order types allowed
                    print(f"     📋 ORDER TYPES ALLOWED:")
                    allowed_types = []
                    if config_info['allow_market_orders']:
                        allowed_types.append("Market")
                    if config_info['allow_limit_orders']:
                        allowed_types.append("Limit")
                    if config_info['allow_stop_orders']:
                        allowed_types.append("Stop")
                    
                    if allowed_types:
                        print(f"       Types: {', '.join(allowed_types)}")
                    else:
                        print(f"       Types: None allowed")
                    
                    # Session behavior
                    print(f"     🔧 SESSION BEHAVIOR:")
                    if config_info['order_entry_mode']:
                        print(f"       Order Entry Mode: {config_info['order_entry_mode']}")
                    if config_info['execution_mode']:
                        print(f"       Execution Mode: {config_info['execution_mode']}")
                    
                    if config_info['reject_duplicate_orders']:
                        print(f"       Duplicate Orders: ❌ REJECTED")
                    else:
                        print(f"       Duplicate Orders: ✅ ALLOWED")
                    
                    if config_info['cancel_on_disconnect']:
                        print(f"       Cancel on Disconnect: ✅ ENABLED")
                    else:
                        print(f"       Cancel on Disconnect: ❌ DISABLED")
                    
                    # Connection settings
                    if config_info['session_timeout'] > 0 or config_info['heartbeat_interval'] > 0:
                        print(f"     🌐 CONNECTION SETTINGS:")
                        if config_info['session_timeout'] > 0:
                            print(f"       Session Timeout: {config_info['session_timeout']} seconds")
                        if config_info['heartbeat_interval'] > 0:
                            print(f"       Heartbeat Interval: {config_info['heartbeat_interval']} seconds")
                    
                    print()
                
                # Session configuration analysis
                print(f"\n📈 SESSION CONFIGURATION SUMMARY:")
                print(f"Total Sessions: {len(configs)}")
                
                # Session analysis
                session_type_counts = {}
                exchange_counts = {}
                template_counts = {}
                
                for config in configs:
                    if config['session_type']:
                        stype = config['session_type']
                        session_type_counts[stype] = session_type_counts.get(stype, 0) + 1
                    
                    if config['exchange']:
                        exchange = config['exchange']
                        exchange_counts[exchange] = exchange_counts.get(exchange, 0) + 1
                    
                    if config['session_template']:
                        template = config['session_template']
                        template_counts[template] = template_counts.get(template, 0) + 1
                
                if session_type_counts:
                    print(f"\nSession Types:")
                    for stype, count in sorted(session_type_counts.items()):
                        print(f"  • {stype}: {count}")
                
                if exchange_counts:
                    print(f"\nExchange Coverage:")
                    for exchange, count in sorted(exchange_counts.items()):
                        print(f"  • {exchange}: {count} sessions")
                
                if template_counts:
                    print(f"\nSession Templates:")
                    for template, count in sorted(template_counts.items()):
                        print(f"  • {template}: {count}")
                
                # Status and configuration analysis
                enabled_sessions = [c for c in configs if c['enabled']]
                default_sessions = [c for c in configs if c['default_session']]
                risk_enabled_sessions = [c for c in configs if c['risk_check_enabled']]
                
                print(f"\nSession Status:")
                print(f"  • Enabled Sessions: {len(enabled_sessions)} ({len(enabled_sessions)/len(configs)*100:.1f}%)")
                print(f"  • Default Sessions: {len(default_sessions)}")
                print(f"  • Risk-Enabled Sessions: {len(risk_enabled_sessions)} ({len(risk_enabled_sessions)/len(configs)*100:.1f}%)")
                
                # Risk management analysis
                pre_trade_risk = len([c for c in configs if c['pre_trade_risk']])
                post_trade_risk = len([c for c in configs if c['post_trade_risk']])
                position_limits = len([c for c in configs if c['position_limit_check']])
                
                print(f"\nRisk Management Coverage:")
                print(f"  • Pre-Trade Risk: {pre_trade_risk} sessions")
                print(f"  • Post-Trade Risk: {post_trade_risk} sessions")
                print(f"  • Position Limit Checks: {position_limits} sessions")
                
                # Order type support
                market_orders = len([c for c in configs if c['allow_market_orders']])
                limit_orders = len([c for c in configs if c['allow_limit_orders']])
                stop_orders = len([c for c in configs if c['allow_stop_orders']])
                
                print(f"\nOrder Type Support:")
                print(f"  • Market Orders: {market_orders} sessions")
                print(f"  • Limit Orders: {limit_orders} sessions")
                print(f"  • Stop Orders: {stop_orders} sessions")
                
                # Limits analysis
                max_order_qtys = [c['max_order_qty'] for c in configs if c['max_order_qty'] > 0]
                max_position_qtys = [c['max_position_qty'] for c in configs if c['max_position_qty'] > 0]
                max_daily_losses = [c['max_daily_loss'] for c in configs if c['max_daily_loss'] > 0]
                
                if max_order_qtys:
                    avg_order_limit = sum(max_order_qtys) / len(max_order_qtys)
                    print(f"\nOrder Quantity Limits:")
                    print(f"  • Sessions with limits: {len(max_order_qtys)}")
                    print(f"  • Average limit: {avg_order_limit:,.0f}")
                    print(f"  • Range: {min(max_order_qtys):,} - {max(max_order_qtys):,}")
                
                if max_daily_losses:
                    avg_loss_limit = sum(max_daily_losses) / len(max_daily_losses)
                    print(f"\nDaily Loss Limits:")
                    print(f"  • Sessions with limits: {len(max_daily_losses)}")
                    print(f"  • Average limit: ${avg_loss_limit:,.2f}")
                    print(f"  • Range: ${min(max_daily_losses):,.2f} - ${max(max_daily_losses):,.2f}")
            
            else:
                print(f"\n📋 No order session configurations found")
                print("   This could indicate:")
                print("   • No session configurations set up for the account")
                print("   • Insufficient permissions to view session details")
                print("   • Account/FCM/IB filter may be too restrictive")
                print("   • Default session configurations may be in use")
            
            # CSV output
            if output_format == "csv" and configs:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Session_Name", "Account_ID", "FCM_ID", "IB_ID", "Exchange", 
                              "Session_Type", "Max_Order_Qty", "Max_Position_Qty", "Max_Daily_Loss",
                              "Risk_Check_Enabled", "Allow_Market_Orders", "Allow_Limit_Orders", 
                              "Allow_Stop_Orders", "Enabled", "Default_Session"]
                print(",".join(csv_headers))
                
                for config in configs:
                    values = [
                        config['session_name'],
                        config['account_id'],
                        config['fcm_id'],
                        config['ib_id'],
                        config['exchange'],
                        config['session_type'],
                        str(config['max_order_qty']),
                        str(config['max_position_qty']),
                        str(config['max_daily_loss']),
                        str(config['risk_check_enabled']),
                        str(config['allow_market_orders']),
                        str(config['allow_limit_orders']),
                        str(config['allow_stop_orders']),
                        str(config['enabled']),
                        str(config['default_session'])
                    ]
                    print(",".join(values))
            
            # Session optimization insights
            if configs:
                print(f"\n💡 SESSION CONFIGURATION INSIGHTS:")
                print(f"✅ Found {len(configs)} session configurations")
                
                # Configuration completeness
                if len(enabled_sessions) == len(configs):
                    print("• All sessions are properly enabled")
                elif len(enabled_sessions) > len(configs) * 0.8:
                    print("• Most sessions are enabled")
                else:
                    print(f"• {len(configs) - len(enabled_sessions)} sessions are disabled")
                    print("  Review disabled sessions for required functionality")
                
                # Risk management assessment
                if len(risk_enabled_sessions) > len(configs) * 0.8:
                    print("• Excellent risk management coverage")
                elif len(risk_enabled_sessions) > len(configs) * 0.5:
                    print("• Good risk management coverage")
                else:
                    print("• Limited risk management coverage")
                    print("  Consider enabling risk checks for better protection")
                
                # Order type flexibility
                full_order_support = len([c for c in configs if c['allow_market_orders'] and c['allow_limit_orders'] and c['allow_stop_orders']])
                if full_order_support > len(configs) * 0.8:
                    print("• Comprehensive order type support")
                else:
                    print("• Limited order type support")
                    print("  Review order type restrictions for trading flexibility")
                
                # Default session configuration
                if len(default_sessions) == 1:
                    print("• Proper default session configuration")
                elif len(default_sessions) > 1:
                    print(f"• Multiple default sessions detected ({len(default_sessions)})")
                    print("  Review default session settings")
                else:
                    print("• No default session configured")
                    print("  Consider setting a default session for easier trading")
                
                # Exchange coverage
                unique_exchanges = set(c['exchange'] for c in configs if c['exchange'])
                if len(unique_exchanges) > 1:
                    print(f"• Multi-exchange configuration ({len(unique_exchanges)} exchanges)")
                else:
                    print("• Single exchange configuration")
                
                # Risk limit assessment
                if max_daily_losses:
                    print("• Daily loss limits configured for risk protection")
                else:
                    print("• No daily loss limits detected")
                    print("  Consider adding daily loss limits for risk management")
        
        else:
            print("❌ Order session config request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Enhanced troubleshooting with timeout awareness
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions to view session configurations")
            print("   • Check if account/FCM/IB IDs are valid")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without filters to see all session configs")
            print("   • Verify session configurations are properly set up")
            print("   • Order Plant timeouts have been optimized - check network connectivity")
            print(f"   • Used progressive timeout strategy: {timeout_result.timeout_used}s (attempt {timeout_result.attempt_number})")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing order session config: {e}")
        print(f"❌ Test failed: {e}")
        
        # Provide timeout-specific error guidance
        if "timeout" in str(e).lower() or "TimeoutError" in str(type(e).__name__):
            print("\n⏰ TIMEOUT ERROR DETECTED:")
            print("   This endpoint has been optimized with progressive timeouts")
            print("   • Check network connectivity to Rithmic servers")
            print("   • Verify Order Plant access permissions")
            print("   • Try running during active market hours")
            print("   • Consider checking Rithmic server status")
        
        return False

async def main():
    """Main function for order session config testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Order Session Config endpoint (Templates 3502/3503)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_order_session_config.py
  python test_order_session_config.py --account-id "123456"
  python test_order_session_config.py --fcm-id "FCM01" --ib-id "IB01"
  python test_order_session_config.py --format csv

Safety:
  This script performs READ-ONLY order session config requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays order session configurations including:
  - Session details and hierarchy
  - Trading limits and risk management settings
  - Order type support and restrictions
  - Trading hours and session behavior
  - Risk management features and coverage analysis
  - Configuration optimization insights
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--fcm-id",
        type=str,
        help="FCM ID filter (optional)"
    )
    
    parser.add_argument(
        "--ib-id",
        type=str,
        help="IB ID filter (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC ORDER SESSION CONFIG ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY ORDER SESSION CONFIG OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_order_session_config(
        account_id=args.account_id,
        fcm_id=args.fcm_id,
        ib_id=args.ib_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Order session config test completed successfully!")
        return 0
    else:
        print("\n❌ Order session config test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)