#!/usr/bin/env python3

"""
Test Rithmic Show Brackets endpoints (Templates 338/339/340/341).

This script demonstrates how to retrieve bracket order information
from the Rithmic API, showing active bracket orders and their stop configurations.

SAFETY: This script only performs READ-ONLY bracket information requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_show_brackets_pb2
import response_show_brackets_pb2
import request_show_bracket_stops_pb2
import response_show_bracket_stops_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_bracket_info(account_id: Optional[str] = None, fcm_id: Optional[str] = None,
                           ib_id: Optional[str] = None, include_stops: bool = True,
                           output_format: str = "human") -> bool:
    """
    Test bracket information functionality.
    
    Args:
        account_id: Optional specific account ID to query
        fcm_id: Optional FCM ID filter
        ib_id: Optional IB ID filter
        include_stops: Whether to also query bracket stops
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📐 TESTING BRACKET INFO ENDPOINTS")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.SHOW_BRACKETS_REQUEST} (Show Brackets Request)")
    print(f"Template ID: {TemplateIDs.SHOW_BRACKETS_RESPONSE} (Show Brackets Response)")
    if include_stops:
        print(f"Template ID: {TemplateIDs.SHOW_BRACKET_STOPS_REQUEST} (Show Bracket Stops Request)")
        print(f"Template ID: {TemplateIDs.SHOW_BRACKET_STOPS_RESPONSE} (Show Bracket Stops Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"FCM ID: {fcm_id or 'All FCMs'}")
    print(f"IB ID: {ib_id or 'All IBs'}")
    print(f"Include Stops: {'✅ YES' if include_stops else '❌ NO'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "bracket_info_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # First, get bracket orders
        brackets_success = await _test_show_brackets(
            connection, account_id, fcm_id, ib_id, output_format, message_handler_obj)
        
        # Then, optionally get bracket stops
        stops_success = True
        if include_stops:
            stops_success = await _test_show_bracket_stops(
                connection, account_id, fcm_id, ib_id, output_format, message_handler_obj)
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return brackets_success and stops_success
        
    except Exception as e:
        logger.error(f"Error testing bracket info: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def _test_show_brackets(connection, account_id: Optional[str], fcm_id: Optional[str],
                             ib_id: Optional[str], output_format: str, message_handler_obj) -> bool:
    """Test the show brackets functionality."""
    
    print("\n📐 TESTING SHOW BRACKETS...")
    
    # Create show brackets request
    request = request_show_brackets_pb2.RequestShowBrackets()
    request.template_id = TemplateIDs.SHOW_BRACKETS_REQUEST
    request.user_msg.append("Show brackets request from endpoint tester")
    
    # Set filters - FCM ID is required by server even if empty
    # Account ID is also required by the server
    request.account_id = account_id if account_id else "demo"
    
    # FCM ID is required by the server (error 1039 if missing)
    # Use default paper trading FCM ID if none provided
    request.fcm_id = fcm_id if fcm_id else "demo"
        
    # IB ID is also required by the server  
    request.ib_id = ib_id if ib_id else "demo"
    
    # Send show brackets request
    serialized_request = request.SerializeToString()
    print(f"📤 Sending show brackets request ({len(serialized_request)} bytes)")
    
    if not await connection.send_message(serialized_request):
        print("❌ Failed to send show brackets request")
        return False
    
    # Parse and log the request
    request_info = parse_message(serialized_request)
    if request_info and output_format != "human":
        print("\n📋 REQUEST DETAILS:")
        print(message_handler_obj.format_message(request_info, output_format))
    
    # Wait for response
    print("⏳ Waiting for show brackets response...")
    config_obj = get_config()
    response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
    
    if not response_bytes:
        print("❌ No response received within timeout")
        return False
    
    print(f"📥 Received response ({len(response_bytes)} bytes)")
    
    # Parse response
    response = response_show_brackets_pb2.ResponseShowBrackets()
    response.ParseFromString(response_bytes)
    
    # Parse and format the response
    response_info = parse_message(response_bytes)
    if response_info and output_format != "human":
        print("\n📋 RESPONSE DETAILS:")
        print(message_handler_obj.format_message(response_info, output_format))
    
    # Analyze response content
    print("\n📊 SHOW BRACKETS ANALYSIS:")
    print(f"Template ID: {response.template_id}")
    print(f"User Messages: {list(response.user_msg)}")
    print(f"Response Code: {list(response.rp_code)}")
    
    # Check if successful - handle both explicit success codes and empty response codes
    explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
    
    # Check for error indicators
    has_error = False
    if len(response.rp_code) >= 2:
        has_error = True  # Error code and message present
    elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
        has_error = True  # Non-zero error code
    
    # Consider empty response codes as success if no explicit error
    empty_response_success = len(response.rp_code) == 0 and not has_error
    
    success = explicit_success or empty_response_success
    
    if success:
        print("✅ Show brackets request successful!")
        
        # Display bracket information
        brackets = []
        if hasattr(response, 'bracket_id') and response.bracket_id:
            print(f"\n📐 BRACKET ORDERS ({len(response.bracket_id)} brackets):")
            print("-" * 100)
            
            for i, bracket_id in enumerate(response.bracket_id):
                bracket_info = {
                    "bracket_id": bracket_id,
                    "account_id": "",
                    "fcm_id": "",
                    "ib_id": "",
                    "symbol": "",
                    "exchange": "",
                    "parent_order_id": "",
                    "target_order_id": "",
                    "stop_order_id": "",
                    "bracket_type": "",
                    "bracket_status": "",
                    "quantity": 0,
                    "target_price": 0,
                    "stop_price": 0,
                    "parent_price": 0,
                    "transaction_type": "",
                    "created_date": "",
                    "created_time": "",
                    "updated_date": "",
                    "updated_time": "",
                    "trailing_stop": False,
                    "trail_amount": 0,
                    "bracket_template": ""
                }
                
                # Get corresponding bracket data
                if hasattr(response, 'account_id') and i < len(response.account_id):
                    bracket_info["account_id"] = response.account_id[i]
                
                if hasattr(response, 'fcm_id') and i < len(response.fcm_id):
                    bracket_info["fcm_id"] = response.fcm_id[i]
                
                if hasattr(response, 'ib_id') and i < len(response.ib_id):
                    bracket_info["ib_id"] = response.ib_id[i]
                
                if hasattr(response, 'symbol') and i < len(response.symbol):
                    bracket_info["symbol"] = response.symbol[i]
                
                if hasattr(response, 'exchange') and i < len(response.exchange):
                    bracket_info["exchange"] = response.exchange[i]
                
                if hasattr(response, 'parent_order_id') and i < len(response.parent_order_id):
                    bracket_info["parent_order_id"] = response.parent_order_id[i]
                
                if hasattr(response, 'target_order_id') and i < len(response.target_order_id):
                    bracket_info["target_order_id"] = response.target_order_id[i]
                
                if hasattr(response, 'stop_order_id') and i < len(response.stop_order_id):
                    bracket_info["stop_order_id"] = response.stop_order_id[i]
                
                if hasattr(response, 'bracket_type') and i < len(response.bracket_type):
                    bracket_info["bracket_type"] = response.bracket_type[i]
                
                if hasattr(response, 'bracket_status') and i < len(response.bracket_status):
                    bracket_info["bracket_status"] = response.bracket_status[i]
                
                if hasattr(response, 'quantity') and i < len(response.quantity):
                    bracket_info["quantity"] = response.quantity[i]
                
                if hasattr(response, 'target_price') and i < len(response.target_price):
                    bracket_info["target_price"] = response.target_price[i]
                
                if hasattr(response, 'stop_price') and i < len(response.stop_price):
                    bracket_info["stop_price"] = response.stop_price[i]
                
                if hasattr(response, 'parent_price') and i < len(response.parent_price):
                    bracket_info["parent_price"] = response.parent_price[i]
                
                if hasattr(response, 'transaction_type') and i < len(response.transaction_type):
                    bracket_info["transaction_type"] = response.transaction_type[i]
                
                if hasattr(response, 'created_date') and i < len(response.created_date):
                    bracket_info["created_date"] = response.created_date[i]
                
                if hasattr(response, 'created_time') and i < len(response.created_time):
                    bracket_info["created_time"] = response.created_time[i]
                
                if hasattr(response, 'updated_date') and i < len(response.updated_date):
                    bracket_info["updated_date"] = response.updated_date[i]
                
                if hasattr(response, 'updated_time') and i < len(response.updated_time):
                    bracket_info["updated_time"] = response.updated_time[i]
                
                if hasattr(response, 'trailing_stop') and i < len(response.trailing_stop):
                    bracket_info["trailing_stop"] = response.trailing_stop[i]
                
                if hasattr(response, 'trail_amount') and i < len(response.trail_amount):
                    bracket_info["trail_amount"] = response.trail_amount[i]
                
                if hasattr(response, 'bracket_template') and i < len(response.bracket_template):
                    bracket_info["bracket_template"] = response.bracket_template[i]
                
                brackets.append(bracket_info)
                
                # Display bracket information
                print(f"{i+1:3d}. Bracket ID: {bracket_info['bracket_id']}")
                print(f"     Account: {bracket_info['account_id']}")
                
                if bracket_info['fcm_id']:
                    print(f"     FCM: {bracket_info['fcm_id']}")
                if bracket_info['ib_id']:
                    print(f"     IB: {bracket_info['ib_id']}")
                
                # Instrument details
                print(f"     📋 INSTRUMENT:")
                print(f"       Symbol: {bracket_info['symbol']}@{bracket_info['exchange']}")
                print(f"       Transaction: {bracket_info['transaction_type']}")
                print(f"       Quantity: {bracket_info['quantity']:,}")
                
                # Bracket structure
                print(f"     📐 BRACKET STRUCTURE:")
                print(f"       Type: {bracket_info['bracket_type']}")
                print(f"       Status: {bracket_info['bracket_status']}")
                
                if bracket_info['parent_order_id']:
                    print(f"       Parent Order: {bracket_info['parent_order_id']}")
                if bracket_info['target_order_id']:
                    print(f"       Target Order: {bracket_info['target_order_id']}")
                if bracket_info['stop_order_id']:
                    print(f"       Stop Order: {bracket_info['stop_order_id']}")
                
                # Price levels
                print(f"     💰 PRICE LEVELS:")
                if bracket_info['parent_price'] > 0:
                    print(f"       Parent Price: ${bracket_info['parent_price']:,.4f}")
                if bracket_info['target_price'] > 0:
                    print(f"       Target Price: ${bracket_info['target_price']:,.4f}")
                    
                    if bracket_info['parent_price'] > 0:
                        if bracket_info['transaction_type'] == 'BUY':
                            target_profit = bracket_info['target_price'] - bracket_info['parent_price']
                        else:
                            target_profit = bracket_info['parent_price'] - bracket_info['target_price']
                        
                        print(f"       Target Profit: ${target_profit:,.4f}")
                
                if bracket_info['stop_price'] > 0:
                    print(f"       Stop Price: ${bracket_info['stop_price']:,.4f}")
                    
                    if bracket_info['parent_price'] > 0:
                        if bracket_info['transaction_type'] == 'BUY':
                            stop_loss = bracket_info['parent_price'] - bracket_info['stop_price']
                        else:
                            stop_loss = bracket_info['stop_price'] - bracket_info['parent_price']
                        
                        print(f"       Stop Loss: ${stop_loss:,.4f}")
                
                # Trailing stop configuration
                if bracket_info['trailing_stop']:
                    print(f"     🔄 TRAILING STOP:")
                    print(f"       Enabled: ✅ YES")
                    if bracket_info['trail_amount'] > 0:
                        print(f"       Trail Amount: ${bracket_info['trail_amount']:,.4f}")
                else:
                    print(f"     🔄 Trailing Stop: ❌ NO")
                
                # Template information
                if bracket_info['bracket_template']:
                    print(f"     📋 Template: {bracket_info['bracket_template']}")
                
                # Timing information
                print(f"     📅 TIMING:")
                if bracket_info['created_date'] and bracket_info['created_time']:
                    print(f"       Created: {bracket_info['created_date']} {bracket_info['created_time']}")
                if bracket_info['updated_date'] and bracket_info['updated_time']:
                    print(f"       Updated: {bracket_info['updated_date']} {bracket_info['updated_time']}")
                
                print()
            
            # Bracket analysis
            print(f"\n📈 BRACKET ANALYSIS:")
            print(f"Total Brackets: {len(brackets)}")
            
            # Status breakdown
            status_counts = {}
            type_counts = {}
            symbol_counts = {}
            
            for bracket in brackets:
                status = bracket['bracket_status'] or 'Unknown'
                status_counts[status] = status_counts.get(status, 0) + 1
                
                btype = bracket['bracket_type'] or 'Unknown'
                type_counts[btype] = type_counts.get(btype, 0) + 1
                
                symbol = bracket['symbol'] or 'Unknown'
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
            
            if status_counts:
                print(f"\nBracket Status:")
                for status, count in sorted(status_counts.items()):
                    print(f"  • {status}: {count}")
            
            if type_counts:
                print(f"\nBracket Types:")
                for btype, count in sorted(type_counts.items()):
                    print(f"  • {btype}: {count}")
            
            if symbol_counts:
                print(f"\nSymbol Distribution:")
                for symbol, count in sorted(symbol_counts.items()):
                    print(f"  • {symbol}: {count}")
            
            # Trailing stop analysis
            trailing_brackets = [b for b in brackets if b['trailing_stop']]
            print(f"\nTrailing Stops: {len(trailing_brackets)} brackets")
            
            # Risk/reward analysis
            profitable_setups = 0
            total_risk = 0
            total_reward = 0
            
            for bracket in brackets:
                if (bracket['parent_price'] > 0 and 
                    bracket['target_price'] > 0 and 
                    bracket['stop_price'] > 0):
                    
                    if bracket['transaction_type'] == 'BUY':
                        risk = bracket['parent_price'] - bracket['stop_price']
                        reward = bracket['target_price'] - bracket['parent_price']
                    else:
                        risk = bracket['stop_price'] - bracket['parent_price']
                        reward = bracket['parent_price'] - bracket['target_price']
                    
                    if risk > 0 and reward > 0:
                        profitable_setups += 1
                        total_risk += risk
                        total_reward += reward
            
            if profitable_setups > 0:
                avg_risk = total_risk / profitable_setups
                avg_reward = total_reward / profitable_setups
                avg_ratio = avg_reward / avg_risk if avg_risk > 0 else 0
                
                print(f"\nRisk/Reward Analysis:")
                print(f"  • Complete Setups: {profitable_setups}")
                print(f"  • Average Risk: ${avg_risk:.4f}")
                print(f"  • Average Reward: ${avg_reward:.4f}")
                print(f"  • Average R/R Ratio: {avg_ratio:.2f}:1")
        
        else:
            print(f"\n📋 No bracket orders found")
            print("   This could indicate:")
            print("   • No active bracket orders in the account")
            print("   • Account filter may be too restrictive")
            print("   • No bracket trading permissions")
    
    else:
        print("❌ Show brackets request failed!")
        if len(response.rp_code) >= 2:
            error_code = response.rp_code[0]
            error_message = response.rp_code[1]
            print(f"   Error Code: {error_code}")
            print(f"   Error Message: {error_message}")
    
    return success

async def _test_show_bracket_stops(connection, account_id: Optional[str], fcm_id: Optional[str],
                                  ib_id: Optional[str], output_format: str, message_handler_obj) -> bool:
    """Test the show bracket stops functionality."""
    
    print("\n🛑 TESTING SHOW BRACKET STOPS...")
    
    # Create show bracket stops request
    request = request_show_bracket_stops_pb2.RequestShowBracketStops()
    request.template_id = TemplateIDs.SHOW_BRACKET_STOPS_REQUEST
    request.user_msg.append("Show bracket stops request from endpoint tester")
    
    # Set filters - FCM ID is required by server even if empty
    # Account ID is also required by the server
    request.account_id = account_id if account_id else "demo"
    
    # FCM ID is required by the server (error 1039 if missing)
    # Use default paper trading FCM ID if none provided
    request.fcm_id = fcm_id if fcm_id else "demo"
        
    # IB ID is also required by the server  
    request.ib_id = ib_id if ib_id else "demo"
    
    # Send show bracket stops request
    serialized_request = request.SerializeToString()
    print(f"📤 Sending show bracket stops request ({len(serialized_request)} bytes)")
    
    if not await connection.send_message(serialized_request):
        print("❌ Failed to send show bracket stops request")
        return False
    
    # Wait for response with advanced timeout management
    print("⏳ Waiting for show bracket stops response...")
    
    timeout_manager = TimeoutManager("bracket_info", InfraType.ORDER_PLANT)
    
    async def receive_response():
        return await connection.receive_message(timeout=30)  # Base timeout, will be managed by timeout_manager
    
    timeout_result = await timeout_manager.execute_with_progressive_timeout(
        receive_response, "show bracket stops response"
    )
    
    if not timeout_result.success:
        print(f"❌ No response received: {timeout_result.error_message}")
        return False
        
    response_bytes = timeout_result.result
    
    print(f"📥 Received response ({len(response_bytes)} bytes)")
    
    # Parse response
    response = response_show_bracket_stops_pb2.ResponseShowBracketStops()
    response.ParseFromString(response_bytes)
    
    # Analyze response content
    print("\n📊 SHOW BRACKET STOPS ANALYSIS:")
    print(f"Template ID: {response.template_id}")
    print(f"User Messages: {list(response.user_msg)}")
    print(f"Response Code: {list(response.rp_code)}")
    
    # Check if successful - handle both explicit success codes and empty response codes
    explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
    
    # Check for error indicators
    has_error = False
    if len(response.rp_code) >= 2:
        has_error = True  # Error code and message present
    elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
        has_error = True  # Non-zero error code
    
    # Consider empty response codes as success if no explicit error
    empty_response_success = len(response.rp_code) == 0 and not has_error
    
    success = explicit_success or empty_response_success
    
    if success:
        print("✅ Show bracket stops request successful!")
        
        # Display bracket stops information
        if hasattr(response, 'bracket_id') and response.bracket_id:
            print(f"\n🛑 BRACKET STOPS ({len(response.bracket_id)} stops):")
            print("-" * 80)
            
            for i, bracket_id in enumerate(response.bracket_id):
                stop_info = {
                    "bracket_id": bracket_id,
                    "stop_type": "",
                    "stop_price": 0,
                    "stop_quantity": 0,
                    "trail_amount": 0,
                    "trail_type": "",
                    "trigger_method": "",
                    "stop_status": ""
                }
                
                # Get stop details
                if hasattr(response, 'stop_type') and i < len(response.stop_type):
                    stop_info["stop_type"] = response.stop_type[i]
                
                if hasattr(response, 'stop_price') and i < len(response.stop_price):
                    stop_info["stop_price"] = response.stop_price[i]
                
                if hasattr(response, 'stop_quantity') and i < len(response.stop_quantity):
                    stop_info["stop_quantity"] = response.stop_quantity[i]
                
                if hasattr(response, 'trail_amount') and i < len(response.trail_amount):
                    stop_info["trail_amount"] = response.trail_amount[i]
                
                if hasattr(response, 'trail_type') and i < len(response.trail_type):
                    stop_info["trail_type"] = response.trail_type[i]
                
                if hasattr(response, 'trigger_method') and i < len(response.trigger_method):
                    stop_info["trigger_method"] = response.trigger_method[i]
                
                if hasattr(response, 'stop_status') and i < len(response.stop_status):
                    stop_info["stop_status"] = response.stop_status[i]
                
                print(f"{i+1:3d}. Bracket: {stop_info['bracket_id']}")
                print(f"     Stop Type: {stop_info['stop_type']}")
                print(f"     Stop Price: ${stop_info['stop_price']:,.4f}")
                print(f"     Quantity: {stop_info['stop_quantity']:,}")
                print(f"     Status: {stop_info['stop_status']}")
                
                if stop_info['trail_amount'] > 0:
                    print(f"     Trail Amount: ${stop_info['trail_amount']:,.4f}")
                    print(f"     Trail Type: {stop_info['trail_type']}")
                
                if stop_info['trigger_method']:
                    print(f"     Trigger Method: {stop_info['trigger_method']}")
                
                print()
        else:
            print(f"\n📋 No bracket stops found")
    
    else:
        print("❌ Show bracket stops request failed!")
        if len(response.rp_code) >= 2:
            error_code = response.rp_code[0]
            error_message = response.rp_code[1]
            print(f"   Error Code: {error_code}")
            print(f"   Error Message: {error_message}")
    
    return success

async def main():
    """Main function for bracket info testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Bracket Info endpoints (Templates 338/339/340/341)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_bracket_info.py
  python test_bracket_info.py --account-id "123456"
  python test_bracket_info.py --no-stops
  python test_bracket_info.py --format csv

Safety:
  This script performs READ-ONLY bracket information requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays bracket order information including:
  - Bracket order structure and relationships
  - Price levels (parent, target, stop)
  - Trailing stop configurations
  - Risk/reward analysis
  - Bracket status and timing information
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--fcm-id",
        type=str,
        help="FCM ID filter (optional)"
    )
    
    parser.add_argument(
        "--ib-id",
        type=str,
        help="IB ID filter (optional)"
    )
    
    parser.add_argument(
        "--no-stops",
        action="store_true",
        help="Skip bracket stops query"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC BRACKET INFO ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY BRACKET INFO OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_bracket_info(
        account_id=args.account_id,
        fcm_id=args.fcm_id,
        ib_id=args.ib_id,
        include_stops=not args.no_stops,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Bracket info test completed successfully!")
        return 0
    else:
        print("\n❌ Bracket info test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)