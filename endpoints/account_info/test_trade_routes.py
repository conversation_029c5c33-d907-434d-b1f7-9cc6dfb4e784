#!/usr/bin/env python3

"""
Test Rithmic Trade Routes endpoints (Templates 310/311).

This script demonstrates how to retrieve available trading routes
from the Rithmic API, showing routing options and exchange connectivity.

SAFETY: This script only performs READ-ONLY trade routes requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_trade_routes_pb2
import response_trade_routes_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_trade_routes(account_id: Optional[str] = None, fcm_id: Optional[str] = None,
                           ib_id: Optional[str] = None, output_format: str = "human") -> bool:
    """
    Test trade routes functionality.
    
    Args:
        account_id: Optional specific account ID to query
        fcm_id: Optional FCM ID filter
        ib_id: Optional IB ID filter
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🛣️  TESTING TRADE ROUTES ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.TRADE_ROUTES_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.TRADE_ROUTES_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"FCM ID: {fcm_id or 'All FCMs'}")
    print(f"IB ID: {ib_id or 'All IBs'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "trade_routes_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create trade routes request
        request = request_trade_routes_pb2.RequestTradeRoutes()
        request.template_id = TemplateIDs.TRADE_ROUTES_REQUEST
        request.user_msg.append("Trade routes request from endpoint tester")
        
        # Set filters - FCM ID and IB ID are required by server even if empty
        # Account ID is also required by the server
        request.account_id = account_id if account_id else "demo"
        
        # FCM ID is required by the server (error 1039 if missing)
        # Use default paper trading FCM ID if none provided
        request.fcm_id = fcm_id if fcm_id else "demo"
            
        # IB ID is also required by the server  
        request.ib_id = ib_id if ib_id else "demo"
        
        # Send trade routes request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending trade routes request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send trade routes request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for trade routes response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_trade_routes_pb2.ResponseTradeRoutes()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 TRADE ROUTES ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Trade routes request successful!")
            
            # Display trade routes information
            routes = []
            if hasattr(response, 'fcm_id') and response.fcm_id:
                print(f"\n🛣️  AVAILABLE TRADE ROUTES ({len(response.fcm_id)} routes):")
                print("-" * 100)
                
                for i, fcm in enumerate(response.fcm_id):
                    route_info = {
                        "fcm_id": fcm,
                        "ib_id": "",
                        "account_id": "",
                        "exchange": "",
                        "route_name": "",
                        "route_type": "",
                        "capacity": "",
                        "session_template": "",
                        "price_format": "",
                        "tick_size": "",
                        "product_code": "",
                        "symbol": "",
                        "enabled": True,
                        "default_route": False
                    }
                    
                    # Get corresponding route data
                    if hasattr(response, 'ib_id') and i < len(response.ib_id):
                        route_info["ib_id"] = response.ib_id[i]
                    
                    if hasattr(response, 'account_id') and i < len(response.account_id):
                        route_info["account_id"] = response.account_id[i]
                    
                    if hasattr(response, 'exchange') and i < len(response.exchange):
                        route_info["exchange"] = response.exchange[i]
                    
                    if hasattr(response, 'route_name') and i < len(response.route_name):
                        route_info["route_name"] = response.route_name[i]
                    
                    if hasattr(response, 'route_type') and i < len(response.route_type):
                        route_info["route_type"] = response.route_type[i]
                    
                    if hasattr(response, 'capacity') and i < len(response.capacity):
                        route_info["capacity"] = response.capacity[i]
                    
                    if hasattr(response, 'session_template') and i < len(response.session_template):
                        route_info["session_template"] = response.session_template[i]
                    
                    if hasattr(response, 'price_format') and i < len(response.price_format):
                        route_info["price_format"] = response.price_format[i]
                    
                    if hasattr(response, 'tick_size') and i < len(response.tick_size):
                        route_info["tick_size"] = response.tick_size[i]
                    
                    if hasattr(response, 'product_code') and i < len(response.product_code):
                        route_info["product_code"] = response.product_code[i]
                    
                    if hasattr(response, 'symbol') and i < len(response.symbol):
                        route_info["symbol"] = response.symbol[i]
                    
                    if hasattr(response, 'enabled') and i < len(response.enabled):
                        route_info["enabled"] = response.enabled[i]
                    
                    if hasattr(response, 'default_route') and i < len(response.default_route):
                        route_info["default_route"] = response.default_route[i]
                    
                    routes.append(route_info)
                    
                    # Display route information
                    print(f"{i+1:3d}. Route: {route_info['route_name'] or 'Unnamed Route'}")
                    
                    # Account and routing hierarchy
                    print(f"     📍 ROUTING HIERARCHY:")
                    print(f"       FCM ID: {route_info['fcm_id']}")
                    if route_info['ib_id']:
                        print(f"       IB ID: {route_info['ib_id']}")
                    if route_info['account_id']:
                        print(f"       Account ID: {route_info['account_id']}")
                    
                    # Exchange and connectivity
                    print(f"     🌐 CONNECTIVITY:")
                    if route_info['exchange']:
                        print(f"       Exchange: {route_info['exchange']}")
                    if route_info['route_type']:
                        print(f"       Route Type: {route_info['route_type']}")
                    if route_info['capacity']:
                        print(f"       Capacity: {route_info['capacity']}")
                    
                    # Product specifications
                    if route_info['symbol'] or route_info['product_code']:
                        print(f"     📋 PRODUCT SPECIFICATIONS:")
                        if route_info['symbol']:
                            print(f"       Symbol: {route_info['symbol']}")
                        if route_info['product_code']:
                            print(f"       Product Code: {route_info['product_code']}")
                        if route_info['price_format']:
                            print(f"       Price Format: {route_info['price_format']}")
                        if route_info['tick_size']:
                            print(f"       Tick Size: {route_info['tick_size']}")
                    
                    # Session and configuration
                    if route_info['session_template']:
                        print(f"     ⚙️  CONFIGURATION:")
                        print(f"       Session Template: {route_info['session_template']}")
                    
                    # Status indicators
                    print(f"     📊 STATUS:")
                    status = "✅ ENABLED" if route_info['enabled'] else "❌ DISABLED"
                    print(f"       Status: {status}")
                    
                    if route_info['default_route']:
                        print(f"       Default Route: ⭐ YES")
                    
                    print()
                
                # Trade routes analysis
                print(f"\n📈 TRADE ROUTES SUMMARY:")
                print(f"Total Routes: {len(routes)}")
                
                # FCM breakdown
                fcm_counts = {}
                ib_counts = {}
                exchange_counts = {}
                route_type_counts = {}
                
                for route in routes:
                    fcm = route['fcm_id'] or 'Unknown'
                    fcm_counts[fcm] = fcm_counts.get(fcm, 0) + 1
                    
                    if route['ib_id']:
                        ib = route['ib_id']
                        ib_counts[ib] = ib_counts.get(ib, 0) + 1
                    
                    if route['exchange']:
                        exchange = route['exchange']
                        exchange_counts[exchange] = exchange_counts.get(exchange, 0) + 1
                    
                    if route['route_type']:
                        route_type = route['route_type']
                        route_type_counts[route_type] = route_type_counts.get(route_type, 0) + 1
                
                if fcm_counts:
                    print(f"\nFCM Distribution:")
                    for fcm, count in sorted(fcm_counts.items()):
                        print(f"  • {fcm}: {count} routes")
                
                if ib_counts:
                    print(f"\nIB Distribution:")
                    for ib, count in sorted(ib_counts.items()):
                        print(f"  • {ib}: {count} routes")
                
                if exchange_counts:
                    print(f"\nExchange Connectivity:")
                    for exchange, count in sorted(exchange_counts.items()):
                        print(f"  • {exchange}: {count} routes")
                
                if route_type_counts:
                    print(f"\nRoute Types:")
                    for route_type, count in sorted(route_type_counts.items()):
                        print(f"  • {route_type}: {count} routes")
                
                # Status analysis
                enabled_routes = [r for r in routes if r['enabled']]
                disabled_routes = [r for r in routes if not r['enabled']]
                default_routes = [r for r in routes if r['default_route']]
                
                print(f"\nRoute Status:")
                print(f"  • Enabled Routes: {len(enabled_routes)} ({len(enabled_routes)/len(routes)*100:.1f}%)")
                print(f"  • Disabled Routes: {len(disabled_routes)} ({len(disabled_routes)/len(routes)*100:.1f}%)")
                print(f"  • Default Routes: {len(default_routes)}")
                
                if default_routes:
                    print(f"  Default Route Details:")
                    for route in default_routes:
                        print(f"    - {route['route_name']} ({route['exchange']})")
                
                # Product coverage analysis
                products_covered = set()
                exchanges_covered = set()
                
                for route in routes:
                    if route['symbol']:
                        products_covered.add(route['symbol'])
                    if route['exchange']:
                        exchanges_covered.add(route['exchange'])
                
                if products_covered:
                    print(f"\nProduct Coverage:")
                    print(f"  • Unique Products: {len(products_covered)}")
                    if len(products_covered) <= 20:
                        print(f"  • Products: {', '.join(sorted(products_covered))}")
                    else:
                        sample_products = sorted(list(products_covered))[:10]
                        print(f"  • Sample Products: {', '.join(sample_products)}")
                        print(f"    ... and {len(products_covered) - 10} more")
                
                if exchanges_covered:
                    print(f"\nExchange Coverage:")
                    print(f"  • Connected Exchanges: {len(exchanges_covered)}")
                    print(f"  • Exchanges: {', '.join(sorted(exchanges_covered))}")
            
            else:
                print(f"\n📋 No trade routes found")
                print("   This could indicate:")
                print("   • No trading routes configured for the account")
                print("   • Insufficient permissions for route information")
                print("   • Account/FCM/IB filter may be too restrictive")
                print("   • No active trading connectivity")
            
            # CSV output
            if output_format == "csv" and routes:
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["FCM_ID", "IB_ID", "Account_ID", "Exchange", "Route_Name", 
                              "Route_Type", "Capacity", "Symbol", "Product_Code", 
                              "Enabled", "Default_Route"]
                print(",".join(csv_headers))
                
                for route in routes:
                    values = [
                        route['fcm_id'],
                        route['ib_id'],
                        route['account_id'],
                        route['exchange'],
                        route['route_name'],
                        route['route_type'],
                        route['capacity'],
                        route['symbol'],
                        route['product_code'],
                        str(route['enabled']),
                        str(route['default_route'])
                    ]
                    print(",".join(values))
            
            # Route optimization insights
            if routes:
                print(f"\n💡 TRADE ROUTING INSIGHTS:")
                print(f"✅ Found {len(routes)} available trade routes")
                
                # Route reliability assessment
                if len(enabled_routes) == len(routes):
                    print("• Excellent connectivity - all routes enabled")
                elif len(enabled_routes) > len(routes) * 0.8:
                    print("• Good connectivity - most routes enabled")
                else:
                    print("• Limited connectivity - some routes disabled")
                    print(f"  Consider enabling {len(disabled_routes)} disabled routes")
                
                # Exchange diversity
                unique_exchanges = len(set(r['exchange'] for r in routes if r['exchange']))
                if unique_exchanges > 5:
                    print("• Excellent exchange diversity for order routing")
                elif unique_exchanges > 2:
                    print("• Good exchange diversity")
                else:
                    print("• Limited exchange connectivity")
                
                # Default route configuration
                if default_routes:
                    if len(default_routes) == 1:
                        print("• Well-configured default routing")
                    else:
                        print(f"• Multiple default routes detected ({len(default_routes)})")
                        print("  Review default route configuration")
                else:
                    print("• No default routes configured")
                    print("  Consider setting default routes for common products")
                
                # Capacity analysis
                capacity_types = set(r['capacity'] for r in routes if r['capacity'])
                if capacity_types:
                    print(f"• Available capacities: {', '.join(sorted(capacity_types))}")
                
                # Route type diversity
                if len(route_type_counts) > 1:
                    print("• Multiple route types available for flexibility")
                else:
                    print("• Single route type configuration")
        
        else:
            print("❌ Trade routes request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions for trade route information")
            print("   • Check if account/FCM/IB IDs are valid")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without filters to see all available routes")
            print("   • Verify trading permissions are properly configured")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing trade routes: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for trade routes testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Trade Routes endpoint (Templates 310/311)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_trade_routes.py
  python test_trade_routes.py --account-id "123456"
  python test_trade_routes.py --fcm-id "FCM01" --ib-id "IB01"
  python test_trade_routes.py --format csv

Safety:
  This script performs READ-ONLY trade routes requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays available trade routes including:
  - Routing hierarchy (FCM, IB, Account relationships)
  - Exchange connectivity and route types
  - Product specifications and pricing formats
  - Route status and configuration details
  - Default route identification
  - Comprehensive routing analysis and optimization insights
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional)"
    )
    
    parser.add_argument(
        "--fcm-id",
        type=str,
        help="FCM ID filter (optional)"
    )
    
    parser.add_argument(
        "--ib-id",
        type=str,
        help="IB ID filter (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC TRADE ROUTES ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY TRADE ROUTES OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_trade_routes(
        account_id=args.account_id,
        fcm_id=args.fcm_id,
        ib_id=args.ib_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Trade routes test completed successfully!")
        return 0
    else:
        print("\n❌ Trade routes test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)