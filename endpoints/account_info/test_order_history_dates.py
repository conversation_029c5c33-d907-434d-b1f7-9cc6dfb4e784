#!/usr/bin/env python3

"""
Test Rithmic Show Order History Dates endpoints (Templates 318/319).

This script demonstrates how to retrieve available order history dates
from the Rithmic API, showing which dates have order history data available.

SAFETY: This script only performs READ-ONLY order history date requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_show_order_history_dates_pb2
import response_show_order_history_dates_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_order_history_dates(account_id: Optional[str] = None,
                                  output_format: str = "human") -> bool:
    """
    Test order history dates retrieval functionality.
    
    Args:
        account_id: Optional specific account ID to query
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📅 TESTING ORDER HISTORY DATES ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.SHOW_ORDER_HISTORY_DATES_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.SHOW_ORDER_HISTORY_DATES_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "order_history_dates_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create order history dates request
        request = request_show_order_history_dates_pb2.RequestShowOrderHistoryDates()
        request.template_id = TemplateIDs.SHOW_ORDER_HISTORY_DATES_REQUEST
        request.user_msg.append("Order history dates request from endpoint tester")
        
        # Set account ID if provided
        if account_id:
            request.account_id = account_id
        
        # Send order history dates request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending order history dates request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send order history dates request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for order history dates response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_show_order_history_dates_pb2.ResponseShowOrderHistoryDates()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 ORDER HISTORY DATES ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Order history dates request successful!")
            
            # Display available dates
            available_dates = []
            if hasattr(response, 'date') and response.date:
                print(f"\n📅 AVAILABLE ORDER HISTORY DATES ({len(response.date)} dates):")
                print("-" * 60)
                
                for i, date_str in enumerate(response.date):
                    available_dates.append(date_str)
                    print(f"{i+1:3d}. {date_str}")
                    
                    # Try to parse and show additional date info
                    try:
                        # Assume format YYYY-MM-DD or YYYYMMDD
                        if '-' in date_str:
                            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        elif len(date_str) == 8:
                            date_obj = datetime.strptime(date_str, "%Y%m%d")
                        else:
                            continue
                        
                        # Calculate days ago
                        today = datetime.now()
                        days_ago = (today - date_obj).days
                        weekday = date_obj.strftime("%A")
                        
                        print(f"     {weekday}, {days_ago} days ago")
                        
                    except ValueError:
                        # If date parsing fails, just show the raw date
                        print(f"     (Date format: {date_str})")
                
                # Date range analysis
                if len(available_dates) > 1:
                    print(f"\n📈 DATE RANGE ANALYSIS:")
                    print(f"Total Dates Available: {len(available_dates)}")
                    print(f"Earliest Date: {available_dates[0]}")
                    print(f"Latest Date: {available_dates[-1]}")
                    
                    # Try to calculate date range
                    try:
                        earliest = None
                        latest = None
                        
                        for date_str in available_dates:
                            if '-' in date_str:
                                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                            elif len(date_str) == 8:
                                date_obj = datetime.strptime(date_str, "%Y%m%d")
                            else:
                                continue
                            
                            if earliest is None or date_obj < earliest:
                                earliest = date_obj
                            if latest is None or date_obj > latest:
                                latest = date_obj
                        
                        if earliest and latest:
                            range_days = (latest - earliest).days
                            print(f"Date Range: {range_days} days")
                            
                            # Recent activity analysis
                            today = datetime.now()
                            latest_days_ago = (today - latest).days
                            
                            if latest_days_ago <= 1:
                                print(f"Recent Activity: ✅ Current (last activity {latest_days_ago} days ago)")
                            elif latest_days_ago <= 7:
                                print(f"Recent Activity: ⚡ Recent (last activity {latest_days_ago} days ago)")
                            elif latest_days_ago <= 30:
                                print(f"Recent Activity: ⚠️  Moderate (last activity {latest_days_ago} days ago)")
                            else:
                                print(f"Recent Activity: ❌ Stale (last activity {latest_days_ago} days ago)")
                            
                            # Check for gaps in recent dates
                            recent_dates = []
                            for date_str in available_dates:
                                try:
                                    if '-' in date_str:
                                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                                    elif len(date_str) == 8:
                                        date_obj = datetime.strptime(date_str, "%Y%m%d")
                                    else:
                                        continue
                                    
                                    days_ago = (today - date_obj).days
                                    if days_ago <= 30:  # Last 30 days
                                        recent_dates.append(date_obj)
                                except ValueError:
                                    continue
                            
                            if len(recent_dates) > 1:
                                recent_dates.sort()
                                gaps = []
                                for i in range(1, len(recent_dates)):
                                    gap = (recent_dates[i] - recent_dates[i-1]).days
                                    if gap > 1:  # Skip weekends
                                        gaps.append(gap)
                                
                                if gaps:
                                    avg_gap = sum(gaps) / len(gaps)
                                    max_gap = max(gaps)
                                    print(f"Recent Activity Pattern: {len(recent_dates)} days with data in last 30 days")
                                    print(f"Average Gap: {avg_gap:.1f} days, Max Gap: {max_gap} days")
                    
                    except Exception as e:
                        logger.warning(f"Error analyzing date range: {e}")
                        print("Date range analysis failed (date format parsing issue)")
                
            else:
                print(f"\n📋 No order history dates found")
                print("   This could indicate:")
                print("   • No order history available for the account")
                print("   • Account has no trading activity")
                print("   • Insufficient permissions for order history")
                print("   • Account ID filter may be too restrictive")
            
            # CSV output
            if output_format == "csv" and available_dates:
                print(f"\n📊 CSV FORMAT:")
                print("Date,Days_Ago,Weekday")
                
                for date_str in available_dates:
                    try:
                        if '-' in date_str:
                            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        elif len(date_str) == 8:
                            date_obj = datetime.strptime(date_str, "%Y%m%d")
                        else:
                            print(f"{date_str},,")
                            continue
                        
                        today = datetime.now()
                        days_ago = (today - date_obj).days
                        weekday = date_obj.strftime("%A")
                        
                        print(f"{date_str},{days_ago},{weekday}")
                        
                    except ValueError:
                        print(f"{date_str},,")
            
            # Usage recommendations
            if available_dates:
                print(f"\n💡 ORDER HISTORY USAGE RECOMMENDATIONS:")
                print(f"✅ Order history data is available for {len(available_dates)} dates")
                
                # Suggest recent dates for testing
                recent_dates = []
                try:
                    today = datetime.now()
                    for date_str in available_dates[-5:]:  # Last 5 dates
                        if '-' in date_str:
                            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        elif len(date_str) == 8:
                            date_obj = datetime.strptime(date_str, "%Y%m%d")
                        else:
                            continue
                        
                        days_ago = (today - date_obj).days
                        if days_ago <= 30:  # Within last 30 days
                            recent_dates.append(date_str)
                    
                    if recent_dates:
                        print(f"• Recommended dates for testing: {', '.join(recent_dates[-3:])}")
                        print(f"• Use these dates with other order history endpoints")
                        print(f"• Most recent activity: {recent_dates[-1]}")
                    else:
                        print(f"• Available dates are older (>30 days)")
                        print(f"• Consider using recent dates: {available_dates[-3:]}")
                        
                except Exception:
                    print(f"• Use any of the available dates for order history queries")
                    print(f"• Most recent dates: {available_dates[-3:]}")
        
        else:
            print("❌ Order history dates request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify permissions for order history access")
            print("   • Check if account ID is valid and accessible")
            print("   • Ensure connection to correct Order Plant")
            print("   • Try without account ID filter to see all accounts")
            print("   • Verify order history is enabled for the account")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing order history dates: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for order history dates testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Order History Dates endpoint (Templates 318/319)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_order_history_dates.py
  python test_order_history_dates.py --account-id "123456"
  python test_order_history_dates.py --format csv
  python test_order_history_dates.py --format json

Safety:
  This script performs READ-ONLY order history date requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays available order history dates including:
  - List of dates with order history data
  - Date range analysis (earliest, latest, gaps)
  - Recent activity assessment
  - Usage recommendations for other order history endpoints
  - Weekday information and days ago calculations
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to query (optional, defaults to all accounts)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC ORDER HISTORY DATES ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY ORDER HISTORY DATE OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_order_history_dates(
        account_id=args.account_id,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Order history dates test completed successfully!")
        return 0
    else:
        print("\n❌ Order history dates test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)