#!/usr/bin/env python3

"""
Test Rithmic Account RMS Updates endpoints (Templates 3508/3509).

This script demonstrates how to subscribe to real-time account RMS updates
from the Rithmic API, monitoring changes in account risk parameters.

SAFETY: This script only performs READ-ONLY account RMS update subscriptions.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime
from collections import defaultdict

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_account_rms_updates_pb2
import response_account_rms_updates_pb2
import account_rms_updates_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class AccountRmsAnalyzer:
    """Analyzes account RMS updates for risk monitoring."""
    
    def __init__(self):
        self.rms_updates = []
        self.accounts_seen = set()
        self.buying_power_history = defaultdict(list)
        self.margin_history = defaultdict(list)
        self.risk_alerts = []
        
    def add_rms_update(self, update_data: Dict[str, Any]):
        """Add an RMS update to the analysis."""
        self.rms_updates.append(update_data)
        account_id = update_data.get('account_id', '')
        self.accounts_seen.add(account_id)
        
        # Track buying power changes
        buying_power = update_data.get('available_buying_power', 0)
        if buying_power > 0:
            self.buying_power_history[account_id].append(buying_power)
        
        # Track margin changes
        margin_excess = update_data.get('margin_excess', 0)
        self.margin_history[account_id].append(margin_excess)
        
        # Check for risk alerts
        if margin_excess < 0:
            self.risk_alerts.append({
                "timestamp": update_data.get('timestamp', ''),
                "account_id": account_id,
                "alert_type": "MARGIN_DEFICIT",
                "value": margin_excess,
                "message": f"Account {account_id} has margin deficit of ${abs(margin_excess):,.2f}"
            })
        
        # Check for low buying power
        total_bp = update_data.get('buying_power', 0)
        if total_bp > 0 and buying_power > 0:
            utilization = (1 - buying_power / total_bp) * 100
            if utilization > 90:
                self.risk_alerts.append({
                    "timestamp": update_data.get('timestamp', ''),
                    "account_id": account_id,
                    "alert_type": "HIGH_UTILIZATION",
                    "value": utilization,
                    "message": f"Account {account_id} has {utilization:.1f}% buying power utilization"
                })
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive RMS analysis summary."""
        summary = {
            "total_updates": len(self.rms_updates),
            "unique_accounts": len(self.accounts_seen),
            "accounts": list(self.accounts_seen),
            "buying_power_analysis": {},
            "margin_analysis": {},
            "risk_alerts": self.risk_alerts,
            "risk_summary": {
                "total_alerts": len(self.risk_alerts),
                "margin_deficits": len([a for a in self.risk_alerts if a["alert_type"] == "MARGIN_DEFICIT"]),
                "high_utilization": len([a for a in self.risk_alerts if a["alert_type"] == "HIGH_UTILIZATION"])
            }
        }
        
        # Buying power analysis per account
        for account_id, bp_history in self.buying_power_history.items():
            if bp_history:
                summary["buying_power_analysis"][account_id] = {
                    "latest_bp": bp_history[-1],
                    "avg_bp": sum(bp_history) / len(bp_history),
                    "min_bp": min(bp_history),
                    "max_bp": max(bp_history),
                    "bp_volatility": max(bp_history) - min(bp_history) if len(bp_history) > 1 else 0,
                    "bp_updates": len(bp_history)
                }
        
        # Margin analysis per account
        for account_id, margin_history in self.margin_history.items():
            if margin_history:
                summary["margin_analysis"][account_id] = {
                    "latest_margin": margin_history[-1],
                    "avg_margin": sum(margin_history) / len(margin_history),
                    "min_margin": min(margin_history),
                    "max_margin": max(margin_history),
                    "margin_trend": "improving" if len(margin_history) > 1 and margin_history[-1] > margin_history[0] else "stable",
                    "margin_updates": len(margin_history)
                }
        
        return summary

async def test_account_rms_updates(account_id: Optional[str] = None, duration: int = 60,
                                 output_format: str = "human") -> bool:
    """
    Test account RMS updates subscription functionality.
    
    Args:
        account_id: Optional specific account ID to monitor
        duration: How long to listen for RMS updates (seconds)
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    analyzer = AccountRmsAnalyzer()
    
    print("=" * 60)
    print("⚖️ TESTING ACCOUNT RMS UPDATES ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.ACCOUNT_RMS_UPDATES_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.ACCOUNT_RMS_UPDATES_RESPONSE} (Response)")
    print(f"Template ID: {TemplateIDs.ACCOUNT_RMS_UPDATES} (Updates)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"Duration: {duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Order Plant
        print("🔐 Establishing connection to Order Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.ORDER_PLANT, "account_rms_updates_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create account RMS updates subscription request
        request = request_account_rms_updates_pb2.RequestAccountRmsUpdates()
        request.template_id = TemplateIDs.ACCOUNT_RMS_UPDATES_REQUEST
        request.user_msg.append("Account RMS updates subscription from endpoint tester")
        
        # Set account ID if provided
        if account_id:
            request.account_id = account_id
        
        request.request = 1  # SUBSCRIBE (1 = SUBSCRIBE, 2 = UNSUBSCRIBE)
        
        # Send subscription request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending account RMS updates subscription ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send account RMS updates subscription")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for subscription response
        print("⏳ Waiting for subscription response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No subscription response received within timeout")
            return False
        
        print(f"📥 Received subscription response ({len(response_bytes)} bytes)")
        
        # Parse subscription response
        response = response_account_rms_updates_pb2.ResponseAccountRmsUpdates()
        response.ParseFromString(response_bytes)
        
        # Check subscription success
        subscription_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if not subscription_success:
            print("❌ Account RMS updates subscription failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            return False
        
        print("✅ Account RMS updates subscription successful!")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Listen for RMS updates
        print(f"\n⚖️ LISTENING FOR ACCOUNT RMS UPDATES ({duration} seconds)...")
        print("=" * 60)
        
        update_count = 0
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < duration:
            try:
                # Wait for RMS updates
                update_bytes = await connection.receive_message(timeout=5.0)
                
                if not update_bytes:
                    continue  # Timeout, keep listening
                
                # Parse update to determine type
                update_info = parse_message(update_bytes)
                if not update_info:
                    continue
                
                template_id = update_info.template_id
                
                if template_id == TemplateIDs.ACCOUNT_RMS_UPDATES:
                    # Account RMS update
                    rms_update = account_rms_updates_pb2.AccountRmsUpdates()
                    rms_update.ParseFromString(update_bytes)
                    
                    update_count += 1
                    
                    # Extract RMS update data
                    update_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "account_id": getattr(rms_update, 'account_id', ''),
                        "buying_power": getattr(rms_update, 'buying_power', 0),
                        "available_buying_power": getattr(rms_update, 'available_buying_power', 0),
                        "initial_margin": getattr(rms_update, 'initial_margin', 0),
                        "maintenance_margin": getattr(rms_update, 'maintenance_margin', 0),
                        "margin_excess": getattr(rms_update, 'margin_excess', 0),
                        "day_trading_buying_power": getattr(rms_update, 'day_trading_buying_power', 0),
                        "overnight_buying_power": getattr(rms_update, 'overnight_buying_power', 0),
                        "max_daily_loss": getattr(rms_update, 'max_daily_loss', 0),
                        "daily_pnl": getattr(rms_update, 'daily_pnl', 0),
                        "realized_pnl": getattr(rms_update, 'realized_pnl', 0),
                        "unrealized_pnl": getattr(rms_update, 'unrealized_pnl', 0)
                    }
                    
                    # Add to analyzer
                    analyzer.add_rms_update(update_data)
                    
                    # Display RMS update
                    print(f"⚖️ RMS UPDATE #{update_count} [{update_data['timestamp']}]")
                    print(f"    Account: {update_data['account_id']}")
                    
                    # Buying power information
                    if update_data['buying_power'] > 0:
                        utilization = 0
                        if update_data['available_buying_power'] >= 0:
                            utilization = (1 - update_data['available_buying_power'] / update_data['buying_power']) * 100
                        
                        print(f"    💰 BUYING POWER:")
                        print(f"      Total: ${update_data['buying_power']:,.2f}")
                        print(f"      Available: ${update_data['available_buying_power']:,.2f}")
                        print(f"      Utilization: {utilization:.1f}%")
                        
                        if utilization > 90:
                            print(f"      🚨 HIGH UTILIZATION WARNING!")
                    
                    # Margin information
                    print(f"    📊 MARGINS:")
                    if update_data['initial_margin'] > 0:
                        print(f"      Initial: ${update_data['initial_margin']:,.2f}")
                    
                    if update_data['maintenance_margin'] > 0:
                        print(f"      Maintenance: ${update_data['maintenance_margin']:,.2f}")
                    
                    if update_data['margin_excess'] != 0:
                        if update_data['margin_excess'] > 0:
                            print(f"      Excess: +${update_data['margin_excess']:,.2f}")
                        else:
                            print(f"      Deficit: ${update_data['margin_excess']:,.2f} 🚨")
                    
                    # Day trading specific
                    if update_data['day_trading_buying_power'] > 0:
                        print(f"      Day Trading BP: ${update_data['day_trading_buying_power']:,.2f}")
                    
                    if update_data['overnight_buying_power'] > 0:
                        print(f"      Overnight BP: ${update_data['overnight_buying_power']:,.2f}")
                    
                    # P&L information
                    if (update_data['daily_pnl'] != 0 or 
                        update_data['realized_pnl'] != 0 or 
                        update_data['unrealized_pnl'] != 0):
                        
                        print(f"    📈 P&L:")
                        if update_data['daily_pnl'] != 0:
                            pnl_symbol = "+" if update_data['daily_pnl'] > 0 else ""
                            print(f"      Daily: {pnl_symbol}${update_data['daily_pnl']:,.2f}")
                        
                        if update_data['realized_pnl'] != 0:
                            pnl_symbol = "+" if update_data['realized_pnl'] > 0 else ""
                            print(f"      Realized: {pnl_symbol}${update_data['realized_pnl']:,.2f}")
                        
                        if update_data['unrealized_pnl'] != 0:
                            pnl_symbol = "+" if update_data['unrealized_pnl'] > 0 else ""
                            print(f"      Unrealized: {pnl_symbol}${update_data['unrealized_pnl']:,.2f}")
                    
                    # Risk limits
                    if update_data['max_daily_loss'] > 0:
                        print(f"    ⚠️  Max Daily Loss: ${update_data['max_daily_loss']:,.2f}")
                        
                        if update_data['daily_pnl'] < 0:
                            loss_pct = abs(update_data['daily_pnl']) / update_data['max_daily_loss'] * 100
                            print(f"      Daily Loss Usage: {loss_pct:.1f}%")
                            
                            if loss_pct > 80:
                                print(f"      🚨 APPROACHING DAILY LOSS LIMIT!")
                    
                    # Display detailed message if requested
                    if output_format != "human":
                        print(f"\n📋 RMS UPDATE DETAILS:")
                        print(message_handler_obj.format_message(update_info, output_format))
                        print("-" * 40)
                    
                    print()  # Blank line for readability
                
                else:
                    # Other update types
                    print(f"📡 OTHER UPDATE: Template {template_id}")
                
            except asyncio.TimeoutError:
                # No updates received in the timeout period
                continue
            except Exception as e:
                logger.warning(f"Error processing RMS update: {e}")
                continue
        
        # Get analysis summary
        summary = analyzer.get_summary()
        
        print(f"\n⚖️ ACCOUNT RMS UPDATES SUMMARY:")
        print("=" * 60)
        print(f"Account Filter: {account_id or 'All accounts'}")
        print(f"Duration: {duration} seconds")
        print(f"Total Updates: {summary['total_updates']}")
        print(f"Unique Accounts: {summary['unique_accounts']}")
        print(f"Risk Alerts: {summary['risk_summary']['total_alerts']}")
        
        if summary['total_updates'] > 0:
            avg_frequency = summary['total_updates'] / duration
            print(f"Average Update Frequency: {avg_frequency:.2f} updates/second")
        
        # Buying power analysis
        if summary['buying_power_analysis']:
            print(f"\n💰 BUYING POWER ANALYSIS:")
            for account_id, analysis in summary['buying_power_analysis'].items():
                print(f"  {account_id}:")
                print(f"    Latest BP: ${analysis['latest_bp']:,.2f}")
                print(f"    BP Range: ${analysis['min_bp']:,.2f} - ${analysis['max_bp']:,.2f}")
                print(f"    BP Volatility: ${analysis['bp_volatility']:,.2f}")
                print(f"    BP Updates: {analysis['bp_updates']}")
        
        # Margin analysis
        if summary['margin_analysis']:
            print(f"\n📊 MARGIN ANALYSIS:")
            for account_id, analysis in summary['margin_analysis'].items():
                print(f"  {account_id}:")
                print(f"    Latest Margin: ${analysis['latest_margin']:,.2f}")
                print(f"    Margin Range: ${analysis['min_margin']:,.2f} - ${analysis['max_margin']:,.2f}")
                print(f"    Margin Trend: {analysis['margin_trend'].title()}")
                print(f"    Margin Updates: {analysis['margin_updates']}")
        
        # Risk alerts
        if summary['risk_alerts']:
            print(f"\n🚨 RISK ALERTS:")
            print(f"Total Alerts: {summary['risk_summary']['total_alerts']}")
            print(f"Margin Deficits: {summary['risk_summary']['margin_deficits']}")
            print(f"High Utilization: {summary['risk_summary']['high_utilization']}")
            
            print(f"\nRecent Alerts:")
            for alert in summary['risk_alerts'][-5:]:  # Last 5 alerts
                print(f"  [{alert['timestamp']}] {alert['alert_type']}: {alert['message']}")
        
        # CSV output
        if output_format == "csv" and analyzer.rms_updates:
            print(f"\n📊 CSV FORMAT (RMS UPDATES):")
            csv_headers = ["Timestamp", "Account_ID", "Buying_Power", "Available_BP", 
                          "Initial_Margin", "Maintenance_Margin", "Margin_Excess",
                          "Daily_PnL", "Realized_PnL", "Unrealized_PnL"]
            print(",".join(csv_headers))
            
            for update in analyzer.rms_updates[-20:]:  # Last 20 updates
                values = [
                    update['timestamp'],
                    update['account_id'],
                    str(update['buying_power']),
                    str(update['available_buying_power']),
                    str(update['initial_margin']),
                    str(update['maintenance_margin']),
                    str(update['margin_excess']),
                    str(update['daily_pnl']),
                    str(update['realized_pnl']),
                    str(update['unrealized_pnl'])
                ]
                print(",".join(values))
        
        # Analysis and insights
        print(f"\n💡 RMS MONITORING ANALYSIS:")
        if summary['total_updates'] > 0:
            print(f"✅ Successfully received {summary['total_updates']} RMS updates")
            
            # Risk assessment
            if summary['risk_summary']['total_alerts'] > 0:
                alert_rate = summary['risk_summary']['total_alerts'] / summary['total_updates'] * 100
                print(f"• Risk Alert Rate: {alert_rate:.1f}% of updates")
                
                if summary['risk_summary']['margin_deficits'] > 0:
                    print(f"• ⚠️  {summary['risk_summary']['margin_deficits']} margin deficit alerts")
                
                if summary['risk_summary']['high_utilization'] > 0:
                    print(f"• ⚡ {summary['risk_summary']['high_utilization']} high utilization alerts")
            else:
                print(f"• ✅ No risk alerts - accounts operating within normal parameters")
            
            # Update frequency assessment
            if avg_frequency > 1:
                print(f"• High frequency RMS updates: {avg_frequency:.2f}/second")
            elif avg_frequency > 0.1:
                print(f"• Moderate RMS update activity: {avg_frequency:.2f}/second")
            else:
                print(f"• Low RMS update activity: {avg_frequency:.2f}/second")
        else:
            print("⚠️  No RMS updates received")
            print("• Accounts may have no activity")
            print("• Check account permissions")
            print("• Verify account ID filter")
        
        # Unsubscribe
        print(f"\n📤 Unsubscribing from account RMS updates...")
        request.request = 2  # UNSUBSCRIBE
        serialized_unsub = request.SerializeToString()
        
        if await connection.send_message(serialized_unsub):
            # Wait for unsubscribe response
            unsub_response = await connection.receive_message(timeout=5.0)
            if unsub_response:
                print("✅ Successfully unsubscribed")
            else:
                print("⚠️  Unsubscribe response not received")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return summary['total_updates'] > 0  # Success if we received any updates
        
    except Exception as e:
        logger.error(f"Error testing account RMS updates: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for account RMS updates testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Account RMS Updates endpoint (Templates 3508/3509)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_account_rms_updates.py
  python test_account_rms_updates.py --account-id "123456" --duration 120
  python test_account_rms_updates.py --format csv

Safety:
  This script performs READ-ONLY account RMS update subscriptions only.
  Requires authentication but no account modifications performed.
  Automatically unsubscribes at the end of the test.
  
Output:
  Displays real-time account RMS updates including:
  - Buying power changes (total, available, utilization)
  - Margin updates (initial, maintenance, excess/deficit)
  - P&L tracking (daily, realized, unrealized)
  - Risk limit monitoring (max daily loss, utilization warnings)
  - Risk alerts and threshold violations
  - Account health assessment and trends
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to monitor (optional, defaults to all accounts)"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=60,
        help="How long to listen for RMS updates in seconds (default: 60)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC ACCOUNT RMS UPDATES ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY ACCOUNT RMS UPDATE OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_account_rms_updates(
        account_id=args.account_id,
        duration=args.duration,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Account RMS updates test completed successfully!")
        return 0
    else:
        print("\n❌ Account RMS updates test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)