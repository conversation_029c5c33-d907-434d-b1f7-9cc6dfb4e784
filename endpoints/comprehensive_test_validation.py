#!/usr/bin/env python3

"""
Comprehensive Test Validation Script for Rithmic API Endpoint Fixes.

This script validates the systematic fixes that have been implemented across
the Rithmic API endpoint tests, measuring success rates and improvements.

Focus areas:
1. Account Info Tests (where major fixes were applied)
2. Historical Data Tests (where parameter fixes were applied)
3. Repository Tests (where error handling was fixed)
4. Select Market Data Tests (baseline comparison)
"""

import asyncio
import logging
import sys
import os
import subprocess
import time
from typing import Dict, List, Tuple, Any
from datetime import datetime

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'test_validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

class TestResult:
    """Container for test results."""
    def __init__(self, test_name: str, category: str, success: bool, 
                 duration: float, error_message: str = ""):
        self.test_name = test_name
        self.category = category
        self.success = success
        self.duration = duration
        self.error_message = error_message
        self.timestamp = datetime.now()

class ComprehensiveTestValidator:
    """Validates endpoint test fixes and measures improvements."""
    
    def __init__(self):
        self.results: List[TestResult] = []
        self.test_categories = {
            'account_info': [
                'test_account_list.py',
                'test_account_rms_info.py', 
                'test_bracket_info.py',
                'test_order_session_config.py',
                'test_show_orders.py',
                'test_exchange_permissions.py'
            ],
            'historical_data': [
                'test_time_bars.py',
                'test_volume_bars.py',
                'test_tick_bars.py',
                'test_resume_bars.py'
            ],
            'repository': [
                'test_list_agreements.py',
                'test_show_agreement.py'
            ],
            'market_data': [
                'test_symbol_search.py',
                'test_product_codes.py',
                'test_front_month_contract.py',
                'test_reference_data.py'
            ],
            'system_discovery': [
                'test_system_info.py',
                'test_gateway_info.py',
                'test_heartbeat.py'
            ]
        }
    
    async def run_single_test(self, category: str, test_file: str) -> TestResult:
        """Run a single test and capture results."""
        test_path = os.path.join(category, test_file)
        test_name = f"{category}/{test_file}"
        
        print(f"🧪 Running {test_name}...")
        start_time = time.time()
        
        try:
            # Run the test with timeout
            process = await asyncio.create_subprocess_exec(
                sys.executable, test_path,
                '--log-level', 'ERROR',  # Reduce noise
                cwd=os.getcwd(),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=60  # 60 second timeout per test
                )
                
                duration = time.time() - start_time
                success = process.returncode == 0
                
                if not success:
                    error_msg = stderr.decode('utf-8', errors='ignore')[-500:]  # Last 500 chars
                    print(f"❌ {test_name} failed (exit code: {process.returncode})")
                    if error_msg:
                        print(f"   Error snippet: {error_msg.split(chr(10))[-2] if chr(10) in error_msg else error_msg[:100]}")
                else:
                    print(f"✅ {test_name} passed ({duration:.1f}s)")
                
                return TestResult(test_name, category, success, duration, error_msg if not success else "")
                
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                duration = time.time() - start_time
                print(f"⏰ {test_name} timed out after 60s")
                return TestResult(test_name, category, False, duration, "Test timed out")
                
        except Exception as e:
            duration = time.time() - start_time
            error_msg = str(e)
            print(f"💥 {test_name} crashed: {error_msg}")
            return TestResult(test_name, category, False, duration, error_msg)
    
    async def run_category_tests(self, category: str, test_files: List[str]) -> List[TestResult]:
        """Run all tests in a category."""
        print(f"\n{'='*60}")
        print(f"📂 TESTING CATEGORY: {category.upper()}")
        print(f"{'='*60}")
        
        category_results = []
        
        for test_file in test_files:
            result = await self.run_single_test(category, test_file)
            category_results.append(result)
            self.results.append(result)
            
            # Brief pause between tests
            await asyncio.sleep(2)
        
        return category_results
    
    def analyze_results(self) -> Dict[str, Any]:
        """Analyze test results and generate comprehensive report."""
        analysis = {
            'overall': {
                'total_tests': len(self.results),
                'passed': len([r for r in self.results if r.success]),
                'failed': len([r for r in self.results if not r.success]),
                'success_rate': 0.0,
                'total_duration': sum(r.duration for r in self.results)
            },
            'by_category': {},
            'key_improvements': [],
            'remaining_issues': [],
            'performance_insights': []
        }
        
        # Calculate overall success rate
        if analysis['overall']['total_tests'] > 0:
            analysis['overall']['success_rate'] = (
                analysis['overall']['passed'] / analysis['overall']['total_tests']
            ) * 100
        
        # Analyze by category
        for category in self.test_categories.keys():
            category_results = [r for r in self.results if r.category == category]
            if category_results:
                passed = len([r for r in category_results if r.success])
                total = len(category_results)
                
                analysis['by_category'][category] = {
                    'total_tests': total,
                    'passed': passed,
                    'failed': total - passed,
                    'success_rate': (passed / total) * 100 if total > 0 else 0,
                    'avg_duration': sum(r.duration for r in category_results) / total,
                    'tests': [
                        {
                            'name': r.test_name,
                            'success': r.success,
                            'duration': r.duration,
                            'error': r.error_message[:100] if r.error_message else ""
                        }
                        for r in category_results
                    ]
                }
        
        # Identify key improvements (focusing on fixed categories)
        fixed_categories = ['account_info', 'historical_data', 'repository']
        for category in fixed_categories:
            if category in analysis['by_category']:
                cat_data = analysis['by_category'][category]
                if cat_data['success_rate'] > 50:  # Threshold for improvement
                    analysis['key_improvements'].append(
                        f"{category.replace('_', ' ').title()}: "
                        f"{cat_data['passed']}/{cat_data['total_tests']} tests passing "
                        f"({cat_data['success_rate']:.1f}%)"
                    )
        
        # Identify remaining issues
        for result in self.results:
            if not result.success:
                issue_desc = f"{result.test_name}: {result.error_message[:80]}..." if result.error_message else f"{result.test_name}: Failed"
                analysis['remaining_issues'].append(issue_desc)
        
        # Performance insights
        fast_tests = [r for r in self.results if r.success and r.duration < 10]
        slow_tests = [r for r in self.results if r.duration > 30]
        
        if fast_tests:
            analysis['performance_insights'].append(f"{len(fast_tests)} tests completed quickly (<10s)")
        if slow_tests:
            analysis['performance_insights'].append(f"{len(slow_tests)} tests were slow (>30s)")
        
        return analysis
    
    def generate_report(self, analysis: Dict[str, Any]) -> str:
        """Generate comprehensive validation report."""
        report = [
            "=" * 80,
            "🔍 COMPREHENSIVE TEST VALIDATION REPORT",
            "=" * 80,
            f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "📊 OVERALL RESULTS:",
            f"   Total Tests: {analysis['overall']['total_tests']}",
            f"   Passed: {analysis['overall']['passed']} ✅",
            f"   Failed: {analysis['overall']['failed']} ❌", 
            f"   Success Rate: {analysis['overall']['success_rate']:.1f}%",
            f"   Total Duration: {analysis['overall']['total_duration']:.1f}s",
            ""
        ]
        
        # Category breakdown
        report.extend([
            "📂 RESULTS BY CATEGORY:",
            "-" * 50
        ])
        
        for category, data in analysis['by_category'].items():
            status_emoji = "✅" if data['success_rate'] > 70 else "⚠️" if data['success_rate'] > 40 else "❌"
            report.extend([
                f"{status_emoji} {category.replace('_', ' ').title()}:",
                f"   Success Rate: {data['success_rate']:.1f}% ({data['passed']}/{data['total_tests']})",
                f"   Average Duration: {data['avg_duration']:.1f}s",
                ""
            ])
            
            # Show individual test results for fixed categories
            if category in ['account_info', 'historical_data', 'repository']:
                for test in data['tests']:
                    status = "✅" if test['success'] else "❌"
                    test_name = test['name'].split('/')[-1].replace('.py', '')
                    report.append(f"     {status} {test_name} ({test['duration']:.1f}s)")
                report.append("")
        
        # Key improvements
        if analysis['key_improvements']:
            report.extend([
                "🎯 KEY IMPROVEMENTS VALIDATED:",
                "-" * 40
            ])
            for improvement in analysis['key_improvements']:
                report.append(f"✅ {improvement}")
            report.append("")
        
        # Remaining issues
        if analysis['remaining_issues']:
            report.extend([
                "⚠️ REMAINING ISSUES:",
                "-" * 30
            ])
            for issue in analysis['remaining_issues'][:10]:  # Show top 10
                report.append(f"❌ {issue}")
            if len(analysis['remaining_issues']) > 10:
                report.append(f"... and {len(analysis['remaining_issues']) - 10} more")
            report.append("")
        
        # Performance insights
        if analysis['performance_insights']:
            report.extend([
                "⚡ PERFORMANCE INSIGHTS:",
                "-" * 35
            ])
            for insight in analysis['performance_insights']:
                report.append(f"📈 {insight}")
            report.append("")
        
        # Recommendations
        report.extend([
            "💡 RECOMMENDATIONS:",
            "-" * 25,
        ])
        
        overall_rate = analysis['overall']['success_rate']
        if overall_rate > 70:
            report.append("🎉 Excellent progress! Most endpoint tests are working correctly.")
        elif overall_rate > 50:
            report.append("📈 Good improvement! Continue addressing remaining issues.")
        else:
            report.append("🔧 More work needed. Focus on systematic error resolution.")
        
        # Category-specific recommendations
        for category, data in analysis['by_category'].items():
            if data['success_rate'] < 50 and category in ['account_info', 'historical_data', 'repository']:
                report.append(f"🔍 {category.replace('_', ' ').title()}: Review authentication and parameter requirements")
        
        report.extend([
            "",
            "=" * 80,
            "End of Validation Report",
            "=" * 80
        ])
        
        return "\n".join(report)

async def main():
    """Main validation execution."""
    print("🧪 RITHMIC API ENDPOINT TEST VALIDATION")
    print("=" * 50)
    print("🎯 Focus: Validating systematic fixes across endpoint categories")
    print("⚠️  Running READ-ONLY tests with paper trading credentials")
    print("=" * 50)
    
    validator = ComprehensiveTestValidator()
    
    # Define test priorities based on fix implementation
    priority_categories = [
        'system_discovery',  # Should work as baseline
        'account_info',      # Major fixes applied
        'historical_data',   # Parameter fixes applied  
        'repository',        # Error handling fixes
        'market_data'        # Selective testing
    ]
    
    start_time = datetime.now()
    
    # Run tests in priority order
    for category in priority_categories:
        if category in validator.test_categories:
            await validator.run_category_tests(
                category, 
                validator.test_categories[category]
            )
            
            # Pause between categories
            print(f"\n⏳ Pausing 5s before next category...")
            await asyncio.sleep(5)
    
    end_time = datetime.now()
    total_duration = (end_time - start_time).total_seconds()
    
    # Analyze results and generate report
    print(f"\n🔍 Analyzing results...")
    analysis = validator.analyze_results()
    report = validator.generate_report(analysis)
    
    # Display results
    print("\n" + report)
    
    # Save detailed report
    report_file = f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_file, 'w') as f:
        f.write(report)
        f.write(f"\n\nDetailed Test Results:\n")
        f.write("=" * 50 + "\n")
        for result in validator.results:
            f.write(f"{result.test_name}: {'PASS' if result.success else 'FAIL'} "
                   f"({result.duration:.1f}s)\n")
            if result.error_message:
                f.write(f"  Error: {result.error_message}\n")
    
    print(f"\n📄 Detailed report saved to: {report_file}")
    print(f"⏱️  Total validation time: {total_duration:.1f}s")
    
    # Summary for user
    success_rate = analysis['overall']['success_rate']
    if success_rate > 70:
        print(f"\n🎉 VALIDATION SUCCESS! {success_rate:.1f}% of tests passing")
        return 0
    elif success_rate > 50:
        print(f"\n📈 GOOD PROGRESS! {success_rate:.1f}% of tests passing") 
        return 0
    else:
        print(f"\n🔧 MORE WORK NEEDED! Only {success_rate:.1f}% of tests passing")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Validation interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Validation failed: {e}")
        sys.exit(1)