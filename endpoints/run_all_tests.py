#!/usr/bin/env python3

"""
Comprehensive Test Runner for Rithmic API Endpoints.

This script runs all available endpoint tests in a coordinated manner,
providing a complete validation of the Rithmic API testing framework.

SAFETY: All tests perform READ-ONLY operations only.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the required paths
project_root = os.path.dirname(__file__)
sys.path.append(os.path.join(project_root, 'shared'))

import config
from config import get_config, validate_config

logger = logging.getLogger(__name__)

class TestResult:
    """Represents the result of a single test."""
    
    def __init__(self, name: str, category: str, success: bool, 
                 duration: float, error: Optional[str] = None):
        self.name = name
        self.category = category
        self.success = success
        self.duration = duration
        self.error = error
        self.timestamp = datetime.now()

class EndpointTestRunner:
    """Manages and coordinates all endpoint tests."""
    
    def __init__(self):
        self.results: List[TestResult] = []
        self.config = get_config()
        self.start_time = None
        self.end_time = None
    
    async def run_test(self, test_name: str, category: str, 
                      test_command: List[str]) -> TestResult:
        """
        Run a single test and capture its result.
        
        Args:
            test_name: Human-readable test name
            category: Test category
            test_command: Command to execute test
            
        Returns:
            TestResult: Result of the test execution
        """
        print(f"\n{'='*60}")
        print(f"🧪 RUNNING: {test_name}")
        print(f"📂 Category: {category}")
        print(f"⚡ Command: {' '.join(test_command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        success = False
        error = None
        
        try:
            # Execute test command
            process = await asyncio.create_subprocess_exec(
                *test_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
                cwd=project_root
            )
            
            stdout, _ = await process.communicate()
            duration = time.time() - start_time
            
            # Determine success based on return code
            success = process.returncode == 0
            
            # Display output
            output = stdout.decode('utf-8', errors='replace')
            print(output)
            
            if not success:
                error = f"Test failed with return code {process.returncode}"
                print(f"❌ {error}")
            else:
                print(f"✅ Test completed successfully in {duration:.1f}s")
                
        except Exception as e:
            duration = time.time() - start_time
            error = str(e)
            print(f"💥 Test execution failed: {error}")
        
        result = TestResult(test_name, category, success, duration, error)
        self.results.append(result)
        return result
    
    async def run_system_discovery_tests(self) -> List[TestResult]:
        """Run all system discovery tests."""
        print(f"\n🔍 SYSTEM DISCOVERY TESTS")
        print(f"{'='*40}")
        
        tests = [
            ("System Info", ["python", "system_discovery/test_system_info.py"]),
            ("Gateway Info", ["python", "system_discovery/test_gateway_info.py"]),
            ("Heartbeat", ["python", "system_discovery/test_heartbeat.py", "--duration", "15"])
        ]
        
        results = []
        for test_name, command in tests:
            result = await self.run_test(test_name, "System Discovery", command)
            results.append(result)
            
            # Brief pause between tests
            if result != tests[-1]:
                print("\n⏳ Waiting 3 seconds before next test...")
                await asyncio.sleep(3)
        
        return results
    
    async def run_market_data_tests(self) -> List[TestResult]:
        """Run all market data tests."""
        print(f"\n📊 MARKET DATA TESTS")
        print(f"{'='*40}")
        
        tests = [
            ("Symbol Search", ["python", "market_data/test_symbol_search.py", "--pattern", "ES*"]),
            ("Reference Data", ["python", "market_data/test_reference_data.py", "--symbol", "ESH5", "--exchange", "CME"]),
            ("Product Codes", ["python", "market_data/test_product_codes.py", "--exchange", "CME"]),
            ("Front Month Contract", ["python", "market_data/test_front_month_contract.py", "--symbol", "ES", "--exchange", "CME"]),
            ("Auxiliary Reference Data", ["python", "market_data/test_auxiliary_reference_data.py", "--symbol", "ESH5", "--exchange", "CME"]),
            ("Tick Size Table", ["python", "market_data/test_tick_size_table.py", "--symbol", "ESH5", "--exchange", "CME"]),
            ("Instrument by Underlying", ["python", "market_data/test_instrument_by_underlying.py", "--underlying", "ES", "--exchange", "CME"]),
            ("Volume at Price", ["python", "market_data/test_volume_at_price.py", "--symbol", "ESH5", "--exchange", "CME"]),
            ("Market Data by Underlying", ["python", "market_data/test_market_data_by_underlying.py", "--underlying", "ES", "--exchange", "CME", "--duration", "20"]),
            ("Depth by Order Snapshot", ["python", "market_data/test_depth_by_order.py", "--symbol", "ESH5", "--exchange", "CME", "--snapshot"]),
            ("Streaming Trades", ["python", "market_data/test_streaming_trades.py", "--symbol", "ESH5", "--exchange", "CME", "--duration", "20"]),
            ("Order Book Streaming", ["python", "market_data/test_order_book_streaming.py", "--symbol", "ESH5", "--exchange", "CME", "--duration", "20"]),
            ("Market Statistics", ["python", "market_data/test_market_statistics.py", "--symbol", "ESH5", "--exchange", "CME", "--duration", "20"]),
            ("Market Data Subscription", ["python", "market_data/test_market_data_subscription.py", 
             "--symbol", "ESH5", "--exchange", "CME", "--duration", "20"])
        ]
        
        results = []
        for test_name, command in tests:
            result = await self.run_test(test_name, "Market Data", command)
            results.append(result)
            
            if result != tests[-1]:
                print("\n⏳ Waiting 5 seconds before next test...")
                await asyncio.sleep(5)
        
        return results
    
    async def run_account_info_tests(self) -> List[TestResult]:
        """Run all account info tests."""
        print(f"\n👥 ACCOUNT INFO TESTS")
        print(f"{'='*40}")
        
        tests = [
            ("Account List", ["python", "account_info/test_account_list.py"]),
            ("Account RMS Info", ["python", "account_info/test_account_rms_info.py"]),
            ("Product RMS Info", ["python", "account_info/test_product_rms_info.py"]),
            ("Account RMS Updates", ["python", "account_info/test_account_rms_updates.py", "--duration", "15"]),
            ("Order History Dates", ["python", "account_info/test_order_history_dates.py"]),
            ("Show Orders", ["python", "account_info/test_show_orders.py"]),
            ("Order History", ["python", "account_info/test_order_history.py"]),
            ("Replay Executions", ["python", "account_info/test_replay_executions.py"]),
            ("Trade Routes", ["python", "account_info/test_trade_routes.py"]),
            ("Exchange Permissions", ["python", "account_info/test_exchange_permissions.py"]),
            ("Order Session Config", ["python", "account_info/test_order_session_config.py"]),
            ("Bracket Info", ["python", "account_info/test_bracket_info.py"]),
            ("Easy to Borrow", ["python", "account_info/test_easy_to_borrow.py"])
        ]
        
        results = []
        for test_name, command in tests:
            result = await self.run_test(test_name, "Account Info", command)
            results.append(result)
            
            if result != tests[-1]:
                print("\n⏳ Waiting 3 seconds before next test...")
                await asyncio.sleep(3)
        
        return results
    
    async def run_historical_data_tests(self) -> List[TestResult]:
        """Run all historical data tests."""
        print(f"\n📈 HISTORICAL DATA TESTS")
        print(f"{'='*40}")
        
        tests = [
            ("Time Bar Replay", ["python", "historical_data/test_time_bars.py", 
             "--symbol", "ESH5", "--exchange", "CME", "--replay", "--max-bars", "10"]),
            ("Tick Bars", ["python", "historical_data/test_tick_bars.py",
             "--symbol", "ESH5", "--exchange", "CME", "--max-bars", "20"]),
            ("Resume Bars", ["python", "historical_data/test_resume_bars.py",
             "--symbol", "ESH5", "--exchange", "CME", "--max-bars", "15"]),
            ("Volume Profile", ["python", "historical_data/test_volume_profile.py",
             "--symbol", "ESH5", "--exchange", "CME", "--max-bars", "10"]),
            ("Volume Bars", ["python", "historical_data/test_volume_bars.py",
             "--symbol", "ESH5", "--exchange", "CME", "--volume-size", "1000", "--max-bars", "15"])
        ]
        
        results = []
        for test_name, command in tests:
            result = await self.run_test(test_name, "Historical Data", command)
            results.append(result)
            
            if result != tests[-1]:
                print("\n⏳ Waiting 5 seconds before next test...")
                await asyncio.sleep(5)
        
        return results
    
    async def run_pnl_data_tests(self) -> List[TestResult]:
        """Run all PnL data tests."""
        print(f"\n💰 PNL DATA TESTS")
        print(f"{'='*40}")
        
        tests = [
            ("PnL Position Snapshot", ["python", "pnl_data/test_pnl_position_snapshot.py"]),
            ("PnL Position Updates", ["python", "pnl_data/test_pnl_position_updates.py", "--duration", "30"])
        ]
        
        results = []
        for test_name, command in tests:
            result = await self.run_test(test_name, "PnL Data", command)
            results.append(result)
            
            if result != tests[-1]:
                print("\n⏳ Waiting 3 seconds before next test...")
                await asyncio.sleep(3)
        
        return results
    
    async def run_repository_tests(self) -> List[TestResult]:
        """Run all repository tests."""
        print(f"\n📜 REPOSITORY TESTS")
        print(f"{'='*40}")
        
        tests = [
            ("List Agreements", ["python", "repository/test_list_agreements.py"]),
            ("Show Agreement", ["python", "repository/test_show_agreement.py"])
        ]
        
        results = []
        for test_name, command in tests:
            result = await self.run_test(test_name, "Repository", command)
            results.append(result)
            
            if result != tests[-1]:
                print("\n⏳ Waiting 3 seconds before next test...")
                await asyncio.sleep(3)
        
        return results
    
    async def run_all_tests(self, include_categories: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Run all available tests.
        
        Args:
            include_categories: Optional list of categories to include
            
        Returns:
            dict: Summary of all test results
        """
        self.start_time = datetime.now()
        
        print("🚀 STARTING COMPREHENSIVE ENDPOINT TESTING")
        print("=" * 60)
        print(f"Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Paper Trading: {'✅ ENABLED' if self.config.paper_trading_only else '❌ DISABLED'}")
        print("=" * 60)
        
        # Define test categories
        all_categories = {
            "system_discovery": self.run_system_discovery_tests,
            "market_data": self.run_market_data_tests,
            "account_info": self.run_account_info_tests,
            "historical_data": self.run_historical_data_tests,
            "pnl_data": self.run_pnl_data_tests,
            "repository": self.run_repository_tests
        }
        
        # Filter categories if specified
        if include_categories:
            categories_to_run = {k: v for k, v in all_categories.items() 
                               if k in include_categories}
        else:
            categories_to_run = all_categories
        
        # Run each category of tests
        for category_name, test_func in categories_to_run.items():
            try:
                print(f"\n🎯 STARTING {category_name.upper().replace('_', ' ')} CATEGORY")
                await test_func()
                
                # Pause between categories
                if category_name != list(categories_to_run.keys())[-1]:
                    print(f"\n⏳ Waiting 10 seconds before next category...")
                    await asyncio.sleep(10)
                    
            except Exception as e:
                logger.error(f"Error in {category_name} tests: {e}")
                print(f"💥 {category_name} tests failed: {e}")
        
        self.end_time = datetime.now()
        
        # Generate summary
        return self.generate_summary()
    
    def generate_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary."""
        total_duration = (self.end_time - self.start_time).total_seconds()
        
        # Calculate statistics
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - passed_tests
        
        # Group by category
        categories = {}
        for result in self.results:
            if result.category not in categories:
                categories[result.category] = {
                    "tests": [],
                    "passed": 0,
                    "failed": 0,
                    "total_duration": 0.0
                }
            
            categories[result.category]["tests"].append(result)
            categories[result.category]["total_duration"] += result.duration
            
            if result.success:
                categories[result.category]["passed"] += 1
            else:
                categories[result.category]["failed"] += 1
        
        summary = {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "total_duration": total_duration,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "categories": categories,
            "results": self.results
        }
        
        return summary
    
    def display_summary(self, summary: Dict[str, Any]) -> None:
        """Display comprehensive test summary."""
        print("\n" + "=" * 80)
        print("📋 COMPREHENSIVE TEST SUMMARY")
        print("=" * 80)
        print(f"Start Time: {summary['start_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"End Time: {summary['end_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total Duration: {summary['total_duration']:.1f} seconds")
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed_tests']} ✅")
        print(f"Failed: {summary['failed_tests']} ❌")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        
        # Category breakdown
        print(f"\n📊 RESULTS BY CATEGORY:")
        print("-" * 60)
        
        for category, data in summary['categories'].items():
            passed = data['passed']
            failed = data['failed']
            total = passed + failed
            duration = data['total_duration']
            
            print(f"{category.replace('_', ' ').title():20s}: {passed}/{total} passed ({duration:.1f}s)")
            
            # Show failed tests
            failed_tests = [t for t in data['tests'] if not t.success]
            if failed_tests:
                for test in failed_tests:
                    print(f"  ❌ {test.name}: {test.error}")
        
        # Overall result
        print("\n" + "=" * 80)
        if summary['failed_tests'] == 0:
            print("🎉 ALL TESTS PASSED! 🎉")
            print("✅ Rithmic API endpoint testing framework is fully functional")
        else:
            print("⚠️  SOME TESTS FAILED")
            print(f"❌ {summary['failed_tests']} out of {summary['total_tests']} tests failed")
            print("📋 Review failed tests above for troubleshooting")
        
        print("=" * 80)

async def main():
    """Main function for comprehensive endpoint testing."""
    parser = argparse.ArgumentParser(
        description="Comprehensive Test Runner for Rithmic API Endpoints",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_all_tests.py
  python run_all_tests.py --categories system_discovery market_data
  python run_all_tests.py --log-level DEBUG
  python run_all_tests.py --save-responses

Categories:
  system_discovery  - System info, gateway info, heartbeat tests (3 tests)
  market_data      - Symbol search, market data subscription, streaming data tests (14 tests)
  account_info     - Account listing, RMS info, order history, trading config tests (13 tests)
  historical_data  - Time bars, tick bars, volume analysis, resume functionality (5 tests)
  pnl_data        - Position snapshots and real-time P&L monitoring (2 tests)
  repository      - Agreement listing and legal document analysis (2 tests)

Total: 39 comprehensive endpoint tests covering 60+ template IDs

Safety:
  ALL TESTS PERFORM READ-ONLY OPERATIONS ONLY.
  No account modifications or order operations are performed.
  Uses paper trading credentials for maximum safety.
  No dangerous templates (order placement, modifications) are tested.
        """
    )
    
    parser.add_argument(
        "--categories",
        nargs="+",
        choices=["system_discovery", "market_data", "account_info", 
                "historical_data", "pnl_data", "repository"],
        help="Specific categories to test (default: all)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save all responses to files"
    )
    
    parser.add_argument(
        "--summary-only",
        action="store_true",
        help="Show only final summary (suppress individual test output)"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = getattr(logging, args.log_level)
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC API COMPREHENSIVE ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: ALL TESTS ARE READ-ONLY OPERATIONS")
    print("⚠️  NO ACCOUNT MODIFICATIONS WILL BE PERFORMED")
    print("=" * 50)
    
    # Create test runner
    runner = EndpointTestRunner()
    
    try:
        # Run tests
        summary = await runner.run_all_tests(include_categories=args.categories)
        
        # Display summary
        runner.display_summary(summary)
        
        # Return appropriate exit code
        if summary['failed_tests'] == 0:
            return 0
        else:
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
        
        # Display partial summary if we have results
        if runner.results:
            runner.end_time = datetime.now()
            summary = runner.generate_summary()
            print("\n📋 PARTIAL RESULTS:")
            runner.display_summary(summary)
        
        return 130
    
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {e}")
        logger.exception("Unexpected error in test runner")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)