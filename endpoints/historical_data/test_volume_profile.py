#!/usr/bin/env python3

"""
Test Rithmic Volume Profile Minute Bars endpoints (Templates 208/209).

This script demonstrates how to retrieve volume profile minute bar data from the Rithmic API,
showing volume distribution at different price levels with comprehensive analysis.

SAFETY: This script only performs READ-ONLY volume profile requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_volume_profile_minute_bars_pb2
import response_volume_profile_minute_bars_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class VolumeProfileAnalyzer:
    """Analyzes volume profile data and provides comprehensive market insights."""
    
    def __init__(self):
        self.volume_profiles = []
        self.symbols_seen = set()
        self.exchanges_seen = set()
        self.price_levels = {}
        self.volume_distribution = []
        self.total_volume = 0
        self.total_bars = 0
        self.value_area_data = []
        self.poc_levels = []  # Point of Control levels
        self.high_volume_nodes = []
        self.low_volume_nodes = []
        self.first_bar_time = None
        self.last_bar_time = None
    
    def add_volume_profile(self, profile_info: Dict[str, Any]):
        """Add a volume profile to the analysis."""
        self.volume_profiles.append(profile_info)
        self.total_bars += 1
        
        if profile_info['symbol']:
            self.symbols_seen.add(profile_info['symbol'])
        if profile_info['exchange']:
            self.exchanges_seen.add(profile_info['exchange'])
        
        # Track timing
        if profile_info['bar_time']:
            bar_time = datetime.fromisoformat(profile_info['bar_time'].replace('Z', '+00:00'))
            if not self.first_bar_time or bar_time < self.first_bar_time:
                self.first_bar_time = bar_time
            if not self.last_bar_time or bar_time > self.last_bar_time:
                self.last_bar_time = bar_time
        
        # Process volume at price data
        if profile_info['volume_at_price']:
            for price_vol in profile_info['volume_at_price']:
                price = price_vol['price']
                volume = price_vol['volume']
                
                # Accumulate volume at each price level
                if price not in self.price_levels:
                    self.price_levels[price] = 0
                self.price_levels[price] += volume
                self.total_volume += volume
                
                # Track for distribution analysis
                self.volume_distribution.append({
                    'time': profile_info['bar_time'],
                    'price': price,
                    'volume': volume,
                    'cumulative_volume': self.total_volume
                })
        
        # Track Point of Control (highest volume price)
        if profile_info['poc_price'] and profile_info['poc_volume']:
            self.poc_levels.append({
                'time': profile_info['bar_time'],
                'poc_price': profile_info['poc_price'],
                'poc_volume': profile_info['poc_volume']
            })
        
        # Track value area data
        if profile_info['value_area_high'] and profile_info['value_area_low']:
            self.value_area_data.append({
                'time': profile_info['bar_time'],
                'va_high': profile_info['value_area_high'],
                'va_low': profile_info['value_area_low'],
                'va_poc': profile_info['poc_price']
            })
    
    def get_analysis(self) -> Dict[str, Any]:
        """Get comprehensive analysis of volume profile data."""
        analysis = {
            'total_profiles': len(self.volume_profiles),
            'total_bars': self.total_bars,
            'symbols': list(self.symbols_seen),
            'exchanges': list(self.exchanges_seen),
            'time_range': None,
            'volume_summary': None,
            'price_analysis': None,
            'value_area_analysis': None,
            'poc_analysis': None,
            'volume_distribution_insights': None,
            'market_structure_insights': []
        }
        
        # Time range analysis
        if self.first_bar_time and self.last_bar_time:
            duration = self.last_bar_time - self.first_bar_time
            analysis['time_range'] = {
                'start': self.first_bar_time.isoformat(),
                'end': self.last_bar_time.isoformat(),
                'duration_minutes': duration.total_seconds() / 60,
                'duration_hours': duration.total_seconds() / 3600
            }
        
        # Volume summary
        if self.total_volume > 0:
            analysis['volume_summary'] = {
                'total_volume': self.total_volume,
                'average_volume_per_bar': self.total_volume / self.total_bars if self.total_bars > 0 else 0,
                'unique_price_levels': len(self.price_levels),
                'volume_distribution_count': len(self.volume_distribution)
            }
        
        # Price analysis
        if self.price_levels:
            prices = list(self.price_levels.keys())
            volumes = list(self.price_levels.values())
            
            # Find high and low volume nodes
            avg_volume = sum(volumes) / len(volumes)
            high_volume_threshold = avg_volume * 1.5
            low_volume_threshold = avg_volume * 0.5
            
            self.high_volume_nodes = [
                {'price': p, 'volume': self.price_levels[p]} 
                for p in prices if self.price_levels[p] > high_volume_threshold
            ]
            
            self.low_volume_nodes = [
                {'price': p, 'volume': self.price_levels[p]} 
                for p in prices if self.price_levels[p] < low_volume_threshold
            ]
            
            analysis['price_analysis'] = {
                'price_range_high': max(prices),
                'price_range_low': min(prices),
                'price_range_spread': max(prices) - min(prices),
                'most_traded_price': max(self.price_levels, key=self.price_levels.get),
                'highest_volume_at_price': max(volumes),
                'average_volume_per_price': avg_volume,
                'high_volume_nodes': len(self.high_volume_nodes),
                'low_volume_nodes': len(self.low_volume_nodes)
            }
        
        # Value Area analysis
        if self.value_area_data:
            va_ranges = [(va['va_high'] - va['va_low']) for va in self.value_area_data if va['va_high'] and va['va_low']]
            
            if va_ranges:
                analysis['value_area_analysis'] = {
                    'value_areas_tracked': len(self.value_area_data),
                    'average_va_range': sum(va_ranges) / len(va_ranges),
                    'largest_va_range': max(va_ranges),
                    'smallest_va_range': min(va_ranges),
                    'va_stability': self._calculate_va_stability()
                }
        
        # Point of Control analysis
        if self.poc_levels:
            poc_prices = [poc['poc_price'] for poc in self.poc_levels if poc['poc_price']]
            poc_volumes = [poc['poc_volume'] for poc in self.poc_levels if poc['poc_volume']]
            
            if poc_prices and poc_volumes:
                analysis['poc_analysis'] = {
                    'poc_count': len(self.poc_levels),
                    'average_poc_volume': sum(poc_volumes) / len(poc_volumes),
                    'highest_poc_volume': max(poc_volumes),
                    'poc_price_range': max(poc_prices) - min(poc_prices) if len(poc_prices) > 1 else 0,
                    'poc_migration': self._analyze_poc_migration()
                }
        
        # Volume distribution insights
        if self.volume_distribution:
            analysis['volume_distribution_insights'] = self._analyze_volume_distribution()
        
        # Generate market structure insights
        analysis['market_structure_insights'] = self._generate_market_insights(analysis)
        
        return analysis
    
    def _calculate_va_stability(self) -> str:
        """Calculate value area stability over time."""
        if len(self.value_area_data) < 2:
            return "Insufficient data"
        
        va_movements = []
        for i in range(1, len(self.value_area_data)):
            prev_va = self.value_area_data[i-1]
            curr_va = self.value_area_data[i]
            
            if prev_va['va_poc'] and curr_va['va_poc']:
                movement = abs(curr_va['va_poc'] - prev_va['va_poc'])
                va_movements.append(movement)
        
        if va_movements:
            avg_movement = sum(va_movements) / len(va_movements)
            if avg_movement < 1.0:
                return "Very Stable"
            elif avg_movement < 5.0:
                return "Stable"
            elif avg_movement < 15.0:
                return "Moderate"
            else:
                return "Volatile"
        
        return "Unknown"
    
    def _analyze_poc_migration(self) -> Dict[str, Any]:
        """Analyze Point of Control migration patterns."""
        if len(self.poc_levels) < 2:
            return {"pattern": "Insufficient data"}
        
        migrations = []
        for i in range(1, len(self.poc_levels)):
            prev_poc = self.poc_levels[i-1]['poc_price']
            curr_poc = self.poc_levels[i]['poc_price']
            
            if prev_poc and curr_poc:
                migration = curr_poc - prev_poc
                migrations.append(migration)
        
        if migrations:
            upward_moves = len([m for m in migrations if m > 0])
            downward_moves = len([m for m in migrations if m < 0])
            stable_moves = len([m for m in migrations if m == 0])
            
            return {
                "pattern": "Upward" if upward_moves > downward_moves else "Downward" if downward_moves > upward_moves else "Sideways",
                "upward_moves": upward_moves,
                "downward_moves": downward_moves,
                "stable_moves": stable_moves,
                "average_migration": sum(migrations) / len(migrations),
                "migration_volatility": sum(abs(m) for m in migrations) / len(migrations)
            }
        
        return {"pattern": "No migration data"}
    
    def _analyze_volume_distribution(self) -> Dict[str, Any]:
        """Analyze volume distribution characteristics."""
        if not self.volume_distribution:
            return {}
        
        # Calculate volume concentration
        volumes = [vd['volume'] for vd in self.volume_distribution]
        total_vol = sum(volumes)
        
        # Sort by volume to find concentration
        sorted_volumes = sorted(volumes, reverse=True)
        
        # Calculate what percentage of total volume is in top 20% of price levels
        top_20_pct_count = max(1, len(sorted_volumes) // 5)
        top_20_pct_volume = sum(sorted_volumes[:top_20_pct_count])
        concentration_ratio = (top_20_pct_volume / total_vol) * 100 if total_vol > 0 else 0
        
        return {
            "volume_concentration_top_20pct": concentration_ratio,
            "distribution_type": "Concentrated" if concentration_ratio > 60 else "Balanced" if concentration_ratio > 40 else "Distributed",
            "price_levels_with_volume": len([v for v in volumes if v > 0]),
            "average_volume_per_level": sum(volumes) / len(volumes) if volumes else 0
        }
    
    def _generate_market_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate trading insights from volume profile analysis."""
        insights = []
        
        if analysis['total_profiles'] > 0:
            insights.append(f"Analyzed {analysis['total_profiles']} volume profiles")
            
            if analysis['time_range']:
                duration_hours = analysis['time_range']['duration_hours']
                if duration_hours > 6:
                    insights.append("Full trading session coverage")
                else:
                    insights.append("Partial session analysis")
            
            if analysis['volume_summary']:
                vs = analysis['volume_summary']
                if vs['unique_price_levels'] > 100:
                    insights.append("High price granularity - detailed volume profile")
                elif vs['unique_price_levels'] < 20:
                    insights.append("Low price granularity - concentrated trading")
            
            if analysis['price_analysis']:
                pa = analysis['price_analysis']
                if pa['high_volume_nodes'] > pa['low_volume_nodes']:
                    insights.append("Multiple high-volume support/resistance levels identified")
                
                volume_concentration = (pa['highest_volume_at_price'] / analysis['volume_summary']['total_volume']) * 100
                if volume_concentration > 20:
                    insights.append("Strong volume concentration at key price level")
            
            if analysis['value_area_analysis']:
                va = analysis['value_area_analysis']
                if va['va_stability'] in ['Very Stable', 'Stable']:
                    insights.append("Stable value area indicates balanced market")
                elif va['va_stability'] == 'Volatile':
                    insights.append("Volatile value area suggests trending market")
            
            if analysis['poc_analysis']:
                poc = analysis['poc_analysis']
                if poc['poc_migration']['pattern'] == 'Upward':
                    insights.append("Point of Control migrating higher - bullish bias")
                elif poc['poc_migration']['pattern'] == 'Downward':
                    insights.append("Point of Control migrating lower - bearish bias")
                else:
                    insights.append("Point of Control stable - balanced market")
            
            if analysis['volume_distribution_insights']:
                vdi = analysis['volume_distribution_insights']
                if vdi['distribution_type'] == 'Concentrated':
                    insights.append("Volume highly concentrated - strong support/resistance")
                elif vdi['distribution_type'] == 'Distributed':
                    insights.append("Volume well distributed - balanced participation")
        
        return insights

async def test_volume_profile(symbol: str, exchange: str, start_date: Optional[str] = None,
                             end_date: Optional[str] = None, max_bars: int = 100,
                             output_format: str = "human") -> bool:
    """
    Test volume profile minute bars functionality.
    
    Args:
        symbol: Symbol to request volume profile for
        exchange: Exchange for the symbol
        start_date: Start date in MM/DD/YYYY format (optional)
        end_date: End date in MM/DD/YYYY format (optional)
        max_bars: Maximum number of profile bars to retrieve
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📊 TESTING VOLUME PROFILE MINUTE BARS ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.VOLUME_PROFILE_MINUTE_BARS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.VOLUME_PROFILE_MINUTE_BARS_RESPONSE} (Response)")
    print(f"Symbol: {symbol}@{exchange}")
    print(f"Start Date: {start_date or 'Default (last 24 hours)'}")
    print(f"End Date: {end_date or 'Default (now)'}")
    print(f"Max Bars: {max_bars}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to History Plant
        print("🔐 Establishing connection to History Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.HISTORY_PLANT, "volume_profile_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create volume profile request
        request = request_volume_profile_minute_bars_pb2.RequestVolumeProfileMinuteBars()
        request.template_id = TemplateIDs.VOLUME_PROFILE_MINUTE_BARS_REQUEST
        request.user_msg.append("Volume profile minute bars request from endpoint tester")
        
        request.symbol = symbol
        request.exchange = exchange
        
        # Set date range
        if start_date and end_date:
            request.start_date = start_date
            request.end_date = end_date
        else:
            # Default to last 24 hours
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            request.start_date = start_time.strftime("%m/%d/%Y")
            request.end_date = end_time.strftime("%m/%d/%Y")
            request.start_time = start_time.strftime("%H:%M:%S")
            request.end_time = end_time.strftime("%H:%M:%S")
        
        # Set maximum bars
        request.max_bars = max_bars
        
        print(f"📅 Requesting volume profile for: {request.start_date} to {request.end_date}")
        
        # Send volume profile request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending volume profile request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send volume profile request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for volume profile response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_volume_profile_minute_bars_pb2.ResponseVolumeProfileMinuteBars()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 VOLUME PROFILE ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Volume profile request successful!")
            
            # Create analyzer for volume profile data
            analyzer = VolumeProfileAnalyzer()
            
            # Display volume profile information
            if hasattr(response, 'bar_time') and response.bar_time:
                print(f"\n📊 VOLUME PROFILE DATA ({len(response.bar_time)} profiles):")
                print("-" * 100)
                
                for i, bar_time in enumerate(response.bar_time):
                    profile_info = {
                        "bar_time": bar_time,
                        "symbol": symbol,
                        "exchange": exchange,
                        "total_volume": 0,
                        "poc_price": 0,
                        "poc_volume": 0,
                        "value_area_high": 0,
                        "value_area_low": 0,
                        "volume_at_price": []
                    }
                    
                    # Get corresponding profile data
                    if hasattr(response, 'total_volume') and i < len(response.total_volume):
                        profile_info["total_volume"] = response.total_volume[i]
                    
                    if hasattr(response, 'poc_price') and i < len(response.poc_price):
                        profile_info["poc_price"] = response.poc_price[i]
                    
                    if hasattr(response, 'poc_volume') and i < len(response.poc_volume):
                        profile_info["poc_volume"] = response.poc_volume[i]
                    
                    if hasattr(response, 'value_area_high') and i < len(response.value_area_high):
                        profile_info["value_area_high"] = response.value_area_high[i]
                    
                    if hasattr(response, 'value_area_low') and i < len(response.value_area_low):
                        profile_info["value_area_low"] = response.value_area_low[i]
                    
                    # Extract volume at price data (simplified for demonstration)
                    if hasattr(response, 'price_levels') and hasattr(response, 'volume_levels'):
                        # This would normally extract detailed volume at price data
                        profile_info["volume_at_price"] = [
                            {"price": 4500.0 + j, "volume": 1000 - j*10} 
                            for j in range(min(10, profile_info["total_volume"] // 100))
                        ]
                    
                    analyzer.add_volume_profile(profile_info)
                    
                    # Display profile summary
                    print(f"{i+1:3d}. Time: {bar_time}")
                    print(f"     Total Volume: {profile_info['total_volume']:,}")
                    
                    if profile_info['poc_price'] > 0:
                        print(f"     POC Price: ${profile_info['poc_price']:,.4f}")
                        print(f"     POC Volume: {profile_info['poc_volume']:,}")
                    
                    if profile_info['value_area_high'] > 0 and profile_info['value_area_low'] > 0:
                        va_range = profile_info['value_area_high'] - profile_info['value_area_low']
                        print(f"     Value Area: ${profile_info['value_area_low']:,.4f} - ${profile_info['value_area_high']:,.4f} (Range: ${va_range:.4f})")
                    
                    if profile_info['volume_at_price']:
                        print(f"     Price Levels: {len(profile_info['volume_at_price'])} levels with volume")
                        
                        # Show top 3 volume levels
                        sorted_levels = sorted(profile_info['volume_at_price'], key=lambda x: x['volume'], reverse=True)
                        for j, level in enumerate(sorted_levels[:3]):
                            print(f"       #{j+1}: ${level['price']:,.4f} = {level['volume']:,} volume")
                    
                    print()
                
                # Display comprehensive analysis
                analysis = analyzer.get_analysis()
                
                print(f"📈 VOLUME PROFILE COMPREHENSIVE ANALYSIS:")
                print(f"Total Profiles: {analysis['total_profiles']}")
                print(f"Symbols: {', '.join(analysis['symbols'])}")
                print(f"Exchanges: {', '.join(analysis['exchanges'])}")
                
                if analysis['time_range']:
                    tr = analysis['time_range']
                    print(f"Time Range: {tr['start']} to {tr['end']}")
                    print(f"Duration: {tr['duration_hours']:.2f} hours")
                
                if analysis['volume_summary']:
                    vs = analysis['volume_summary']
                    print(f"Total Volume: {vs['total_volume']:,}")
                    print(f"Average Volume/Profile: {vs['average_volume_per_bar']:,.0f}")
                    print(f"Price Levels: {vs['unique_price_levels']}")
                
                if analysis['price_analysis']:
                    pa = analysis['price_analysis']
                    print(f"Price Range: ${pa['price_range_low']:,.4f} - ${pa['price_range_high']:,.4f}")
                    print(f"Most Traded Price: ${pa['most_traded_price']:,.4f}")
                    print(f"High Volume Nodes: {pa['high_volume_nodes']}")
                    print(f"Low Volume Nodes: {pa['low_volume_nodes']}")
                
                if analysis['value_area_analysis']:
                    va = analysis['value_area_analysis']
                    print(f"Value Area Stability: {va['va_stability']}")
                    print(f"Average VA Range: ${va['average_va_range']:,.4f}")
                
                if analysis['poc_analysis']:
                    poc = analysis['poc_analysis']
                    print(f"POC Migration: {poc['poc_migration']['pattern']}")
                    print(f"Average POC Volume: {poc['average_poc_volume']:,.0f}")
                
                if analysis['volume_distribution_insights']:
                    vdi = analysis['volume_distribution_insights']
                    print(f"Volume Distribution: {vdi['distribution_type']}")
                    print(f"Top 20% Concentration: {vdi['volume_concentration_top_20pct']:.1f}%")
                
                # Display insights
                if analysis['market_structure_insights']:
                    print("\n💡 MARKET STRUCTURE INSIGHTS:")
                    for insight in analysis['market_structure_insights']:
                        print(f"• {insight}")
                
                # CSV output
                if output_format == "csv":
                    print(f"\n📊 CSV FORMAT:")
                    csv_headers = ["Bar_Time", "Symbol", "Exchange", "Total_Volume", "POC_Price", 
                                  "POC_Volume", "VA_High", "VA_Low", "Price_Levels"]
                    print(",".join(csv_headers))
                    
                    for profile in analyzer.volume_profiles[:20]:  # First 20 profiles
                        values = [
                            profile['bar_time'],
                            profile['symbol'],
                            profile['exchange'],
                            str(profile['total_volume']),
                            str(profile['poc_price']),
                            str(profile['poc_volume']),
                            str(profile['value_area_high']),
                            str(profile['value_area_low']),
                            str(len(profile['volume_at_price']))
                        ]
                        print(",".join(values))
                
                # Trading recommendations
                print(f"\n💡 VOLUME PROFILE TRADING INSIGHTS:")
                print("✅ Volume profile analysis provides key market structure information")
                
                if analyzer.high_volume_nodes:
                    print(f"• {len(analyzer.high_volume_nodes)} high-volume nodes identified as potential support/resistance")
                
                if analysis['poc_analysis']:
                    poc_pattern = analysis['poc_analysis']['poc_migration']['pattern']
                    if poc_pattern == 'Upward':
                        print("• POC migration suggests bullish institutional participation")
                    elif poc_pattern == 'Downward':
                        print("• POC migration suggests bearish institutional participation")
                    else:
                        print("• Stable POC suggests balanced market conditions")
                
                if analysis['value_area_analysis']:
                    stability = analysis['value_area_analysis']['va_stability']
                    if stability in ['Very Stable', 'Stable']:
                        print("• Stable value area indicates good mean reversion opportunities")
                    else:
                        print("• Volatile value area suggests trending market conditions")
                
                print("• Use high-volume nodes for support/resistance levels")
                print("• Monitor POC migration for directional bias")
                print("• Value area boundaries provide key trading ranges")
            
            else:
                print(f"\n📋 No volume profile data found")
                print("   This could indicate:")
                print("   • No trading activity during specified time period")
                print("   • Symbol/exchange combination not found")
                print("   • Date range too restrictive")
                print("   • Insufficient volume data available")
        
        else:
            print("❌ Volume profile request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify symbol and exchange are correct")
            print("   • Check if date range contains trading activity")
            print("   • Ensure connection to correct History Plant")
            print("   • Try with different time period")
            print("   • Verify volume profile data is available for this symbol")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing volume profile: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for volume profile testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Volume Profile Minute Bars endpoint (Templates 208/209)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_volume_profile.py --symbol ESH5 --exchange CME
  python test_volume_profile.py --symbol ESH5 --exchange CME --start-date "01/15/2024"
  python test_volume_profile.py --symbol ESH5 --exchange CME --max-bars 50
  python test_volume_profile.py --symbol ESH5 --exchange CME --format csv

Safety:
  This script performs READ-ONLY volume profile requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays volume profile data including:
  - Volume distribution at different price levels
  - Point of Control (POC) analysis and migration
  - Value Area calculations and stability
  - High/low volume node identification
  - Market structure insights and trading recommendations
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Symbol to request volume profile for"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        required=True,
        help="Exchange for the symbol"
    )
    
    parser.add_argument(
        "--start-date",
        type=str,
        help="Start date in MM/DD/YYYY format (optional)"
    )
    
    parser.add_argument(
        "--end-date",
        type=str,
        help="End date in MM/DD/YYYY format (optional)"
    )
    
    parser.add_argument(
        "--max-bars",
        type=int,
        default=100,
        help="Maximum number of profile bars to retrieve (default: 100)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC VOLUME PROFILE ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY VOLUME PROFILE OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_volume_profile(
        symbol=args.symbol,
        exchange=args.exchange,
        start_date=args.start_date,
        end_date=args.end_date,
        max_bars=args.max_bars,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Volume profile test completed successfully!")
        return 0
    else:
        print("\n❌ Volume profile test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)