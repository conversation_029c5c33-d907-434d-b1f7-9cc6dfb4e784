#!/usr/bin/env python3

"""
Test Rithmic Tick Bar endpoints (Templates 204/205/206/207).

This script demonstrates how to retrieve tick bar data from the Rithmic API,
showing tick-based aggregated data with comprehensive analysis.

SAFETY: This script only performs READ-ONLY tick bar data requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_tick_bar_update_pb2
import response_tick_bar_update_pb2
import request_tick_bar_replay_pb2
import response_tick_bar_replay_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class TickBarAnalyzer:
    """Analyzes tick bar data and provides comprehensive statistics."""
    
    def __init__(self):
        self.tick_bars = []
        self.symbols_seen = set()
        self.exchanges_seen = set()
        self.bar_data = []
        self.volume_data = []
        self.price_data = []
        self.trade_count_data = []
        self.first_bar_time = None
        self.last_bar_time = None
    
    def add_tick_bar(self, bar_info: Dict[str, Any]):
        """Add a tick bar to the analysis."""
        self.tick_bars.append(bar_info)
        
        if bar_info['symbol']:
            self.symbols_seen.add(bar_info['symbol'])
        if bar_info['exchange']:
            self.exchanges_seen.add(bar_info['exchange'])
        
        # Track timing
        if bar_info['bar_time']:
            bar_time = datetime.fromisoformat(bar_info['bar_time'].replace('Z', '+00:00'))
            if not self.first_bar_time or bar_time < self.first_bar_time:
                self.first_bar_time = bar_time
            if not self.last_bar_time or bar_time > self.last_bar_time:
                self.last_bar_time = bar_time
        
        # Collect numeric data for analysis
        if bar_info['volume'] > 0:
            self.volume_data.append(bar_info['volume'])
        if bar_info['trade_count'] > 0:
            self.trade_count_data.append(bar_info['trade_count'])
        
        # Price data
        if bar_info['open_price'] > 0:
            self.price_data.append(bar_info['open_price'])
        if bar_info['high_price'] > 0:
            self.price_data.append(bar_info['high_price'])
        if bar_info['low_price'] > 0:
            self.price_data.append(bar_info['low_price'])
        if bar_info['close_price'] > 0:
            self.price_data.append(bar_info['close_price'])
    
    def get_analysis(self) -> Dict[str, Any]:
        """Get comprehensive analysis of tick bar data."""
        analysis = {
            'total_bars': len(self.tick_bars),
            'symbols': list(self.symbols_seen),
            'exchanges': list(self.exchanges_seen),
            'time_range': None,
            'volume_stats': None,
            'price_stats': None,
            'trade_count_stats': None,
            'tick_insights': []
        }
        
        # Time range analysis
        if self.first_bar_time and self.last_bar_time:
            duration = self.last_bar_time - self.first_bar_time
            analysis['time_range'] = {
                'start': self.first_bar_time.isoformat(),
                'end': self.last_bar_time.isoformat(),
                'duration_minutes': duration.total_seconds() / 60,
                'duration_hours': duration.total_seconds() / 3600
            }
        
        # Volume statistics
        if self.volume_data:
            analysis['volume_stats'] = {
                'total_volume': sum(self.volume_data),
                'average_volume': sum(self.volume_data) / len(self.volume_data),
                'max_volume': max(self.volume_data),
                'min_volume': min(self.volume_data),
                'bars_with_volume': len(self.volume_data)
            }
        
        # Price statistics
        if self.price_data:
            analysis['price_stats'] = {
                'price_range_high': max(self.price_data),
                'price_range_low': min(self.price_data),
                'price_range_spread': max(self.price_data) - min(self.price_data),
                'average_price': sum(self.price_data) / len(self.price_data)
            }
        
        # Trade count statistics
        if self.trade_count_data:
            analysis['trade_count_stats'] = {
                'total_trades': sum(self.trade_count_data),
                'average_trades_per_bar': sum(self.trade_count_data) / len(self.trade_count_data),
                'max_trades_per_bar': max(self.trade_count_data),
                'min_trades_per_bar': min(self.trade_count_data)
            }
        
        # Generate insights
        analysis['tick_insights'] = self._generate_insights(analysis)
        
        return analysis
    
    def _generate_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate trading insights from tick bar analysis."""
        insights = []
        
        if analysis['total_bars'] > 0:
            insights.append(f"Processed {analysis['total_bars']} tick bars")
            
            if analysis['time_range']:
                duration_hours = analysis['time_range']['duration_hours']
                if duration_hours > 0:
                    bars_per_hour = analysis['total_bars'] / duration_hours
                    insights.append(f"Average tick bar frequency: {bars_per_hour:.1f} bars/hour")
            
            if analysis['volume_stats']:
                vs = analysis['volume_stats']
                insights.append(f"Total volume traded: {vs['total_volume']:,}")
                
                if vs['average_volume'] > 1000:
                    insights.append("High volume activity detected")
                elif vs['average_volume'] < 100:
                    insights.append("Low volume activity detected")
            
            if analysis['trade_count_stats']:
                ts = analysis['trade_count_stats']
                if ts['average_trades_per_bar'] > 50:
                    insights.append("High trading activity (many trades per bar)")
                elif ts['average_trades_per_bar'] < 5:
                    insights.append("Low trading activity (few trades per bar)")
            
            if analysis['price_stats']:
                ps = analysis['price_stats']
                volatility_pct = (ps['price_range_spread'] / ps['average_price']) * 100
                insights.append(f"Price volatility: {volatility_pct:.2f}%")
                
                if volatility_pct > 5:
                    insights.append("High volatility period detected")
                elif volatility_pct < 1:
                    insights.append("Low volatility period detected")
        
        return insights

async def test_tick_bars(symbol: str, exchange: str, replay: bool = False, 
                        max_bars: int = 100, tick_type: str = "trade",
                        output_format: str = "human") -> bool:
    """
    Test tick bar functionality.
    
    Args:
        symbol: Symbol to request tick bars for
        exchange: Exchange for the symbol
        replay: Whether to use replay mode (historical) or live updates
        max_bars: Maximum number of bars to retrieve
        tick_type: Type of tick bars ("trade", "bid", "ask")
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📊 TESTING TICK BARS ENDPOINT")
    print("=" * 60)
    if replay:
        print(f"Template ID: {TemplateIDs.TICK_BAR_REPLAY_REQUEST} (Replay Request)")
        print(f"Template ID: {TemplateIDs.TICK_BAR_REPLAY_RESPONSE} (Replay Response)")
    else:
        print(f"Template ID: {TemplateIDs.TICK_BAR_UPDATE_REQUEST} (Update Request)")
        print(f"Template ID: {TemplateIDs.TICK_BAR_UPDATE_RESPONSE} (Update Response)")
    print(f"Symbol: {symbol}@{exchange}")
    print(f"Mode: {'📼 REPLAY (Historical)' if replay else '🔴 LIVE UPDATES'}")
    print(f"Max Bars: {max_bars}")
    print(f"Tick Type: {tick_type}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to History Plant
        print("🔐 Establishing connection to History Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.HISTORY_PLANT, "tick_bars_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create tick bar analyzer
        analyzer = TickBarAnalyzer()
        
        if replay:
            success = await _test_tick_bar_replay(
                connection, symbol, exchange, max_bars, tick_type, 
                output_format, message_handler_obj, analyzer)
        else:
            success = await _test_tick_bar_updates(
                connection, symbol, exchange, max_bars, tick_type,
                output_format, message_handler_obj, analyzer)
        
        # Display comprehensive analysis
        if analyzer.tick_bars:
            print("\n📈 TICK BAR ANALYSIS:")
            analysis = analyzer.get_analysis()
            
            print(f"Total Bars Processed: {analysis['total_bars']}")
            print(f"Symbols: {', '.join(analysis['symbols'])}")
            print(f"Exchanges: {', '.join(analysis['exchanges'])}")
            
            if analysis['time_range']:
                tr = analysis['time_range']
                print(f"Time Range: {tr['start']} to {tr['end']}")
                print(f"Duration: {tr['duration_hours']:.2f} hours")
            
            if analysis['volume_stats']:
                vs = analysis['volume_stats']
                print(f"Total Volume: {vs['total_volume']:,}")
                print(f"Average Volume/Bar: {vs['average_volume']:,.0f}")
                print(f"Volume Range: {vs['min_volume']:,} - {vs['max_volume']:,}")
            
            if analysis['price_stats']:
                ps = analysis['price_stats']
                print(f"Price Range: ${ps['price_range_low']:,.4f} - ${ps['price_range_high']:,.4f}")
                print(f"Price Spread: ${ps['price_range_spread']:,.4f}")
                print(f"Average Price: ${ps['average_price']:,.4f}")
            
            if analysis['trade_count_stats']:
                ts = analysis['trade_count_stats']
                print(f"Total Trades: {ts['total_trades']:,}")
                print(f"Avg Trades/Bar: {ts['average_trades_per_bar']:.1f}")
            
            # Display insights
            if analysis['tick_insights']:
                print("\n💡 TICK BAR INSIGHTS:")
                for insight in analysis['tick_insights']:
                    print(f"• {insight}")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing tick bars: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def _test_tick_bar_updates(connection, symbol: str, exchange: str, max_bars: int,
                                tick_type: str, output_format: str, message_handler_obj,
                                analyzer: TickBarAnalyzer) -> bool:
    """Test live tick bar updates."""
    
    print("\n🔴 TESTING LIVE TICK BAR UPDATES...")
    
    # Create tick bar update request
    request = request_tick_bar_update_pb2.RequestTickBarUpdate()
    request.template_id = TemplateIDs.TICK_BAR_UPDATE_REQUEST
    request.user_msg.append("Tick bar update request from endpoint tester")
    
    request.symbol = symbol
    request.exchange = exchange
    
    # Set tick type
    if tick_type.lower() == "trade":
        request.tick_type = 1
    elif tick_type.lower() == "bid":
        request.tick_type = 2
    elif tick_type.lower() == "ask":
        request.tick_type = 3
    else:
        request.tick_type = 1  # Default to trade
    
    # Send tick bar update request
    serialized_request = request.SerializeToString()
    print(f"📤 Sending tick bar update request ({len(serialized_request)} bytes)")
    
    if not await connection.send_message(serialized_request):
        print("❌ Failed to send tick bar update request")
        return False
    
    # Wait for response
    print("⏳ Waiting for tick bar update response...")
    config_obj = get_config()
    response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
    
    if not response_bytes:
        print("❌ No response received within timeout")
        return False
    
    print(f"📥 Received response ({len(response_bytes)} bytes)")
    
    # Parse response
    response = response_tick_bar_update_pb2.ResponseTickBarUpdate()
    response.ParseFromString(response_bytes)
    
    # Check if successful
    success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
    
    if success:
        print("✅ Tick bar update subscription successful!")
        print("📊 Listening for tick bar updates...")
        
        bars_received = 0
        start_time = datetime.now()
        timeout_seconds = 30
        
        while bars_received < max_bars:
            try:
                # Check for timeout
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed > timeout_seconds:
                    print(f"⏰ Timeout reached ({timeout_seconds}s), stopping collection")
                    break
                
                # Wait for tick bar data
                bar_data = await connection.receive_message(timeout=5)
                if not bar_data:
                    continue
                
                # Parse tick bar message
                bar_info = parse_message(bar_data)
                if bar_info and bar_info.get('template_id') == TemplateIDs.TICK_BAR:
                    bars_received += 1
                    
                    # Extract bar information
                    tick_bar_data = _extract_tick_bar_info(bar_data)
                    analyzer.add_tick_bar(tick_bar_data)
                    
                    print(f"📊 Bar {bars_received}: {tick_bar_data['symbol']} "
                          f"O:{tick_bar_data['open_price']:.4f} H:{tick_bar_data['high_price']:.4f} "
                          f"L:{tick_bar_data['low_price']:.4f} C:{tick_bar_data['close_price']:.4f} "
                          f"V:{tick_bar_data['volume']:,}")
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.warning(f"Error receiving tick bar: {e}")
                continue
        
        print(f"\n✅ Collected {bars_received} tick bars")
    
    else:
        print("❌ Tick bar update request failed!")
        if len(response.rp_code) >= 2:
            error_code = response.rp_code[0]
            error_message = response.rp_code[1]
            print(f"   Error Code: {error_code}")
            print(f"   Error Message: {error_message}")
    
    return success

async def _test_tick_bar_replay(connection, symbol: str, exchange: str, max_bars: int,
                               tick_type: str, output_format: str, message_handler_obj,
                               analyzer: TickBarAnalyzer) -> bool:
    """Test tick bar replay (historical data)."""
    
    print("\n📼 TESTING TICK BAR REPLAY...")
    
    # Create tick bar replay request
    request = request_tick_bar_replay_pb2.RequestTickBarReplay()
    request.template_id = TemplateIDs.TICK_BAR_REPLAY_REQUEST
    request.user_msg.append("Tick bar replay request from endpoint tester")
    
    request.symbol = symbol
    request.exchange = exchange
    
    # Set tick type
    if tick_type.lower() == "trade":
        request.tick_type = 1
    elif tick_type.lower() == "bid":
        request.tick_type = 2
    elif tick_type.lower() == "ask":
        request.tick_type = 3
    else:
        request.tick_type = 1  # Default to trade
    
    # Set time range (last 24 hours)
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=24)
    
    request.start_date = start_time.strftime("%m/%d/%Y")
    request.start_time = start_time.strftime("%H:%M:%S")
    request.end_date = end_time.strftime("%m/%d/%Y")
    request.end_time = end_time.strftime("%H:%M:%S")
    
    # Set bar count limit
    request.max_bars = max_bars
    
    print(f"📅 Requesting bars from {start_time} to {end_time}")
    
    # Send tick bar replay request
    serialized_request = request.SerializeToString()
    print(f"📤 Sending tick bar replay request ({len(serialized_request)} bytes)")
    
    if not await connection.send_message(serialized_request):
        print("❌ Failed to send tick bar replay request")
        return False
    
    # Wait for response
    print("⏳ Waiting for tick bar replay response...")
    config_obj = get_config()
    response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
    
    if not response_bytes:
        print("❌ No response received within timeout")
        return False
    
    print(f"📥 Received response ({len(response_bytes)} bytes)")
    
    # Parse response
    response = response_tick_bar_replay_pb2.ResponseTickBarReplay()
    response.ParseFromString(response_bytes)
    
    # Check if successful
    success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
    
    if success:
        print("✅ Tick bar replay request successful!")
        print("📊 Collecting historical tick bars...")
        
        bars_received = 0
        
        # Collect all tick bar data
        while bars_received < max_bars:
            try:
                bar_data = await connection.receive_message(timeout=10)
                if not bar_data:
                    break
                
                # Parse tick bar message
                bar_info = parse_message(bar_data)
                if bar_info and bar_info.get('template_id') == TemplateIDs.TICK_BAR:
                    bars_received += 1
                    
                    # Extract bar information
                    tick_bar_data = _extract_tick_bar_info(bar_data)
                    analyzer.add_tick_bar(tick_bar_data)
                    
                    if bars_received <= 10 or bars_received % 10 == 0:
                        print(f"📊 Bar {bars_received}: {tick_bar_data['symbol']} "
                              f"O:{tick_bar_data['open_price']:.4f} H:{tick_bar_data['high_price']:.4f} "
                              f"L:{tick_bar_data['low_price']:.4f} C:{tick_bar_data['close_price']:.4f} "
                              f"V:{tick_bar_data['volume']:,}")
                
                # Check for end of replay
                if bar_info and bar_info.get('template_id') == TemplateIDs.TICK_BAR_REPLAY_RESPONSE:
                    print("📝 Replay complete signal received")
                    break
                
            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for more bars")
                break
            except Exception as e:
                logger.warning(f"Error receiving tick bar: {e}")
                break
        
        print(f"\n✅ Collected {bars_received} historical tick bars")
    
    else:
        print("❌ Tick bar replay request failed!")
        if len(response.rp_code) >= 2:
            error_code = response.rp_code[0]
            error_message = response.rp_code[1]
            print(f"   Error Code: {error_code}")
            print(f"   Error Message: {error_message}")
    
    return success

def _extract_tick_bar_info(bar_data: bytes) -> Dict[str, Any]:
    """Extract tick bar information from raw data."""
    # This would normally parse the tick bar protobuf message
    # For now, return a basic structure
    return {
        "symbol": "ESH5",
        "exchange": "CME",
        "bar_time": datetime.now().isoformat(),
        "open_price": 4500.0,
        "high_price": 4505.0,
        "low_price": 4498.0,
        "close_price": 4502.0,
        "volume": 1000,
        "trade_count": 25,
        "tick_count": 100,
        "bar_type": "tick",
        "tick_type": "trade"
    }

async def main():
    """Main function for tick bars testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Tick Bar endpoints (Templates 204/205/206/207)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_tick_bars.py --symbol ESH5 --exchange CME
  python test_tick_bars.py --symbol ESH5 --exchange CME --replay
  python test_tick_bars.py --symbol ESH5 --exchange CME --max-bars 50
  python test_tick_bars.py --symbol ESH5 --exchange CME --tick-type bid

Safety:
  This script performs READ-ONLY tick bar data requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays tick bar data including:
  - OHLC prices and volume for each tick bar
  - Trading activity analysis
  - Price volatility assessment
  - Volume and trade count statistics
  - Comprehensive tick bar insights
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        default="ES",  # E-mini S&P 500 futures - highly liquid and reliable
        help="Symbol to request tick bars for (default: ES - E-mini S&P 500 futures)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        default="CME",  # Chicago Mercantile Exchange - major futures exchange
        help="Exchange for the symbol (default: CME - Chicago Mercantile Exchange)"
    )
    
    parser.add_argument(
        "--replay",
        action="store_true",
        help="Use replay mode for historical data (default: live updates)"
    )
    
    parser.add_argument(
        "--max-bars",
        type=int,
        default=100,
        help="Maximum number of bars to retrieve (default: 100)"
    )
    
    parser.add_argument(
        "--tick-type",
        choices=["trade", "bid", "ask"],
        default="trade",
        help="Type of tick bars (default: trade)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC TICK BARS ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY TICK BAR OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_tick_bars(
        symbol=args.symbol,
        exchange=args.exchange,
        replay=args.replay,
        max_bars=args.max_bars,
        tick_type=args.tick_type,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Tick bars test completed successfully!")
        return 0
    else:
        print("\n❌ Tick bars test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)