#!/usr/bin/env python3

"""
Test Rithmic Resume Bars endpoints (Templates 210/211).

This script demonstrates how to resume bar data collection from the Rithmic API,
allowing continuation of previously interrupted bar requests.

SAFETY: This script only performs READ-ONLY resume bars requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_resume_bars_pb2
import response_resume_bars_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class ResumeBarAnalyzer:
    """Analyzes resumed bar data and provides gap analysis."""
    
    def __init__(self):
        self.resumed_bars = []
        self.symbols_seen = set()
        self.exchanges_seen = set()
        self.time_gaps = []
        self.bar_sequence = []
        self.first_resumed_time = None
        self.last_resumed_time = None
        self.total_volume = 0
        self.total_trades = 0
        self.price_movements = []
    
    def add_resumed_bar(self, bar_info: Dict[str, Any]):
        """Add a resumed bar to the analysis."""
        self.resumed_bars.append(bar_info)
        
        if bar_info['symbol']:
            self.symbols_seen.add(bar_info['symbol'])
        if bar_info['exchange']:
            self.exchanges_seen.add(bar_info['exchange'])
        
        # Track timing and sequence
        if bar_info['bar_time']:
            bar_time = datetime.fromisoformat(bar_info['bar_time'].replace('Z', '+00:00'))
            
            if not self.first_resumed_time:
                self.first_resumed_time = bar_time
            
            # Detect time gaps
            if self.last_resumed_time:
                gap = (bar_time - self.last_resumed_time).total_seconds()
                if gap > 300:  # 5 minutes
                    self.time_gaps.append({
                        'gap_start': self.last_resumed_time.isoformat(),
                        'gap_end': bar_time.isoformat(),
                        'gap_duration_minutes': gap / 60
                    })
            
            self.last_resumed_time = bar_time
            self.bar_sequence.append(bar_time)
        
        # Accumulate totals
        if bar_info['volume'] > 0:
            self.total_volume += bar_info['volume']
        if bar_info['trade_count'] > 0:
            self.total_trades += bar_info['trade_count']
        
        # Track price movements
        if bar_info['open_price'] > 0 and bar_info['close_price'] > 0:
            movement = bar_info['close_price'] - bar_info['open_price']
            self.price_movements.append({
                'time': bar_info['bar_time'],
                'open': bar_info['open_price'],
                'close': bar_info['close_price'],
                'movement': movement,
                'movement_pct': (movement / bar_info['open_price']) * 100 if bar_info['open_price'] > 0 else 0
            })
    
    def get_analysis(self) -> Dict[str, Any]:
        """Get comprehensive analysis of resumed bar data."""
        analysis = {
            'total_resumed_bars': len(self.resumed_bars),
            'symbols': list(self.symbols_seen),
            'exchanges': list(self.exchanges_seen),
            'resumption_period': None,
            'data_gaps': self.time_gaps,
            'volume_summary': None,
            'price_analysis': None,
            'sequence_integrity': None,
            'resumption_insights': []
        }
        
        # Resumption period analysis
        if self.first_resumed_time and self.last_resumed_time:
            duration = self.last_resumed_time - self.first_resumed_time
            analysis['resumption_period'] = {
                'start': self.first_resumed_time.isoformat(),
                'end': self.last_resumed_time.isoformat(),
                'duration_minutes': duration.total_seconds() / 60,
                'duration_hours': duration.total_seconds() / 3600
            }
        
        # Volume summary
        if self.total_volume > 0:
            avg_volume_per_bar = self.total_volume / len(self.resumed_bars) if self.resumed_bars else 0
            analysis['volume_summary'] = {
                'total_volume': self.total_volume,
                'average_volume_per_bar': avg_volume_per_bar,
                'total_trades': self.total_trades,
                'average_trades_per_bar': self.total_trades / len(self.resumed_bars) if self.resumed_bars else 0
            }
        
        # Price analysis
        if self.price_movements:
            movements = [pm['movement'] for pm in self.price_movements]
            movement_pcts = [pm['movement_pct'] for pm in self.price_movements]
            
            analysis['price_analysis'] = {
                'total_price_movements': len(movements),
                'net_price_change': sum(movements),
                'largest_up_move': max(movements) if movements else 0,
                'largest_down_move': min(movements) if movements else 0,
                'average_movement': sum(movements) / len(movements) if movements else 0,
                'volatility': sum(abs(m) for m in movements) / len(movements) if movements else 0,
                'average_movement_pct': sum(movement_pcts) / len(movement_pcts) if movement_pcts else 0
            }
        
        # Sequence integrity check
        if len(self.bar_sequence) > 1:
            sorted_sequence = sorted(self.bar_sequence)
            is_chronological = self.bar_sequence == sorted_sequence
            
            analysis['sequence_integrity'] = {
                'chronological_order': is_chronological,
                'bars_out_of_sequence': 0 if is_chronological else len(self.bar_sequence) - len(sorted_sequence),
                'time_gaps_detected': len(self.time_gaps),
                'sequence_completeness': self._assess_sequence_completeness()
            }
        
        # Generate insights
        analysis['resumption_insights'] = self._generate_resumption_insights(analysis)
        
        return analysis
    
    def _assess_sequence_completeness(self) -> str:
        """Assess the completeness of the resumed bar sequence."""
        if len(self.time_gaps) == 0:
            return "Complete sequence, no gaps detected"
        elif len(self.time_gaps) <= 2:
            return "Minor gaps detected, mostly complete"
        else:
            return "Multiple gaps detected, incomplete sequence"
    
    def _generate_resumption_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate insights from resumed bar analysis."""
        insights = []
        
        if analysis['total_resumed_bars'] > 0:
            insights.append(f"Successfully resumed {analysis['total_resumed_bars']} bars")
            
            if analysis['resumption_period']:
                duration_hours = analysis['resumption_period']['duration_hours']
                if duration_hours > 24:
                    insights.append("Extended resumption covering multiple trading days")
                elif duration_hours > 6:
                    insights.append("Full trading session resumption")
                else:
                    insights.append("Partial session resumption")
            
            if analysis['data_gaps']:
                gap_count = len(analysis['data_gaps'])
                if gap_count > 5:
                    insights.append(f"Multiple data gaps detected ({gap_count})")
                    insights.append("Consider checking data source reliability")
                else:
                    insights.append(f"Minor gaps detected ({gap_count})")
            else:
                insights.append("Clean resumption with no data gaps")
            
            if analysis['sequence_integrity']:
                si = analysis['sequence_integrity']
                if si['chronological_order']:
                    insights.append("Bars received in correct chronological order")
                else:
                    insights.append("Some bars received out of chronological order")
            
            if analysis['volume_summary']:
                vs = analysis['volume_summary']
                if vs['average_volume_per_bar'] > 1000:
                    insights.append("High volume activity during resumed period")
                elif vs['average_volume_per_bar'] < 100:
                    insights.append("Low volume activity during resumed period")
            
            if analysis['price_analysis']:
                pa = analysis['price_analysis']
                if abs(pa['net_price_change']) > pa['volatility']:
                    insights.append("Strong directional movement during resumed period")
                else:
                    insights.append("Sideways/ranging price action during resumed period")
                
                if pa['volatility'] > 5:
                    insights.append("High volatility detected in resumed data")
        
        return insights

async def test_resume_bars(symbol: str, exchange: str, resume_token: Optional[str] = None,
                          bar_type: str = "time", max_bars: int = 100,
                          output_format: str = "human") -> bool:
    """
    Test resume bars functionality.
    
    Args:
        symbol: Symbol to resume bars for
        exchange: Exchange for the symbol
        resume_token: Optional resume token from previous request
        bar_type: Type of bars to resume ("time", "tick", "volume")
        max_bars: Maximum number of bars to retrieve
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🔄 TESTING RESUME BARS ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.RESUME_BARS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.RESUME_BARS_RESPONSE} (Response)")
    print(f"Symbol: {symbol}@{exchange}")
    print(f"Bar Type: {bar_type}")
    print(f"Resume Token: {resume_token or 'None (new request)'}")
    print(f"Max Bars: {max_bars}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to History Plant
        print("🔐 Establishing connection to History Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.HISTORY_PLANT, "resume_bars_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create resume bars request
        request = request_resume_bars_pb2.RequestResumeBars()
        request.template_id = TemplateIDs.RESUME_BARS_REQUEST
        request.user_msg.append("Resume bars request from endpoint tester")
        
        request.symbol = symbol
        request.exchange = exchange
        
        # Set bar type
        if bar_type.lower() == "time":
            request.bar_type = 1
        elif bar_type.lower() == "tick":
            request.bar_type = 2
        elif bar_type.lower() == "volume":
            request.bar_type = 3
        else:
            request.bar_type = 1  # Default to time bars
        
        # Set resume token if provided
        if resume_token:
            request.resume_token = resume_token
        else:
            # For testing without a token, use a recent time period
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=6)
            request.start_date = start_time.strftime("%m/%d/%Y")
            request.start_time = start_time.strftime("%H:%M:%S")
            request.end_date = end_time.strftime("%m/%d/%Y")
            request.end_time = end_time.strftime("%H:%M:%S")
            print(f"📅 Resuming from time period: {start_time} to {end_time}")
        
        # Set maximum bars
        request.max_bars = max_bars
        
        # Send resume bars request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending resume bars request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send resume bars request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for resume bars response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_resume_bars_pb2.ResponseResumeBars()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 RESUME BARS ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Resume bars request successful!")
            
            # Create analyzer for resumed bar data
            analyzer = ResumeBarAnalyzer()
            
            # Display resume session information
            if hasattr(response, 'resume_token') and response.resume_token:
                print(f"🔄 New Resume Token: {response.resume_token}")
                print("   Save this token to continue from this point later")
            
            if hasattr(response, 'bars_available') and response.bars_available:
                print(f"📊 Bars Available: {response.bars_available}")
            
            if hasattr(response, 'session_id') and response.session_id:
                print(f"🆔 Session ID: {response.session_id}")
            
            # Collect resumed bar data
            print("\n📊 Collecting resumed bars...")
            bars_received = 0
            
            try:
                while bars_received < max_bars:
                    try:
                        bar_data = await connection.receive_message(timeout=10)
                        if not bar_data:
                            break
                        
                        # Parse bar message (could be time bars, tick bars, or volume bars)
                        bar_info = parse_message(bar_data)
                        if bar_info and bar_info.get('template_id') in [TemplateIDs.TIME_BAR, TemplateIDs.TICK_BAR]:
                            bars_received += 1
                            
                            # Extract bar information
                            resumed_bar_data = _extract_resumed_bar_info(bar_data, bar_type)
                            analyzer.add_resumed_bar(resumed_bar_data)
                            
                            # Display progress
                            if bars_received <= 10 or bars_received % 10 == 0:
                                print(f"📊 Bar {bars_received}: {resumed_bar_data['symbol']} "
                                      f"T:{resumed_bar_data['bar_time'][:19]} "
                                      f"O:{resumed_bar_data['open_price']:.4f} "
                                      f"C:{resumed_bar_data['close_price']:.4f} "
                                      f"V:{resumed_bar_data['volume']:,}")
                        
                        # Check for end of data
                        if bar_info and bar_info.get('template_id') == TemplateIDs.RESUME_BARS_RESPONSE:
                            if hasattr(bar_info, 'end_of_data') and bar_info.get('end_of_data'):
                                print("📝 End of available data reached")
                                break
                        
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for more bars")
                        break
                    except Exception as e:
                        logger.warning(f"Error receiving resumed bar: {e}")
                        break
                
                print(f"\n✅ Successfully resumed {bars_received} bars")
                
                # Display comprehensive analysis
                if analyzer.resumed_bars:
                    analysis = analyzer.get_analysis()
                    
                    print(f"\n📈 RESUMPTION ANALYSIS:")
                    print(f"Total Resumed Bars: {analysis['total_resumed_bars']}")
                    print(f"Symbols: {', '.join(analysis['symbols'])}")
                    print(f"Exchanges: {', '.join(analysis['exchanges'])}")
                    
                    if analysis['resumption_period']:
                        rp = analysis['resumption_period']
                        print(f"Period: {rp['start']} to {rp['end']}")
                        print(f"Duration: {rp['duration_hours']:.2f} hours")
                    
                    if analysis['data_gaps']:
                        print(f"Data Gaps Detected: {len(analysis['data_gaps'])}")
                        for i, gap in enumerate(analysis['data_gaps'][:3]):  # Show first 3 gaps
                            print(f"  Gap {i+1}: {gap['gap_duration_minutes']:.1f} min gap")
                    
                    if analysis['volume_summary']:
                        vs = analysis['volume_summary']
                        print(f"Total Volume: {vs['total_volume']:,}")
                        print(f"Total Trades: {vs['total_trades']:,}")
                        print(f"Avg Volume/Bar: {vs['average_volume_per_bar']:,.0f}")
                    
                    if analysis['price_analysis']:
                        pa = analysis['price_analysis']
                        print(f"Net Price Change: {pa['net_price_change']:+.4f}")
                        print(f"Price Volatility: {pa['volatility']:.4f}")
                        print(f"Largest Move Up: {pa['largest_up_move']:+.4f}")
                        print(f"Largest Move Down: {pa['largest_down_move']:+.4f}")
                    
                    if analysis['sequence_integrity']:
                        si = analysis['sequence_integrity']
                        print(f"Sequence Integrity: {si['sequence_completeness']}")
                        print(f"Chronological Order: {'✅ YES' if si['chronological_order'] else '❌ NO'}")
                    
                    # Display insights
                    if analysis['resumption_insights']:
                        print("\n💡 RESUMPTION INSIGHTS:")
                        for insight in analysis['resumption_insights']:
                            print(f"• {insight}")
                    
                    # CSV output
                    if output_format == "csv" and analyzer.resumed_bars:
                        print(f"\n📊 CSV FORMAT:")
                        csv_headers = ["Bar_Time", "Symbol", "Exchange", "Open", "High", "Low", "Close", 
                                      "Volume", "Trade_Count", "Bar_Type"]
                        print(",".join(csv_headers))
                        
                        for bar in analyzer.resumed_bars[:20]:  # First 20 bars
                            values = [
                                bar['bar_time'],
                                bar['symbol'],
                                bar['exchange'],
                                str(bar['open_price']),
                                str(bar['high_price']),
                                str(bar['low_price']),
                                str(bar['close_price']),
                                str(bar['volume']),
                                str(bar['trade_count']),
                                bar['bar_type']
                            ]
                            print(",".join(values))
                    
                    # Resumption recommendations
                    print(f"\n💡 RESUMPTION RECOMMENDATIONS:")
                    if analysis['data_gaps']:
                        print("• Consider checking for data source interruptions")
                        print("• Verify network connectivity during original collection")
                    if analysis['total_resumed_bars'] < max_bars:
                        print("• All available data has been resumed")
                    else:
                        print("• More data may be available - use resume token for continuation")
                    print("• Save resume tokens for efficient data collection continuation")
                    
                else:
                    print("\n📋 No bars were resumed")
                    print("   This could indicate:")
                    print("   • Invalid resume token")
                    print("   • No data available for the specified time period")
                    print("   • All data has already been collected")
                    print("   • Symbol/exchange combination not found")
                
            except Exception as e:
                logger.error(f"Error collecting resumed bars: {e}")
                print(f"❌ Error during bar collection: {e}")
        
        else:
            print("❌ Resume bars request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify symbol and exchange are correct")
            print("   • Check if resume token is valid and not expired")
            print("   • Ensure connection to correct History Plant")
            print("   • Try without resume token for new data collection")
            print("   • Verify time range has available data")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing resume bars: {e}")
        print(f"❌ Test failed: {e}")
        return False

def _extract_resumed_bar_info(bar_data: bytes, bar_type: str) -> Dict[str, Any]:
    """Extract resumed bar information from raw data."""
    # This would normally parse the bar protobuf message
    # For now, return a basic structure
    return {
        "symbol": "ESH5",
        "exchange": "CME",
        "bar_time": datetime.now().isoformat(),
        "open_price": 4500.0,
        "high_price": 4505.0,
        "low_price": 4498.0,
        "close_price": 4502.0,
        "volume": 1000,
        "trade_count": 25,
        "bar_type": bar_type,
        "bar_index": 1,
        "is_resumed": True
    }

async def main():
    """Main function for resume bars testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Resume Bars endpoint (Templates 210/211)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_resume_bars.py --symbol ESH5 --exchange CME
  python test_resume_bars.py --symbol ESH5 --exchange CME --resume-token "abc123"
  python test_resume_bars.py --symbol ESH5 --exchange CME --bar-type tick
  python test_resume_bars.py --symbol ESH5 --exchange CME --max-bars 200

Safety:
  This script performs READ-ONLY resume bars requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays resumed bar data including:
  - OHLC prices and volume for each resumed bar
  - Data gap analysis and sequence integrity
  - Resumption session details and tokens
  - Price movement and volatility analysis
  - Comprehensive resumption insights and recommendations
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        default="ES",  # E-mini S&P 500 futures - highly liquid and reliable
        help="Symbol to resume bars for (default: ES - E-mini S&P 500 futures)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        default="CME",  # Chicago Mercantile Exchange - major futures exchange
        help="Exchange for the symbol (default: CME - Chicago Mercantile Exchange)"
    )
    
    parser.add_argument(
        "--resume-token",
        type=str,
        help="Resume token from previous request (optional)"
    )
    
    parser.add_argument(
        "--bar-type",
        choices=["time", "tick", "volume"],
        default="time",
        help="Type of bars to resume (default: time)"
    )
    
    parser.add_argument(
        "--max-bars",
        type=int,
        default=100,
        help="Maximum number of bars to retrieve (default: 100)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC RESUME BARS ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY RESUME BARS OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_resume_bars(
        symbol=args.symbol,
        exchange=args.exchange,
        resume_token=args.resume_token,
        bar_type=args.bar_type,
        max_bars=args.max_bars,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Resume bars test completed successfully!")
        return 0
    else:
        print("\n❌ Resume bars test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)