#!/usr/bin/env python3

"""
Test Rithmic Volume-based Bar endpoints using time bar infrastructure.

This script demonstrates how to retrieve volume-based bar data from the Rithmic API,
showing bars aggregated by volume rather than time with comprehensive analysis.

SAFETY: This script only performs READ-ONLY volume bar requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_time_bar_replay_pb2
import response_time_bar_replay_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message
from timeout_manager import TimeoutManager

logger = logging.getLogger(__name__)

class VolumeBarAnalyzer:
    """Analyzes volume-based bar data and provides comprehensive trading insights."""
    
    def __init__(self):
        self.volume_bars = []
        self.symbols_seen = set()
        self.exchanges_seen = set()
        self.volume_targets = []
        self.execution_times = []
        self.price_movements = []
        self.volume_efficiency = []
        self.total_volume = 0
        self.total_trades = 0
        self.bar_durations = []
        self.first_bar_time = None
        self.last_bar_time = None
        self.volume_acceleration = []
        self.price_velocity = []
    
    def add_volume_bar(self, bar_info: Dict[str, Any]):
        """Add a volume bar to the analysis."""
        self.volume_bars.append(bar_info)
        
        if bar_info['symbol']:
            self.symbols_seen.add(bar_info['symbol'])
        if bar_info['exchange']:
            self.exchanges_seen.add(bar_info['exchange'])
        
        # Track timing
        if bar_info['bar_time']:
            bar_time = datetime.fromisoformat(bar_info['bar_time'].replace('Z', '+00:00'))
            
            if self.last_bar_time:
                duration = (bar_time - self.last_bar_time).total_seconds()
                self.bar_durations.append(duration)
                
                # Calculate volume acceleration (volume per time)
                if duration > 0 and bar_info['volume'] > 0:
                    vol_acceleration = bar_info['volume'] / duration
                    self.volume_acceleration.append(vol_acceleration)
            
            if not self.first_bar_time:
                self.first_bar_time = bar_time
            self.last_bar_time = bar_time
        
        # Track volume and trading data
        if bar_info['volume'] > 0:
            self.total_volume += bar_info['volume']
            self.volume_targets.append(bar_info['volume'])
        
        if bar_info['trade_count'] > 0:
            self.total_trades += bar_info['trade_count']
        
        # Track price movements and efficiency
        if bar_info['open_price'] > 0 and bar_info['close_price'] > 0:
            price_change = bar_info['close_price'] - bar_info['open_price']
            self.price_movements.append({
                'time': bar_info['bar_time'],
                'open': bar_info['open_price'],
                'close': bar_info['close_price'],
                'high': bar_info['high_price'],
                'low': bar_info['low_price'],
                'change': price_change,
                'change_pct': (price_change / bar_info['open_price']) * 100 if bar_info['open_price'] > 0 else 0,
                'range': bar_info['high_price'] - bar_info['low_price'] if bar_info['high_price'] > 0 and bar_info['low_price'] > 0 else 0
            })
            
            # Calculate volume efficiency (price move per unit volume)
            if bar_info['volume'] > 0:
                efficiency = abs(price_change) / bar_info['volume']
                self.volume_efficiency.append({
                    'time': bar_info['bar_time'],
                    'efficiency': efficiency,
                    'volume': bar_info['volume'],
                    'price_change': price_change
                })
            
            # Calculate price velocity (price change per time)
            if len(self.bar_durations) > 0 and self.bar_durations[-1] > 0:
                velocity = abs(price_change) / self.bar_durations[-1]
                self.price_velocity.append(velocity)
    
    def get_analysis(self) -> Dict[str, Any]:
        """Get comprehensive analysis of volume bar data."""
        analysis = {
            'total_volume_bars': len(self.volume_bars),
            'symbols': list(self.symbols_seen),
            'exchanges': list(self.exchanges_seen),
            'time_analysis': None,
            'volume_analysis': None,
            'price_analysis': None,
            'efficiency_analysis': None,
            'execution_analysis': None,
            'volume_bar_insights': []
        }
        
        # Time analysis
        if self.first_bar_time and self.last_bar_time and self.bar_durations:
            total_duration = (self.last_bar_time - self.first_bar_time).total_seconds()
            avg_bar_duration = sum(self.bar_durations) / len(self.bar_durations)
            
            analysis['time_analysis'] = {
                'start_time': self.first_bar_time.isoformat(),
                'end_time': self.last_bar_time.isoformat(),
                'total_duration_minutes': total_duration / 60,
                'average_bar_duration': avg_bar_duration,
                'shortest_bar_duration': min(self.bar_durations),
                'longest_bar_duration': max(self.bar_durations),
                'duration_volatility': self._calculate_duration_volatility()
            }
        
        # Volume analysis
        if self.volume_targets:
            analysis['volume_analysis'] = {
                'total_volume': self.total_volume,
                'average_volume_per_bar': sum(self.volume_targets) / len(self.volume_targets),
                'volume_consistency': self._calculate_volume_consistency(),
                'volume_target_achievement': self._analyze_volume_targets(),
                'total_trades': self.total_trades,
                'average_trades_per_bar': self.total_trades / len(self.volume_bars) if self.volume_bars else 0
            }
        
        # Price analysis
        if self.price_movements:
            price_changes = [pm['change'] for pm in self.price_movements]
            price_ranges = [pm['range'] for pm in self.price_movements if pm['range'] > 0]
            
            analysis['price_analysis'] = {
                'total_price_bars': len(self.price_movements),
                'net_price_change': sum(price_changes),
                'average_price_change': sum(price_changes) / len(price_changes),
                'largest_up_move': max(price_changes),
                'largest_down_move': min(price_changes),
                'average_bar_range': sum(price_ranges) / len(price_ranges) if price_ranges else 0,
                'price_volatility': sum(abs(pc) for pc in price_changes) / len(price_changes),
                'directional_consistency': self._calculate_directional_consistency()
            }
        
        # Efficiency analysis
        if self.volume_efficiency:
            efficiencies = [ve['efficiency'] for ve in self.volume_efficiency]
            
            analysis['efficiency_analysis'] = {
                'average_volume_efficiency': sum(efficiencies) / len(efficiencies),
                'highest_efficiency': max(efficiencies),
                'lowest_efficiency': min(efficiencies),
                'efficiency_trend': self._analyze_efficiency_trend(),
                'volume_impact_assessment': self._assess_volume_impact()
            }
        
        # Execution analysis
        if self.volume_acceleration and self.price_velocity:
            analysis['execution_analysis'] = {
                'average_volume_acceleration': sum(self.volume_acceleration) / len(self.volume_acceleration),
                'average_price_velocity': sum(self.price_velocity) / len(self.price_velocity),
                'execution_speed_analysis': self._analyze_execution_speed(),
                'market_participation_intensity': self._calculate_market_intensity()
            }
        
        # Generate insights
        analysis['volume_bar_insights'] = self._generate_volume_bar_insights(analysis)
        
        return analysis
    
    def _calculate_duration_volatility(self) -> str:
        """Calculate the volatility of bar durations."""
        if len(self.bar_durations) < 2:
            return "Insufficient data"
        
        avg_duration = sum(self.bar_durations) / len(self.bar_durations)
        variance = sum((d - avg_duration) ** 2 for d in self.bar_durations) / len(self.bar_durations)
        std_dev = variance ** 0.5
        cv = std_dev / avg_duration if avg_duration > 0 else 0
        
        if cv < 0.2:
            return "Very Consistent"
        elif cv < 0.5:
            return "Consistent"
        elif cv < 1.0:
            return "Moderate"
        else:
            return "Highly Variable"
    
    def _calculate_volume_consistency(self) -> str:
        """Calculate how consistent volume targets are being met."""
        if not self.volume_targets:
            return "No data"
        
        # Check variance in volume targets
        avg_volume = sum(self.volume_targets) / len(self.volume_targets)
        variance = sum((v - avg_volume) ** 2 for v in self.volume_targets) / len(self.volume_targets)
        std_dev = variance ** 0.5
        cv = std_dev / avg_volume if avg_volume > 0 else 0
        
        if cv < 0.1:
            return "Highly Consistent"
        elif cv < 0.25:
            return "Consistent"
        elif cv < 0.5:
            return "Moderately Consistent"
        else:
            return "Inconsistent"
    
    def _analyze_volume_targets(self) -> Dict[str, Any]:
        """Analyze how well volume targets are being achieved."""
        if not self.volume_targets:
            return {}
        
        # Assuming a target volume (this would normally be specified)
        target_volume = sum(self.volume_targets) / len(self.volume_targets)
        
        within_target = len([v for v in self.volume_targets if abs(v - target_volume) / target_volume < 0.1])
        over_target = len([v for v in self.volume_targets if v > target_volume * 1.1])
        under_target = len([v for v in self.volume_targets if v < target_volume * 0.9])
        
        return {
            'target_volume': target_volume,
            'within_target_pct': (within_target / len(self.volume_targets)) * 100,
            'over_target_pct': (over_target / len(self.volume_targets)) * 100,
            'under_target_pct': (under_target / len(self.volume_targets)) * 100
        }
    
    def _calculate_directional_consistency(self) -> str:
        """Calculate directional consistency of price movements."""
        if not self.price_movements:
            return "No data"
        
        up_moves = len([pm for pm in self.price_movements if pm['change'] > 0])
        down_moves = len([pm for pm in self.price_movements if pm['change'] < 0])
        flat_moves = len([pm for pm in self.price_movements if pm['change'] == 0])
        
        total_moves = len(self.price_movements)
        dominant_direction = max(up_moves, down_moves, flat_moves)
        consistency_pct = (dominant_direction / total_moves) * 100
        
        if consistency_pct > 80:
            return "Highly Directional"
        elif consistency_pct > 60:
            return "Moderately Directional"
        else:
            return "Choppy/Balanced"
    
    def _analyze_efficiency_trend(self) -> str:
        """Analyze the trend in volume efficiency over time."""
        if len(self.volume_efficiency) < 3:
            return "Insufficient data"
        
        # Simple trend analysis - compare first third to last third
        third = len(self.volume_efficiency) // 3
        early_efficiency = sum(ve['efficiency'] for ve in self.volume_efficiency[:third]) / third
        late_efficiency = sum(ve['efficiency'] for ve in self.volume_efficiency[-third:]) / third
        
        change_pct = ((late_efficiency - early_efficiency) / early_efficiency) * 100
        
        if change_pct > 10:
            return "Improving Efficiency"
        elif change_pct < -10:
            return "Declining Efficiency"
        else:
            return "Stable Efficiency"
    
    def _assess_volume_impact(self) -> str:
        """Assess the overall impact of volume on price movement."""
        if not self.volume_efficiency:
            return "No data"
        
        avg_efficiency = sum(ve['efficiency'] for ve in self.volume_efficiency) / len(self.volume_efficiency)
        
        # High efficiency means price moves significantly per unit volume
        if avg_efficiency > 0.001:  # Threshold depends on price scale
            return "High Volume Impact"
        elif avg_efficiency > 0.0001:
            return "Moderate Volume Impact"
        else:
            return "Low Volume Impact"
    
    def _analyze_execution_speed(self) -> str:
        """Analyze the speed of order execution based on bar completion times."""
        if not self.bar_durations:
            return "No data"
        
        avg_duration = sum(self.bar_durations) / len(self.bar_durations)
        
        if avg_duration < 60:  # Less than 1 minute average
            return "Very Fast Execution"
        elif avg_duration < 300:  # Less than 5 minutes
            return "Fast Execution"
        elif avg_duration < 900:  # Less than 15 minutes
            return "Moderate Execution"
        else:
            return "Slow Execution"
    
    def _calculate_market_intensity(self) -> str:
        """Calculate market participation intensity."""
        if not self.volume_acceleration:
            return "No data"
        
        avg_acceleration = sum(self.volume_acceleration) / len(self.volume_acceleration)
        
        # High acceleration means volume is being consumed quickly
        if avg_acceleration > 1000:  # Volume per second threshold
            return "High Intensity"
        elif avg_acceleration > 100:
            return "Moderate Intensity"
        else:
            return "Low Intensity"
    
    def _generate_volume_bar_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate trading insights from volume bar analysis."""
        insights = []
        
        if analysis['total_volume_bars'] > 0:
            insights.append(f"Analyzed {analysis['total_volume_bars']} volume-based bars")
            
            if analysis['time_analysis']:
                ta = analysis['time_analysis']
                duration_vol = ta['duration_volatility']
                if duration_vol == 'Very Consistent':
                    insights.append("Consistent execution times suggest steady market participation")
                elif duration_vol == 'Highly Variable':
                    insights.append("Variable execution times indicate changing market conditions")
                
                avg_duration = ta['average_bar_duration']
                if avg_duration < 300:  # 5 minutes
                    insights.append("Fast volume consumption indicates strong market interest")
                elif avg_duration > 1800:  # 30 minutes
                    insights.append("Slow volume consumption suggests low market interest")
            
            if analysis['volume_analysis']:
                va = analysis['volume_analysis']
                consistency = va['volume_consistency']
                if consistency == 'Highly Consistent':
                    insights.append("Volume targets being met consistently")
                elif consistency == 'Inconsistent':
                    insights.append("Volume targets showing high variance")
            
            if analysis['price_analysis']:
                pa = analysis['price_analysis']
                direction = pa['directional_consistency']
                if direction == 'Highly Directional':
                    insights.append("Strong directional price movement with volume")
                elif direction == 'Choppy/Balanced':
                    insights.append("Balanced buying and selling pressure")
                
                if pa['net_price_change'] > 0 and pa['directional_consistency'] != 'Choppy/Balanced':
                    insights.append("Net upward price movement supported by volume")
                elif pa['net_price_change'] < 0 and pa['directional_consistency'] != 'Choppy/Balanced':
                    insights.append("Net downward price movement supported by volume")
            
            if analysis['efficiency_analysis']:
                ea = analysis['efficiency_analysis']
                impact = ea['volume_impact_assessment']
                if impact == 'High Volume Impact':
                    insights.append("Volume driving significant price movement (high efficiency)")
                elif impact == 'Low Volume Impact':
                    insights.append("Large volume with minimal price impact (absorption)")
                
                trend = ea['efficiency_trend']
                if trend == 'Improving Efficiency':
                    insights.append("Volume efficiency improving - increasing price sensitivity")
                elif trend == 'Declining Efficiency':
                    insights.append("Volume efficiency declining - market becoming less sensitive")
            
            if analysis['execution_analysis']:
                ea = analysis['execution_analysis']
                speed = ea['execution_speed_analysis']
                intensity = ea['market_participation_intensity']
                
                if speed == 'Very Fast Execution' and intensity == 'High Intensity':
                    insights.append("Aggressive market participation with fast execution")
                elif speed == 'Slow Execution' and intensity == 'Low Intensity':
                    insights.append("Passive market participation with slow accumulation")
        
        return insights

async def test_volume_bars(symbol: str, exchange: str, volume_size: int = 1000,
                          start_date: Optional[str] = None, end_date: Optional[str] = None,
                          max_bars: int = 100, output_format: str = "human") -> bool:
    """
    Test volume-based bars functionality.
    
    Args:
        symbol: Symbol to request volume bars for
        exchange: Exchange for the symbol
        volume_size: Volume size for each bar (contracts/shares)
        start_date: Start date in MM/DD/YYYY format (optional)
        end_date: End date in MM/DD/YYYY format (optional)
        max_bars: Maximum number of bars to retrieve
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📊 TESTING VOLUME-BASED BARS ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.TICK_BAR_REPLAY_REQUEST} (Request - Volume Mode)")
    print(f"Template ID: {TemplateIDs.TICK_BAR_REPLAY_RESPONSE} (Response)")
    print(f"Symbol: {symbol}@{exchange}")
    print(f"Volume Size: {volume_size:,} per bar")
    print(f"Start Date: {start_date or 'Default (last 24 hours)'}")
    print(f"End Date: {end_date or 'Default (now)'}")
    print(f"Max Bars: {max_bars}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to History Plant
        print("🔐 Establishing connection to History Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.HISTORY_PLANT, "volume_bars_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create volume bars request (using time bar infrastructure with volume specification)
        request = request_tick_bar_replay_pb2.RequestTickBarReplay()
        request.template_id = TemplateIDs.TICK_BAR_REPLAY_REQUEST
        request.user_msg.append("Volume bars request from endpoint tester")
        
        request.symbol = symbol
        request.exchange = exchange
        
        # Set volume-based bar specification using correct protobuf enums
        request.bar_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarType.VOLUME_BAR
        request.bar_sub_type = request_tick_bar_replay_pb2.RequestTickBarReplay.BarSubType.REGULAR
        request.bar_type_specifier = str(volume_size)  # Volume size specification
        
        # Set date range
        if start_date and end_date:
            request.start_date = start_date
            request.end_date = end_date
        else:
            # Default to last 24 hours
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            request.start_date = start_time.strftime("%m/%d/%Y")
            request.end_date = end_time.strftime("%m/%d/%Y")
            request.start_time = start_time.strftime("%H:%M:%S")
            request.end_time = end_time.strftime("%H:%M:%S")
        
        # Remove invalid field - max_bars doesn't exist in tick bar protobuf
        # Using user_max_count instead (set above)
        
        # Add required parameters for proper tick bar request
        request.direction = request_tick_bar_replay_pb2.RequestTickBarReplay.Direction.LAST  # Get most recent bars
        request.time_order = request_tick_bar_replay_pb2.RequestTickBarReplay.TimeOrder.BACKWARDS  # Most recent first
        
        # Set user_max_count for limiting results
        request.user_max_count = max_bars
        
        print(f"📅 Requesting volume bars for: {request.start_date} to {request.end_date}")
        print(f"📊 Volume increment: {volume_size:,} per bar")
        
        # Send volume bars request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending volume bars request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send volume bars request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response with advanced timeout management
        print("⏳ Waiting for volume bars response...")
        
        timeout_manager = TimeoutManager("volume_bars", InfraType.HISTORY_PLANT)
        
        async def receive_response():
            return await connection.receive_message(timeout=30)  # Base timeout, will be managed by timeout_manager
        
        timeout_result = await timeout_manager.execute_with_progressive_timeout(
            receive_response, "volume bars response"
        )
        
        if not timeout_result.success:
            print(f"❌ No response received: {timeout_result.error_message}")
            return False
            
        response_bytes = timeout_result.result
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_tick_bar_replay_pb2.ResponseTickBarReplay()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 VOLUME BARS ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Volume bars request successful!")
            
            # Create analyzer for volume bar data
            analyzer = VolumeBarAnalyzer()
            
            print("\n📊 Collecting volume bars...")
            bars_received = 0
            
            try:
                while bars_received < max_bars:
                    try:
                        bar_data = await connection.receive_message(timeout=10)
                        if not bar_data:
                            break
                        
                        # Parse bar message
                        bar_info = parse_message(bar_data)
                        if bar_info and bar_info.get('template_id') == TemplateIDs.TIME_BAR:
                            bars_received += 1
                            
                            # Extract volume bar information
                            volume_bar_data = _extract_volume_bar_info(bar_data, volume_size)
                            analyzer.add_volume_bar(volume_bar_data)
                            
                            # Display progress
                            if bars_received <= 10 or bars_received % 10 == 0:
                                print(f"📊 Volume Bar {bars_received}: {volume_bar_data['symbol']} "
                                      f"T:{volume_bar_data['bar_time'][:19]} "
                                      f"O:{volume_bar_data['open_price']:.4f} "
                                      f"H:{volume_bar_data['high_price']:.4f} "
                                      f"L:{volume_bar_data['low_price']:.4f} "
                                      f"C:{volume_bar_data['close_price']:.4f} "
                                      f"V:{volume_bar_data['volume']:,}")
                        
                        # Check for end of data
                        if bar_info and bar_info.get('template_id') == TemplateIDs.TIME_BAR_REPLAY_RESPONSE:
                            break
                        
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for more bars")
                        break
                    except Exception as e:
                        logger.warning(f"Error receiving volume bar: {e}")
                        break
                
                print(f"\n✅ Successfully collected {bars_received} volume bars")
                
                # Display comprehensive analysis
                if analyzer.volume_bars:
                    analysis = analyzer.get_analysis()
                    
                    print(f"\n📈 VOLUME BAR COMPREHENSIVE ANALYSIS:")
                    print(f"Total Volume Bars: {analysis['total_volume_bars']}")
                    print(f"Symbols: {', '.join(analysis['symbols'])}")
                    print(f"Exchanges: {', '.join(analysis['exchanges'])}")
                    
                    if analysis['time_analysis']:
                        ta = analysis['time_analysis']
                        print(f"Time Range: {ta['start_time']} to {ta['end_time']}")
                        print(f"Total Duration: {ta['total_duration_minutes']:.1f} minutes")
                        print(f"Average Bar Duration: {ta['average_bar_duration']:.1f} seconds")
                        print(f"Duration Volatility: {ta['duration_volatility']}")
                    
                    if analysis['volume_analysis']:
                        va = analysis['volume_analysis']
                        print(f"Total Volume: {va['total_volume']:,}")
                        print(f"Average Volume/Bar: {va['average_volume_per_bar']:,.0f}")
                        print(f"Volume Consistency: {va['volume_consistency']}")
                        print(f"Total Trades: {va['total_trades']:,}")
                    
                    if analysis['price_analysis']:
                        pa = analysis['price_analysis']
                        print(f"Net Price Change: {pa['net_price_change']:+.4f}")
                        print(f"Average Bar Range: {pa['average_bar_range']:.4f}")
                        print(f"Price Volatility: {pa['price_volatility']:.4f}")
                        print(f"Directional Consistency: {pa['directional_consistency']}")
                    
                    if analysis['efficiency_analysis']:
                        ea = analysis['efficiency_analysis']
                        print(f"Volume Efficiency: {ea['volume_impact_assessment']}")
                        print(f"Efficiency Trend: {ea['efficiency_trend']}")
                        print(f"Average Efficiency: {ea['average_volume_efficiency']:.6f}")
                    
                    if analysis['execution_analysis']:
                        ea = analysis['execution_analysis']
                        print(f"Execution Speed: {ea['execution_speed_analysis']}")
                        print(f"Market Intensity: {ea['market_participation_intensity']}")
                    
                    # Display insights
                    if analysis['volume_bar_insights']:
                        print("\n💡 VOLUME BAR INSIGHTS:")
                        for insight in analysis['volume_bar_insights']:
                            print(f"• {insight}")
                    
                    # CSV output
                    if output_format == "csv":
                        print(f"\n📊 CSV FORMAT:")
                        csv_headers = ["Bar_Time", "Symbol", "Exchange", "Open", "High", "Low", "Close", 
                                      "Volume", "Trade_Count", "Duration_Seconds"]
                        print(",".join(csv_headers))
                        
                        for i, bar in enumerate(analyzer.volume_bars[:20]):  # First 20 bars
                            duration = analyzer.bar_durations[i] if i < len(analyzer.bar_durations) else 0
                            values = [
                                bar['bar_time'],
                                bar['symbol'],
                                bar['exchange'],
                                str(bar['open_price']),
                                str(bar['high_price']),
                                str(bar['low_price']),
                                str(bar['close_price']),
                                str(bar['volume']),
                                str(bar['trade_count']),
                                str(duration)
                            ]
                            print(",".join(values))
                    
                    # Volume bar trading recommendations
                    print(f"\n💡 VOLUME BAR TRADING INSIGHTS:")
                    print("✅ Volume bars provide superior insights into market participation")
                    
                    if analysis['efficiency_analysis']:
                        impact = analysis['efficiency_analysis']['volume_impact_assessment']
                        if impact == 'High Volume Impact':
                            print("• High volume efficiency - price sensitive to volume")
                            print("• Consider position sizing based on volume participation")
                        elif impact == 'Low Volume Impact':
                            print("• Low volume efficiency - potential absorption/distribution")
                            print("• Large volume with minimal price impact may indicate institutional activity")
                    
                    if analysis['execution_analysis']:
                        speed = analysis['execution_analysis']['execution_speed_analysis']
                        if speed == 'Very Fast Execution':
                            print("• Fast volume consumption indicates urgent market participation")
                            print("• Consider breakout/momentum strategies")
                        elif speed == 'Slow Execution':
                            print("• Slow volume consumption suggests accumulation/distribution")
                            print("• Consider mean reversion strategies")
                    
                    if analysis['time_analysis']:
                        consistency = analysis['time_analysis']['duration_volatility']
                        if consistency == 'Very Consistent':
                            print("• Consistent bar durations suggest steady market rhythm")
                        elif consistency == 'Highly Variable':
                            print("• Variable bar durations indicate changing market dynamics")
                    
                    print("• Use volume bar duration for market timing")
                    print("• Monitor volume efficiency for entry/exit signals")
                    print("• Fast bars may indicate momentum, slow bars accumulation")
                
                else:
                    print("\n📋 No volume bars collected")
                    print("   This could indicate:")
                    print("   • Insufficient volume during specified time period")
                    print("   • Volume size too large for available trading activity")
                    print("   • Symbol/exchange combination not found")
                    print("   • Date range contains no trading sessions")
            
            except Exception as e:
                logger.error(f"Error collecting volume bars: {e}")
                print(f"❌ Error during bar collection: {e}")
        
        else:
            print("❌ Volume bars request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify symbol and exchange are correct")
            print("   • Check if volume size is appropriate for symbol")
            print("   • Ensure date range contains trading activity")
            print("   • Try smaller volume size for more frequent bars")
            print("   • Verify connection to correct History Plant")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing volume bars: {e}")
        print(f"❌ Test failed: {e}")
        return False

def _extract_volume_bar_info(bar_data: bytes, volume_size: int) -> Dict[str, Any]:
    """Extract volume bar information from raw data."""
    # This would normally parse the time bar protobuf message and interpret it as volume bar
    # For now, return a basic structure
    return {
        "symbol": "ESH5",
        "exchange": "CME",
        "bar_time": datetime.now().isoformat(),
        "open_price": 4500.0,
        "high_price": 4505.0,
        "low_price": 4498.0,
        "close_price": 4502.0,
        "volume": volume_size,  # Should match the requested volume size
        "trade_count": 25,
        "bar_type": "volume",
        "volume_target": volume_size
    }

async def main():
    """Main function for volume bars testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Volume-based Bars endpoint",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_volume_bars.py --symbol ESH5 --exchange CME
  python test_volume_bars.py --symbol ESH5 --exchange CME --volume-size 5000
  python test_volume_bars.py --symbol ESH5 --exchange CME --start-date "01/15/2024"
  python test_volume_bars.py --symbol ESH5 --exchange CME --max-bars 50

Safety:
  This script performs READ-ONLY volume bar requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays volume bar data including:
  - OHLC prices for each volume-based bar
  - Bar completion times and duration analysis
  - Volume efficiency and market impact assessment
  - Execution speed and market participation intensity
  - Comprehensive volume-based trading insights
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        default="ES",  # E-mini S&P 500 futures - highly liquid and reliable
        help="Symbol to request volume bars for (default: ES - E-mini S&P 500 futures)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        default="CME",  # Chicago Mercantile Exchange - major futures exchange
        help="Exchange for the symbol (default: CME - Chicago Mercantile Exchange)"
    )
    
    parser.add_argument(
        "--volume-size",
        type=int,
        default=1000,
        help="Volume size for each bar (default: 1000)"
    )
    
    parser.add_argument(
        "--start-date",
        type=str,
        help="Start date in MM/DD/YYYY format (optional)"
    )
    
    parser.add_argument(
        "--end-date",
        type=str,
        help="End date in MM/DD/YYYY format (optional)"
    )
    
    parser.add_argument(
        "--max-bars",
        type=int,
        default=100,
        help="Maximum number of bars to retrieve (default: 100)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC VOLUME BARS ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY VOLUME BAR OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_volume_bars(
        symbol=args.symbol,
        exchange=args.exchange,
        volume_size=args.volume_size,
        start_date=args.start_date,
        end_date=args.end_date,
        max_bars=args.max_bars,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Volume bars test completed successfully!")
        return 0
    else:
        print("\n❌ Volume bars test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)