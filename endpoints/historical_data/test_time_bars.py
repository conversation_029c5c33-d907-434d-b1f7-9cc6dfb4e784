#!/usr/bin/env python3

"""
Test Rithmic Time Bar endpoints (Templates 200/201, 202/203).

This script demonstrates how to request historical time bar data
from the Rithmic API, including real-time updates and replay functionality.

SAFETY: This script only performs READ-ONLY historical data requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_time_bar_update_pb2
import response_time_bar_update_pb2
import request_time_bar_replay_pb2
import response_time_bar_replay_pb2
import time_bar_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class TimeBarCollector:
    """Collects and manages time bar data."""
    
    def __init__(self):
        self.bars: List[Dict[str, Any]] = []
        self.update_count = 0
        self.message_handler = get_message_handler()
    
    def add_bar(self, bar_msg: time_bar_pb2.TimeBar) -> None:
        """Add a time bar to the collection."""
        bar_data = {
            "symbol": getattr(bar_msg, 'symbol', ''),
            "exchange": getattr(bar_msg, 'exchange', ''),
            "type": getattr(bar_msg, 'type', 0),
            "type_name": self._get_bar_type_name(getattr(bar_msg, 'type', 0)),
            "open_price": getattr(bar_msg, 'open_price', 0.0),
            "high_price": getattr(bar_msg, 'high_price', 0.0),
            "low_price": getattr(bar_msg, 'low_price', 0.0),
            "close_price": getattr(bar_msg, 'close_price', 0.0),
            "volume": getattr(bar_msg, 'volume', 0),
            "num_trades": getattr(bar_msg, 'num_trades', 0),
            "ssboe": getattr(bar_msg, 'ssboe', 0),
            "usecs": getattr(bar_msg, 'usecs', 0),
            "timestamp": time.time()
        }
        
        self.bars.append(bar_data)
        self.update_count += 1
    
    def _get_bar_type_name(self, bar_type: int) -> str:
        """Get human-readable bar type name."""
        type_names = {
            1: "1 Minute",
            2: "5 Minutes", 
            3: "10 Minutes",
            4: "15 Minutes",
            5: "30 Minutes",
            6: "1 Hour",
            7: "2 Hours",
            8: "4 Hours",
            9: "Daily",
            10: "Weekly",
            11: "Monthly"
        }
        return type_names.get(bar_type, f"Type {bar_type}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary statistics of collected bars."""
        if not self.bars:
            return {"total_bars": 0}
        
        # Calculate price statistics
        prices = []
        volumes = []
        
        for bar in self.bars:
            if bar["close_price"] > 0:
                prices.append(bar["close_price"])
            if bar["volume"] > 0:
                volumes.append(bar["volume"])
        
        summary = {
            "total_bars": len(self.bars),
            "symbols": list(set(bar["symbol"] for bar in self.bars)),
            "exchanges": list(set(bar["exchange"] for bar in self.bars)),
            "bar_types": list(set(bar["type_name"] for bar in self.bars)),
            "time_range": {
                "start_ssboe": min(bar["ssboe"] for bar in self.bars if bar["ssboe"] > 0),
                "end_ssboe": max(bar["ssboe"] for bar in self.bars if bar["ssboe"] > 0)
            } if any(bar["ssboe"] > 0 for bar in self.bars) else None
        }
        
        if prices:
            summary["price_stats"] = {
                "min": min(prices),
                "max": max(prices),
                "first": self.bars[0]["close_price"],
                "last": self.bars[-1]["close_price"]
            }
        
        if volumes:
            summary["volume_stats"] = {
                "total": sum(volumes),
                "avg": sum(volumes) / len(volumes),
                "max": max(volumes)
            }
        
        return summary

async def test_time_bar_subscription(symbol: str, exchange: str,
                                   bar_type: int = 1,
                                   duration: int = 60,
                                   output_format: str = "human") -> bool:
    """
    Test time bar subscription for real-time updates.
    
    Args:
        symbol: Trading symbol
        exchange: Exchange name
        bar_type: Bar type (1=1min, 2=5min, etc.)
        duration: Duration to monitor in seconds
        output_format: Output format
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    collector = TimeBarCollector()
    
    print("=" * 60)
    print("📊 TESTING TIME BAR SUBSCRIPTION")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.TIME_BAR_UPDATE_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.TIME_BAR_UPDATE_RESPONSE} (Response)")
    print(f"Template ID: {TemplateIDs.TIME_BAR} (Time Bar Data)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Bar Type: {collector._get_bar_type_name(bar_type)}")
    print(f"Duration: {duration} seconds")
    print("=" * 60)
    
    try:
        # Create authenticated connection to History Plant
        print("🔐 Establishing connection to History Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.HISTORY_PLANT, "time_bar_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create time bar subscription request
        request = request_time_bar_update_pb2.RequestTimeBarUpdate()
        request.template_id = TemplateIDs.TIME_BAR_UPDATE_REQUEST
        request.user_msg.append(f"Time bar subscription for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        request.type = bar_type
        request.request = request_time_bar_update_pb2.RequestTimeBarUpdate.Request.SUBSCRIBE
        
        # Send subscription request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending time bar subscription request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send subscription request")
            return False
        
        # Wait for subscription response
        print("⏳ Waiting for subscription response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No subscription response received")
            return False
        
        # Parse subscription response
        response = response_time_bar_update_pb2.ResponseTimeBarUpdate()
        response.ParseFromString(response_bytes)
        
        print(f"📥 Subscription response: {list(response.rp_code)}")
        
        # Check if subscription successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if not success:
            print("❌ Time bar subscription failed!")
            if len(response.rp_code) >= 2:
                print(f"Error: {response.rp_code[0]} - {response.rp_code[1]}")
            return False
        
        print("✅ Time bar subscription successful!")
        
        # Monitor for time bar updates
        print(f"\n📈 MONITORING TIME BARS FOR {duration} SECONDS")
        print("Press Ctrl+C to stop early")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            while time.time() - start_time < duration:
                # Wait for time bar data
                message_bytes = await connection.receive_message(timeout=2.0)
                
                if not message_bytes:
                    continue
                
                # Parse message to check type
                message_info = parse_message(message_bytes)
                if not message_info:
                    continue
                
                if message_info.template_id == TemplateIDs.TIME_BAR:
                    # Process time bar
                    bar_msg = time_bar_pb2.TimeBar()
                    bar_msg.ParseFromString(message_bytes)
                    collector.add_bar(bar_msg)
                    
                    # Display bar information
                    bar_type_name = collector._get_bar_type_name(bar_msg.type)
                    print(f"📊 Bar #{collector.update_count}: {bar_msg.symbol} {bar_type_name}")
                    print(f"   OHLC: {bar_msg.open_price:.2f} / {bar_msg.high_price:.2f} / "
                          f"{bar_msg.low_price:.2f} / {bar_msg.close_price:.2f}")
                    print(f"   Volume: {bar_msg.volume}, Trades: {bar_msg.num_trades}")
                    
                elif message_info.template_id == TemplateIDs.HEARTBEAT_RESPONSE:
                    print("💓 Heartbeat received")
                
                # Progress indicator
                elapsed = time.time() - start_time
                if int(elapsed) % 10 == 0:
                    remaining = duration - elapsed
                    print(f"⏱️  {remaining:.0f}s remaining, {collector.update_count} bars received")
        
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        
        # Unsubscribe
        print("\n🚫 Unsubscribing from time bars...")
        request.request = request_time_bar_update_pb2.RequestTimeBarUpdate.Request.UNSUBSCRIBE
        serialized_request = request.SerializeToString()
        await connection.send_message(serialized_request)
        
        # Cleanup
        print("🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        # Display summary
        summary = collector.get_summary()
        print(f"\n📋 TIME BAR SUMMARY:")
        print(f"Total Bars Received: {summary['total_bars']}")
        if summary.get('time_range'):
            start_time = datetime.fromtimestamp(summary['time_range']['start_ssboe'])
            end_time = datetime.fromtimestamp(summary['time_range']['end_ssboe'])
            print(f"Time Range: {start_time} to {end_time}")
        
        return summary['total_bars'] > 0
        
    except Exception as e:
        logger.error(f"Error testing time bar subscription: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_time_bar_replay(symbol: str, exchange: str,
                              bar_type: int = 1,
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None,
                              max_bars: int = 100,
                              output_format: str = "human") -> bool:
    """
    Test time bar replay for historical data.
    
    Args:
        symbol: Trading symbol
        exchange: Exchange name
        bar_type: Bar type (1=1min, 2=5min, etc.)
        start_date: Start date (YYYY-MM-DD format)
        end_date: End date (YYYY-MM-DD format)
        max_bars: Maximum number of bars to retrieve
        output_format: Output format
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    collector = TimeBarCollector()
    
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")
    if not start_date:
        start_dt = datetime.now() - timedelta(days=7)
        start_date = start_dt.strftime("%Y-%m-%d")
    
    print("=" * 60)
    print("🔄 TESTING TIME BAR REPLAY")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.TIME_BAR_REPLAY_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.TIME_BAR_REPLAY_RESPONSE} (Response)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Bar Type: {collector._get_bar_type_name(bar_type)}")
    print(f"Date Range: {start_date} to {end_date}")
    print(f"Max Bars: {max_bars}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to History Plant
        print("🔐 Establishing connection to History Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.HISTORY_PLANT, "time_bar_replay_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Convert dates to timestamps
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_ssboe = int(start_dt.timestamp())
        end_ssboe = int(end_dt.timestamp())
        
        # Create time bar replay request
        request = request_time_bar_replay_pb2.RequestTimeBarReplay()
        request.template_id = TemplateIDs.TIME_BAR_REPLAY_REQUEST
        request.user_msg.append(f"Time bar replay for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        request.bar_type = bar_type  # Fixed: use correct field name from proto
        request.start_index = start_ssboe
        request.finish_index = end_ssboe
        
        # Add required parameters for proper bar request
        request.bar_type_period = 1  # 1-unit period (e.g., 1 minute for MINUTE_BAR)
        request.direction = request_time_bar_replay_pb2.RequestTimeBarReplay.Direction.LAST  # Get most recent bars
        request.time_order = request_time_bar_replay_pb2.RequestTimeBarReplay.TimeOrder.BACKWARDS  # Most recent first
        
        # Send replay request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending time bar replay request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send replay request")
            return False
        
        # Wait for replay response
        print("⏳ Waiting for replay response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No replay response received")
            return False
        
        # Parse replay response
        response = response_time_bar_replay_pb2.ResponseTimeBarReplay()
        response.ParseFromString(response_bytes)
        
        print(f"📥 Replay response: {list(response.rp_code)}")
        
        # Check if replay successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if not success:
            print("❌ Time bar replay failed!")
            if len(response.rp_code) >= 2:
                print(f"Error: {response.rp_code[0]} - {response.rp_code[1]}")
            return False
        
        print("✅ Time bar replay started successfully!")
        
        # Collect historical bars
        print(f"\n📊 COLLECTING HISTORICAL BARS (max {max_bars})")
        print("-" * 50)
        
        bars_received = 0
        
        try:
            while bars_received < max_bars:
                # Wait for time bar data
                message_bytes = await connection.receive_message(timeout=5.0)
                
                if not message_bytes:
                    print("⏳ No more data received, replay may be complete")
                    break
                
                # Parse message to check type
                message_info = parse_message(message_bytes)
                if not message_info:
                    continue
                
                if message_info.template_id == TemplateIDs.TIME_BAR:
                    # Process time bar
                    bar_msg = time_bar_pb2.TimeBar()
                    bar_msg.ParseFromString(message_bytes)
                    collector.add_bar(bar_msg)
                    bars_received += 1
                    
                    # Display progress
                    if bars_received <= 5 or bars_received % 10 == 0:
                        bar_time = datetime.fromtimestamp(bar_msg.ssboe)
                        print(f"Bar #{bars_received}: {bar_msg.symbol} @ {bar_time.strftime('%Y-%m-%d %H:%M')}")
                        print(f"   Close: {bar_msg.close_price:.2f}, Volume: {bar_msg.volume}")
                
                elif message_info.template_id == TemplateIDs.HEARTBEAT_RESPONSE:
                    continue  # Skip heartbeat messages
                
                else:
                    # Check for end of replay or error
                    if hasattr(message_info, 'template_name'):
                        if "response" in message_info.template_name.lower():
                            print(f"📨 Received {message_info.template_name}")
                            break
        
        except Exception as e:
            print(f"⚠️  Data collection stopped: {e}")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        # Display detailed summary
        summary = collector.get_summary()
        print(f"\n📋 HISTORICAL DATA SUMMARY:")
        print(f"Total Bars Retrieved: {summary['total_bars']}")
        
        if summary.get('time_range'):
            start_time = datetime.fromtimestamp(summary['time_range']['start_ssboe'])
            end_time = datetime.fromtimestamp(summary['time_range']['end_ssboe'])
            print(f"Data Time Range: {start_time} to {end_time}")
        
        if summary.get('price_stats'):
            ps = summary['price_stats']
            print(f"Price Range: {ps['min']:.2f} - {ps['max']:.2f}")
            print(f"Price Change: {ps['first']:.2f} → {ps['last']:.2f}")
        
        if summary.get('volume_stats'):
            vs = summary['volume_stats']
            print(f"Total Volume: {vs['total']:,}")
            print(f"Average Volume: {vs['avg']:.0f}")
        
        return summary['total_bars'] > 0
        
    except Exception as e:
        logger.error(f"Error testing time bar replay: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for time bar testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Time Bar endpoints (Templates 200/201, 202/203)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_time_bars.py --symbol ESH5 --exchange CME --subscription
  python test_time_bars.py --symbol ESH5 --exchange CME --replay --start-date 2024-01-01
  python test_time_bars.py --symbol AAPL --exchange NASDAQ --bar-type 6 --replay
  python test_time_bars.py --symbol ESH5 --exchange CME --both --duration 30

Bar Types:
  1 = 1 Minute    2 = 5 Minutes   3 = 10 Minutes   4 = 15 Minutes
  5 = 30 Minutes  6 = 1 Hour      7 = 2 Hours      8 = 4 Hours
  9 = Daily       10 = Weekly     11 = Monthly

Safety:
  This script performs READ-ONLY historical data requests only.
  Requires authentication but no account modifications performed.
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        default="ES",  # E-mini S&P 500 futures - highly liquid and reliable
        help="Trading symbol (default: ES - E-mini S&P 500 futures)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        default="CME",  # Chicago Mercantile Exchange - major futures exchange
        help="Exchange name (default: CME - Chicago Mercantile Exchange)"
    )
    
    parser.add_argument(
        "--bar-type",
        type=int,
        default=1,
        choices=range(1, 12),
        help="Bar type (1=1min, 2=5min, etc.) (default: 1)"
    )
    
    parser.add_argument(
        "--subscription",
        action="store_true",
        help="Test real-time subscription"
    )
    
    parser.add_argument(
        "--replay",
        action="store_true",
        help="Test historical replay"
    )
    
    parser.add_argument(
        "--both",
        action="store_true",
        help="Test both subscription and replay"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=60,
        help="Subscription duration in seconds (default: 60)"
    )
    
    parser.add_argument(
        "--start-date",
        type=str,
        help="Start date for replay (YYYY-MM-DD format)"
    )
    
    parser.add_argument(
        "--end-date",
        type=str,
        help="End date for replay (YYYY-MM-DD format)"
    )
    
    parser.add_argument(
        "--max-bars",
        type=int,
        default=100,
        help="Maximum bars to retrieve in replay (default: 100)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    parser.add_argument(
        "--test-mode",
        action="store_true",
        help="Enable test mode with default parameters for automated testing"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not any([args.subscription, args.replay, args.both]):
        print("❌ Must specify --subscription, --replay, or --both")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC TIME BAR ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY HISTORICAL DATA OPERATIONS ONLY")
    
    # Show default parameter usage for automated testing
    if args.symbol == "ES" and args.exchange == "CME":
        print("🤖 AUTOMATED TESTING MODE: Using default parameters")
        print(f"   Symbol: {args.symbol} (E-mini S&P 500 futures)")
        print(f"   Exchange: {args.exchange} (Chicago Mercantile Exchange)")
        print("   💡 Use --symbol and --exchange to specify different instruments")
    
    print("=" * 50)
    
    # Run the test(s)
    results = []
    
    if args.subscription or args.both:
        print("\n" + "="*60)
        print("TESTING REAL-TIME SUBSCRIPTION")
        print("="*60)
        
        success = await test_time_bar_subscription(
            symbol=args.symbol,
            exchange=args.exchange,
            bar_type=args.bar_type,
            duration=args.duration,
            output_format=args.format
        )
        results.append(("Subscription", success))
    
    if args.replay or args.both:
        if args.both:
            print("\n⏳ Waiting 5 seconds before replay test...")
            await asyncio.sleep(5)
        
        print("\n" + "="*60)
        print("TESTING HISTORICAL REPLAY")
        print("="*60)
        
        success = await test_time_bar_replay(
            symbol=args.symbol,
            exchange=args.exchange,
            bar_type=args.bar_type,
            start_date=args.start_date,
            end_date=args.end_date,
            max_bars=args.max_bars,
            output_format=args.format
        )
        results.append(("Replay", success))
    
    # Summary
    if len(results) > 1:
        print("\n" + "=" * 60)
        print("📋 OVERALL TEST SUMMARY")
        print("=" * 60)
        
        all_passed = True
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        if all_passed:
            print("\n🎉 ALL TIME BAR TESTS PASSED!")
            return 0
        else:
            print("\n⚠️  SOME TIME BAR TESTS FAILED!")
            return 1
    else:
        success = results[0][1] if results else False
        if success:
            print("\n✅ Time bar test completed successfully!")
            return 0
        else:
            print("\n❌ Time bar test failed!")
            return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)