================================================================================
🔍 COMPREHENSIVE TEST VALIDATION REPORT
================================================================================
Report Generated: 2025-07-02 20:51:50

📊 OVERALL RESULTS:
   Total Tests: 19
   Passed: 5 ✅
   Failed: 14 ❌
   Success Rate: 26.3%
   Total Duration: 189.3s

📂 RESULTS BY CATEGORY:
--------------------------------------------------
❌ Account Info:
   Success Rate: 33.3% (2/6)
   Average Duration: 20.4s

     ✅ test_account_list (0.5s)
     ✅ test_account_rms_info (0.5s)
     ❌ test_bracket_info (0.6s)
     ❌ test_order_session_config (60.0s)
     ❌ test_show_orders (0.6s)
     ❌ test_exchange_permissions (60.0s)

❌ Historical Data:
   Success Rate: 0.0% (0/4)
   Average Duration: 0.1s

     ❌ test_time_bars (0.1s)
     ❌ test_volume_bars (0.1s)
     ❌ test_tick_bars (0.1s)
     ❌ test_resume_bars (0.1s)

❌ Repository:
   Success Rate: 0.0% (0/2)
   Average Duration: 2.3s

     ❌ test_list_agreements (4.0s)
     ❌ test_show_agreement (0.6s)

❌ Market Data:
   Success Rate: 0.0% (0/4)
   Average Duration: 7.7s

✅ System Discovery:
   Success Rate: 100.0% (3/3)
   Average Duration: 10.5s

⚠️ REMAINING ISSUES:
------------------------------
❌ account_info/test_bracket_info.py: Failed
❌ account_info/test_order_session_config.py: Test timed out...
❌ account_info/test_show_orders.py: Failed
❌ account_info/test_exchange_permissions.py: Test timed out...
❌ historical_data/test_time_bars.py:          [--bar-type {1,2,3,4,5,6,7,8,9,10,11}]
                         [--subs...
❌ historical_data/test_volume_bars.py: usage: test_volume_bars.py [-h] --symbol SYMBOL --exchange EXCHANGE
            ...
❌ historical_data/test_tick_bars.py: usage: test_tick_bars.py [-h] --symbol SYMBOL --exchange EXCHANGE [--replay]
   ...
❌ historical_data/test_resume_bars.py: usage: test_resume_bars.py [-h] --symbol SYMBOL --exchange EXCHANGE
            ...
❌ repository/test_list_agreements.py: Failed
❌ repository/test_show_agreement.py: Failed
... and 4 more

⚡ PERFORMANCE INSIGHTS:
-----------------------------------
📈 4 tests completed quickly (<10s)
📈 4 tests were slow (>30s)

💡 RECOMMENDATIONS:
-------------------------
🔧 More work needed. Focus on systematic error resolution.
🔍 Account Info: Review authentication and parameter requirements
🔍 Historical Data: Review authentication and parameter requirements
🔍 Repository: Review authentication and parameter requirements

================================================================================
End of Validation Report
================================================================================

Detailed Test Results:
==================================================
system_discovery/test_system_info.py: PASS (0.5s)
system_discovery/test_gateway_info.py: PASS (0.5s)
system_discovery/test_heartbeat.py: PASS (30.5s)
account_info/test_account_list.py: PASS (0.5s)
account_info/test_account_rms_info.py: PASS (0.5s)
account_info/test_bracket_info.py: FAIL (0.6s)
account_info/test_order_session_config.py: FAIL (60.0s)
  Error: Test timed out
account_info/test_show_orders.py: FAIL (0.6s)
account_info/test_exchange_permissions.py: FAIL (60.0s)
  Error: Test timed out
historical_data/test_time_bars.py: FAIL (0.1s)
  Error:          [--bar-type {1,2,3,4,5,6,7,8,9,10,11}]
                         [--subscription] [--replay] [--both]
                         [--duration DURATION] [--start-date START_DATE]
                         [--end-date END_DATE] [--max-bars MAX_BARS]
                         [--format {human,json,csv}]
                         [--log-level {DEBUG,INFO,WARNING,ERROR}]
                         [--save-responses]
test_time_bars.py: error: the following arguments are required: --symbol, --exchange

historical_data/test_volume_bars.py: FAIL (0.1s)
  Error: usage: test_volume_bars.py [-h] --symbol SYMBOL --exchange EXCHANGE
                           [--volume-size VOLUME_SIZE]
                           [--start-date START_DATE] [--end-date END_DATE]
                           [--max-bars MAX_BARS] [--format {human,json,csv}]
                           [--log-level {DEBUG,INFO,WARNING,ERROR}]
                           [--save-responses]
test_volume_bars.py: error: the following arguments are required: --symbol, --exchange

historical_data/test_tick_bars.py: FAIL (0.1s)
  Error: usage: test_tick_bars.py [-h] --symbol SYMBOL --exchange EXCHANGE [--replay]
                         [--max-bars MAX_BARS] [--tick-type {trade,bid,ask}]
                         [--format {human,json,csv}]
                         [--log-level {DEBUG,INFO,WARNING,ERROR}]
                         [--save-responses]
test_tick_bars.py: error: the following arguments are required: --symbol, --exchange

historical_data/test_resume_bars.py: FAIL (0.1s)
  Error: usage: test_resume_bars.py [-h] --symbol SYMBOL --exchange EXCHANGE
                           [--resume-token RESUME_TOKEN]
                           [--bar-type {time,tick,volume}]
                           [--max-bars MAX_BARS] [--format {human,json,csv}]
                           [--log-level {DEBUG,INFO,WARNING,ERROR}]
                           [--save-responses]
test_resume_bars.py: error: the following arguments are required: --symbol, --exchange

repository/test_list_agreements.py: FAIL (4.0s)
repository/test_show_agreement.py: FAIL (0.6s)
market_data/test_symbol_search.py: FAIL (0.1s)
market_data/test_product_codes.py: FAIL (30.3s)
market_data/test_front_month_contract.py: FAIL (0.1s)
market_data/test_reference_data.py: FAIL (0.1s)
