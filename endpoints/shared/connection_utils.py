#!/usr/bin/env python3

"""
Connection utilities for robust Rithmic API connectivity.

This module provides enhanced connection management with retry logic,
health checks, and adaptive timeouts for improved reliability.
"""

import asyncio
import logging
import time
from typing import Optional, Callable, Any, Union
from dataclasses import dataclass

from config import get_config, get_timeout_for_operation, get_retry_delay

logger = logging.getLogger(__name__)

@dataclass
class ConnectionHealth:
    """Connection health status information."""
    is_healthy: bool
    last_successful_message: float
    connection_attempts: int
    last_error: Optional[str] = None
    avg_response_time: float = 0.0

class RobustConnection:
    """Enhanced connection wrapper with retry logic and health monitoring."""
    
    def __init__(self, base_connection):
        """
        Initialize robust connection wrapper.
        
        Args:
            base_connection: Base connection object to wrap
        """
        self.base_connection = base_connection
        self.health = ConnectionHealth(
            is_healthy=True,
            last_successful_message=time.time(),
            connection_attempts=0
        )
        self.config = get_config()
        
    async def send_message_with_retry(self, message: bytes, 
                                    operation_type: str = "standard") -> bool:
        """
        Send message with retry logic and health monitoring.
        
        Args:
            message: Message bytes to send
            operation_type: Type of operation for timeout selection
            
        Returns:
            bool: True if message sent successfully
        """
        max_retries = self.config.max_retries
        
        for attempt in range(max_retries + 1):
            try:
                start_time = time.time()
                
                # Attempt to send message
                success = await self.base_connection.send_message(message)
                
                if success:
                    # Update health metrics
                    response_time = time.time() - start_time
                    self._update_health_success(response_time)
                    
                    if attempt > 0:
                        logger.info(f"Message sent successfully after {attempt} retries")
                    
                    return True
                else:
                    self._update_health_failure(f"Send failed on attempt {attempt + 1}")
                    
                    if attempt < max_retries:
                        delay = get_retry_delay(attempt)
                        logger.warning(f"Send failed, retrying in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                        await asyncio.sleep(delay)
                    
            except Exception as e:
                error_msg = f"Send error on attempt {attempt + 1}: {e}"
                self._update_health_failure(error_msg)
                
                if attempt < max_retries:
                    delay = get_retry_delay(attempt)
                    logger.warning(f"Send error, retrying in {delay:.1f}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Send failed after {max_retries + 1} attempts: {e}")
        
        return False
    
    async def receive_message_with_retry(self, operation_type: str = "standard") -> Optional[bytes]:
        """
        Receive message with adaptive timeout and retry logic.
        
        Args:
            operation_type: Type of operation for timeout selection
            
        Returns:
            Optional[bytes]: Received message bytes or None if failed
        """
        timeout = get_timeout_for_operation(operation_type)
        max_retries = self.config.max_retries
        
        for attempt in range(max_retries + 1):
            try:
                start_time = time.time()
                
                # Attempt to receive message with appropriate timeout
                message_bytes = await self.base_connection.receive_message(timeout=timeout)
                
                if message_bytes:
                    # Update health metrics
                    response_time = time.time() - start_time
                    self._update_health_success(response_time)
                    
                    if attempt > 0:
                        logger.info(f"Message received successfully after {attempt} retries")
                    
                    return message_bytes
                else:
                    self._update_health_failure(f"Receive timeout on attempt {attempt + 1}")
                    
                    if attempt < max_retries:
                        delay = get_retry_delay(attempt)
                        logger.warning(f"Receive timeout, retrying in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                        await asyncio.sleep(delay)
                    
            except asyncio.TimeoutError:
                error_msg = f"Receive timeout on attempt {attempt + 1} (timeout: {timeout}s)"
                self._update_health_failure(error_msg)
                
                if attempt < max_retries:
                    delay = get_retry_delay(attempt)
                    logger.warning(f"Timeout, retrying in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(delay)
                
            except Exception as e:
                error_msg = f"Receive error on attempt {attempt + 1}: {e}"
                self._update_health_failure(error_msg)
                
                if attempt < max_retries:
                    delay = get_retry_delay(attempt)
                    logger.warning(f"Receive error, retrying in {delay:.1f}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Receive failed after {max_retries + 1} attempts: {e}")
        
        return None
    
    async def health_check(self) -> bool:
        """
        Perform connection health check.
        
        Returns:
            bool: True if connection is healthy
        """
        try:
            # Simple connectivity test - try to send a heartbeat or lightweight message
            # This would depend on the specific connection implementation
            
            # For now, check if we've had recent successful communication
            time_since_success = time.time() - self.health.last_successful_message
            max_idle_time = 60  # 60 seconds max without communication
            
            is_healthy = time_since_success < max_idle_time
            self.health.is_healthy = is_healthy
            
            if not is_healthy:
                logger.warning(f"Connection appears unhealthy - {time_since_success:.1f}s since last success")
            
            return is_healthy
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            self.health.is_healthy = False
            self.health.last_error = str(e)
            return False
    
    def get_health_status(self) -> ConnectionHealth:
        """Get current connection health status."""
        return self.health
    
    def _update_health_success(self, response_time: float):
        """Update health metrics after successful operation."""
        self.health.is_healthy = True
        self.health.last_successful_message = time.time()
        self.health.last_error = None
        
        # Update average response time with exponential moving average
        if self.health.avg_response_time == 0:
            self.health.avg_response_time = response_time
        else:
            alpha = 0.1  # Smoothing factor
            self.health.avg_response_time = (
                alpha * response_time + (1 - alpha) * self.health.avg_response_time
            )
    
    def _update_health_failure(self, error_msg: str):
        """Update health metrics after failed operation."""
        self.health.is_healthy = False
        self.health.last_error = error_msg
        self.health.connection_attempts += 1
    
    async def disconnect(self):
        """Disconnect the underlying connection."""
        if hasattr(self.base_connection, 'disconnect'):
            await self.base_connection.disconnect()

async def create_robust_connection(base_connection) -> RobustConnection:
    """
    Create a robust connection wrapper.
    
    Args:
        base_connection: Base connection to wrap
        
    Returns:
        RobustConnection: Enhanced connection with retry logic
    """
    robust_conn = RobustConnection(base_connection)
    
    # Perform initial health check
    await robust_conn.health_check()
    
    return robust_conn

def log_connection_diagnostics(health: ConnectionHealth, operation_type: str = ""):
    """
    Log detailed connection diagnostics.
    
    Args:
        health: Connection health status
        operation_type: Type of operation being performed
    """
    status = "✅ HEALTHY" if health.is_healthy else "❌ UNHEALTHY"
    
    logger.info(f"Connection Status: {status}")
    logger.info(f"Operation Type: {operation_type}")
    logger.info(f"Last Success: {time.time() - health.last_successful_message:.1f}s ago")
    logger.info(f"Connection Attempts: {health.connection_attempts}")
    logger.info(f"Avg Response Time: {health.avg_response_time:.3f}s")
    
    if health.last_error:
        logger.warning(f"Last Error: {health.last_error}")