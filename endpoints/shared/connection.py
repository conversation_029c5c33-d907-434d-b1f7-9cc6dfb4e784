#!/usr/bin/env python3

"""
WebSocket connection management for Rithmic API endpoints.

This module provides WebSocket connection handling with automatic reconnection,
heartbeat management, and proper SSL configuration for the Rithmic API.
"""

import asyncio
import ssl
import pathlib
import websockets
import logging
from typing import Optional, Callable, Any
from websockets.exceptions import (
    InvalidURI, InvalidHandshake, ConnectionClosedError, 
    InvalidMessage, ConnectionClosed
)

from config import get_config

logger = logging.getLogger(__name__)

class RithmicConnection:
    """Manages WebSocket connection to Rithmic API with heartbeat and reconnection."""
    
    def __init__(self, uri: Optional[str] = None, ssl_cert_path: Optional[str] = None):
        self.config = get_config()
        self.uri = uri or self.config.uri
        self.ssl_cert_path = ssl_cert_path or self.config.ssl_cert_path
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.ssl_context = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.message_handler: Optional[Callable] = None
        self.connected = False
        
        # Setup SSL context
        self._setup_ssl_context()
    
    def _setup_ssl_context(self) -> None:
        """Setup SSL context for secure WebSocket connection."""
        if "wss://" in self.uri:
            self.ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
            
            # Load the Rithmic SSL certificate
            cert_path = pathlib.Path(self.ssl_cert_path)
            if cert_path.exists():
                self.ssl_context.load_verify_locations(cert_path)
                logger.info(f"Loaded SSL certificate from {cert_path}")
            else:
                logger.warning(f"SSL certificate not found at {cert_path}")
                # Use default SSL context if certificate file not found
                self.ssl_context = ssl.create_default_context()
    
    async def connect(self) -> bool:
        """
        Establish WebSocket connection to Rithmic API.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to {self.uri}")
            
            self.websocket = await websockets.connect(
                self.uri,
                ssl=self.ssl_context,
                ping_interval=None,  # Disable websockets ping, use Rithmic heartbeat
                close_timeout=self.config.connection_timeout
            )
            
            self.connected = True
            logger.info(f"Successfully connected to {self.uri}")
            return True
            
        except InvalidURI as e:
            logger.error(f"Invalid URI: {e}")
            return False
        except InvalidHandshake as e:
            logger.error(f"Invalid handshake with {self.uri}: {e}")
            return False
        except ConnectionClosedError as e:
            logger.error(f"Connection to {self.uri} was closed unexpectedly: {e}")
            return False
        except InvalidMessage as e:
            logger.error(f"Received invalid message from {self.uri}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to {self.uri}: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Close the WebSocket connection gracefully."""
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        if self.websocket and not self.websocket.closed:
            await self.websocket.close(1000, "Disconnecting")
            logger.info("WebSocket connection closed")
        
        self.connected = False
    
    async def send_message(self, message_bytes: bytes) -> bool:
        """
        Send a binary message over the WebSocket connection.
        
        Args:
            message_bytes: Serialized protobuf message
            
        Returns:
            bool: True if message sent successfully, False otherwise
        """
        if not self.websocket or self.websocket.closed:
            logger.error("Cannot send message: WebSocket connection not open")
            return False
        
        try:
            await self.websocket.send(message_bytes)
            logger.debug(f"Sent message of {len(message_bytes)} bytes")
            return True
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return False
    
    async def receive_message(self, timeout: Optional[float] = None) -> Optional[bytes]:
        """
        Receive a binary message from the WebSocket connection.
        
        Args:
            timeout: Optional timeout in seconds
            
        Returns:
            bytes: Received message bytes, or None if error/timeout
        """
        if not self.websocket or self.websocket.closed:
            logger.error("Cannot receive message: WebSocket connection not open")
            return None
        
        try:
            timeout = timeout or self.config.message_timeout
            message = await asyncio.wait_for(self.websocket.recv(), timeout=timeout)
            logger.debug(f"Received message of {len(message)} bytes")
            return message
        except asyncio.TimeoutError:
            logger.debug("Message receive timeout")
            return None
        except ConnectionClosed:
            logger.warning("Connection closed while waiting for message")
            self.connected = False
            return None
        except Exception as e:
            logger.error(f"Error receiving message: {e}")
            return None
    
    async def start_heartbeat(self, heartbeat_sender: Callable) -> None:
        """
        Start automatic heartbeat sending.
        
        Args:
            heartbeat_sender: Async function that sends heartbeat messages
        """
        async def heartbeat_loop():
            while self.connected and self.websocket and not self.websocket.closed:
                try:
                    await asyncio.sleep(self.config.heartbeat_interval)
                    if self.connected:
                        await heartbeat_sender()
                        logger.debug("Heartbeat sent")
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"Error in heartbeat loop: {e}")
                    break
        
        self.heartbeat_task = asyncio.create_task(heartbeat_loop())
        logger.info(f"Started heartbeat with {self.config.heartbeat_interval}s interval")
    
    def is_connected(self) -> bool:
        """Check if the WebSocket connection is active."""
        return self.connected and self.websocket and not self.websocket.closed
    
    async def wait_for_close(self) -> None:
        """Wait for the WebSocket connection to close."""
        if self.websocket:
            await self.websocket.wait_closed()

class ConnectionPool:
    """Manages multiple connections to different Rithmic infrastructure plants."""
    
    def __init__(self):
        self.connections: dict[str, RithmicConnection] = {}
    
    async def get_connection(self, plant_name: str, uri: Optional[str] = None) -> Optional[RithmicConnection]:
        """
        Get or create a connection for a specific infrastructure plant.
        
        Args:
            plant_name: Name of the infrastructure plant (ticker, order, history, etc.)
            uri: Optional custom URI for the connection
            
        Returns:
            RithmicConnection: Connection instance, or None if connection failed
        """
        if plant_name in self.connections:
            conn = self.connections[plant_name]
            if conn.is_connected():
                return conn
            else:
                # Connection lost, remove it and create new one
                await conn.disconnect()
                del self.connections[plant_name]
        
        # Create new connection
        conn = RithmicConnection(uri)
        if await conn.connect():
            self.connections[plant_name] = conn
            return conn
        else:
            return None
    
    async def close_all(self) -> None:
        """Close all connections in the pool."""
        for plant_name, conn in self.connections.items():
            logger.info(f"Closing connection to {plant_name}")
            await conn.disconnect()
        self.connections.clear()
    
    def get_active_connections(self) -> list[str]:
        """Get list of active connection names."""
        return [name for name, conn in self.connections.items() if conn.is_connected()]

# Global connection pool
_connection_pool = ConnectionPool()

async def get_connection(plant_name: str = "default", uri: Optional[str] = None) -> Optional[RithmicConnection]:
    """
    Get a connection from the global connection pool.
    
    Args:
        plant_name: Name of the infrastructure plant
        uri: Optional custom URI
        
    Returns:
        RithmicConnection: Connection instance, or None if failed
    """
    return await _connection_pool.get_connection(plant_name, uri)

async def close_all_connections() -> None:
    """Close all connections in the global pool."""
    await _connection_pool.close_all()

def get_active_connections() -> list[str]:
    """Get list of active connection names from global pool."""
    return _connection_pool.get_active_connections()