#!/usr/bin/env python3

"""
Authentication management for Rithmic API endpoints.

This module handles login/logout functionality for different infrastructure plants
and manages authentication state across endpoint testing sessions.
"""

import asyncio
import logging
import sys
import os
from typing import Optional, Dict, Any
from dataclasses import dataclass

# Add the proto_generated directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'proto_generated'))

import base_pb2
import request_login_pb2
import response_login_pb2
import request_logout_pb2
import response_logout_pb2
import request_heartbeat_pb2
import response_heartbeat_pb2

from config import get_config, InfraType, TemplateIDs
from connection import RithmicConnection

logger = logging.getLogger(__name__)

@dataclass
class LoginResponse:
    """Structured login response data."""
    success: bool
    template_id: int
    user_msg: list[str]
    rp_code: list[str]
    fcm_id: Optional[str] = None
    ib_id: Optional[str] = None
    country_code: Optional[str] = None
    state_code: Optional[str] = None
    heartbeat_interval: Optional[int] = None
    unique_user_id: Optional[str] = None
    error_message: Optional[str] = None

class RithmicAuthenticator:
    """Manages authentication for Rithmic API connections."""
    
    def __init__(self, connection: RithmicConnection):
        self.connection = connection
        self.config = get_config()
        self.authenticated = False
        self.login_responses: Dict[int, LoginResponse] = {}  # infra_type -> LoginResponse
        self.heartbeat_enabled = False
    
    async def login(self, infra_type: int, system_name: Optional[str] = None,
                   username: Optional[str] = None, password: Optional[str] = None) -> LoginResponse:
        """
        Authenticate with a specific Rithmic infrastructure plant.
        
        Args:
            infra_type: Infrastructure plant type (InfraType constants)
            system_name: Optional system name override
            username: Optional username override
            password: Optional password override
            
        Returns:
            LoginResponse: Structured login response
        """
        if not self.connection.is_connected():
            return LoginResponse(
                success=False,
                template_id=0,
                user_msg=[],
                rp_code=["Connection not established"],
                error_message="WebSocket connection not established"
            )
        
        # Use configured credentials by default
        username = username or self.config.username
        password = password or self.config.password
        system_name = system_name or self.config.system_name
        
        # Validate paper trading mode
        if self.config.paper_trading_only and "paper" not in system_name.lower():
            logger.warning("Paper trading mode enabled but system name doesn't contain 'paper'")
        
        # Create login request
        request = request_login_pb2.RequestLogin()
        request.template_id = TemplateIDs.LOGIN_REQUEST
        request.template_version = self.config.template_version
        request.user_msg.append(f"Login to {self._get_infra_name(infra_type)}")
        
        request.user = username
        request.password = password
        request.app_name = self.config.app_name
        request.app_version = self.config.app_version
        request.system_name = system_name
        request.infra_type = infra_type
        
        # Send login request
        serialized = request.SerializeToString()
        logger.info(f"Sending login request for {self._get_infra_name(infra_type)}")
        
        if not await self.connection.send_message(serialized):
            return LoginResponse(
                success=False,
                template_id=0,
                user_msg=[],
                rp_code=["Failed to send login request"],
                error_message="Failed to send login request"
            )
        
        # Wait for login response
        response_bytes = await self.connection.receive_message(timeout=self.config.message_timeout)
        if not response_bytes:
            return LoginResponse(
                success=False,
                template_id=0,
                user_msg=[],
                rp_code=["No login response received"],
                error_message="No login response received within timeout"
            )
        
        # Parse login response
        response = response_login_pb2.ResponseLogin()
        response.ParseFromString(response_bytes)
        
        # Create structured response
        login_result = LoginResponse(
            success=len(response.rp_code) == 1 and response.rp_code[0] == "0",
            template_id=response.template_id,
            user_msg=list(response.user_msg),
            rp_code=list(response.rp_code)
        )
        
        if login_result.success:
            # Successful login - extract additional fields
            login_result.fcm_id = getattr(response, 'fcm_id', None)
            login_result.ib_id = getattr(response, 'ib_id', None)
            login_result.country_code = getattr(response, 'country_code', None)
            login_result.state_code = getattr(response, 'state_code', None)
            login_result.heartbeat_interval = getattr(response, 'heartbeat_interval', None)
            login_result.unique_user_id = getattr(response, 'unique_user_id', None)
            
            self.authenticated = True
            self.login_responses[infra_type] = login_result
            
            logger.info(f"Successfully authenticated to {self._get_infra_name(infra_type)}")
            logger.info(f"Heartbeat interval: {login_result.heartbeat_interval} seconds")
            
            # Start heartbeat if not already running
            if not self.heartbeat_enabled and login_result.heartbeat_interval:
                await self._start_heartbeat(login_result.heartbeat_interval)
        else:
            # Failed login
            error_code = response.rp_code[0] if response.rp_code else "Unknown"
            error_text = response.rp_code[1] if len(response.rp_code) > 1 else "Unknown error"
            login_result.error_message = f"Login failed: {error_code} - {error_text}"
            logger.error(login_result.error_message)
        
        return login_result
    
    async def logout(self) -> bool:
        """
        Send logout request to terminate the session.
        
        Returns:
            bool: True if logout sent successfully
        """
        if not self.connection.is_connected():
            logger.warning("Cannot logout: connection not established")
            return False
        
        request = request_logout_pb2.RequestLogout()
        request.template_id = TemplateIDs.LOGOUT_REQUEST
        request.user_msg.append("Logout request")
        
        serialized = request.SerializeToString()
        logger.info("Sending logout request")
        
        success = await self.connection.send_message(serialized)
        if success:
            self.authenticated = False
            self.login_responses.clear()
            self.heartbeat_enabled = False
        
        return success
    
    async def send_heartbeat(self) -> bool:
        """
        Send a heartbeat request to maintain the connection.
        
        Returns:
            bool: True if heartbeat sent successfully
        """
        if not self.connection.is_connected():
            return False
        
        request = request_heartbeat_pb2.RequestHeartbeat()
        request.template_id = TemplateIDs.HEARTBEAT_REQUEST
        
        serialized = request.SerializeToString()
        return await self.connection.send_message(serialized)
    
    async def _start_heartbeat(self, interval: int) -> None:
        """Start automatic heartbeat sending."""
        await self.connection.start_heartbeat(self.send_heartbeat)
        self.heartbeat_enabled = True
    
    def is_authenticated(self, infra_type: Optional[int] = None) -> bool:
        """
        Check if authenticated to a specific infrastructure plant.
        
        Args:
            infra_type: Optional infrastructure plant type to check
            
        Returns:
            bool: True if authenticated
        """
        if not self.authenticated:
            return False
        
        if infra_type is None:
            return len(self.login_responses) > 0
        
        return infra_type in self.login_responses
    
    def get_login_response(self, infra_type: int) -> Optional[LoginResponse]:
        """Get login response for a specific infrastructure plant."""
        return self.login_responses.get(infra_type)
    
    def _get_infra_name(self, infra_type: int) -> str:
        """Get human-readable name for infrastructure plant type."""
        names = {
            InfraType.TICKER_PLANT: "Ticker Plant",
            InfraType.ORDER_PLANT: "Order Plant", 
            InfraType.HISTORY_PLANT: "History Plant",
            InfraType.PNL_PLANT: "PnL Plant",
            InfraType.REPOSITORY_PLANT: "Repository Plant"
        }
        return names.get(infra_type, f"Unknown Plant ({infra_type})")

async def create_authenticated_connection(infra_type: int, 
                                        plant_name: Optional[str] = None,
                                        uri: Optional[str] = None,
                                        system_name: Optional[str] = None,
                                        username: Optional[str] = None,
                                        password: Optional[str] = None) -> tuple[Optional[RithmicConnection], Optional[RithmicAuthenticator]]:
    """
    Create and authenticate a connection to a Rithmic infrastructure plant.
    
    Args:
        infra_type: Infrastructure plant type
        plant_name: Optional plant name for connection pool
        uri: Optional custom URI
        system_name: Optional system name override
        username: Optional username override  
        password: Optional password override
        
    Returns:
        tuple: (RithmicConnection, RithmicAuthenticator) or (None, None) if failed
    """
    from connection import get_connection
    
    # Get connection
    plant_name = plant_name or f"plant_{infra_type}"
    connection = await get_connection(plant_name, uri)
    if not connection:
        logger.error("Failed to establish connection")
        return None, None
    
    # Authenticate
    authenticator = RithmicAuthenticator(connection)
    login_response = await authenticator.login(infra_type, system_name, username, password)
    
    if not login_response.success:
        logger.error(f"Authentication failed: {login_response.error_message}")
        await connection.disconnect()
        return None, None
    
    return connection, authenticator