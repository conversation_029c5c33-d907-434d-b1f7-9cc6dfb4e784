#!/usr/bin/env python3

"""
Advanced timeout management for Rithmic API endpoint testing.

This module provides intelligent timeout strategies, progressive timeout escalation,
and comprehensive timeout diagnostics to resolve connection timeout issues.
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable, Awaitable
from dataclasses import dataclass
from contextlib import asynccontextmanager

from config import get_config, get_endpoint_timeout, get_timeout_strategy, log_timeout_diagnostics, InfraType
from timeout_analytics import get_timeout_analytics

logger = logging.getLogger(__name__)

@dataclass
class TimeoutResult:
    """Result of a timeout-managed operation."""
    success: bool
    result: Any = None
    elapsed_time: float = 0.0
    timeout_used: int = 0
    attempt_number: int = 1
    error_message: str = ""

class TimeoutManager:
    """Advanced timeout management with progressive escalation and diagnostics."""
    
    def __init__(self, endpoint_name: str, plant_type: int = None, enable_analytics: bool = True):
        self.endpoint_name = endpoint_name
        self.plant_type = plant_type
        self.config = get_config()
        self.enable_analytics = enable_analytics
        self.analytics = get_timeout_analytics() if enable_analytics else None
        
        # Use analytics-optimized timeout strategy if available
        if self.analytics and self.analytics._has_sufficient_data(endpoint_name, plant_type):
            self.timeout_strategy = self.analytics.get_timeout_strategy(endpoint_name, plant_type)
            print(f"🧠 Using analytics-optimized timeout strategy for {endpoint_name}")
        else:
            self.timeout_strategy = get_timeout_strategy(endpoint_name, plant_type)
            print(f"⚙️  Using default timeout strategy for {endpoint_name}")
        
    async def execute_with_progressive_timeout(self, 
                                              operation: Callable[[], Awaitable[Any]],
                                              operation_name: str = "operation") -> TimeoutResult:
        """
        Execute an operation with progressive timeout escalation.
        
        Args:
            operation: Async function to execute
            operation_name: Name of the operation for logging
            
        Returns:
            TimeoutResult with success status and timing information
        """
        print(f"⏱️  TIMEOUT STRATEGY: {self.endpoint_name}")
        print(f"   Base Timeout: {self.timeout_strategy['base_timeout']}s")
        print(f"   Max Attempts: {self.timeout_strategy['max_attempts']}")
        if self.timeout_strategy['progressive_enabled']:
            print(f"   Progressive Timeouts: {self.timeout_strategy['timeouts_by_attempt']}")
        print()
        
        last_error = None
        
        for attempt in range(1, self.timeout_strategy['max_attempts'] + 1):
            timeout_value = self.timeout_strategy['timeouts_by_attempt'][attempt - 1]
            
            print(f"🔄 Attempt {attempt}/{self.timeout_strategy['max_attempts']} - Timeout: {timeout_value}s")
            
            start_time = time.time()
            
            try:
                result = await asyncio.wait_for(operation(), timeout=timeout_value)
                elapsed_time = time.time() - start_time
                
                # Log successful operation
                log_timeout_diagnostics(
                    self.endpoint_name, 
                    self.plant_type, 
                    timeout_value, 
                    True, 
                    elapsed_time
                )
                
                # Record analytics data
                if self.analytics:
                    self.analytics.record_operation(
                        self.endpoint_name,
                        self.plant_type,
                        timeout_value,
                        True,
                        elapsed_time,
                        attempt
                    )
                
                print(f"✅ {operation_name} completed successfully in {elapsed_time:.2f}s")
                if self.analytics:
                    efficiency = elapsed_time / timeout_value
                    print(f"📊 Timeout efficiency: {efficiency:.1%} (used {elapsed_time:.1f}s of {timeout_value}s)")
                
                return TimeoutResult(
                    success=True,
                    result=result,
                    elapsed_time=elapsed_time,
                    timeout_used=timeout_value,
                    attempt_number=attempt
                )
                
            except asyncio.TimeoutError as e:
                elapsed_time = time.time() - start_time
                last_error = e
                
                # Log timeout failure
                log_timeout_diagnostics(
                    self.endpoint_name,
                    self.plant_type, 
                    timeout_value,
                    False,
                    elapsed_time
                )
                
                # Record analytics data
                if self.analytics:
                    self.analytics.record_operation(
                        self.endpoint_name,
                        self.plant_type,
                        timeout_value,
                        False,
                        elapsed_time,
                        attempt,
                        "TimeoutError"
                    )
                
                print(f"⏰ Attempt {attempt} timed out after {timeout_value}s")
                
                # If not the last attempt, continue with escalated timeout
                if attempt < self.timeout_strategy['max_attempts']:
                    next_timeout = self.timeout_strategy['timeouts_by_attempt'][attempt]
                    print(f"   Escalating to {next_timeout}s timeout for next attempt...")
                    
                    # Brief delay between attempts
                    await asyncio.sleep(self.config.retry_base_delay)
                
            except Exception as e:
                elapsed_time = time.time() - start_time
                last_error = e
                
                # Record analytics data for non-timeout errors
                if self.analytics:
                    self.analytics.record_operation(
                        self.endpoint_name,
                        self.plant_type,
                        timeout_value,
                        False,
                        elapsed_time,
                        attempt,
                        type(e).__name__
                    )
                
                print(f"❌ {operation_name} failed with error: {e}")
                
                return TimeoutResult(
                    success=False,
                    elapsed_time=elapsed_time,
                    timeout_used=timeout_value,
                    attempt_number=attempt,
                    error_message=str(e)
                )
        
        # All attempts failed
        print(f"💥 All {self.timeout_strategy['max_attempts']} attempts failed")
        self._provide_timeout_troubleshooting()
        
        return TimeoutResult(
            success=False,
            timeout_used=self.timeout_strategy['timeouts_by_attempt'][-1],
            attempt_number=self.timeout_strategy['max_attempts'],
            error_message=f"All timeout attempts failed. Last error: {last_error}"
        )
    
    def _provide_timeout_troubleshooting(self):
        """Provide comprehensive timeout troubleshooting guidance."""
        plant_name = InfraType.get_plant_name(self.plant_type) if self.plant_type else "Unknown"
        
        print(f"\n🔧 TIMEOUT TROUBLESHOOTING FOR {self.endpoint_name}:")
        print(f"   Plant Type: {plant_name}")
        print(f"   Endpoint: {self.endpoint_name}")
        print()
        
        # Plant-specific troubleshooting
        if self.plant_type == InfraType.ORDER_PLANT:
            print("📋 ORDER PLANT SPECIFIC GUIDANCE:")
            print("   • Verify account credentials have Order Plant access")
            print("   • Check if FCM/IB/Account IDs are correctly configured")
            print("   • Order Plant operations may require additional permissions")
            print("   • Some endpoints need active trading session")
            
        elif self.plant_type == InfraType.HISTORY_PLANT:
            print("📋 HISTORY PLANT SPECIFIC GUIDANCE:")
            print("   • Historical data requests can be slow for large date ranges")
            print("   • Check if symbol/exchange combination is valid")
            print("   • Verify date ranges contain trading activity")
            print("   • Consider reducing the scope of data request")
            
        elif self.plant_type == InfraType.TICKER_PLANT:
            print("📋 TICKER PLANT SPECIFIC GUIDANCE:")
            print("   • Market data subscriptions require active market hours")
            print("   • Check if symbol is actively traded")
            print("   • Verify exchange permissions")
            print("   • Some symbols may have restricted access")
            
        elif self.plant_type == InfraType.REPOSITORY_PLANT:
            print("📋 REPOSITORY PLANT SPECIFIC GUIDANCE:")
            print("   • Repository access may be limited in paper trading")
            print("   • Check if user agreements exist for account")
            print("   • Verify Repository Plant permissions")
            print("   • Some repository data may not be available")
        
        print("\n🌐 GENERAL TIMEOUT GUIDANCE:")
        print("   • Check network connectivity and latency")
        print("   • Verify SSL certificate is valid and accessible")
        print("   • Consider running during different market hours")
        print("   • Review Rithmic server status and maintenance schedules")
        print("   • Try testing with simpler endpoint first")
        print()

@asynccontextmanager
async def managed_timeout(endpoint_name: str, plant_type: int = None, operation_name: str = "operation"):
    """
    Context manager for timeout-managed operations.
    
    Usage:
        async with managed_timeout("account_rms_info", InfraType.ORDER_PLANT) as tm:
            result = await tm.execute(my_async_operation)
    """
    manager = TimeoutManager(endpoint_name, plant_type)
    
    class ContextualTimeoutManager:
        def __init__(self, timeout_manager):
            self.tm = timeout_manager
            
        async def execute(self, operation: Callable[[], Awaitable[Any]]) -> TimeoutResult:
            return await self.tm.execute_with_progressive_timeout(operation, operation_name)
    
    try:
        yield ContextualTimeoutManager(manager)
    finally:
        # Cleanup if needed
        pass

def create_timeout_wrapper(endpoint_name: str, plant_type: int = None):
    """
    Create a timeout wrapper decorator for async functions.
    
    Usage:
        @create_timeout_wrapper("account_list", InfraType.ORDER_PLANT)
        async def my_operation():
            # Your async code here
            pass
    """
    def decorator(func: Callable[[], Awaitable[Any]]):
        async def wrapper(*args, **kwargs):
            manager = TimeoutManager(endpoint_name, plant_type)
            
            async def operation():
                return await func(*args, **kwargs)
            
            result = await manager.execute_with_progressive_timeout(operation, func.__name__)
            return result
        
        return wrapper
    return decorator

async def test_timeout_configuration():
    """Test and validate timeout configuration for all endpoints."""
    print("🧪 TIMEOUT CONFIGURATION TEST")
    print("=" * 50)
    
    config = get_config()
    
    # Test each plant type
    plant_types = [
        (InfraType.TICKER_PLANT, "Ticker Plant"),
        (InfraType.ORDER_PLANT, "Order Plant"),
        (InfraType.HISTORY_PLANT, "History Plant"),
        (InfraType.PNL_PLANT, "PnL Plant"),
        (InfraType.REPOSITORY_PLANT, "Repository Plant")
    ]
    
    for plant_type, plant_name in plant_types:
        print(f"\n📡 {plant_name} Timeout Configuration:")
        
        # Test a few representative endpoints
        test_endpoints = list(config.endpoint_timeouts.keys())[:3]
        
        for endpoint in test_endpoints:
            strategy = get_timeout_strategy(endpoint, plant_type)
            print(f"   {endpoint}:")
            print(f"     Base: {strategy['base_timeout']}s")
            print(f"     Progressive: {strategy['timeouts_by_attempt']}")
    
    print("\n✅ Timeout configuration test completed")

if __name__ == "__main__":
    # Run timeout configuration test
    asyncio.run(test_timeout_configuration())