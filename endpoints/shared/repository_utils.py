#!/usr/bin/env python3

"""
Repository Plant utilities for enhanced error handling and paper trading awareness.

This module provides intelligent handling of Repository Plant responses,
with awareness of paper trading limitations and graceful fallbacks.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RepositoryCapabilities:
    """Repository Plant capability detection results."""
    authentication_working: bool
    list_agreements_available: bool
    show_agreements_available: bool
    paper_trading_detected: bool
    last_error: Optional[str] = None

class RepositoryResponseHandler:
    """Enhanced Repository Plant response handler with paper trading awareness."""
    
    # Known error codes and their meanings
    ERROR_CODES = {
        "0": "Success",
        "7": "No data available", 
        "1100": "Agreement ID is not set",
        "1039": "FCM ID field is not received",
        "1041": "User Type field is not received"
    }
    
    # Paper trading limitation indicators
    PAPER_TRADING_INDICATORS = {
        "7": ["no data", "empty", "not found"],
        "1100": ["agreement id", "not set", "missing"]
    }
    
    def __init__(self, config):
        """Initialize with configuration."""
        self.config = config
        self.paper_trading = config.paper_trading_only
    
    def interpret_response(self, response_code: list, template_name: str = "") -> Dict[str, Any]:
        """
        Interpret Repository Plant response with context awareness.
        
        Args:
            response_code: Response code list from protobuf message
            template_name: Name of the template/operation being performed
            
        Returns:
            Dict containing interpretation results
        """
        result = {
            "success": False,
            "status": "unknown",
            "message": "",
            "error_code": None,
            "error_message": None,
            "paper_trading_limitation": False,
            "user_action_required": False,
            "troubleshooting": []
        }
        
        # Handle empty response codes
        if not response_code:
            result.update({
                "success": True,
                "status": "success",
                "message": "Operation completed successfully (empty response code)"
            })
            return result
        
        # Handle single success code
        if len(response_code) == 1 and response_code[0] == "0":
            result.update({
                "success": True,
                "status": "success", 
                "message": "Operation completed successfully"
            })
            return result
        
        # Handle error responses
        if len(response_code) >= 1:
            error_code = response_code[0]
            error_message = response_code[1] if len(response_code) >= 2 else ""
            
            result.update({
                "error_code": error_code,
                "error_message": error_message,
                "message": f"Error {error_code}: {error_message}"
            })
            
            # Special handling for known Repository Plant scenarios
            if error_code == "7" and "no data" in error_message.lower():
                if self.paper_trading:
                    result.update({
                        "success": True,  # This is actually expected in paper trading
                        "status": "paper_trading_limitation",
                        "message": "No user agreements in paper trading environment (expected behavior)",
                        "paper_trading_limitation": True,
                        "troubleshooting": [
                            "This is normal behavior in paper trading environments",
                            "Paper trading accounts have no user agreements to manage",
                            "User agreements are pre-configured at account setup level"
                        ]
                    })
                else:
                    result.update({
                        "status": "no_data",
                        "message": "No agreement data found for this account",
                        "troubleshooting": [
                            "Check if account has any user agreements",
                            "Verify account permissions for agreement access",
                            "Contact support if agreements are expected"
                        ]
                    })
            
            elif error_code == "1100" and "agreement id" in error_message.lower():
                result.update({
                    "status": "missing_parameter",
                    "message": "Agreement ID required for show agreement operation",
                    "user_action_required": True,
                    "troubleshooting": [
                        "Use --agreement-id parameter to specify which agreement to show",
                        "First run list agreements to see available agreement IDs",
                        "Example: python test_show_agreement.py --agreement-id 'RithmicAgreement1'"
                    ]
                })
            
            elif error_code in ["1039", "1041"]:
                result.update({
                    "status": "authentication_field_missing",
                    "message": f"Required authentication field missing: {error_message}",
                    "troubleshooting": [
                        "Verify FCM ID and User Type are properly set",
                        "Check paper trading credentials configuration",
                        "Ensure authentication fields are not empty"
                    ]
                })
            
            else:
                # Generic error handling
                result.update({
                    "status": "error",
                    "message": f"Repository Plant error: {error_code} - {error_message}",
                    "troubleshooting": [
                        "Check Repository Plant connection and authentication",
                        "Verify account permissions for Repository Plant access",
                        "Review error code in Rithmic API documentation"
                    ]
                })
        
        return result
    
    def display_repository_status(self, response_result: Dict[str, Any], 
                                 operation_name: str = "Repository Operation"):
        """Display user-friendly Repository Plant status information."""
        
        print(f"\n📜 {operation_name.upper()} STATUS:")
        print("-" * 50)
        
        if response_result["success"]:
            if response_result.get("paper_trading_limitation"):
                print("✅ Paper Trading Limitation Detected (Normal)")
                print(f"   {response_result['message']}")
                print("\n💡 PAPER TRADING INFO:")
                for tip in response_result["troubleshooting"]:
                    print(f"   • {tip}")
            else:
                print("✅ Operation Successful")
                print(f"   {response_result['message']}")
        
        else:
            status_emoji = "⚠️" if response_result.get("user_action_required") else "❌"
            print(f"{status_emoji} Operation Status: {response_result['status'].replace('_', ' ').title()}")
            print(f"   {response_result['message']}")
            
            if response_result["troubleshooting"]:
                print("\n🔧 TROUBLESHOOTING:")
                for tip in response_result["troubleshooting"]:
                    print(f"   • {tip}")
    
    def is_paper_trading_response(self, response_code: list) -> bool:
        """Check if response indicates paper trading limitation."""
        if not response_code:
            return False
        
        error_code = response_code[0]
        error_message = response_code[1] if len(response_code) >= 2 else ""
        
        return (self.paper_trading and 
                error_code == "7" and 
                "no data" in error_message.lower())

async def detect_repository_capabilities(connection) -> RepositoryCapabilities:
    """
    Detect Repository Plant capabilities and limitations.
    
    Args:
        connection: Active Repository Plant connection
        
    Returns:
        RepositoryCapabilities: Detected capabilities
    """
    capabilities = RepositoryCapabilities(
        authentication_working=False,
        list_agreements_available=False,
        show_agreements_available=False,
        paper_trading_detected=False
    )
    
    try:
        # If we have an active connection, authentication is working
        if connection:
            capabilities.authentication_working = True
            logger.info("Repository Plant authentication detected as working")
        
        # Additional capability detection would go here
        # For now, we assume limited capabilities in paper trading
        
    except Exception as e:
        capabilities.last_error = str(e)
        logger.warning(f"Repository Plant capability detection failed: {e}")
    
    return capabilities

def create_repository_response_handler(config):
    """Create a Repository Plant response handler with configuration."""
    return RepositoryResponseHandler(config)

def log_repository_diagnostics(response_result: Dict[str, Any], operation: str):
    """Log detailed Repository Plant diagnostics."""
    logger.info(f"Repository Plant {operation} Results:")
    logger.info(f"  Status: {response_result['status']}")
    logger.info(f"  Success: {response_result['success']}")
    logger.info(f"  Message: {response_result['message']}")
    
    if response_result.get("error_code"):
        logger.info(f"  Error Code: {response_result['error_code']}")
        logger.info(f"  Error Message: {response_result['error_message']}")
    
    if response_result.get("paper_trading_limitation"):
        logger.info("  Paper Trading Limitation: Detected")
    
    if response_result.get("user_action_required"):
        logger.info("  User Action Required: Yes")