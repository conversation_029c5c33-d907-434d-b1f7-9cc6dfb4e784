#!/usr/bin/env python3

"""
Advanced timeout analytics and optimization for Rithmic API endpoint testing.

This module provides intelligent timeout learning, performance tracking, and
automatic timeout optimization based on real endpoint behavior patterns.
"""

import json
import time
import statistics
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

from config import get_config, InfraType

@dataclass
class TimeoutRecord:
    """Record of a timeout operation with performance metrics."""
    endpoint_name: str
    plant_type: int
    timeout_used: int
    success: bool
    elapsed_time: float
    attempt_number: int
    timestamp: str
    error_type: Optional[str] = None
    network_conditions: Optional[str] = None

@dataclass
class EndpointStats:
    """Statistical analysis for an endpoint."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    success_rate: float = 0.0
    avg_response_time: float = 0.0
    median_response_time: float = 0.0
    p95_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    recommended_timeout: int = 0
    timeout_efficiency: float = 0.0

class TimeoutAnalytics:
    """Advanced timeout analytics and optimization system."""
    
    def __init__(self, data_file: str = None):
        self.config = get_config()
        self.data_file = data_file or "timeout_analytics.json"
        self.records: List[TimeoutRecord] = []
        self.endpoint_stats: Dict[str, EndpointStats] = {}
        self.recent_records = deque(maxlen=1000)  # Keep recent records in memory
        self.load_historical_data()
    
    def record_operation(self, endpoint_name: str, plant_type: int, timeout_used: int,
                        success: bool, elapsed_time: float, attempt_number: int = 1,
                        error_type: str = None) -> None:
        """Record a timeout operation with performance metrics."""
        record = TimeoutRecord(
            endpoint_name=endpoint_name,
            plant_type=plant_type,
            timeout_used=timeout_used,
            success=success,
            elapsed_time=elapsed_time,
            attempt_number=attempt_number,
            timestamp=datetime.now().isoformat(),
            error_type=error_type,
            network_conditions=self._assess_network_conditions()
        )
        
        self.records.append(record)
        self.recent_records.append(record)
        
        # Update statistics incrementally
        self._update_endpoint_stats(record)
        
        # Periodically save data
        if len(self.recent_records) % 10 == 0:
            self.save_data()
    
    def get_optimized_timeout(self, endpoint_name: str, plant_type: int = None,
                             confidence_level: float = 0.95) -> int:
        """Get optimized timeout based on historical performance data."""
        endpoint_key = self._get_endpoint_key(endpoint_name, plant_type)
        
        if endpoint_key not in self.endpoint_stats:
            # No historical data - use configured defaults
            from config import get_endpoint_timeout
            return get_endpoint_timeout(endpoint_name, plant_type)
        
        stats = self.endpoint_stats[endpoint_key]
        
        # Use percentile-based approach for timeout calculation
        if confidence_level >= 0.99:
            timeout = int(stats.p95_response_time * 2.0)  # Very conservative
        elif confidence_level >= 0.95:
            timeout = int(stats.p95_response_time * 1.5)  # Conservative
        elif confidence_level >= 0.90:
            timeout = int(stats.median_response_time * 2.0)  # Balanced
        else:
            timeout = int(stats.avg_response_time * 1.8)  # Aggressive
        
        # Apply bounds checking
        min_timeout = max(5, int(stats.min_response_time * 1.2))
        max_timeout = min(120, int(stats.max_response_time * 2.0))
        
        return max(min_timeout, min(timeout, max_timeout))
    
    def get_timeout_strategy(self, endpoint_name: str, plant_type: int = None) -> Dict[str, Any]:
        """Get comprehensive timeout strategy with progressive escalation."""
        base_timeout = self.get_optimized_timeout(endpoint_name, plant_type, 0.90)
        conservative_timeout = self.get_optimized_timeout(endpoint_name, plant_type, 0.95)
        aggressive_timeout = self.get_optimized_timeout(endpoint_name, plant_type, 0.99)
        
        return {
            "base_timeout": base_timeout,
            "progressive_timeouts": [base_timeout, conservative_timeout, aggressive_timeout],
            "max_attempts": 3,
            "confidence_levels": [0.90, 0.95, 0.99],
            "optimization_source": "analytics" if self._has_sufficient_data(endpoint_name, plant_type) else "defaults"
        }
    
    def analyze_endpoint_performance(self, endpoint_name: str = None, 
                                   plant_type: int = None) -> Dict[str, Any]:
        """Analyze performance for specific endpoint or all endpoints."""
        if endpoint_name:
            return self._analyze_single_endpoint(endpoint_name, plant_type)
        else:
            return self._analyze_all_endpoints()
    
    def generate_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive timeout optimization report."""
        report = {
            "report_generated": datetime.now().isoformat(),
            "total_records": len(self.records),
            "analysis_period": self._get_analysis_period(),
            "summary": self._generate_summary_stats(),
            "plant_analysis": self._analyze_by_plant(),
            "endpoint_performance": {},
            "optimization_recommendations": [],
            "timeout_efficiency": self._calculate_overall_efficiency()
        }
        
        # Add endpoint-specific analysis
        for endpoint_key, stats in self.endpoint_stats.items():
            endpoint_name, plant_type = self._parse_endpoint_key(endpoint_key)
            report["endpoint_performance"][endpoint_key] = {
                "endpoint_name": endpoint_name,
                "plant_type": InfraType.get_plant_name(plant_type) if plant_type else "Unknown",
                "stats": asdict(stats),
                "optimization_impact": self._calculate_optimization_impact(endpoint_name, plant_type)
            }
        
        # Generate optimization recommendations
        report["optimization_recommendations"] = self._generate_optimization_recommendations()
        
        return report
    
    def load_historical_data(self) -> None:
        """Load historical timeout data from file."""
        try:
            data_path = Path(self.data_file)
            if data_path.exists():
                with open(data_path, 'r') as f:
                    data = json.load(f)
                    
                # Load records
                for record_data in data.get("records", []):
                    record = TimeoutRecord(**record_data)
                    self.records.append(record)
                    self.recent_records.append(record)
                
                # Rebuild statistics
                self._rebuild_statistics()
                
                print(f"📊 Loaded {len(self.records)} historical timeout records")
        except Exception as e:
            print(f"⚠️  Could not load historical data: {e}")
    
    def save_data(self) -> None:
        """Save timeout data to file."""
        try:
            data = {
                "last_updated": datetime.now().isoformat(),
                "records": [asdict(record) for record in self.records[-1000:]],  # Keep last 1000 records
                "endpoint_stats": {k: asdict(v) for k, v in self.endpoint_stats.items()}
            }
            
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"⚠️  Could not save timeout data: {e}")
    
    def _get_endpoint_key(self, endpoint_name: str, plant_type: int = None) -> str:
        """Generate unique key for endpoint."""
        return f"{endpoint_name}_{plant_type}" if plant_type else f"{endpoint_name}_unknown"
    
    def _parse_endpoint_key(self, endpoint_key: str) -> Tuple[str, int]:
        """Parse endpoint key back to components."""
        parts = endpoint_key.rsplit('_', 1)
        endpoint_name = parts[0]
        plant_type = int(parts[1]) if parts[1] != 'unknown' else None
        return endpoint_name, plant_type
    
    def _update_endpoint_stats(self, record: TimeoutRecord) -> None:
        """Update endpoint statistics with new record."""
        endpoint_key = self._get_endpoint_key(record.endpoint_name, record.plant_type)
        
        if endpoint_key not in self.endpoint_stats:
            self.endpoint_stats[endpoint_key] = EndpointStats()
        
        stats = self.endpoint_stats[endpoint_key]
        
        # Update counters
        stats.total_calls += 1
        if record.success:
            stats.successful_calls += 1
        else:
            stats.failed_calls += 1
        
        # Calculate success rate
        stats.success_rate = stats.successful_calls / stats.total_calls
        
        # Update response time statistics (only for successful calls)
        if record.success:
            # Get recent successful response times for this endpoint
            recent_times = [
                r.elapsed_time for r in self.records
                if r.endpoint_name == record.endpoint_name 
                and r.plant_type == record.plant_type
                and r.success
                and datetime.fromisoformat(r.timestamp) > datetime.now() - timedelta(days=7)
            ]
            
            if recent_times:
                stats.avg_response_time = statistics.mean(recent_times)
                stats.median_response_time = statistics.median(recent_times)
                stats.min_response_time = min(recent_times)
                stats.max_response_time = max(recent_times)
                
                # Calculate 95th percentile
                stats.p95_response_time = self._calculate_percentile(recent_times, 0.95)
                
                # Calculate recommended timeout (conservative approach)
                stats.recommended_timeout = int(stats.p95_response_time * 1.5)
                
                # Calculate timeout efficiency
                avg_timeout_used = statistics.mean([
                    r.timeout_used for r in self.records
                    if r.endpoint_name == record.endpoint_name 
                    and r.plant_type == record.plant_type
                    and r.success
                ])
                stats.timeout_efficiency = stats.avg_response_time / avg_timeout_used if avg_timeout_used > 0 else 0
    
    def _rebuild_statistics(self) -> None:
        """Rebuild all endpoint statistics from scratch."""
        self.endpoint_stats = {}
        for record in self.records:
            self._update_endpoint_stats(record)
    
    def _calculate_percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of data."""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _assess_network_conditions(self) -> str:
        """Assess current network conditions (simplified)."""
        # This could be enhanced with actual network monitoring
        return "normal"
    
    def _has_sufficient_data(self, endpoint_name: str, plant_type: int = None) -> bool:
        """Check if we have sufficient data for optimization."""
        endpoint_key = self._get_endpoint_key(endpoint_name, plant_type)
        return (endpoint_key in self.endpoint_stats and 
                self.endpoint_stats[endpoint_key].total_calls >= 5)
    
    def _analyze_single_endpoint(self, endpoint_name: str, plant_type: int = None) -> Dict[str, Any]:
        """Analyze performance for a single endpoint."""
        endpoint_key = self._get_endpoint_key(endpoint_name, plant_type)
        
        if endpoint_key not in self.endpoint_stats:
            return {"error": "No data available for this endpoint"}
        
        stats = self.endpoint_stats[endpoint_key]
        
        # Get recent records for trend analysis
        recent_records = [
            r for r in self.records
            if r.endpoint_name == endpoint_name 
            and r.plant_type == plant_type
            and datetime.fromisoformat(r.timestamp) > datetime.now() - timedelta(days=7)
        ]
        
        return {
            "endpoint_name": endpoint_name,
            "plant_type": InfraType.get_plant_name(plant_type) if plant_type else "Unknown",
            "statistics": asdict(stats),
            "recent_performance": self._analyze_recent_performance(recent_records),
            "optimization_suggestions": self._get_optimization_suggestions(stats)
        }
    
    def _analyze_all_endpoints(self) -> Dict[str, Any]:
        """Analyze performance across all endpoints."""
        return {
            "total_endpoints": len(self.endpoint_stats),
            "overall_success_rate": self._calculate_overall_success_rate(),
            "best_performing": self._get_best_performing_endpoints(),
            "worst_performing": self._get_worst_performing_endpoints(),
            "timeout_distribution": self._analyze_timeout_distribution()
        }
    
    def _get_analysis_period(self) -> Dict[str, str]:
        """Get the analysis period."""
        if not self.records:
            return {"start": "N/A", "end": "N/A"}
        
        start_time = min(datetime.fromisoformat(r.timestamp) for r in self.records)
        end_time = max(datetime.fromisoformat(r.timestamp) for r in self.records)
        
        return {
            "start": start_time.isoformat(),
            "end": end_time.isoformat(),
            "duration_days": (end_time - start_time).days
        }
    
    def _generate_summary_stats(self) -> Dict[str, Any]:
        """Generate overall summary statistics."""
        if not self.records:
            return {}
        
        total_calls = len(self.records)
        successful_calls = sum(1 for r in self.records if r.success)
        
        return {
            "total_operations": total_calls,
            "successful_operations": successful_calls,
            "failed_operations": total_calls - successful_calls,
            "overall_success_rate": successful_calls / total_calls,
            "unique_endpoints": len(self.endpoint_stats),
            "avg_response_time": statistics.mean([r.elapsed_time for r in self.records if r.success])
        }
    
    def _analyze_by_plant(self) -> Dict[str, Any]:
        """Analyze performance by infrastructure plant."""
        plant_stats = defaultdict(lambda: {"calls": 0, "successes": 0, "response_times": []})
        
        for record in self.records:
            plant_name = InfraType.get_plant_name(record.plant_type) if record.plant_type else "Unknown"
            plant_stats[plant_name]["calls"] += 1
            if record.success:
                plant_stats[plant_name]["successes"] += 1
                plant_stats[plant_name]["response_times"].append(record.elapsed_time)
        
        result = {}
        for plant_name, stats in plant_stats.items():
            result[plant_name] = {
                "total_calls": stats["calls"],
                "success_rate": stats["successes"] / stats["calls"] if stats["calls"] > 0 else 0,
                "avg_response_time": statistics.mean(stats["response_times"]) if stats["response_times"] else 0
            }
        
        return result
    
    def _calculate_overall_efficiency(self) -> float:
        """Calculate overall timeout efficiency."""
        if not self.endpoint_stats:
            return 0.0
        
        efficiencies = [stats.timeout_efficiency for stats in self.endpoint_stats.values() 
                       if stats.timeout_efficiency > 0]
        return statistics.mean(efficiencies) if efficiencies else 0.0
    
    def _calculate_optimization_impact(self, endpoint_name: str, plant_type: int = None) -> Dict[str, Any]:
        """Calculate potential optimization impact."""
        endpoint_key = self._get_endpoint_key(endpoint_name, plant_type)
        if endpoint_key not in self.endpoint_stats:
            return {}
        
        stats = self.endpoint_stats[endpoint_key]
        current_timeout = stats.recommended_timeout
        
        from config import get_endpoint_timeout
        default_timeout = get_endpoint_timeout(endpoint_name, plant_type)
        
        return {
            "current_recommended": current_timeout,
            "default_timeout": default_timeout,
            "optimization_factor": current_timeout / default_timeout if default_timeout > 0 else 1.0,
            "potential_time_savings": max(0, default_timeout - current_timeout),
            "confidence_level": "high" if stats.total_calls >= 10 else "low"
        }
    
    def _generate_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Generate optimization recommendations."""
        recommendations = []
        
        for endpoint_key, stats in self.endpoint_stats.items():
            endpoint_name, plant_type = self._parse_endpoint_key(endpoint_key)
            
            if stats.total_calls >= 5:  # Only recommend if we have sufficient data
                
                # High failure rate recommendation
                if stats.success_rate < 0.8:
                    recommendations.append({
                        "type": "timeout_increase",
                        "endpoint": endpoint_name,
                        "plant": InfraType.get_plant_name(plant_type) if plant_type else "Unknown",
                        "current_success_rate": stats.success_rate,
                        "recommendation": f"Increase timeout to {int(stats.recommended_timeout * 1.3)}s",
                        "reason": "Low success rate indicates timeouts may be too aggressive"
                    })
                
                # Low efficiency recommendation
                if stats.timeout_efficiency < 0.5:
                    recommendations.append({
                        "type": "timeout_decrease",
                        "endpoint": endpoint_name,
                        "plant": InfraType.get_plant_name(plant_type) if plant_type else "Unknown", 
                        "current_efficiency": stats.timeout_efficiency,
                        "recommendation": f"Decrease timeout to {int(stats.recommended_timeout * 0.8)}s",
                        "reason": "Low efficiency indicates timeouts may be too conservative"
                    })
        
        return recommendations
    
    def _analyze_recent_performance(self, recent_records: List[TimeoutRecord]) -> Dict[str, Any]:
        """Analyze recent performance trends."""
        if not recent_records:
            return {}
        
        return {
            "recent_calls": len(recent_records),
            "recent_success_rate": sum(1 for r in recent_records if r.success) / len(recent_records),
            "trend": "improving" if len(recent_records) >= 2 and recent_records[-1].success else "stable"
        }
    
    def _get_optimization_suggestions(self, stats: EndpointStats) -> List[str]:
        """Get optimization suggestions for an endpoint."""
        suggestions = []
        
        if stats.success_rate < 0.9:
            suggestions.append("Consider increasing timeout - success rate is below 90%")
        
        if stats.timeout_efficiency < 0.6:
            suggestions.append("Consider decreasing timeout - efficiency is low")
        
        if stats.total_calls < 10:
            suggestions.append("Collect more data for better optimization recommendations")
        
        return suggestions
    
    def _calculate_overall_success_rate(self) -> float:
        """Calculate overall success rate across all endpoints."""
        if not self.endpoint_stats:
            return 0.0
        
        total_calls = sum(stats.total_calls for stats in self.endpoint_stats.values())
        total_successes = sum(stats.successful_calls for stats in self.endpoint_stats.values())
        
        return total_successes / total_calls if total_calls > 0 else 0.0
    
    def _get_best_performing_endpoints(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get best performing endpoints by success rate."""
        endpoint_performance = []
        
        for endpoint_key, stats in self.endpoint_stats.items():
            if stats.total_calls >= 3:  # Minimum calls for consideration
                endpoint_name, plant_type = self._parse_endpoint_key(endpoint_key)
                endpoint_performance.append({
                    "endpoint": endpoint_name,
                    "plant": InfraType.get_plant_name(plant_type) if plant_type else "Unknown",
                    "success_rate": stats.success_rate,
                    "total_calls": stats.total_calls
                })
        
        return sorted(endpoint_performance, key=lambda x: x["success_rate"], reverse=True)[:limit]
    
    def _get_worst_performing_endpoints(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get worst performing endpoints by success rate."""
        return list(reversed(self._get_best_performing_endpoints(len(self.endpoint_stats))))[:limit]
    
    def _analyze_timeout_distribution(self) -> Dict[str, Any]:
        """Analyze distribution of timeout values used."""
        timeouts_used = [r.timeout_used for r in self.records]
        
        if not timeouts_used:
            return {}
        
        return {
            "min_timeout": min(timeouts_used),
            "max_timeout": max(timeouts_used),
            "avg_timeout": statistics.mean(timeouts_used),
            "median_timeout": statistics.median(timeouts_used),
            "most_common": max(set(timeouts_used), key=timeouts_used.count)
        }

# Global analytics instance
_global_analytics = None

def get_timeout_analytics() -> TimeoutAnalytics:
    """Get global timeout analytics instance."""
    global _global_analytics
    if _global_analytics is None:
        _global_analytics = TimeoutAnalytics()
    return _global_analytics

async def run_timeout_optimization_report():
    """Generate and display comprehensive timeout optimization report."""
    analytics = get_timeout_analytics()
    report = analytics.generate_optimization_report()
    
    print("📊 COMPREHENSIVE TIMEOUT OPTIMIZATION REPORT")
    print("=" * 80)
    print(f"Report Generated: {report['report_generated']}")
    print(f"Analysis Period: {report['analysis_period']['duration_days']} days")
    print(f"Total Records: {report['total_records']:,}")
    print()
    
    # Summary statistics
    if report['summary']:
        summary = report['summary']
        print("📈 OVERALL PERFORMANCE:")
        print(f"   Success Rate: {summary['overall_success_rate']:.1%}")
        print(f"   Average Response Time: {summary['avg_response_time']:.2f}s")
        print(f"   Unique Endpoints: {summary['unique_endpoints']}")
        print()
    
    # Plant analysis
    if report['plant_analysis']:
        print("🏭 PERFORMANCE BY PLANT:")
        for plant_name, stats in report['plant_analysis'].items():
            print(f"   {plant_name}: {stats['success_rate']:.1%} success, {stats['avg_response_time']:.2f}s avg")
        print()
    
    # Optimization recommendations
    if report['optimization_recommendations']:
        print("🎯 OPTIMIZATION RECOMMENDATIONS:")
        for rec in report['optimization_recommendations'][:5]:
            print(f"   • {rec['endpoint']} ({rec['plant']}): {rec['recommendation']}")
            print(f"     Reason: {rec['reason']}")
        print()
    
    # Save detailed report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"timeout_optimization_report_{timestamp}.json"
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"💾 Detailed report saved to: {report_file}")
    
    return report

if __name__ == "__main__":
    import asyncio
    asyncio.run(run_timeout_optimization_report())