#!/usr/bin/env python3

"""
Message handling and formatting utilities for Rithmic API endpoints.

This module provides utilities for parsing, formatting, and logging
Rithmic API messages in various output formats.
"""

import json
import logging
import time
import csv
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
import sys

# Add the proto_generated directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'proto_generated'))

import base_pb2
from google.protobuf.message import Message
from google.protobuf.descriptor import FieldDescriptor

from config import get_config, TemplateIDs, is_dangerous_template

logger = logging.getLogger(__name__)

@dataclass
class MessageInfo:
    """Information about a parsed message."""
    template_id: int
    template_name: str
    message_type: str  # "request", "response", "update"
    timestamp: float
    size_bytes: int
    is_dangerous: bool
    fields: Dict[str, Any]
    raw_message: Optional[bytes] = None

class MessageHandler:
    """Handles parsing, formatting, and logging of Rithmic API messages."""
    
    def __init__(self):
        self.config = get_config()
        self.message_count = 0
        self.template_names = self._build_template_name_map()
        
        # Create response directory if needed
        if self.config.save_responses:
            os.makedirs(self.config.response_dir, exist_ok=True)
    
    def parse_message(self, message_bytes: bytes, save_raw: bool = True) -> Optional[MessageInfo]:
        """
        Parse a Rithmic API message from bytes.
        
        Args:
            message_bytes: Raw message bytes
            save_raw: Whether to save raw bytes in MessageInfo
            
        Returns:
            MessageInfo: Parsed message information, or None if parsing failed
        """
        if not message_bytes:
            return None
        
        try:
            # Parse base message to get template ID
            base_msg = base_pb2.Base()
            base_msg.ParseFromString(message_bytes)
            template_id = base_msg.template_id
            
            # Get template information
            template_name = self.template_names.get(template_id, f"Unknown_{template_id}")
            message_type = self._determine_message_type(template_id)
            is_dangerous = is_dangerous_template(template_id)
            
            # Extract all fields from the base message
            fields = self._extract_message_fields(base_msg)
            
            # Create message info
            msg_info = MessageInfo(
                template_id=template_id,
                template_name=template_name,
                message_type=message_type,
                timestamp=time.time(),
                size_bytes=len(message_bytes),
                is_dangerous=is_dangerous,
                fields=fields,
                raw_message=message_bytes if save_raw else None
            )
            
            self.message_count += 1
            
            # Log the message
            if self.config.log_all_messages:
                self._log_message(msg_info)
            
            # Save response if configured
            if self.config.save_responses:
                self._save_message(msg_info)
            
            return msg_info
            
        except Exception as e:
            logger.error(f"Error parsing message: {e}")
            return None
    
    def format_message(self, msg_info: MessageInfo, format_type: Optional[str] = None) -> str:
        """
        Format a message for display.
        
        Args:
            msg_info: Message information to format
            format_type: Output format ("human", "json", "csv")
            
        Returns:
            str: Formatted message string
        """
        format_type = format_type or self.config.output_format
        
        if format_type == "json":
            return self._format_json(msg_info)
        elif format_type == "csv":
            return self._format_csv(msg_info)
        else:  # default to human-readable
            return self._format_human(msg_info)
    
    def _extract_message_fields(self, message: Message) -> Dict[str, Any]:
        """Extract all fields from a protobuf message."""
        fields = {}
        
        for field, value in message.ListFields():
            field_name = field.name
            
            if field.label == FieldDescriptor.LABEL_REPEATED:
                # Handle repeated fields
                if field.type == FieldDescriptor.TYPE_MESSAGE:
                    fields[field_name] = [self._extract_message_fields(item) for item in value]
                else:
                    fields[field_name] = list(value)
            elif field.type == FieldDescriptor.TYPE_MESSAGE:
                # Handle nested messages
                fields[field_name] = self._extract_message_fields(value)
            else:
                # Handle primitive fields
                fields[field_name] = value
        
        return fields
    
    def _determine_message_type(self, template_id: int) -> str:
        """Determine if message is request, response, or update."""
        if template_id % 2 == 0:
            return "request"
        elif template_id < 100 or (100 <= template_id <= 122 and template_id % 2 == 1):
            return "response"
        else:
            return "update"
    
    def _format_human(self, msg_info: MessageInfo) -> str:
        """Format message in human-readable format."""
        lines = []
        lines.append(f"={'='*60}")
        lines.append(f"Template ID: {msg_info.template_id}")
        lines.append(f"Template Name: {msg_info.template_name}")
        lines.append(f"Message Type: {msg_info.message_type}")
        lines.append(f"Timestamp: {datetime.fromtimestamp(msg_info.timestamp).isoformat()}")
        lines.append(f"Size: {msg_info.size_bytes} bytes")
        
        if msg_info.is_dangerous:
            lines.append(f"⚠️  WARNING: This is a DANGEROUS endpoint!")
        
        lines.append(f"Fields:")
        lines.extend(self._format_fields_human(msg_info.fields, indent=2))
        lines.append(f"={'='*60}")
        
        return "\n".join(lines)
    
    def _format_fields_human(self, fields: Dict[str, Any], indent: int = 0) -> List[str]:
        """Format fields in human-readable format with indentation."""
        lines = []
        prefix = " " * indent
        
        for key, value in fields.items():
            if isinstance(value, dict):
                lines.append(f"{prefix}{key}:")
                lines.extend(self._format_fields_human(value, indent + 2))
            elif isinstance(value, list):
                lines.append(f"{prefix}{key}: [{len(value)} items]")
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        lines.append(f"{prefix}  [{i}]:")
                        lines.extend(self._format_fields_human(item, indent + 4))
                    else:
                        lines.append(f"{prefix}  [{i}]: {item}")
            else:
                lines.append(f"{prefix}{key}: {value}")
        
        return lines
    
    def _format_json(self, msg_info: MessageInfo) -> str:
        """Format message as JSON."""
        data = {
            "template_id": msg_info.template_id,
            "template_name": msg_info.template_name,
            "message_type": msg_info.message_type,
            "timestamp": msg_info.timestamp,
            "size_bytes": msg_info.size_bytes,
            "is_dangerous": msg_info.is_dangerous,
            "fields": msg_info.fields
        }
        return json.dumps(data, indent=2, default=str)
    
    def _format_csv(self, msg_info: MessageInfo) -> str:
        """Format message as CSV (flattened fields)."""
        # Flatten nested fields
        flat_fields = self._flatten_fields(msg_info.fields)
        
        # Create CSV row
        row = {
            "template_id": msg_info.template_id,
            "template_name": msg_info.template_name,
            "message_type": msg_info.message_type,
            "timestamp": msg_info.timestamp,
            "size_bytes": msg_info.size_bytes,
            "is_dangerous": msg_info.is_dangerous,
            **flat_fields
        }
        
        # Convert to CSV format (simplified)
        return ",".join([f'"{v}"' for v in row.values()])
    
    def _flatten_fields(self, fields: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
        """Flatten nested dictionary fields for CSV output."""
        flat = {}
        for key, value in fields.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                flat.update(self._flatten_fields(value, full_key))
            elif isinstance(value, list):
                flat[full_key] = str(value)  # Convert list to string
            else:
                flat[full_key] = value
        
        return flat
    
    def _log_message(self, msg_info: MessageInfo) -> None:
        """Log message information."""
        log_level = logging.WARNING if msg_info.is_dangerous else logging.INFO
        logger.log(log_level, 
                  f"Message {self.message_count}: {msg_info.template_name} "
                  f"({msg_info.template_id}) - {msg_info.size_bytes} bytes")
    
    def _save_message(self, msg_info: MessageInfo) -> None:
        """Save message to file."""
        try:
            timestamp_str = datetime.fromtimestamp(msg_info.timestamp).strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"{timestamp_str}_{msg_info.template_name}_{msg_info.template_id}.json"
            filepath = os.path.join(self.config.response_dir, filename)
            
            # Save as JSON
            data = {
                "template_id": msg_info.template_id,
                "template_name": msg_info.template_name,
                "message_type": msg_info.message_type,
                "timestamp": msg_info.timestamp,
                "size_bytes": msg_info.size_bytes,
                "is_dangerous": msg_info.is_dangerous,
                "fields": msg_info.fields
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error saving message to file: {e}")
    
    def _build_template_name_map(self) -> Dict[int, str]:
        """Build mapping of template IDs to names."""
        names = {}
        
        # Shared templates
        names[10] = "Login_Request"
        names[11] = "Login_Response"
        names[12] = "Logout_Request"
        names[13] = "Logout_Response"
        names[14] = "Reference_Data_Request"
        names[15] = "Reference_Data_Response"
        names[16] = "System_Info_Request"
        names[17] = "System_Info_Response"
        names[18] = "Heartbeat_Request"
        names[19] = "Heartbeat_Response"
        names[20] = "Gateway_Info_Request"
        names[21] = "Gateway_Info_Response"
        
        # Market Data (Ticker Plant)
        names[100] = "Market_Data_Update_Request"
        names[101] = "Market_Data_Update_Response"
        names[102] = "Get_Instrument_By_Underlying_Request"
        names[103] = "Get_Instrument_By_Underlying_Response"
        names[104] = "Get_Instrument_By_Underlying_Keys_Response"
        names[105] = "Market_Data_Update_By_Underlying_Request"
        names[106] = "Market_Data_Update_By_Underlying_Response"
        names[107] = "Give_Tick_Size_Type_Table_Request"
        names[108] = "Give_Tick_Size_Type_Table_Response"
        names[109] = "Search_Symbols_Request"
        names[110] = "Search_Symbols_Response"
        names[111] = "Product_Codes_Request"
        names[112] = "Product_Codes_Response"
        names[113] = "Front_Month_Contract_Request"
        names[114] = "Front_Month_Contract_Response"
        names[115] = "Depth_By_Order_Snapshot_Request"
        names[116] = "Depth_By_Order_Snapshot_Response"
        names[117] = "Depth_By_Order_Updates_Request"
        names[118] = "Depth_By_Order_Updates_Response"
        names[119] = "Get_Volume_At_Price_Request"
        names[120] = "Get_Volume_At_Price_Response"
        names[121] = "Auxilliary_Reference_Data_Request"
        names[122] = "Auxilliary_Reference_Data_Response"
        names[150] = "Last_Trade"
        names[151] = "Best_Bid_Offer"
        names[152] = "Trade_Statistics"
        names[153] = "Quote_Statistics"
        names[154] = "Indicator_Prices"
        names[155] = "End_Of_Day_Prices"
        names[156] = "Order_Book"
        names[157] = "Market_Mode"
        names[158] = "Open_Interest"
        names[159] = "Front_Month_Contract_Update"
        names[160] = "Depth_By_Order"
        names[161] = "Depth_By_Order_End_Event"
        names[162] = "Symbol_Margin_Rate"
        names[163] = "Order_Price_Limits"
        
        # Add other plant templates as needed...
        
        return names

# Global message handler instance
_message_handler = MessageHandler()

def get_message_handler() -> MessageHandler:
    """Get the global message handler instance."""
    return _message_handler

def parse_message(message_bytes: bytes) -> Optional[MessageInfo]:
    """Parse a message using the global handler."""
    return _message_handler.parse_message(message_bytes)

def format_message(msg_info: MessageInfo, format_type: Optional[str] = None) -> str:
    """Format a message using the global handler."""
    return _message_handler.format_message(msg_info, format_type)