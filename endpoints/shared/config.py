#!/usr/bin/env python3

"""
Configuration management for Rithmic API endpoint testing.

This module provides centralized configuration for all endpoint testing scripts,
including paper trading credentials, connection parameters, and safety settings.
"""

import os
import pathlib
from dataclasses import dataclass
from typing import Dict, Optional, Any

@dataclass
class RithmicConfig:
    """Configuration settings for Rithmic API testing."""
    
    # Paper Trading Credentials (from CLAUDE.md)
    username: str = "PP-013155"
    password: str = "b7neA8k6JA"
    system_name: str = "Rithmic Paper Trading"  # Corrected: Use paper trading system
    gateway: str = "Chicago Area"
    
    # Connection Settings
    uri: str = "wss://rprotocol.rithmic.com:443"  # Fixed: Chicago endpoint
    app_name: str = "RithmicEndpointTester"
    app_version: str = "1.0.0"
    template_version: str = "3.9"
    
    # SSL Configuration
    ssl_cert_path: Optional[str] = None
    
    # Timeouts and Limits
    connection_timeout: int = 30
    heartbeat_interval: int = 5
    message_timeout: int = 10
    max_retries: int = 3
    
    # Adaptive Timeout Settings
    auth_timeout: int = 5      # Quick operations like authentication
    standard_timeout: int = 15  # Standard API calls
    complex_timeout: int = 30   # Complex queries like historical data
    
    # Plant-Specific Timeout Settings
    ticker_plant_timeout: int = 15     # Market data operations
    order_plant_timeout: int = 25      # Account and order operations
    history_plant_timeout: int = 45    # Historical data requests
    pnl_plant_timeout: int = 20        # PnL data operations
    repository_plant_timeout: int = 30 # Repository operations
    
    # Endpoint-Specific Timeout Overrides
    endpoint_timeouts: Dict[str, int] = None
    
    # Progressive Timeout Settings
    enable_progressive_timeouts: bool = True
    timeout_multiplier: float = 1.5   # Multiplier for each retry attempt
    max_timeout_attempts: int = 3      # Maximum timeout escalation attempts
    
    # Retry Configuration
    retry_base_delay: float = 1.0  # Base delay for exponential backoff
    retry_max_delay: float = 16.0  # Maximum delay between retries
    enable_retry_logic: bool = True
    
    # Safety Settings
    paper_trading_only: bool = True
    require_confirmation: bool = True
    log_all_messages: bool = True
    
    # Output Settings
    output_format: str = "human"  # "human", "json", "csv"
    save_responses: bool = True
    response_dir: str = "endpoint_responses"
    
    def __post_init__(self):
        """Initialize derived configuration values."""
        if self.ssl_cert_path is None:
            # Default to the SSL cert in the project's etc/ directory
            # Find project root by looking for the characteristic RProtocolAPI directory
            config_dir = pathlib.Path(__file__).parent.resolve()
            project_root = config_dir
            
            # Walk up directories until we find the RProtocolAPI root (contains etc/ and proto/ dirs)
            while project_root.parent != project_root:
                if (project_root / "etc").exists() and (project_root / "proto").exists():
                    break
                project_root = project_root.parent
            
            self.ssl_cert_path = str(project_root / "etc" / "rithmic_ssl_cert_auth_params")
        
        # Initialize endpoint-specific timeouts if not set
        if self.endpoint_timeouts is None:
            self.endpoint_timeouts = {
                # Order Plant endpoints that commonly timeout
                "order_session_config": 45,
                "exchange_permissions": 35,
                "bracket_info": 30,
                "show_orders": 40,
                
                # Historical data endpoints
                "time_bars": 60,
                "tick_bars": 50,
                "volume_bars": 55,
                "volume_profile": 45,
                
                # Market data endpoints
                "market_data_subscription": 25,
                "depth_by_order": 30,
                "streaming_trades": 20,
                
                # Repository endpoints
                "list_agreements": 35,
                "show_agreement": 40
            }
        
        # Ensure response directory exists
        if self.save_responses:
            os.makedirs(self.response_dir, exist_ok=True)

# Global configuration instance
config = RithmicConfig()

def get_config() -> RithmicConfig:
    """Get the global configuration instance."""
    return config

def update_config(**kwargs) -> None:
    """Update configuration values."""
    global config
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            raise ValueError(f"Unknown configuration parameter: {key}")

def get_plant_timeout(plant_type: int) -> int:
    """Get timeout value for specific plant type."""
    plant_timeouts = {
        InfraType.TICKER_PLANT: config.ticker_plant_timeout,
        InfraType.ORDER_PLANT: config.order_plant_timeout,
        InfraType.HISTORY_PLANT: config.history_plant_timeout,
        InfraType.PNL_PLANT: config.pnl_plant_timeout,
        InfraType.REPOSITORY_PLANT: config.repository_plant_timeout
    }
    return plant_timeouts.get(plant_type, config.standard_timeout)

def get_endpoint_timeout(endpoint_name: str, plant_type: int = None, attempt: int = 1) -> int:
    """Get timeout value for specific endpoint with progressive timeout support."""
    # Check for endpoint-specific timeout first
    base_timeout = config.endpoint_timeouts.get(endpoint_name)
    
    # Fall back to plant-specific timeout
    if base_timeout is None and plant_type is not None:
        base_timeout = get_plant_timeout(plant_type)
    
    # Fall back to standard timeout
    if base_timeout is None:
        base_timeout = config.standard_timeout
    
    # Apply progressive timeout scaling if enabled
    if config.enable_progressive_timeouts and attempt > 1:
        timeout_multiplier = config.timeout_multiplier ** (attempt - 1)
        base_timeout = int(base_timeout * timeout_multiplier)
    
    return base_timeout

def get_timeout_strategy(endpoint_name: str, plant_type: int = None) -> Dict[str, any]:
    """Get comprehensive timeout strategy for an endpoint."""
    return {
        "base_timeout": get_endpoint_timeout(endpoint_name, plant_type, 1),
        "max_attempts": config.max_timeout_attempts,
        "progressive_enabled": config.enable_progressive_timeouts,
        "timeouts_by_attempt": [
            get_endpoint_timeout(endpoint_name, plant_type, attempt)
            for attempt in range(1, config.max_timeout_attempts + 1)
        ]
    }

def log_timeout_diagnostics(endpoint_name: str, plant_type: int, timeout_used: int, 
                          success: bool, elapsed_time: float = None) -> None:
    """Log timeout diagnostics for analysis."""
    plant_names = {
        InfraType.TICKER_PLANT: "Ticker",
        InfraType.ORDER_PLANT: "Order", 
        InfraType.HISTORY_PLANT: "History",
        InfraType.PNL_PLANT: "PnL",
        InfraType.REPOSITORY_PLANT: "Repository"
    }
    
    plant_name = plant_names.get(plant_type, "Unknown")
    status = "SUCCESS" if success else "TIMEOUT"
    
    print(f"🕐 TIMEOUT DIAGNOSTICS: {endpoint_name} ({plant_name} Plant)")
    print(f"   Timeout Used: {timeout_used}s | Status: {status}")
    if elapsed_time is not None:
        print(f"   Elapsed Time: {elapsed_time:.2f}s")
        if elapsed_time > timeout_used * 0.8:
            print(f"   ⚠️  Close to timeout threshold - consider increasing timeout")
    print()

def validate_config() -> bool:
    """Validate configuration settings."""
    if not config.paper_trading_only:
        print("WARNING: Paper trading mode is disabled!")
        if config.require_confirmation:
            response = input("Are you sure you want to proceed with live trading? (type 'yes'): ")
            if response.lower() != 'yes':
                return False
    
    # Check SSL certificate exists
    if not os.path.exists(config.ssl_cert_path):
        print(f"ERROR: SSL certificate not found at {config.ssl_cert_path}")
        return False
    
    return True

# Infrastructure plant types
class InfraType:
    """Constants for Rithmic infrastructure plant types."""
    TICKER_PLANT = 1
    ORDER_PLANT = 2
    HISTORY_PLANT = 3
    PNL_PLANT = 4
    REPOSITORY_PLANT = 5
    
    @classmethod
    def get_plant_name(cls, plant_type: int) -> str:
        """Get human-readable plant name."""
        names = {
            cls.TICKER_PLANT: "Ticker Plant",
            cls.ORDER_PLANT: "Order Plant",
            cls.HISTORY_PLANT: "History Plant", 
            cls.PNL_PLANT: "PnL Plant",
            cls.REPOSITORY_PLANT: "Repository Plant"
        }
        return names.get(plant_type, f"Unknown Plant ({plant_type})")

# Template ID constants (safe endpoints only)
class TemplateIDs:
    """Template ID constants for safe (read-only) endpoints."""
    
    # Shared across plants
    LOGIN_REQUEST = 10
    LOGIN_RESPONSE = 11
    LOGOUT_REQUEST = 12
    LOGOUT_RESPONSE = 13
    REFERENCE_DATA_REQUEST = 14
    REFERENCE_DATA_RESPONSE = 15
    SYSTEM_INFO_REQUEST = 16
    SYSTEM_INFO_RESPONSE = 17
    HEARTBEAT_REQUEST = 18
    HEARTBEAT_RESPONSE = 19
    GATEWAY_INFO_REQUEST = 20
    GATEWAY_INFO_RESPONSE = 21
    
    # Market Data (Ticker Plant) - Safe endpoints only
    MARKET_DATA_UPDATE_REQUEST = 100
    MARKET_DATA_UPDATE_RESPONSE = 101
    GET_INSTRUMENT_BY_UNDERLYING_REQUEST = 102
    GET_INSTRUMENT_BY_UNDERLYING_RESPONSE = 103
    GET_INSTRUMENT_BY_UNDERLYING_KEYS_RESPONSE = 104
    MARKET_DATA_UPDATE_BY_UNDERLYING_REQUEST = 105
    MARKET_DATA_UPDATE_BY_UNDERLYING_RESPONSE = 106
    GIVE_TICK_SIZE_TYPE_TABLE_REQUEST = 107
    GIVE_TICK_SIZE_TYPE_TABLE_RESPONSE = 108
    SEARCH_SYMBOLS_REQUEST = 109
    SEARCH_SYMBOLS_RESPONSE = 110
    PRODUCT_CODES_REQUEST = 111
    PRODUCT_CODES_RESPONSE = 112
    FRONT_MONTH_CONTRACT_REQUEST = 113
    FRONT_MONTH_CONTRACT_RESPONSE = 114
    DEPTH_BY_ORDER_SNAPSHOT_REQUEST = 115
    DEPTH_BY_ORDER_SNAPSHOT_RESPONSE = 116
    DEPTH_BY_ORDER_UPDATES_REQUEST = 117
    DEPTH_BY_ORDER_UPDATES_RESPONSE = 118
    GET_VOLUME_AT_PRICE_REQUEST = 119
    GET_VOLUME_AT_PRICE_RESPONSE = 120
    AUXILIARY_REFERENCE_DATA_REQUEST = 121
    AUXILIARY_REFERENCE_DATA_RESPONSE = 122
    
    # Aliases for consistent naming
    TICK_SIZE_TABLE_REQUEST = 107
    TICK_SIZE_TABLE_RESPONSE = 108
    
    # Streaming market data (from server)
    LAST_TRADE = 150
    BEST_BID_OFFER = 151
    TRADE_STATISTICS = 152
    QUOTE_STATISTICS = 153
    INDICATOR_PRICES = 154
    END_OF_DAY_PRICES = 155
    ORDER_BOOK = 156
    MARKET_MODE = 157
    OPEN_INTEREST = 158
    FRONT_MONTH_CONTRACT_UPDATE = 159
    DEPTH_BY_ORDER = 160
    DEPTH_BY_ORDER_END_EVENT = 161
    SYMBOL_MARGIN_RATE = 162
    ORDER_PRICE_LIMITS = 163
    
    # Order Plant - SAFE READ-ONLY endpoints only
    LOGIN_INFO_REQUEST = 300
    LOGIN_INFO_RESPONSE = 301
    ACCOUNT_LIST_REQUEST = 302
    ACCOUNT_LIST_RESPONSE = 303
    ACCOUNT_RMS_INFO_REQUEST = 304
    ACCOUNT_RMS_INFO_RESPONSE = 305
    PRODUCT_RMS_INFO_REQUEST = 306
    PRODUCT_RMS_INFO_RESPONSE = 307
    TRADE_ROUTES_REQUEST = 310
    TRADE_ROUTES_RESPONSE = 311
    SHOW_ORDER_HISTORY_DATES_REQUEST = 318
    SHOW_ORDER_HISTORY_DATES_RESPONSE = 319
    SHOW_ORDERS_REQUEST = 320
    SHOW_ORDERS_RESPONSE = 321
    SHOW_ORDER_HISTORY_REQUEST = 322
    SHOW_ORDER_HISTORY_RESPONSE = 323
    SHOW_ORDER_HISTORY_SUMMARY_REQUEST = 324
    SHOW_ORDER_HISTORY_SUMMARY_RESPONSE = 325
    SHOW_ORDER_HISTORY_DETAIL_REQUEST = 326
    SHOW_ORDER_HISTORY_DETAIL_RESPONSE = 327
    SHOW_BRACKETS_REQUEST = 338
    SHOW_BRACKETS_RESPONSE = 339
    SHOW_BRACKET_STOPS_REQUEST = 340
    SHOW_BRACKET_STOPS_RESPONSE = 341
    LIST_EXCHANGE_PERMISSIONS_REQUEST = 342
    LIST_EXCHANGE_PERMISSIONS_RESPONSE = 343
    EASY_TO_BORROW_LIST_REQUEST = 348
    EASY_TO_BORROW_LIST_RESPONSE = 349
    ORDER_SESSION_CONFIG_REQUEST = 3502
    ORDER_SESSION_CONFIG_RESPONSE = 3503
    REPLAY_EXECUTIONS_REQUEST = 3506
    REPLAY_EXECUTIONS_RESPONSE = 3507
    ACCOUNT_RMS_UPDATES_REQUEST = 3508
    ACCOUNT_RMS_UPDATES_RESPONSE = 3509
    
    # History Plant
    TIME_BAR_UPDATE_REQUEST = 200
    TIME_BAR_UPDATE_RESPONSE = 201
    TIME_BAR_REPLAY_REQUEST = 202
    TIME_BAR_REPLAY_RESPONSE = 203
    TICK_BAR_UPDATE_REQUEST = 204
    TICK_BAR_UPDATE_RESPONSE = 205
    TICK_BAR_REPLAY_REQUEST = 206
    TICK_BAR_REPLAY_RESPONSE = 207
    VOLUME_PROFILE_MINUTE_BARS_REQUEST = 208
    VOLUME_PROFILE_MINUTE_BARS_RESPONSE = 209
    RESUME_BARS_REQUEST = 210
    RESUME_BARS_RESPONSE = 211
    TIME_BAR = 250
    TICK_BAR = 251
    
    # PnL Plant
    PNL_POSITION_UPDATES_REQUEST = 400
    PNL_POSITION_UPDATES_RESPONSE = 401
    PNL_POSITION_SNAPSHOT_REQUEST = 402
    PNL_POSITION_SNAPSHOT_RESPONSE = 403
    INSTRUMENT_PNL_POSITION_UPDATE = 450
    ACCOUNT_PNL_POSITION_UPDATE = 451
    
    # Repository Plant - SAFE READ-ONLY endpoints only
    LIST_UNACCEPTED_AGREEMENTS_REQUEST = 500
    LIST_UNACCEPTED_AGREEMENTS_RESPONSE = 501
    LIST_ACCEPTED_AGREEMENTS_REQUEST = 502
    LIST_ACCEPTED_AGREEMENTS_RESPONSE = 503
    SHOW_AGREEMENT_REQUEST = 506
    SHOW_AGREEMENT_RESPONSE = 507

# DANGEROUS endpoints that must NEVER be tested
DANGEROUS_TEMPLATES = {
    308: "Subscribe For Order Updates Request - Can trigger unwanted notifications",
    309: "Subscribe For Order Updates Response",
    312: "New Order Request - CREATES ORDERS!",
    313: "New Order Response",
    314: "Modify Order Request - MODIFIES ORDERS!",
    315: "Modify Order Response",
    316: "Cancel Order Request - CANCELS ORDERS!",
    317: "Cancel Order Response",
    328: "OCO Order Request - CREATES ORDERS!",
    329: "OCO Order Response",
    330: "Bracket Order Request - CREATES ORDERS!",
    331: "Bracket Order Response",
    332: "Update Target Bracket Level Request - MODIFIES ORDERS!",
    333: "Update Target Bracket Level Response",
    334: "Update Stop Bracket Level Request - MODIFIES ORDERS!",
    335: "Update Stop Bracket Level Response",
    336: "Subscribe To Bracket Updates Request - Can trigger unwanted notifications",
    337: "Subscribe To Bracket Updates Response",
    344: "Link Orders Request - MODIFIES ORDERS!",
    345: "Link Orders Response",
    346: "Cancel All Orders Request - CANCELS ALL ORDERS!",
    347: "Cancel All Orders Response",
    3500: "Modify Order Reference Data Request - MODIFIES ORDERS!",
    3501: "Modify Order Reference Data Response",
    3504: "Exit Position Request - CREATES ORDERS TO EXIT POSITIONS!",
    3505: "Exit Position Response",
    504: "Accept Agreement Request - Accepts legal agreements!",
    505: "Accept Agreement Response",
    508: "Set Rithmic MarketData Self Certification Status Request - Changes account settings!",
    509: "Set Rithmic MarketData Self Certification Status Response",
}

def is_dangerous_template(template_id: int) -> bool:
    """Check if a template ID corresponds to a dangerous endpoint."""
    return template_id in DANGEROUS_TEMPLATES

def get_dangerous_template_description(template_id: int) -> str:
    """Get the description of why a template ID is dangerous."""
    return DANGEROUS_TEMPLATES.get(template_id, "Unknown dangerous template")

def get_timeout_for_operation(operation_type: str) -> int:
    """
    Get appropriate timeout for different operation types.
    
    Args:
        operation_type: Type of operation ('auth', 'standard', 'complex', 'historical')
        
    Returns:
        int: Timeout in seconds
    """
    config = get_config()
    
    timeout_map = {
        'auth': config.auth_timeout,
        'authentication': config.auth_timeout,
        'login': config.auth_timeout,
        'standard': config.standard_timeout,
        'api': config.standard_timeout,
        'market_data': config.standard_timeout,
        'account': config.standard_timeout,
        'complex': config.complex_timeout,
        'historical': config.complex_timeout,
        'bars': config.complex_timeout,
        'replay': config.complex_timeout,
        'volume': config.complex_timeout,
    }
    
    return timeout_map.get(operation_type.lower(), config.message_timeout)

def get_retry_delay(attempt: int) -> float:
    """
    Calculate retry delay using exponential backoff.
    
    Args:
        attempt: Current retry attempt (0-based)
        
    Returns:
        float: Delay in seconds
    """
    config = get_config()
    
    if not config.enable_retry_logic:
        return 0.0
    
    # Exponential backoff: base_delay * (2 ^ attempt)
    delay = config.retry_base_delay * (2 ** attempt)
    
    # Cap at maximum delay
    return min(delay, config.retry_max_delay)