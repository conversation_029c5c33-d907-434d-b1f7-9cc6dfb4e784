#!/usr/bin/env python3

"""
Test Rithmic PnL Position Snapshot endpoints (Templates 402/403).

This script demonstrates how to retrieve position and P&L snapshot data
from the Rithmic API for portfolio analysis and risk management.

SAFETY: This script only performs READ-ONLY position data retrieval.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, Dict, Any, List

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_pnl_position_snapshot_pb2
import response_pnl_position_snapshot_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_pnl_position_snapshot(account_id: Optional[str] = None,
                                   output_format: str = "human") -> bool:
    """
    Test PnL position snapshot functionality.
    
    Args:
        account_id: Optional specific account ID to query
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    # Use configured account if not provided
    account_id = account_id or config_obj.username
    
    print("=" * 60)
    print("💰 TESTING PNL POSITION SNAPSHOT ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.PNL_POSITION_SNAPSHOT_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.PNL_POSITION_SNAPSHOT_RESPONSE} (Response)")
    print(f"Account ID: {account_id}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to PnL Plant
        print("🔐 Establishing connection to PnL Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.PNL_PLANT, "pnl_snapshot_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create PnL position snapshot request
        request = request_pnl_position_snapshot_pb2.RequestPnLPositionSnapshot()
        request.template_id = TemplateIDs.PNL_POSITION_SNAPSHOT_REQUEST
        request.user_msg.append(f"PnL position snapshot for account {account_id}")
        
        # Set account filter
        if account_id:
            request.account = account_id
        
        # Send snapshot request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending PnL position snapshot request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send snapshot request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for PnL position snapshot response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_pnl_position_snapshot_pb2.ResponsePnLPositionSnapshot()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 PNL POSITION ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ PnL position snapshot request successful!")
            
            # Extract position data
            positions = []
            
            # Process position information if available
            if hasattr(response, 'symbol') and response.symbol:
                print(f"\n📈 POSITION DATA ({len(response.symbol)} positions):")
                print("-" * 80)
                
                # Collect all position data
                for i in range(len(response.symbol)):
                    position = {
                        "symbol": response.symbol[i] if i < len(response.symbol) else "",
                        "exchange": "",
                        "quantity": 0,
                        "avg_price": 0.0,
                        "market_value": 0.0,
                        "pnl": 0.0,
                        "unrealized_pnl": 0.0
                    }
                    
                    # Get corresponding data fields
                    if hasattr(response, 'exchange') and i < len(response.exchange):
                        position["exchange"] = response.exchange[i]
                    
                    if hasattr(response, 'net_quantity') and i < len(response.net_quantity):
                        position["quantity"] = response.net_quantity[i]
                    
                    if hasattr(response, 'avg_price') and i < len(response.avg_price):
                        position["avg_price"] = response.avg_price[i]
                    
                    if hasattr(response, 'market_value') and i < len(response.market_value):
                        position["market_value"] = response.market_value[i]
                    
                    if hasattr(response, 'pnl') and i < len(response.pnl):
                        position["pnl"] = response.pnl[i]
                    
                    if hasattr(response, 'unrealized_pnl') and i < len(response.unrealized_pnl):
                        position["unrealized_pnl"] = response.unrealized_pnl[i]
                    
                    positions.append(position)
                
                # Display positions
                if output_format == "human":
                    for i, pos in enumerate(positions, 1):
                        print(f"{i:2d}. {pos['symbol']:15s} @ {pos['exchange']:10s}")
                        print(f"    Quantity: {pos['quantity']:>10.0f}")
                        print(f"    Avg Price: ${pos['avg_price']:>10.2f}")
                        print(f"    Market Value: ${pos['market_value']:>10.2f}")
                        print(f"    P&L: ${pos['pnl']:>10.2f}")
                        print(f"    Unrealized P&L: ${pos['unrealized_pnl']:>10.2f}")
                        print()
                
                elif output_format == "csv":
                    # Print CSV header
                    headers = ["Symbol", "Exchange", "Quantity", "Avg_Price", "Market_Value", "PnL", "Unrealized_PnL"]
                    print(",".join(headers))
                    
                    # Print position data
                    for pos in positions:
                        values = [
                            pos['symbol'],
                            pos['exchange'],
                            str(pos['quantity']),
                            f"{pos['avg_price']:.2f}",
                            f"{pos['market_value']:.2f}",
                            f"{pos['pnl']:.2f}",
                            f"{pos['unrealized_pnl']:.2f}"
                        ]
                        print(",".join(values))
                
                # Calculate summary statistics
                if positions:
                    total_market_value = sum(pos['market_value'] for pos in positions)
                    total_pnl = sum(pos['pnl'] for pos in positions)
                    total_unrealized_pnl = sum(pos['unrealized_pnl'] for pos in positions)
                    
                    print(f"\n💰 PORTFOLIO SUMMARY:")
                    print(f"Total Positions: {len(positions)}")
                    print(f"Total Market Value: ${total_market_value:,.2f}")
                    print(f"Total Realized P&L: ${total_pnl:,.2f}")
                    print(f"Total Unrealized P&L: ${total_unrealized_pnl:,.2f}")
                    print(f"Net P&L: ${total_pnl + total_unrealized_pnl:,.2f}")
                    
                    # Analyze position types
                    long_positions = [p for p in positions if p['quantity'] > 0]
                    short_positions = [p for p in positions if p['quantity'] < 0]
                    
                    if long_positions:
                        print(f"Long Positions: {len(long_positions)}")
                    if short_positions:
                        print(f"Short Positions: {len(short_positions)}")
                    
                    # Show top positions by market value
                    if len(positions) > 1:
                        top_positions = sorted(positions, key=lambda x: abs(x['market_value']), reverse=True)[:5]
                        print(f"\n🔝 TOP POSITIONS BY MARKET VALUE:")
                        for i, pos in enumerate(top_positions, 1):
                            print(f"{i}. {pos['symbol']} - ${abs(pos['market_value']):,.2f}")
            
            else:
                print("\n📊 No position data found")
                print("   This could indicate:")
                print("   • No open positions in the account")
                print("   • Account has no trading activity")
                print("   • Insufficient permissions to view position data")
                print("   • Different account ID required")
            
            # Display account information if available
            if hasattr(response, 'account') and response.account:
                print(f"\n👤 ACCOUNT INFORMATION:")
                for i, account in enumerate(response.account):
                    print(f"Account {i+1}: {account}")
        
        else:
            print("❌ PnL position snapshot request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify account ID is correct")
            print("   • Check account permissions for position data")
            print("   • Ensure account has trading activity")
            print("   • Try connecting to different infrastructure plant")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing PnL position snapshot: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for PnL position snapshot testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic PnL Position Snapshot endpoint (Templates 402/403)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_pnl_position_snapshot.py
  python test_pnl_position_snapshot.py --account PP-013155
  python test_pnl_position_snapshot.py --format json
  python test_pnl_position_snapshot.py --format csv > positions.csv

Safety:
  This script performs READ-ONLY position data retrieval only.
  Requires authentication but no account modifications performed.
  
Output:
  The script will display all positions with their P&L data including:
  - Symbol and exchange information
  - Position quantities and average prices
  - Market values and realized/unrealized P&L
  - Portfolio-level summary statistics
        """
    )
    
    parser.add_argument(
        "--account",
        type=str,
        help="Account ID to query (default from config)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC PNL POSITION SNAPSHOT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY POSITION DATA RETRIEVAL ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_pnl_position_snapshot(
        account_id=args.account,
        output_format=args.format
    )
    
    if success:
        print("\n✅ PnL position snapshot test completed successfully!")
        return 0
    else:
        print("\n❌ PnL position snapshot test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)