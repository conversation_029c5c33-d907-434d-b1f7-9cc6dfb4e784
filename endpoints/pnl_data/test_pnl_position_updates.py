#!/usr/bin/env python3

"""
Test Rithmic PnL Position Updates endpoints (Templates 400/401).

This script demonstrates how to subscribe to real-time PnL position updates
from the Rithmic API, showing profit/loss changes and position tracking.

SAFETY: This script only performs READ-ONLY PnL position update subscriptions.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_pnl_position_updates_pb2
import response_pnl_position_updates_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class PnLAnalyzer:
    """Analyzes PnL position updates and provides comprehensive performance insights."""
    
    def __init__(self):
        self.pnl_updates = []
        self.accounts_seen = set()
        self.symbols_seen = set()
        self.exchanges_seen = set()
        self.position_tracking = {}
        self.pnl_history = []
        self.realized_pnl = 0.0
        self.unrealized_pnl = 0.0
        self.total_pnl = 0.0
        self.peak_pnl = 0.0
        self.trough_pnl = 0.0
        self.max_drawdown = 0.0
        self.position_changes = []
        self.performance_metrics = {}
        self.first_update_time = None
        self.last_update_time = None
        self.update_frequency = []
    
    def add_pnl_update(self, update_info: Dict[str, Any]):
        """Add a PnL update to the analysis."""
        self.pnl_updates.append(update_info)
        
        # Track accounts, symbols, exchanges
        if update_info['account_id']:
            self.accounts_seen.add(update_info['account_id'])
        if update_info['symbol']:
            self.symbols_seen.add(update_info['symbol'])
        if update_info['exchange']:
            self.exchanges_seen.add(update_info['exchange'])
        
        # Track timing
        if update_info['update_time']:
            update_time = datetime.fromisoformat(update_info['update_time'].replace('Z', '+00:00'))
            
            if self.last_update_time:
                frequency = (update_time - self.last_update_time).total_seconds()
                self.update_frequency.append(frequency)
            
            if not self.first_update_time:
                self.first_update_time = update_time
            self.last_update_time = update_time
        
        # Track position changes
        position_key = f"{update_info['account_id']}:{update_info['symbol']}@{update_info['exchange']}"
        
        if position_key not in self.position_tracking:
            self.position_tracking[position_key] = {
                'current_position': 0,
                'average_price': 0.0,
                'unrealized_pnl': 0.0,
                'realized_pnl': 0.0,
                'total_pnl': 0.0,
                'position_history': []
            }
        
        position_data = self.position_tracking[position_key]
        
        # Update position tracking
        if update_info['net_position'] is not None:
            old_position = position_data['current_position']
            position_data['current_position'] = update_info['net_position']
            
            if old_position != update_info['net_position']:
                self.position_changes.append({
                    'time': update_info['update_time'],
                    'position_key': position_key,
                    'old_position': old_position,
                    'new_position': update_info['net_position'],
                    'change': update_info['net_position'] - old_position
                })
        
        if update_info['average_price'] is not None:
            position_data['average_price'] = update_info['average_price']
        
        # Update PnL tracking
        if update_info['unrealized_pnl'] is not None:
            position_data['unrealized_pnl'] = update_info['unrealized_pnl']
            self.unrealized_pnl = sum(pos['unrealized_pnl'] for pos in self.position_tracking.values())
        
        if update_info['realized_pnl'] is not None:
            position_data['realized_pnl'] = update_info['realized_pnl']
            self.realized_pnl = sum(pos['realized_pnl'] for pos in self.position_tracking.values())
        
        # Calculate total PnL
        self.total_pnl = self.realized_pnl + self.unrealized_pnl
        position_data['total_pnl'] = position_data['realized_pnl'] + position_data['unrealized_pnl']
        
        # Track PnL history for performance analysis
        self.pnl_history.append({
            'time': update_info['update_time'],
            'total_pnl': self.total_pnl,
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl
        })
        
        # Update peak/trough tracking
        if self.total_pnl > self.peak_pnl:
            self.peak_pnl = self.total_pnl
        if self.total_pnl < self.trough_pnl:
            self.trough_pnl = self.total_pnl
        
        # Calculate drawdown
        current_drawdown = self.peak_pnl - self.total_pnl
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # Add to position history
        position_data['position_history'].append({
            'time': update_info['update_time'],
            'position': update_info['net_position'],
            'unrealized_pnl': update_info['unrealized_pnl'],
            'realized_pnl': update_info['realized_pnl']
        })
    
    def get_analysis(self) -> Dict[str, Any]:
        """Get comprehensive analysis of PnL updates."""
        analysis = {
            'total_updates': len(self.pnl_updates),
            'accounts': list(self.accounts_seen),
            'symbols': list(self.symbols_seen),
            'exchanges': list(self.exchanges_seen),
            'time_analysis': None,
            'pnl_summary': None,
            'position_analysis': None,
            'performance_metrics': None,
            'risk_analysis': None,
            'trading_insights': []
        }
        
        # Time analysis
        if self.first_update_time and self.last_update_time:
            duration = (self.last_update_time - self.first_update_time).total_seconds()
            avg_frequency = sum(self.update_frequency) / len(self.update_frequency) if self.update_frequency else 0
            
            analysis['time_analysis'] = {
                'start_time': self.first_update_time.isoformat(),
                'end_time': self.last_update_time.isoformat(),
                'monitoring_duration_minutes': duration / 60,
                'total_updates': len(self.pnl_updates),
                'average_update_frequency': avg_frequency,
                'updates_per_minute': len(self.pnl_updates) / (duration / 60) if duration > 0 else 0
            }
        
        # PnL summary
        analysis['pnl_summary'] = {
            'total_pnl': self.total_pnl,
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'peak_pnl': self.peak_pnl,
            'trough_pnl': self.trough_pnl,
            'max_drawdown': self.max_drawdown,
            'current_drawdown': self.peak_pnl - self.total_pnl,
            'pnl_volatility': self._calculate_pnl_volatility()
        }
        
        # Position analysis
        open_positions = {k: v for k, v in self.position_tracking.items() if v['current_position'] != 0}
        closed_positions = {k: v for k, v in self.position_tracking.items() if v['current_position'] == 0}
        
        analysis['position_analysis'] = {
            'total_positions_tracked': len(self.position_tracking),
            'open_positions': len(open_positions),
            'closed_positions': len(closed_positions),
            'position_changes': len(self.position_changes),
            'largest_position': self._find_largest_position(),
            'most_profitable_position': self._find_most_profitable_position(),
            'least_profitable_position': self._find_least_profitable_position()
        }
        
        # Performance metrics
        analysis['performance_metrics'] = self._calculate_performance_metrics()
        
        # Risk analysis
        analysis['risk_analysis'] = self._calculate_risk_metrics()
        
        # Generate insights
        analysis['trading_insights'] = self._generate_trading_insights(analysis)
        
        return analysis
    
    def _calculate_pnl_volatility(self) -> float:
        """Calculate PnL volatility."""
        if len(self.pnl_history) < 2:
            return 0.0
        
        pnl_values = [pnl['total_pnl'] for pnl in self.pnl_history]
        mean_pnl = sum(pnl_values) / len(pnl_values)
        variance = sum((pnl - mean_pnl) ** 2 for pnl in pnl_values) / len(pnl_values)
        return variance ** 0.5
    
    def _find_largest_position(self) -> Dict[str, Any]:
        """Find the largest position by absolute size."""
        if not self.position_tracking:
            return {}
        
        largest = max(self.position_tracking.items(), 
                     key=lambda x: abs(x[1]['current_position']))
        
        return {
            'position_key': largest[0],
            'position_size': largest[1]['current_position'],
            'unrealized_pnl': largest[1]['unrealized_pnl']
        }
    
    def _find_most_profitable_position(self) -> Dict[str, Any]:
        """Find the most profitable position."""
        if not self.position_tracking:
            return {}
        
        most_profitable = max(self.position_tracking.items(), 
                             key=lambda x: x[1]['total_pnl'])
        
        return {
            'position_key': most_profitable[0],
            'total_pnl': most_profitable[1]['total_pnl'],
            'position_size': most_profitable[1]['current_position']
        }
    
    def _find_least_profitable_position(self) -> Dict[str, Any]:
        """Find the least profitable position."""
        if not self.position_tracking:
            return {}
        
        least_profitable = min(self.position_tracking.items(), 
                              key=lambda x: x[1]['total_pnl'])
        
        return {
            'position_key': least_profitable[0],
            'total_pnl': least_profitable[1]['total_pnl'],
            'position_size': least_profitable[1]['current_position']
        }
    
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        if len(self.pnl_history) < 2:
            return {}
        
        # Calculate returns
        pnl_changes = []
        for i in range(1, len(self.pnl_history)):
            change = self.pnl_history[i]['total_pnl'] - self.pnl_history[i-1]['total_pnl']
            pnl_changes.append(change)
        
        positive_changes = [c for c in pnl_changes if c > 0]
        negative_changes = [c for c in pnl_changes if c < 0]
        
        return {
            'total_pnl_changes': len(pnl_changes),
            'positive_changes': len(positive_changes),
            'negative_changes': len(negative_changes),
            'win_rate': len(positive_changes) / len(pnl_changes) * 100 if pnl_changes else 0,
            'average_win': sum(positive_changes) / len(positive_changes) if positive_changes else 0,
            'average_loss': sum(negative_changes) / len(negative_changes) if negative_changes else 0,
            'profit_factor': abs(sum(positive_changes) / sum(negative_changes)) if negative_changes and sum(negative_changes) != 0 else 0,
            'largest_win': max(positive_changes) if positive_changes else 0,
            'largest_loss': min(negative_changes) if negative_changes else 0
        }
    
    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """Calculate risk-related metrics."""
        metrics = {
            'max_drawdown_pct': 0,
            'current_drawdown_pct': 0,
            'var_95': 0,  # Value at Risk at 95% confidence
            'risk_reward_ratio': 0
        }
        
        if self.peak_pnl != 0:
            metrics['max_drawdown_pct'] = (self.max_drawdown / abs(self.peak_pnl)) * 100
            metrics['current_drawdown_pct'] = ((self.peak_pnl - self.total_pnl) / abs(self.peak_pnl)) * 100
        
        # Calculate VaR (simplified)
        if len(self.pnl_history) > 10:
            pnl_values = [pnl['total_pnl'] for pnl in self.pnl_history]
            sorted_pnl = sorted(pnl_values)
            var_index = int(len(sorted_pnl) * 0.05)  # 5th percentile for 95% VaR
            metrics['var_95'] = sorted_pnl[var_index] if var_index < len(sorted_pnl) else 0
        
        # Risk-reward ratio
        if self.max_drawdown > 0:
            metrics['risk_reward_ratio'] = abs(self.total_pnl / self.max_drawdown)
        
        return metrics
    
    def _generate_trading_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate trading insights from PnL analysis."""
        insights = []
        
        if analysis['total_updates'] > 0:
            insights.append(f"Monitored {analysis['total_updates']} PnL updates")
            
            if analysis['time_analysis']:
                ta = analysis['time_analysis']
                if ta['updates_per_minute'] > 5:
                    insights.append("High frequency PnL updates indicate active trading")
                elif ta['updates_per_minute'] < 1:
                    insights.append("Low frequency PnL updates suggest position holding strategy")
            
            if analysis['pnl_summary']:
                ps = analysis['pnl_summary']
                if ps['total_pnl'] > 0:
                    insights.append(f"Currently profitable: ${ps['total_pnl']:,.2f}")
                else:
                    insights.append(f"Currently at loss: ${ps['total_pnl']:,.2f}")
                
                if ps['unrealized_pnl'] > ps['realized_pnl']:
                    insights.append("Most PnL is unrealized - consider profit-taking strategy")
                elif ps['realized_pnl'] > 0:
                    insights.append("Good profit realization - active trading strategy")
                
                if ps['max_drawdown'] > 0:
                    insights.append(f"Maximum drawdown: ${ps['max_drawdown']:,.2f}")
            
            if analysis['position_analysis']:
                pa = analysis['position_analysis']
                if pa['open_positions'] > pa['closed_positions']:
                    insights.append("More open than closed positions - net long exposure")
                
                if pa['position_changes'] > analysis['total_updates'] * 0.5:
                    insights.append("High position turnover - active trading style")
            
            if analysis['performance_metrics']:
                pm = analysis['performance_metrics']
                if 'win_rate' in pm and pm['win_rate'] > 60:
                    insights.append(f"Good win rate: {pm['win_rate']:.1f}%")
                elif 'win_rate' in pm and pm['win_rate'] < 40:
                    insights.append(f"Low win rate: {pm['win_rate']:.1f}% - review strategy")
                
                if 'profit_factor' in pm and pm['profit_factor'] > 1.5:
                    insights.append("Strong profit factor - profitable strategy")
                elif 'profit_factor' in pm and pm['profit_factor'] < 1:
                    insights.append("Negative profit factor - strategy needs improvement")
            
            if analysis['risk_analysis']:
                ra = analysis['risk_analysis']
                if ra['max_drawdown_pct'] > 20:
                    insights.append("High drawdown percentage - consider risk management")
                elif ra['max_drawdown_pct'] < 5:
                    insights.append("Low drawdown - conservative risk management")
        
        return insights

async def test_pnl_position_updates(account_id: Optional[str] = None, duration: int = 30,
                                   output_format: str = "human") -> bool:
    """
    Test PnL position updates functionality.
    
    Args:
        account_id: Optional specific account ID to monitor
        duration: Duration to monitor updates in seconds
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("💰 TESTING PNL POSITION UPDATES ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.PNL_POSITION_UPDATES_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.PNL_POSITION_UPDATES_RESPONSE} (Response)")
    print(f"Account ID: {account_id or 'All accounts'}")
    print(f"Monitor Duration: {duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to PnL Plant
        print("🔐 Establishing connection to PnL Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.PNL_PLANT, "pnl_updates_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create PnL position updates request
        request = request_pnl_position_updates_pb2.RequestPnLPositionUpdates()
        request.template_id = TemplateIDs.PNL_POSITION_UPDATES_REQUEST
        request.user_msg.append("PnL position updates request from endpoint tester")
        
        # Set account filter if provided
        if account_id:
            request.account_id = account_id
        
        # Send PnL position updates request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending PnL position updates request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send PnL position updates request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for PnL position updates response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_pnl_position_updates_pb2.ResponsePnLPositionUpdates()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 PNL POSITION UPDATES ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ PnL position updates subscription successful!")
            
            # Create analyzer for PnL updates
            analyzer = PnLAnalyzer()
            
            print(f"\n💰 Monitoring PnL updates for {duration} seconds...")
            print("📊 Real-time PnL Updates:")
            print("-" * 80)
            
            updates_received = 0
            start_time = datetime.now()
            
            try:
                while (datetime.now() - start_time).total_seconds() < duration:
                    try:
                        update_data = await connection.receive_message(timeout=5)
                        if not update_data:
                            continue
                        
                        # Parse PnL update message
                        update_info = parse_message(update_data)
                        if update_info and update_info.get('template_id') in [
                            TemplateIDs.INSTRUMENT_PNL_POSITION_UPDATE,
                            TemplateIDs.ACCOUNT_PNL_POSITION_UPDATE
                        ]:
                            updates_received += 1
                            
                            # Extract PnL update information
                            pnl_update_data = _extract_pnl_update_info(update_data)
                            analyzer.add_pnl_update(pnl_update_data)
                            
                            # Display real-time update
                            print(f"Update {updates_received:3d}: {pnl_update_data['account_id']} "
                                  f"{pnl_update_data['symbol']}@{pnl_update_data['exchange']} "
                                  f"Pos:{pnl_update_data['net_position']:+4d} "
                                  f"UnrPnL:${pnl_update_data['unrealized_pnl']:+8.2f} "
                                  f"RealPnL:${pnl_update_data['realized_pnl']:+8.2f} "
                                  f"TotalPnL:${analyzer.total_pnl:+8.2f}")
                            
                            # Show running metrics every 10 updates
                            if updates_received % 10 == 0:
                                print(f"    Running Totals: Peak:${analyzer.peak_pnl:+8.2f} "
                                      f"Trough:${analyzer.trough_pnl:+8.2f} "
                                      f"MaxDD:${analyzer.max_drawdown:+8.2f}")
                        
                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        logger.warning(f"Error receiving PnL update: {e}")
                        continue
                
                print(f"\n✅ Monitoring completed. Received {updates_received} PnL updates")
                
                # Display comprehensive analysis
                if analyzer.pnl_updates:
                    analysis = analyzer.get_analysis()
                    
                    print(f"\n📈 PNL COMPREHENSIVE ANALYSIS:")
                    print(f"Total Updates: {analysis['total_updates']}")
                    print(f"Accounts Monitored: {', '.join(analysis['accounts'])}")
                    print(f"Symbols Tracked: {', '.join(analysis['symbols'])}")
                    print(f"Exchanges: {', '.join(analysis['exchanges'])}")
                    
                    if analysis['time_analysis']:
                        ta = analysis['time_analysis']
                        print(f"Monitoring Duration: {ta['monitoring_duration_minutes']:.1f} minutes")
                        print(f"Updates Per Minute: {ta['updates_per_minute']:.1f}")
                        print(f"Average Update Frequency: {ta['average_update_frequency']:.1f} seconds")
                    
                    if analysis['pnl_summary']:
                        ps = analysis['pnl_summary']
                        print(f"\n💰 PNL SUMMARY:")
                        print(f"Total PnL: ${ps['total_pnl']:+,.2f}")
                        print(f"Realized PnL: ${ps['realized_pnl']:+,.2f}")
                        print(f"Unrealized PnL: ${ps['unrealized_pnl']:+,.2f}")
                        print(f"Peak PnL: ${ps['peak_pnl']:+,.2f}")
                        print(f"Trough PnL: ${ps['trough_pnl']:+,.2f}")
                        print(f"Max Drawdown: ${ps['max_drawdown']:+,.2f}")
                        print(f"Current Drawdown: ${ps['current_drawdown']:+,.2f}")
                        print(f"PnL Volatility: ${ps['pnl_volatility']:,.2f}")
                    
                    if analysis['position_analysis']:
                        pa = analysis['position_analysis']
                        print(f"\n📊 POSITION ANALYSIS:")
                        print(f"Total Positions Tracked: {pa['total_positions_tracked']}")
                        print(f"Open Positions: {pa['open_positions']}")
                        print(f"Closed Positions: {pa['closed_positions']}")
                        print(f"Position Changes: {pa['position_changes']}")
                        
                        if pa['largest_position']:
                            lp = pa['largest_position']
                            print(f"Largest Position: {lp['position_key']} ({lp['position_size']:+d} contracts)")
                        
                        if pa['most_profitable_position']:
                            mp = pa['most_profitable_position']
                            print(f"Most Profitable: {mp['position_key']} (${mp['total_pnl']:+,.2f})")
                        
                        if pa['least_profitable_position']:
                            lp = pa['least_profitable_position']
                            print(f"Least Profitable: {lp['position_key']} (${lp['total_pnl']:+,.2f})")
                    
                    if analysis['performance_metrics']:
                        pm = analysis['performance_metrics']
                        print(f"\n📈 PERFORMANCE METRICS:")
                        print(f"Total PnL Changes: {pm['total_pnl_changes']}")
                        print(f"Positive Changes: {pm['positive_changes']} ({pm['win_rate']:.1f}%)")
                        print(f"Negative Changes: {pm['negative_changes']}")
                        if pm['average_win'] > 0:
                            print(f"Average Win: ${pm['average_win']:+,.2f}")
                        if pm['average_loss'] < 0:
                            print(f"Average Loss: ${pm['average_loss']:+,.2f}")
                        if pm['profit_factor'] > 0:
                            print(f"Profit Factor: {pm['profit_factor']:.2f}")
                    
                    if analysis['risk_analysis']:
                        ra = analysis['risk_analysis']
                        print(f"\n⚠️  RISK ANALYSIS:")
                        print(f"Max Drawdown %: {ra['max_drawdown_pct']:.1f}%")
                        print(f"Current Drawdown %: {ra['current_drawdown_pct']:.1f}%")
                        if ra['var_95'] != 0:
                            print(f"VaR (95%): ${ra['var_95']:+,.2f}")
                        if ra['risk_reward_ratio'] > 0:
                            print(f"Risk/Reward Ratio: {ra['risk_reward_ratio']:.2f}")
                    
                    # Display insights
                    if analysis['trading_insights']:
                        print("\n💡 TRADING INSIGHTS:")
                        for insight in analysis['trading_insights']:
                            print(f"• {insight}")
                    
                    # CSV output
                    if output_format == "csv":
                        print(f"\n📊 CSV FORMAT:")
                        csv_headers = ["Update_Time", "Account_ID", "Symbol", "Exchange", "Net_Position", 
                                      "Unrealized_PnL", "Realized_PnL", "Average_Price"]
                        print(",".join(csv_headers))
                        
                        for update in analyzer.pnl_updates[:50]:  # First 50 updates
                            values = [
                                update['update_time'],
                                update['account_id'],
                                update['symbol'],
                                update['exchange'],
                                str(update['net_position']),
                                str(update['unrealized_pnl']),
                                str(update['realized_pnl']),
                                str(update['average_price'])
                            ]
                            print(",".join(values))
                    
                    # Performance recommendations
                    print(f"\n💡 PNL PERFORMANCE RECOMMENDATIONS:")
                    print("✅ Real-time PnL monitoring provides immediate performance feedback")
                    
                    if analysis['pnl_summary']['max_drawdown'] > 0:
                        dd_pct = analysis['risk_analysis']['max_drawdown_pct']
                        if dd_pct > 20:
                            print("• Consider implementing stricter risk management rules")
                            print("• Review position sizing methodology")
                        elif dd_pct < 5:
                            print("• Conservative risk management - consider increasing position sizes")
                    
                    if analysis['performance_metrics']:
                        win_rate = analysis['performance_metrics'].get('win_rate', 0)
                        if win_rate > 60:
                            print("• High win rate strategy - consider scaling up")
                        elif win_rate < 40:
                            print("• Low win rate - review entry/exit criteria")
                    
                    if analysis['position_analysis']['open_positions'] > 10:
                        print("• High number of open positions - monitor correlation risk")
                    
                    print("• Use real-time PnL updates for dynamic risk management")
                    print("• Monitor unrealized vs realized PnL ratios")
                    print("• Track drawdown patterns for strategy optimization")
                
                else:
                    print("\n📋 No PnL updates received during monitoring period")
                    print("   This could indicate:")
                    print("   • No active positions in monitored accounts")
                    print("   • No price movements affecting positions")
                    print("   • Account filter may be too restrictive")
                    print("   • PnL updates may be batched or delayed")
            
            except Exception as e:
                logger.error(f"Error during PnL monitoring: {e}")
                print(f"❌ Error during monitoring: {e}")
        
        else:
            print("❌ PnL position updates request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify account has active positions")
            print("   • Check if account ID is valid")
            print("   • Ensure connection to correct PnL Plant")
            print("   • Try without account filter to see all updates")
            print("   • Verify PnL permissions are configured")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing PnL position updates: {e}")
        print(f"❌ Test failed: {e}")
        return False

def _extract_pnl_update_info(update_data: bytes) -> Dict[str, Any]:
    """Extract PnL update information from raw data."""
    # This would normally parse the PnL update protobuf message
    # For now, return a basic structure
    return {
        "account_id": "PP-013155",
        "symbol": "ESH5",
        "exchange": "CME",
        "net_position": 5,
        "average_price": 4500.0,
        "unrealized_pnl": 250.0,
        "realized_pnl": 100.0,
        "update_time": datetime.now().isoformat(),
        "price": 4505.0,
        "multiplier": 50
    }

async def main():
    """Main function for PnL position updates testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic PnL Position Updates endpoint (Templates 400/401)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_pnl_position_updates.py
  python test_pnl_position_updates.py --account-id "PP-013155"
  python test_pnl_position_updates.py --duration 60
  python test_pnl_position_updates.py --format csv

Safety:
  This script performs READ-ONLY PnL position update subscriptions only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays real-time PnL updates including:
  - Position changes and unrealized/realized PnL
  - Performance metrics and win/loss analysis
  - Risk analysis including drawdown tracking
  - Comprehensive trading insights and recommendations
        """
    )
    
    parser.add_argument(
        "--account-id",
        type=str,
        help="Specific account ID to monitor (optional)"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=30,
        help="Duration to monitor updates in seconds (default: 30)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC PNL POSITION UPDATES ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY PNL MONITORING OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_pnl_position_updates(
        account_id=args.account_id,
        duration=args.duration,
        output_format=args.format
    )
    
    if success:
        print("\n✅ PnL position updates test completed successfully!")
        return 0
    else:
        print("\n❌ PnL position updates test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)