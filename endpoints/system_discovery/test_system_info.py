#!/usr/bin/env python3

"""
Test Rithmic System Info endpoints (Templates 16/17).

This script demonstrates how to request and parse system information
from the Rithmic API, including available systems and their details.

SAFETY: This script only performs READ-ONLY system discovery operations.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional

# Add the proto_generated directory to path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_rithmic_system_info_pb2
import response_rithmic_system_info_pb2

import config
from config import get_config, TemplateIDs, validate_config
import connection
from connection import get_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_system_info(uri: Optional[str] = None, 
                          output_format: str = "human") -> bool:
    """
    Test the Rithmic System Info endpoint (Templates 16/17).
    
    Args:
        uri: Optional custom URI to test
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful, False otherwise
    """
    config = get_config()
    message_handler = get_message_handler()
    
    # Override output format
    config.output_format = output_format
    
    print("=" * 60)
    print("🔍 TESTING RITHMIC SYSTEM INFO ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.SYSTEM_INFO_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.SYSTEM_INFO_RESPONSE} (Response)")
    print(f"URI: {uri or config.uri}")
    print(f"Paper Trading: {'✅ ENABLED' if config.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Get connection (no authentication needed for system info)
        connection = await get_connection("system_discovery", uri)
        if not connection:
            logger.error("Failed to establish connection")
            return False
        
        print("✅ WebSocket connection established")
        
        # Create system info request
        request = request_rithmic_system_info_pb2.RequestRithmicSystemInfo()
        request.template_id = TemplateIDs.SYSTEM_INFO_REQUEST
        request.user_msg.append("System info request from endpoint tester")
        
        # Send request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending system info request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            logger.error("Failed to send system info request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info:
            print("\n📋 REQUEST DETAILS:")
            print(message_handler.format_message(request_info, output_format))
        
        # Wait for response
        print("\n⏳ Waiting for system info response...")
        response_bytes = await connection.receive_message(timeout=config.message_timeout)
        
        if not response_bytes:
            logger.error("No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_rithmic_system_info_pb2.ResponseRithmicSystemInfo()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info:
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 RESPONSE ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ System info request successful!")
            print(f"\n🖥️  AVAILABLE SYSTEMS ({len(response.system_name)}):")
            print("-" * 40)
            for i, system_name in enumerate(response.system_name, 1):
                print(f"{i:2d}. {system_name}")
            
            # Highlight paper trading systems
            paper_systems = [s for s in response.system_name if "paper" in s.lower()]
            if paper_systems:
                print(f"\n📄 PAPER TRADING SYSTEMS:")
                for system in paper_systems:
                    print(f"   • {system} ✅")
            
            # Check for configured system
            if config.system_name in response.system_name:
                print(f"\n✅ Configured system found: {config.system_name}")
            else:
                print(f"\n⚠️  Configured system not found: {config.system_name}")
                print("   Available systems listed above.")
        else:
            print("❌ System info request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
        
        # Cleanup
        print("\n🔄 Closing connection...")
        await connection.disconnect()
        print("✅ Connection closed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing system info: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for system info testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic System Info endpoint (Templates 16/17)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_system_info.py
  python test_system_info.py --format json
  python test_system_info.py --uri wss://rituz00100.rithmic.com:443
  python test_system_info.py --log-level DEBUG

Safety:
  This script performs READ-ONLY system discovery operations only.
  No authentication required. No account modifications performed.
        """
    )
    
    parser.add_argument(
        "--uri",
        type=str,
        help="Custom URI to test (default from config)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config = get_config()
        config.save_responses = True
    
    print("🧪 RITHMIC SYSTEM INFO ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY SYSTEM DISCOVERY ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_system_info(
        uri=args.uri,
        output_format=args.format
    )
    
    if success:
        print("\n✅ System info test completed successfully!")
        return 0
    else:
        print("\n❌ System info test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)