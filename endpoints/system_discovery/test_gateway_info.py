#!/usr/bin/env python3

"""
Test Rithmic System Gateway Info endpoints (Templates 20/21).

This script demonstrates how to request and parse gateway information
from the Rithmic API, including available gateways and their details.

SAFETY: This script only performs READ-ONLY gateway discovery operations.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional

# Add the proto_generated directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'proto_generated'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

import request_rithmic_system_gateway_info_pb2
import response_rithmic_system_gateway_info_pb2

from config import get_config, TemplateIDs, validate_config
from connection import get_connection
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_gateway_info(system_name: Optional[str] = None,
                           uri: Optional[str] = None, 
                           output_format: str = "human") -> bool:
    """
    Test the Rithmic Gateway Info endpoint (Templates 20/21).
    
    Args:
        system_name: System name to query gateways for
        uri: Optional custom URI to test
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful, False otherwise
    """
    config = get_config()
    message_handler = get_message_handler()
    
    # Override output format
    config.output_format = output_format
    
    # Use configured system name if not provided
    system_name = system_name or config.system_name
    
    print("=" * 60)
    print("🌐 TESTING RITHMIC GATEWAY INFO ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.GATEWAY_INFO_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.GATEWAY_INFO_RESPONSE} (Response)")
    print(f"URI: {uri or config.uri}")
    print(f"System Name: {system_name}")
    print(f"Paper Trading: {'✅ ENABLED' if config.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Get connection (no authentication needed for gateway info)
        connection = await get_connection("gateway_discovery", uri)
        if not connection:
            logger.error("Failed to establish connection")
            return False
        
        print("✅ WebSocket connection established")
        
        # Create gateway info request
        request = request_rithmic_system_gateway_info_pb2.RequestRithmicSystemGatewayInfo()
        request.template_id = TemplateIDs.GATEWAY_INFO_REQUEST
        request.user_msg.append("Gateway info request from endpoint tester")
        request.system_name = system_name
        
        # Send request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending gateway info request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            logger.error("Failed to send gateway info request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info:
            print("\n📋 REQUEST DETAILS:")
            print(message_handler.format_message(request_info, output_format))
        
        # Wait for response
        print("\n⏳ Waiting for gateway info response...")
        response_bytes = await connection.receive_message(timeout=config.message_timeout)
        
        if not response_bytes:
            logger.error("No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_rithmic_system_gateway_info_pb2.ResponseRithmicSystemGatewayInfo()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info:
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 RESPONSE ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful
        success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if success:
            print("✅ Gateway info request successful!")
            
            # Display gateway information
            if hasattr(response, 'gateway_name') and response.gateway_name:
                print(f"\n🌐 AVAILABLE GATEWAYS ({len(response.gateway_name)}):")
                print("-" * 50)
                
                # Create list of gateways with their details
                gateways = []
                for i in range(len(response.gateway_name)):
                    gateway = {
                        'name': response.gateway_name[i] if i < len(response.gateway_name) else 'N/A',
                        'domain': response.domain_server[i] if hasattr(response, 'domain_server') and i < len(response.domain_server) else 'N/A',
                        'logger': response.logger_server[i] if hasattr(response, 'logger_server') and i < len(response.logger_server) else 'N/A',
                        'license': response.license_server[i] if hasattr(response, 'license_server') and i < len(response.license_server) else 'N/A'
                    }
                    gateways.append(gateway)
                
                # Display gateway details
                for i, gateway in enumerate(gateways, 1):
                    print(f"{i:2d}. Gateway: {gateway['name']}")
                    if gateway['domain'] != 'N/A':
                        print(f"    Domain Server: {gateway['domain']}")
                    if gateway['logger'] != 'N/A':
                        print(f"    Logger Server: {gateway['logger']}")
                    if gateway['license'] != 'N/A':
                        print(f"    License Server: {gateway['license']}")
                    print()
                
                # Highlight configured gateway
                if config.gateway in response.gateway_name:
                    print(f"✅ Configured gateway found: {config.gateway}")
                else:
                    print(f"⚠️  Configured gateway not found: {config.gateway}")
                    print("   Available gateways listed above.")
                
                # Show additional response fields if present
                if hasattr(response, 'system_name') and response.system_name:
                    print(f"\n🖥️  System Name: {response.system_name}")
                
                if hasattr(response, 'infra_type') and response.infra_type:
                    infra_names = {
                        1: "Ticker Plant",
                        2: "Order Plant", 
                        3: "History Plant",
                        4: "PnL Plant",
                        5: "Repository Plant"
                    }
                    infra_list = [infra_names.get(infra, f"Unknown ({infra})") for infra in response.infra_type]
                    print(f"🏭 Infrastructure Types: {', '.join(infra_list)}")
                
            else:
                print("\n⚠️  No gateway information returned in response")
                print("   This may be normal for some systems or configurations")
        
        else:
            print("❌ Gateway info request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify the system name is correct")
            print("   • Check network connectivity")
            print("   • Ensure the system supports gateway queries")
        
        # Cleanup
        print("\n🔄 Closing connection...")
        await connection.disconnect()
        print("✅ Connection closed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing gateway info: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for gateway info testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Gateway Info endpoint (Templates 20/21)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_gateway_info.py
  python test_gateway_info.py --system "Rithmic Paper Trading"
  python test_gateway_info.py --format json
  python test_gateway_info.py --uri wss://rituz00100.rithmic.com:443
  python test_gateway_info.py --log-level DEBUG

Safety:
  This script performs READ-ONLY gateway discovery operations only.
  No authentication required. No account modifications performed.
        """
    )
    
    parser.add_argument(
        "--system",
        type=str,
        help="System name to query gateways for (default from config)"
    )
    
    parser.add_argument(
        "--uri",
        type=str,
        help="Custom URI to test (default from config)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config = get_config()
        config.save_responses = True
    
    print("🧪 RITHMIC GATEWAY INFO ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY GATEWAY DISCOVERY ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_gateway_info(
        system_name=args.system,
        uri=args.uri,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Gateway info test completed successfully!")
        return 0
    else:
        print("\n❌ Gateway info test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)