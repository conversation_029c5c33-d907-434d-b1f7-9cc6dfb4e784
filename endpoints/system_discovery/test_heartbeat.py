#!/usr/bin/env python3

"""
Test Rithmic Heartbeat endpoints (Templates 18/19).

This script demonstrates heartbeat functionality with the Rithmic API,
including manual heartbeat sending and automatic heartbeat management.

SAFETY: This script only performs READ-ONLY heartbeat operations.
No account modifications or order operations are performed.
Requires authentication to test heartbeat after login.
"""

import asyncio
import argparse
import logging
import sys
import os
import time
from typing import Optional

# Add the proto_generated directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'proto_generated'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

import request_heartbeat_pb2
import response_heartbeat_pb2

from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection, RithmicAuthenticator
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_heartbeat_manual(authenticator: Rith<PERSON><PERSON>uthenticator,
                               output_format: str = "human") -> bool:
    """
    Test manual heartbeat sending (Templates 18/19).
    
    Args:
        authenticator: Authenticated connection handler
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful, False otherwise
    """
    config = get_config()
    message_handler = get_message_handler()
    
    print("\n" + "=" * 60)
    print("💓 TESTING MANUAL HEARTBEAT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.HEARTBEAT_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.HEARTBEAT_RESPONSE} (Response)")
    print("=" * 60)
    
    try:
        # Send manual heartbeat
        print("📤 Sending manual heartbeat request...")
        start_time = time.time()
        
        success = await authenticator.send_heartbeat()
        if not success:
            print("❌ Failed to send heartbeat request")
            return False
        
        send_time = time.time() - start_time
        print(f"✅ Heartbeat request sent ({send_time:.3f}s)")
        
        # Wait for heartbeat response
        print("⏳ Waiting for heartbeat response...")
        response_bytes = await authenticator.connection.receive_message(timeout=config.message_timeout)
        
        if not response_bytes:
            print("❌ No heartbeat response received within timeout")
            return False
        
        receive_time = time.time() - start_time
        print(f"📥 Heartbeat response received ({receive_time:.3f}s total)")
        
        # Parse response
        response = response_heartbeat_pb2.ResponseHeartbeat()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info:
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler.format_message(response_info, output_format))
        
        # Analyze response
        print("\n📊 HEARTBEAT ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"Round-trip time: {receive_time:.3f} seconds")
        
        if hasattr(response, 'user_msg') and response.user_msg:
            print(f"User Messages: {list(response.user_msg)}")
        
        if hasattr(response, 'ssboe'):
            print(f"Server Time (SSBOE): {response.ssboe}")
        
        if hasattr(response, 'usecs'):
            print(f"Server Time (µsecs): {response.usecs}")
        
        # Calculate timing statistics
        if receive_time < 0.1:
            print("🚀 Excellent response time (< 100ms)")
        elif receive_time < 0.5:
            print("✅ Good response time (< 500ms)")
        elif receive_time < 1.0:
            print("⚠️  Moderate response time (< 1s)")
        else:
            print("🐌 Slow response time (> 1s)")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in manual heartbeat test: {e}")
        print(f"❌ Manual heartbeat test failed: {e}")
        return False

async def test_heartbeat_automatic(authenticator: RithmicAuthenticator,
                                  duration: int = 30,
                                  output_format: str = "human") -> bool:
    """
    Test automatic heartbeat functionality.
    
    Args:
        authenticator: Authenticated connection handler
        duration: Test duration in seconds
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful, False otherwise
    """
    print("\n" + "=" * 60)
    print("🔄 TESTING AUTOMATIC HEARTBEAT")
    print("=" * 60)
    print(f"Duration: {duration} seconds")
    print(f"Heartbeat interval: {get_config().heartbeat_interval} seconds")
    print("=" * 60)
    
    try:
        # Monitor connection and count messages
        start_time = time.time()
        message_count = 0
        heartbeat_count = 0
        
        print(f"⏳ Monitoring heartbeat for {duration} seconds...")
        print("   Press Ctrl+C to stop early")
        
        while time.time() - start_time < duration:
            try:
                # Check for incoming messages (including heartbeat responses)
                response_bytes = await authenticator.connection.receive_message(timeout=1.0)
                
                if response_bytes:
                    message_count += 1
                    
                    # Check if it's a heartbeat response
                    response_info = parse_message(response_bytes)
                    if response_info and response_info.template_id == TemplateIDs.HEARTBEAT_RESPONSE:
                        heartbeat_count += 1
                        elapsed = time.time() - start_time
                        print(f"💓 Heartbeat #{heartbeat_count} received at {elapsed:.1f}s")
                        
                        if output_format == "human":
                            # Show minimal heartbeat info
                            response = response_heartbeat_pb2.ResponseHeartbeat()
                            response.ParseFromString(response_bytes)
                            if hasattr(response, 'ssboe'):
                                print(f"   Server time: {response.ssboe}")
                    else:
                        print(f"📨 Other message received: {response_info.template_name if response_info else 'Unknown'}")
                
                # Show progress indicator
                elapsed = time.time() - start_time
                remaining = duration - elapsed
                if int(elapsed) % 5 == 0 and remaining > 0:
                    print(f"⏱️  {remaining:.0f}s remaining, {heartbeat_count} heartbeats received")
                
            except asyncio.TimeoutError:
                # No message received, this is normal
                continue
        
        elapsed_time = time.time() - start_time
        
        print("\n📊 AUTOMATIC HEARTBEAT SUMMARY:")
        print(f"Test duration: {elapsed_time:.1f} seconds")
        print(f"Total messages: {message_count}")
        print(f"Heartbeat responses: {heartbeat_count}")
        
        # Calculate expected heartbeats
        expected_heartbeats = elapsed_time / get_config().heartbeat_interval
        print(f"Expected heartbeats: ~{expected_heartbeats:.1f}")
        
        # Analyze results
        if heartbeat_count > 0:
            avg_interval = elapsed_time / heartbeat_count
            print(f"Average interval: {avg_interval:.1f} seconds")
            
            if abs(avg_interval - get_config().heartbeat_interval) < 2.0:
                print("✅ Heartbeat timing is within expected range")
            else:
                print("⚠️  Heartbeat timing differs from configured interval")
        
        # Check connection health
        if authenticator.connection.is_connected():
            print("✅ Connection remained stable during test")
        else:
            print("❌ Connection lost during test")
            return False
        
        return heartbeat_count > 0
        
    except KeyboardInterrupt:
        print("\n🛑 Automatic heartbeat test interrupted by user")
        return True
    except Exception as e:
        logger.error(f"Error in automatic heartbeat test: {e}")
        print(f"❌ Automatic heartbeat test failed: {e}")
        return False

async def test_heartbeat_comprehensive(infra_type: int = InfraType.TICKER_PLANT,
                                     manual_test: bool = True,
                                     auto_test: bool = True,
                                     auto_duration: int = 30,
                                     output_format: str = "human") -> bool:
    """
    Comprehensive heartbeat testing.
    
    Args:
        infra_type: Infrastructure plant type for authentication
        manual_test: Whether to test manual heartbeat
        auto_test: Whether to test automatic heartbeat
        auto_duration: Duration for automatic test
        output_format: Output format
        
    Returns:
        bool: True if all enabled tests successful
    """
    config = get_config()
    
    print("=" * 60)
    print("💓 COMPREHENSIVE HEARTBEAT TESTING")
    print("=" * 60)
    print(f"Infrastructure: {infra_type}")
    print(f"Manual Test: {'✅' if manual_test else '❌'}")
    print(f"Automatic Test: {'✅' if auto_test else '❌'}")
    if auto_test:
        print(f"Auto Duration: {auto_duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection
        print("🔐 Establishing authenticated connection...")
        connection, authenticator = await create_authenticated_connection(infra_type, "heartbeat_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        results = []
        
        # Test manual heartbeat
        if manual_test:
            manual_result = await test_heartbeat_manual(authenticator, output_format)
            results.append(("Manual Heartbeat", manual_result))
        
        # Test automatic heartbeat
        if auto_test:
            auto_result = await test_heartbeat_automatic(authenticator, auto_duration, output_format)
            results.append(("Automatic Heartbeat", auto_result))
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 HEARTBEAT TEST SUMMARY")
        print("=" * 60)
        
        all_passed = True
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        print("=" * 60)
        if all_passed:
            print("🎉 ALL HEARTBEAT TESTS PASSED!")
        else:
            print("⚠️  SOME HEARTBEAT TESTS FAILED!")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"Error in comprehensive heartbeat test: {e}")
        print(f"❌ Comprehensive test failed: {e}")
        return False

async def main():
    """Main function for heartbeat testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Heartbeat endpoints (Templates 18/19)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_heartbeat.py
  python test_heartbeat.py --manual-only
  python test_heartbeat.py --auto-only --duration 60
  python test_heartbeat.py --infra-type 2  # Order Plant
  python test_heartbeat.py --format json
  python test_heartbeat.py --log-level DEBUG

Safety:
  This script performs READ-ONLY heartbeat operations only.
  Requires authentication but no account modifications performed.
        """
    )
    
    parser.add_argument(
        "--infra-type",
        type=int,
        choices=[1, 2, 3, 4, 5],
        default=1,
        help="Infrastructure plant type (1=Ticker, 2=Order, 3=History, 4=PnL, 5=Repository)"
    )
    
    parser.add_argument(
        "--manual-only",
        action="store_true",
        help="Test manual heartbeat only"
    )
    
    parser.add_argument(
        "--auto-only",
        action="store_true",
        help="Test automatic heartbeat only"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=30,
        help="Duration for automatic heartbeat test in seconds (default: 30)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config = get_config()
        config.save_responses = True
    
    # Determine test types
    manual_test = not args.auto_only
    auto_test = not args.manual_only
    
    print("🧪 RITHMIC HEARTBEAT ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY HEARTBEAT OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_heartbeat_comprehensive(
        infra_type=args.infra_type,
        manual_test=manual_test,
        auto_test=auto_test,
        auto_duration=args.duration,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Heartbeat tests completed successfully!")
        return 0
    else:
        print("\n❌ Heartbeat tests failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)