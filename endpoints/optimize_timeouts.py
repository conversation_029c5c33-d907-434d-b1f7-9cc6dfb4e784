#!/usr/bin/env python3

"""
Comprehensive timeout optimization utility for Rithmic API endpoints.

This script analyzes timeout performance across all endpoints and provides
intelligent optimization recommendations based on historical data and analytics.
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# Add the required paths
project_root = Path(__file__).parent
sys.path.append(str(project_root / 'shared'))

from timeout_analytics import get_timeout_analytics, run_timeout_optimization_report
from timeout_manager import TimeoutManager
from config import get_config, InfraType

async def run_timeout_benchmark(endpoint_name: str, plant_type: int, iterations: int = 5):
    """Run timeout benchmark for a specific endpoint."""
    print(f"🔬 BENCHMARKING: {endpoint_name} ({InfraType.get_plant_name(plant_type)})")
    print("-" * 60)
    
    analytics = get_timeout_analytics()
    
    # Get current timeout strategy
    manager = TimeoutManager(endpoint_name, plant_type)
    strategy = manager.timeout_strategy
    
    print(f"Current Strategy: {strategy['progressive_timeouts']}")
    print(f"Optimization Source: {strategy.get('optimization_source', 'default')}")
    print()
    
    # Simulate different timeout scenarios
    scenarios = [
        ("Conservative", int(strategy['base_timeout'] * 1.5)),
        ("Balanced", strategy['base_timeout']),
        ("Aggressive", int(strategy['base_timeout'] * 0.7))
    ]
    
    results = []
    
    for scenario_name, timeout_value in scenarios:
        print(f"Testing {scenario_name} scenario (timeout: {timeout_value}s)...")
        
        # Simulate endpoint calls with different success rates and response times
        scenario_results = {
            "name": scenario_name,
            "timeout": timeout_value,
            "simulated_calls": iterations,
            "estimated_success_rate": 0.0,
            "estimated_avg_response": 0.0,
            "efficiency_score": 0.0
        }
        
        # Simulate based on historical data if available
        if analytics._has_sufficient_data(endpoint_name, plant_type):
            endpoint_key = analytics._get_endpoint_key(endpoint_name, plant_type)
            stats = analytics.endpoint_stats[endpoint_key]
            
            # Estimate success rate based on timeout vs historical response times
            if timeout_value > stats.p95_response_time:
                scenario_results["estimated_success_rate"] = min(0.98, stats.success_rate + 0.1)
            elif timeout_value > stats.median_response_time:
                scenario_results["estimated_success_rate"] = stats.success_rate
            else:
                scenario_results["estimated_success_rate"] = max(0.5, stats.success_rate - 0.2)
            
            scenario_results["estimated_avg_response"] = stats.avg_response_time
            scenario_results["efficiency_score"] = stats.avg_response_time / timeout_value
        else:
            # Default estimates for endpoints without data
            base_response_time = timeout_value * 0.6
            if scenario_name == "Conservative":
                scenario_results["estimated_success_rate"] = 0.95
                scenario_results["estimated_avg_response"] = base_response_time
            elif scenario_name == "Balanced":
                scenario_results["estimated_success_rate"] = 0.85
                scenario_results["estimated_avg_response"] = base_response_time
            else:  # Aggressive
                scenario_results["estimated_success_rate"] = 0.70
                scenario_results["estimated_avg_response"] = base_response_time
            
            scenario_results["efficiency_score"] = scenario_results["estimated_avg_response"] / timeout_value
        
        results.append(scenario_results)
        
        print(f"   Estimated Success Rate: {scenario_results['estimated_success_rate']:.1%}")
        print(f"   Estimated Avg Response: {scenario_results['estimated_avg_response']:.2f}s")
        print(f"   Efficiency Score: {scenario_results['efficiency_score']:.2f}")
        print()
    
    # Recommend best scenario
    best_scenario = max(results, key=lambda x: x['estimated_success_rate'] * x['efficiency_score'])
    print(f"🎯 RECOMMENDATION: {best_scenario['name']} scenario")
    print(f"   Timeout: {best_scenario['timeout']}s")
    print(f"   Expected Success Rate: {best_scenario['estimated_success_rate']:.1%}")
    print(f"   Expected Efficiency: {best_scenario['efficiency_score']:.2f}")
    print()
    
    return results

async def analyze_all_endpoints():
    """Analyze timeout performance across all configured endpoints."""
    print("🔍 ANALYZING ALL ENDPOINT TIMEOUTS")
    print("=" * 80)
    
    analytics = get_timeout_analytics()
    
    # Get list of endpoints to analyze
    config = get_config()
    endpoints_to_analyze = [
        # Order Plant endpoints
        ("account_list", InfraType.ORDER_PLANT),
        ("account_rms_info", InfraType.ORDER_PLANT),
        ("order_session_config", InfraType.ORDER_PLANT),
        ("exchange_permissions", InfraType.ORDER_PLANT),
        ("bracket_info", InfraType.ORDER_PLANT),
        
        # Historical Data endpoints
        ("time_bars", InfraType.HISTORY_PLANT),
        ("tick_bars", InfraType.HISTORY_PLANT),
        ("volume_bars", InfraType.HISTORY_PLANT),
        
        # Market Data endpoints
        ("market_data_subscription", InfraType.TICKER_PLANT),
        ("symbol_search", InfraType.TICKER_PLANT),
        ("product_codes", InfraType.TICKER_PLANT),
        
        # Repository endpoints
        ("list_agreements", InfraType.REPOSITORY_PLANT),
        ("show_agreement", InfraType.REPOSITORY_PLANT)
    ]
    
    analysis_results = []
    
    for endpoint_name, plant_type in endpoints_to_analyze:
        print(f"\n📊 ANALYZING: {endpoint_name} ({InfraType.get_plant_name(plant_type)})")
        
        # Check if we have data for this endpoint
        if analytics._has_sufficient_data(endpoint_name, plant_type):
            endpoint_key = analytics._get_endpoint_key(endpoint_name, plant_type)
            stats = analytics.endpoint_stats[endpoint_key]
            
            analysis = {
                "endpoint": endpoint_name,
                "plant": InfraType.get_plant_name(plant_type),
                "has_data": True,
                "total_calls": stats.total_calls,
                "success_rate": stats.success_rate,
                "avg_response_time": stats.avg_response_time,
                "recommended_timeout": stats.recommended_timeout,
                "current_efficiency": stats.timeout_efficiency,
                "optimization_status": "optimal" if stats.success_rate > 0.9 and stats.timeout_efficiency > 0.6 else "needs_optimization"
            }
            
            print(f"   📈 Success Rate: {stats.success_rate:.1%}")
            print(f"   ⏱️  Avg Response: {stats.avg_response_time:.2f}s")
            print(f"   🎯 Recommended Timeout: {stats.recommended_timeout}s")
            print(f"   ⚡ Efficiency: {stats.timeout_efficiency:.2f}")
            print(f"   📊 Status: {analysis['optimization_status'].replace('_', ' ').title()}")
            
        else:
            analysis = {
                "endpoint": endpoint_name,
                "plant": InfraType.get_plant_name(plant_type),
                "has_data": False,
                "optimization_status": "insufficient_data"
            }
            
            print(f"   ❌ No performance data available")
            print(f"   💡 Recommendation: Run endpoint tests to collect data")
        
        analysis_results.append(analysis)
    
    # Summary
    print(f"\n📋 ANALYSIS SUMMARY")
    print("=" * 40)
    
    endpoints_with_data = [a for a in analysis_results if a["has_data"]]
    optimal_endpoints = [a for a in endpoints_with_data if a["optimization_status"] == "optimal"]
    need_optimization = [a for a in endpoints_with_data if a["optimization_status"] == "needs_optimization"]
    no_data = [a for a in analysis_results if not a["has_data"]]
    
    print(f"Total Endpoints Analyzed: {len(analysis_results)}")
    print(f"Endpoints with Data: {len(endpoints_with_data)}")
    print(f"Optimal Performance: {len(optimal_endpoints)}")
    print(f"Need Optimization: {len(need_optimization)}")
    print(f"Insufficient Data: {len(no_data)}")
    
    if need_optimization:
        print(f"\n🔧 ENDPOINTS NEEDING OPTIMIZATION:")
        for endpoint in need_optimization:
            print(f"   • {endpoint['endpoint']} ({endpoint['plant']})")
            print(f"     Success Rate: {endpoint['success_rate']:.1%}, Efficiency: {endpoint['current_efficiency']:.2f}")
    
    return analysis_results

async def optimize_endpoint_timeouts(dry_run: bool = True):
    """Optimize timeout configurations based on analytics data."""
    print("🎯 TIMEOUT OPTIMIZATION")
    print("=" * 40)
    
    if dry_run:
        print("👀 DRY RUN MODE - No changes will be made")
    else:
        print("⚠️  LIVE MODE - Configuration will be updated")
    
    print()
    
    analytics = get_timeout_analytics()
    config = get_config()
    
    optimizations = []
    
    # Analyze each endpoint with sufficient data
    for endpoint_key, stats in analytics.endpoint_stats.items():
        if stats.total_calls >= 5:  # Sufficient data threshold
            endpoint_name, plant_type = analytics._parse_endpoint_key(endpoint_key)
            
            # Get current configured timeout
            from config import get_endpoint_timeout
            current_timeout = get_endpoint_timeout(endpoint_name, plant_type)
            optimized_timeout = analytics.get_optimized_timeout(endpoint_name, plant_type)
            
            if abs(current_timeout - optimized_timeout) > 2:  # Significant difference
                optimization = {
                    "endpoint": endpoint_name,
                    "plant": InfraType.get_plant_name(plant_type),
                    "current_timeout": current_timeout,
                    "optimized_timeout": optimized_timeout,
                    "improvement": current_timeout - optimized_timeout,
                    "confidence": "high" if stats.total_calls >= 10 else "medium"
                }
                optimizations.append(optimization)
                
                print(f"📈 {endpoint_name} ({optimization['plant']}):")
                print(f"   Current: {current_timeout}s → Optimized: {optimized_timeout}s")
                print(f"   Improvement: {optimization['improvement']:+.1f}s ({optimization['confidence']} confidence)")
                print()
    
    if not optimizations:
        print("✅ All endpoints are already optimally configured!")
        return
    
    if not dry_run:
        # Apply optimizations to configuration
        for opt in optimizations:
            print(f"Applying optimization for {opt['endpoint']}...")
            # This would update the configuration file
            # config.endpoint_timeouts[opt['endpoint']] = opt['optimized_timeout']
        
        print("💾 Timeout optimizations applied!")
    else:
        print(f"💡 Found {len(optimizations)} optimization opportunities")
        print("   Run with --apply to implement changes")

async def main():
    """Main timeout optimization utility."""
    parser = argparse.ArgumentParser(
        description="Timeout optimization utility for Rithmic API endpoints",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python optimize_timeouts.py --analyze        # Analyze all endpoints
  python optimize_timeouts.py --report         # Generate comprehensive report
  python optimize_timeouts.py --benchmark account_rms_info order_plant
  python optimize_timeouts.py --optimize --dry-run
  python optimize_timeouts.py --optimize --apply

Commands:
  --analyze     Analyze timeout performance across all endpoints
  --report      Generate comprehensive timeout optimization report
  --benchmark   Run timeout benchmark for specific endpoint
  --optimize    Optimize timeout configurations based on analytics
        """
    )
    
    parser.add_argument("--analyze", action="store_true",
                       help="Analyze timeout performance across all endpoints")
    
    parser.add_argument("--report", action="store_true",
                       help="Generate comprehensive timeout optimization report")
    
    parser.add_argument("--benchmark", nargs=2, metavar=("ENDPOINT", "PLANT"),
                       help="Run timeout benchmark for specific endpoint")
    
    parser.add_argument("--optimize", action="store_true",
                       help="Optimize timeout configurations")
    
    parser.add_argument("--dry-run", action="store_true", default=True,
                       help="Dry run mode (default)")
    
    parser.add_argument("--apply", action="store_true",
                       help="Apply optimizations (overrides --dry-run)")
    
    args = parser.parse_args()
    
    # Validate plant type for benchmark
    if args.benchmark:
        endpoint_name, plant_name = args.benchmark
        plant_mapping = {
            "ticker_plant": InfraType.TICKER_PLANT,
            "order_plant": InfraType.ORDER_PLANT,
            "history_plant": InfraType.HISTORY_PLANT,
            "pnl_plant": InfraType.PNL_PLANT,
            "repository_plant": InfraType.REPOSITORY_PLANT
        }
        
        if plant_name.lower() not in plant_mapping:
            print(f"❌ Invalid plant type: {plant_name}")
            print(f"Valid options: {', '.join(plant_mapping.keys())}")
            return 1
        
        plant_type = plant_mapping[plant_name.lower()]
    
    print("⚡ RITHMIC API TIMEOUT OPTIMIZATION UTILITY")
    print("=" * 60)
    print()
    
    try:
        if args.analyze:
            await analyze_all_endpoints()
            
        elif args.report:
            await run_timeout_optimization_report()
            
        elif args.benchmark:
            await run_timeout_benchmark(endpoint_name, plant_type)
            
        elif args.optimize:
            dry_run = not args.apply
            await optimize_endpoint_timeouts(dry_run)
            
        else:
            print("❓ No command specified. Use --help for options.")
            parser.print_help()
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 Operation interrupted by user")
        return 130
    except Exception as e:
        print(f"\n💥 Error: {e}")
        return 1
    
    print("\n✅ Timeout optimization utility completed successfully!")
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)