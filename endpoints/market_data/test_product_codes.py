#!/usr/bin/env python3

"""
Test Rithmic Product Codes endpoints (Templates 111/112).

This script demonstrates how to retrieve product code information
from the Rithmic API, providing available product listings.

SAFETY: This script only performs READ-ONLY product code requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_product_codes_pb2
import response_product_codes_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_product_codes(exchange: Optional[str] = None,
                           output_format: str = "human") -> bool:
    """
    Test product codes retrieval functionality.
    
    Args:
        exchange: Optional exchange filter
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🏭 TESTING PRODUCT CODES ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.PRODUCT_CODES_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.PRODUCT_CODES_RESPONSE} (Response)")
    print(f"Exchange Filter: {exchange or 'All exchanges'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "product_codes_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create product codes request
        request = request_product_codes_pb2.RequestProductCodes()
        request.template_id = TemplateIDs.PRODUCT_CODES_REQUEST
        request.user_msg.append("Product codes request from endpoint tester")
        
        # Set exchange filter if provided
        if exchange:
            request.exchange = exchange
        
        # Send product codes request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending product codes request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send product codes request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for product codes response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_product_codes_pb2.ResponseProductCodes()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 PRODUCT CODES ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Product codes request successful!")
            
            # Display product codes information
            products = []
            if hasattr(response, 'product_code') and response.product_code:
                print(f"\n🏭 AVAILABLE PRODUCTS ({len(response.product_code)}):")
                print("-" * 80)
                
                for i, product_code in enumerate(response.product_code):
                    product = {
                        "product_code": product_code,
                        "exchange": "",
                        "description": "",
                        "product_type": "",
                        "currency": ""
                    }
                    
                    # Get corresponding product details
                    if hasattr(response, 'exchange') and i < len(response.exchange):
                        product["exchange"] = response.exchange[i]
                    
                    if hasattr(response, 'product_name') and i < len(response.product_name):
                        product["description"] = response.product_name[i]
                    
                    if hasattr(response, 'product_type') and i < len(response.product_type):
                        product["product_type"] = response.product_type[i]
                    
                    if hasattr(response, 'currency') and i < len(response.currency):
                        product["currency"] = response.currency[i]
                    
                    products.append(product)
                
                # Display products
                if output_format == "human":
                    for i, product in enumerate(products, 1):
                        print(f"{i:3d}. Product Code: {product['product_code']}")
                        if product['exchange']:
                            print(f"     Exchange: {product['exchange']}")
                        if product['description']:
                            print(f"     Description: {product['description']}")
                        if product['product_type']:
                            print(f"     Type: {product['product_type']}")
                        if product['currency']:
                            print(f"     Currency: {product['currency']}")
                        print()
                
                elif output_format == "csv":
                    # Print CSV header
                    headers = ["Product_Code", "Exchange", "Description", "Type", "Currency"]
                    print(",".join(headers))
                    
                    # Print product data
                    for product in products:
                        values = [
                            product['product_code'],
                            product['exchange'],
                            product['description'].replace(',', ';'),  # Escape commas
                            product['product_type'],
                            product['currency']
                        ]
                        print(",".join(values))
                
                # Summary statistics
                print(f"\n📈 PRODUCT SUMMARY:")
                print(f"Total Products: {len(products)}")
                
                # Analyze by exchange
                if products:
                    exchanges = set(p['exchange'] for p in products if p['exchange'])
                    if exchanges:
                        print(f"Exchanges: {len(exchanges)}")
                        if len(exchanges) <= 10:
                            print(f"Exchange List: {', '.join(sorted(exchanges))}")
                    
                    # Analyze by product type
                    product_types = set(p['product_type'] for p in products if p['product_type'])
                    if product_types:
                        print(f"Product Types: {len(product_types)}")
                        if len(product_types) <= 10:
                            print(f"Type List: {', '.join(sorted(product_types))}")
                    
                    # Analyze by currency
                    currencies = set(p['currency'] for p in products if p['currency'])
                    if currencies:
                        print(f"Currencies: {len(currencies)}")
                        if len(currencies) <= 10:
                            print(f"Currency List: {', '.join(sorted(currencies))}")
                    
                    # Show samples by category
                    if len(products) > 10:
                        print(f"\n🔍 SAMPLE PRODUCTS:")
                        for i in range(min(5, len(products))):
                            p = products[i]
                            print(f"   {p['product_code']} @ {p['exchange']} - {p['description']}")
            
            else:
                print("\n📋 No product codes found")
                print("   This could indicate:")
                print("   • No products available for the specified exchange")
                print("   • Insufficient market data permissions")
                print("   • Exchange filter may be too restrictive")
        
        else:
            print("❌ Product codes request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Try without exchange filter to see all products")
            print("   • Verify exchange name is correct (e.g., CME, NASDAQ)")
            print("   • Check market data permissions")
            print("   • Ensure connection to correct system")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing product codes: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for product codes testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Product Codes endpoint (Templates 111/112)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_product_codes.py
  python test_product_codes.py --exchange CME
  python test_product_codes.py --exchange NASDAQ --format csv
  python test_product_codes.py --format json

Common Exchanges:
  CME, CBOT, NYMEX, COMEX (CME Group)
  NASDAQ, NYSE (Equity markets)
  ICE (Intercontinental Exchange)

Safety:
  This script performs READ-ONLY product code requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays available product codes with details including:
  - Product codes and exchange information
  - Product descriptions and types
  - Currency information
  - Summary statistics by exchange and type
        """
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange filter (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC PRODUCT CODES ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY PRODUCT CODE OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_product_codes(
        exchange=args.exchange,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Product codes test completed successfully!")
        return 0
    else:
        print("\n❌ Product codes test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)