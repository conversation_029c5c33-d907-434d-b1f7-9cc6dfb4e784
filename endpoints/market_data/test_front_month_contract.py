#!/usr/bin/env python3

"""
Test Rithmic Front Month Contract endpoints (Templates 113/114).

This script demonstrates how to retrieve front month contract information
from the Rithmic API, which provides the most active contract for a product.

SAFETY: This script only performs READ-ONLY front month contract requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_front_month_contract_pb2
import response_front_month_contract_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_front_month_contract(symbol: str, exchange: str,
                                  output_format: str = "human") -> bool:
    """
    Test front month contract retrieval functionality.
    
    Args:
        symbol: Base symbol to get front month contract for (e.g., "ES", "NQ")
        exchange: Exchange name
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📅 TESTING FRONT MONTH CONTRACT ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.FRONT_MONTH_CONTRACT_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.FRONT_MONTH_CONTRACT_RESPONSE} (Response)")
    print(f"Base Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "front_month_contract_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create front month contract request
        request = request_front_month_contract_pb2.RequestFrontMonthContract()
        request.template_id = TemplateIDs.FRONT_MONTH_CONTRACT_REQUEST
        request.user_msg.append(f"Front month contract request for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        
        # Send front month contract request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending front month contract request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send front month contract request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for front month contract response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_front_month_contract_pb2.ResponseFrontMonthContract()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 FRONT MONTH CONTRACT ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Front month contract request successful!")
            
            # Display front month contract information
            print(f"\n📅 FRONT MONTH CONTRACT DETAILS:")
            print("-" * 60)
            
            # Basic contract information
            if hasattr(response, 'symbol') and response.symbol:
                print(f"Front Month Symbol: {response.symbol}")
            if hasattr(response, 'exchange') and response.exchange:
                print(f"Exchange: {response.exchange}")
            
            # Contract specifications
            contract_info = {}
            
            if hasattr(response, 'expiry_date') and response.expiry_date:
                contract_info["expiry_date"] = response.expiry_date
                print(f"Expiry Date: {response.expiry_date}")
            
            if hasattr(response, 'contract_month') and response.contract_month:
                contract_info["contract_month"] = response.contract_month
                print(f"Contract Month: {response.contract_month}")
            
            if hasattr(response, 'contract_year') and response.contract_year:
                contract_info["contract_year"] = response.contract_year
                print(f"Contract Year: {response.contract_year}")
            
            # Trading information
            if hasattr(response, 'tick_size') and response.tick_size:
                contract_info["tick_size"] = response.tick_size
                print(f"Tick Size: {response.tick_size}")
            
            if hasattr(response, 'display_factor') and response.display_factor:
                contract_info["display_factor"] = response.display_factor
                print(f"Display Factor: {response.display_factor}")
            
            if hasattr(response, 'currency') and response.currency:
                contract_info["currency"] = response.currency
                print(f"Currency: {response.currency}")
            
            # Market data availability
            if hasattr(response, 'is_tradeable'):
                tradeable = getattr(response, 'is_tradeable', False)
                contract_info["is_tradeable"] = tradeable
                print(f"Tradeable: {'Yes' if tradeable else 'No'}")
            
            # Additional contract details
            additional_fields = [
                'underlying_symbol', 'contract_size', 'price_increment',
                'settlement_currency', 'listing_date', 'first_notice_date',
                'last_trading_date', 'delivery_month'
            ]
            
            print(f"\n📋 ADDITIONAL CONTRACT INFORMATION:")
            for field in additional_fields:
                if hasattr(response, field):
                    value = getattr(response, field)
                    if value:
                        field_name = field.replace('_', ' ').title()
                        print(f"{field_name}: {value}")
                        contract_info[field] = value
            
            # Market status
            if hasattr(response, 'market_status') and response.market_status:
                print(f"\nMarket Status: {response.market_status}")
            
            # CSV output
            if output_format == "csv":
                print(f"\n📊 CSV FORMAT:")
                
                # Define key fields for CSV
                csv_fields = ['symbol', 'exchange', 'expiry_date', 'contract_month', 
                             'contract_year', 'tick_size', 'currency', 'is_tradeable']
                
                # Header
                headers = [f.replace('_', ' ').title() for f in csv_fields]
                print(",".join(headers))
                
                # Data
                values = []
                for field in csv_fields:
                    if hasattr(response, field):
                        value = getattr(response, field, '')
                    else:
                        value = contract_info.get(field, '')
                    values.append(str(value))
                print(",".join(values))
            
            # Provide interpretation
            if hasattr(response, 'symbol') and response.symbol:
                front_month_symbol = response.symbol
                base_symbol = symbol
                
                print(f"\n💡 INTERPRETATION:")
                print(f"For base symbol '{base_symbol}', the front month contract is '{front_month_symbol}'")
                
                if hasattr(response, 'expiry_date') and response.expiry_date:
                    print(f"This contract expires on {response.expiry_date}")
                
                print(f"Use '{front_month_symbol}' for market data subscriptions and trading")
        
        else:
            print("❌ Front month contract request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify base symbol is correct (e.g., 'ES' not 'ESH5')")
            print("   • Check if exchange is correct (e.g., CME for ES)")
            print("   • Ensure symbol has active contracts")
            print("   • Try with a different base symbol (e.g., NQ, CL)")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing front month contract: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_multiple_front_month_contracts(symbols: List[tuple],
                                            output_format: str = "human") -> bool:
    """
    Test front month contracts for multiple symbols.
    
    Args:
        symbols: List of (base_symbol, exchange) tuples
        output_format: Output format
        
    Returns:
        bool: True if all tests successful
    """
    print("=" * 60)
    print("📅 TESTING MULTIPLE FRONT MONTH CONTRACTS")
    print("=" * 60)
    print(f"Base Symbols: {', '.join([f'{s}@{e}' for s, e in symbols])}")
    print("=" * 60)
    
    results = []
    
    for i, (symbol, exchange) in enumerate(symbols, 1):
        print(f"\n{'='*20} REQUEST {i}/{len(symbols)} {'='*20}")
        success = await test_front_month_contract(symbol, exchange, output_format)
        results.append((f"{symbol}@{exchange}", success))
        
        if i < len(symbols):
            print("\n⏳ Waiting 3 seconds before next request...")
            await asyncio.sleep(3)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 MULTIPLE FRONT MONTH CONTRACT SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for symbol_exchange, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{symbol_exchange:20s}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL FRONT MONTH CONTRACT REQUESTS PASSED!")
    else:
        print("⚠️  SOME FRONT MONTH CONTRACT REQUESTS FAILED!")
    
    return all_passed

async def main():
    """Main function for front month contract testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Front Month Contract endpoint (Templates 113/114)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_front_month_contract.py --symbol ES --exchange CME
  python test_front_month_contract.py --symbol NQ --exchange CME
  python test_front_month_contract.py --multiple ES,CME NQ,CME CL,NYMEX
  python test_front_month_contract.py --symbol ES --exchange CME --format csv

Common Base Symbols:
  ES (E-mini S&P 500), NQ (E-mini NASDAQ), YM (E-mini Dow)
  CL (Crude Oil), GC (Gold), SI (Silver)
  ZN (10-Year Treasury), ZB (30-Year Treasury)

Safety:
  This script performs READ-ONLY front month contract requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays front month contract information including:
  - Active contract symbol for the base symbol
  - Expiry dates and contract specifications
  - Trading information and market status
  - Guidance on which contract to use for trading
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        help="Base symbol to get front month contract for (e.g., ES, NQ, CL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange name (e.g., CME, NYMEX, CBOT)"
    )
    
    parser.add_argument(
        "--multiple",
        nargs="+",
        help="Test multiple symbols in format BASE_SYMBOL,EXCHANGE"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.symbol and not args.multiple:
        print("❌ Either --symbol and --exchange, or --multiple must be specified")
        return 1
    
    if args.symbol and not args.exchange:
        print("❌ --exchange is required when using --symbol")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC FRONT MONTH CONTRACT ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY FRONT MONTH CONTRACT OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test(s)
    if args.multiple:
        # Parse multiple symbols
        symbols = []
        for item in args.multiple:
            try:
                symbol, exchange = item.split(',')
                symbols.append((symbol.strip(), exchange.strip()))
            except ValueError:
                print(f"❌ Invalid format for {item}. Use BASE_SYMBOL,EXCHANGE format.")
                return 1
        
        success = await test_multiple_front_month_contracts(symbols, args.format)
    else:
        success = await test_front_month_contract(args.symbol, args.exchange, args.format)
    
    if success:
        print("\n✅ Front month contract test completed successfully!")
        return 0
    else:
        print("\n❌ Front month contract test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)