#!/usr/bin/env python3

"""
Test Rithmic Streaming Trade Data endpoints.

This script demonstrates how to subscribe to real-time trade data streams
from the Rithmic API, processing Last Trade (Template 150) updates.

SAFETY: This script only performs READ-ONLY streaming trade data requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_market_data_update_pb2
import response_market_data_update_pb2
import last_trade_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class TradeAnalyzer:
    """Analyzes streaming trade data for insights."""
    
    def __init__(self):
        self.trades = []
        self.symbols_seen = set()
        self.price_history = {}
        self.volume_history = {}
        
    def add_trade(self, trade_data: Dict[str, Any]):
        """Add a trade to the analysis."""
        self.trades.append(trade_data)
        symbol = trade_data.get('symbol', '')
        self.symbols_seen.add(symbol)
        
        # Track price history
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        if trade_data.get('trade_price'):
            self.price_history[symbol].append(trade_data['trade_price'])
        
        # Track volume history
        if symbol not in self.volume_history:
            self.volume_history[symbol] = []
        if trade_data.get('volume'):
            self.volume_history[symbol].append(trade_data['volume'])
    
    def get_summary(self) -> Dict[str, Any]:
        """Get trade analysis summary."""
        summary = {
            "total_trades": len(self.trades),
            "unique_symbols": len(self.symbols_seen),
            "symbols": list(self.symbols_seen),
            "price_analysis": {},
            "volume_analysis": {},
            "trade_frequency": 0
        }
        
        # Price analysis per symbol
        for symbol, prices in self.price_history.items():
            if prices:
                summary["price_analysis"][symbol] = {
                    "min_price": min(prices),
                    "max_price": max(prices),
                    "latest_price": prices[-1],
                    "price_change": prices[-1] - prices[0] if len(prices) > 1 else 0,
                    "trade_count": len(prices)
                }
        
        # Volume analysis per symbol
        for symbol, volumes in self.volume_history.items():
            if volumes:
                summary["volume_analysis"][symbol] = {
                    "latest_volume": volumes[-1],
                    "avg_volume": sum(volumes) / len(volumes) if volumes else 0,
                    "volume_updates": len(volumes)
                }
        
        return summary

async def test_streaming_trades(symbol: str, exchange: str, duration: int = 60,
                              output_format: str = "human") -> bool:
    """
    Test streaming trade data functionality.
    
    Args:
        symbol: Trading symbol to subscribe to
        exchange: Exchange name
        duration: How long to listen for trade updates (seconds)
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    analyzer = TradeAnalyzer()
    
    print("=" * 60)
    print("📊 TESTING STREAMING TRADE DATA ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.MARKET_DATA_UPDATE_REQUEST} (Subscription)")
    print(f"Template ID: {TemplateIDs.LAST_TRADE} (Trade Updates)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Duration: {duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "streaming_trades_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create market data subscription request for trade updates
        request = request_market_data_update_pb2.RequestMarketDataUpdate()
        request.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
        request.user_msg.append(f"Streaming trade data subscription for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        request.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.SUBSCRIBE
        
        # Set update bits for trade data only
        request.update_bits = request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.LAST_TRADE
        
        # Send subscription request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending trade data subscription ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send trade data subscription")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for subscription response
        print("⏳ Waiting for subscription response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No subscription response received within timeout")
            return False
        
        print(f"📥 Received subscription response ({len(response_bytes)} bytes)")
        
        # Parse subscription response
        response = response_market_data_update_pb2.ResponseMarketDataUpdate()
        response.ParseFromString(response_bytes)
        
        # Check subscription success
        subscription_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if not subscription_success:
            print("❌ Trade data subscription failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            return False
        
        print("✅ Trade data subscription successful!")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Listen for trade updates
        print(f"\n📊 LISTENING FOR TRADE UPDATES ({duration} seconds)...")
        print("=" * 60)
        
        trade_count = 0
        start_time = asyncio.get_event_loop().time()
        last_trade_time = start_time
        
        while (asyncio.get_event_loop().time() - start_time) < duration:
            try:
                # Wait for trade updates
                update_bytes = await connection.receive_message(timeout=5.0)
                
                if not update_bytes:
                    continue  # Timeout, keep listening
                
                # Parse update to determine type
                update_info = parse_message(update_bytes)
                if not update_info:
                    continue
                
                template_id = update_info.template_id
                
                if template_id == TemplateIDs.LAST_TRADE:
                    # Last trade update
                    last_trade = last_trade_pb2.LastTrade()
                    last_trade.ParseFromString(update_bytes)
                    
                    trade_count += 1
                    current_time = asyncio.get_event_loop().time()
                    time_since_last = current_time - last_trade_time
                    last_trade_time = current_time
                    
                    # Extract trade data
                    trade_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "symbol": getattr(last_trade, 'symbol', ''),
                        "exchange": getattr(last_trade, 'exchange', ''),
                        "trade_price": getattr(last_trade, 'trade_price', 0),
                        "trade_size": getattr(last_trade, 'trade_size', 0),
                        "volume": getattr(last_trade, 'volume', 0),
                        "net_change": getattr(last_trade, 'net_change', 0),
                        "percent_change": getattr(last_trade, 'percent_change', 0),
                        "is_snapshot": getattr(last_trade, 'is_snapshot', False),
                        "time_gap": time_since_last
                    }
                    
                    # Add to analyzer
                    analyzer.add_trade(trade_data)
                    
                    # Display trade update
                    print(f"📊 TRADE #{trade_count} [{trade_data['timestamp']}]")
                    print(f"    Symbol: {trade_data['symbol']}@{trade_data['exchange']}")
                    
                    if trade_data['trade_price']:
                        print(f"    Price: {trade_data['trade_price']:8.2f}")
                    
                    if trade_data['trade_size']:
                        print(f"    Size: {trade_data['trade_size']:6d}")
                    
                    if trade_data['volume']:
                        print(f"    Volume: {trade_data['volume']:,}")
                    
                    if trade_data['net_change']:
                        change_symbol = "+" if trade_data['net_change'] > 0 else ""
                        print(f"    Change: {change_symbol}{trade_data['net_change']:6.2f}")
                    
                    if trade_data['percent_change']:
                        percent_symbol = "+" if trade_data['percent_change'] > 0 else ""
                        print(f"    % Change: {percent_symbol}{trade_data['percent_change']:5.2f}%")
                    
                    if trade_data['is_snapshot']:
                        print(f"    Type: SNAPSHOT")
                    
                    print(f"    Gap: {time_since_last:.3f}s")
                    
                    # Display detailed message if requested
                    if output_format != "human":
                        print(f"\n📋 TRADE UPDATE DETAILS:")
                        print(message_handler_obj.format_message(update_info, output_format))
                        print("-" * 40)
                    
                    print()  # Blank line for readability
                
                else:
                    # Other update types (should be minimal with trade-only subscription)
                    print(f"📡 OTHER UPDATE: Template {template_id}")
                
            except asyncio.TimeoutError:
                # No updates received in the timeout period
                continue
            except Exception as e:
                logger.warning(f"Error processing trade update: {e}")
                continue
        
        # Get analysis summary
        summary = analyzer.get_summary()
        
        print(f"\n📊 STREAMING TRADE DATA SUMMARY:")
        print("=" * 60)
        print(f"Symbol: {symbol}")
        print(f"Exchange: {exchange}")
        print(f"Duration: {duration} seconds")
        print(f"Total Trades: {summary['total_trades']}")
        print(f"Unique Symbols: {summary['unique_symbols']}")
        
        if summary['total_trades'] > 0:
            avg_frequency = summary['total_trades'] / duration
            print(f"Average Trade Frequency: {avg_frequency:.2f} trades/second")
        
        # Price analysis
        if summary['price_analysis']:
            print(f"\n📈 PRICE ANALYSIS:")
            for sym, analysis in summary['price_analysis'].items():
                print(f"  {sym}:")
                print(f"    Latest Price: {analysis['latest_price']:8.2f}")
                print(f"    Price Range: {analysis['min_price']:8.2f} - {analysis['max_price']:8.2f}")
                if analysis['price_change'] != 0:
                    change_symbol = "+" if analysis['price_change'] > 0 else ""
                    print(f"    Price Change: {change_symbol}{analysis['price_change']:6.2f}")
                print(f"    Trade Count: {analysis['trade_count']}")
        
        # Volume analysis
        if summary['volume_analysis']:
            print(f"\n📊 VOLUME ANALYSIS:")
            for sym, analysis in summary['volume_analysis'].items():
                print(f"  {sym}:")
                print(f"    Latest Volume: {analysis['latest_volume']:,}")
                print(f"    Average Volume: {analysis['avg_volume']:,.0f}")
                print(f"    Volume Updates: {analysis['volume_updates']}")
        
        # CSV output
        if output_format == "csv" and analyzer.trades:
            print(f"\n📊 CSV FORMAT (TRADE DATA):")
            csv_headers = ["Timestamp", "Symbol", "Exchange", "Price", "Size", 
                          "Volume", "Net_Change", "Percent_Change", "Is_Snapshot"]
            print(",".join(csv_headers))
            
            for trade in analyzer.trades[-50:]:  # Last 50 trades
                values = [
                    trade['timestamp'],
                    trade['symbol'],
                    trade['exchange'],
                    str(trade['trade_price']),
                    str(trade['trade_size']),
                    str(trade['volume']),
                    str(trade['net_change']),
                    str(trade['percent_change']),
                    str(trade['is_snapshot'])
                ]
                print(",".join(values))
        
        # Analysis and insights
        print(f"\n💡 TRADE STREAM ANALYSIS:")
        if summary['total_trades'] > 0:
            print(f"✅ Successfully received {summary['total_trades']} trade updates")
            
            if avg_frequency > 1:
                print(f"• High frequency trading: {avg_frequency:.2f} trades/sec")
            elif avg_frequency > 0.1:
                print(f"• Moderate trading activity: {avg_frequency:.2f} trades/sec")
            else:
                print(f"• Low trading activity: {avg_frequency:.2f} trades/sec")
            
            # Price movement analysis
            for sym, analysis in summary['price_analysis'].items():
                if analysis['price_change'] > 0:
                    print(f"• {sym}: Upward price movement (+{analysis['price_change']:.2f})")
                elif analysis['price_change'] < 0:
                    print(f"• {sym}: Downward price movement ({analysis['price_change']:.2f})")
                else:
                    print(f"• {sym}: Stable pricing")
        else:
            print("⚠️  No trade updates received")
            print("• Market may be closed")
            print("• Symbol may have limited trading activity")
            print("• Check market hours and symbol liquidity")
        
        # Unsubscribe
        print(f"\n📤 Unsubscribing from trade data...")
        request.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.UNSUBSCRIBE
        serialized_unsub = request.SerializeToString()
        
        if await connection.send_message(serialized_unsub):
            # Wait for unsubscribe response
            unsub_response = await connection.receive_message(timeout=5.0)
            if unsub_response:
                print("✅ Successfully unsubscribed")
            else:
                print("⚠️  Unsubscribe response not received")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return summary['total_trades'] > 0  # Success if we received any trade updates
        
    except Exception as e:
        logger.error(f"Error testing streaming trade data: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for streaming trade data testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Streaming Trade Data endpoint",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_streaming_trades.py --symbol ESH5 --exchange CME
  python test_streaming_trades.py --symbol AAPL --exchange NASDAQ --duration 120
  python test_streaming_trades.py --symbol ESH5 --exchange CME --format csv

Safety:
  This script performs READ-ONLY streaming trade data requests only.
  Requires authentication but no account modifications performed.
  Automatically unsubscribes at the end of the test.
  
Output:
  Displays real-time trade updates including:
  - Trade price, size, and volume
  - Net change and percent change
  - Trade frequency and timing analysis
  - Price movement and volume patterns
  - Summary statistics and insights
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Trading symbol to subscribe to (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        required=True,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=60,
        help="How long to listen for trade updates in seconds (default: 60)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC STREAMING TRADE DATA ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY STREAMING TRADE DATA OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_streaming_trades(
        args.symbol,
        args.exchange,
        args.duration,
        args.format
    )
    
    if success:
        print("\n✅ Streaming trade data test completed successfully!")
        return 0
    else:
        print("\n❌ Streaming trade data test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)