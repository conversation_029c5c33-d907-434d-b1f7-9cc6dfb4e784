#!/usr/bin/env python3

"""
Test Rithmic Auxiliary Reference Data endpoints (Templates 121/122).

This script demonstrates how to retrieve auxiliary reference data
from the Rithmic API, providing additional instrument specifications.

SAFETY: This script only performs READ-ONLY auxiliary reference data requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_auxilliary_reference_data_pb2
import response_auxilliary_reference_data_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_auxiliary_reference_data(symbol: str, exchange: str,
                                      output_format: str = "human") -> bool:
    """
    Test auxiliary reference data retrieval functionality.
    
    Args:
        symbol: Trading symbol to get auxiliary reference data for
        exchange: Exchange name
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🔧 TESTING AUXILIARY REFERENCE DATA ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.AUXILIARY_REFERENCE_DATA_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.AUXILIARY_REFERENCE_DATA_RESPONSE} (Response)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "auxiliary_reference_data_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create auxiliary reference data request
        request = request_auxilliary_reference_data_pb2.RequestAuxilliaryReferenceData()
        request.template_id = TemplateIDs.AUXILIARY_REFERENCE_DATA_REQUEST
        request.user_msg.append(f"Auxiliary reference data request for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        
        # Send auxiliary reference data request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending auxiliary reference data request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send auxiliary reference data request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for auxiliary reference data response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_auxilliary_reference_data_pb2.ResponseAuxilliaryReferenceData()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 AUXILIARY REFERENCE DATA ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Auxiliary reference data request successful!")
            
            # Display auxiliary reference data
            print(f"\n🔧 AUXILIARY INSTRUMENT DATA:")
            print("-" * 60)
            
            # Basic instrument information
            if hasattr(response, 'symbol') and response.symbol:
                print(f"Symbol: {response.symbol}")
            if hasattr(response, 'exchange') and response.exchange:
                print(f"Exchange: {response.exchange}")
            
            # Market making and liquidity information
            if hasattr(response, 'market_maker_spread') and response.market_maker_spread:
                print(f"Market Maker Spread: {response.market_maker_spread}")
            if hasattr(response, 'min_lot_size') and response.min_lot_size:
                print(f"Minimum Lot Size: {response.min_lot_size}")
            if hasattr(response, 'max_lot_size') and response.max_lot_size:
                print(f"Maximum Lot Size: {response.max_lot_size}")
            
            # Trading session information
            if hasattr(response, 'trading_session_hours') and response.trading_session_hours:
                print(f"Trading Session Hours: {response.trading_session_hours}")
            if hasattr(response, 'settlement_session_hours') and response.settlement_session_hours:
                print(f"Settlement Session Hours: {response.settlement_session_hours}")
            
            # Price and volume limits
            if hasattr(response, 'price_limit_upper') and response.price_limit_upper:
                print(f"Upper Price Limit: {response.price_limit_upper}")
            if hasattr(response, 'price_limit_lower') and response.price_limit_lower:
                print(f"Lower Price Limit: {response.price_limit_lower}")
            if hasattr(response, 'volume_limit') and response.volume_limit:
                print(f"Volume Limit: {response.volume_limit}")
            
            # Additional auxiliary fields
            auxiliary_fields = [
                'settlement_type', 'delivery_method', 'clearing_organization',
                'regulatory_classification', 'product_group', 'product_subgroup',
                'trading_halt_status', 'circuit_breaker_level', 'volatility_index',
                'option_underlying', 'option_style', 'option_exercise_type'
            ]
            
            print(f"\n📋 ADDITIONAL AUXILIARY DATA:")
            aux_data = {}
            for field in auxiliary_fields:
                if hasattr(response, field):
                    value = getattr(response, field)
                    if value:
                        field_name = field.replace('_', ' ').title()
                        print(f"{field_name}: {value}")
                        aux_data[field] = value
            
            # Market state and status information
            if hasattr(response, 'market_state') and response.market_state:
                print(f"\nMarket State: {response.market_state}")
            if hasattr(response, 'trading_status') and response.trading_status:
                print(f"Trading Status: {response.trading_status}")
            if hasattr(response, 'halt_reason') and response.halt_reason:
                print(f"Halt Reason: {response.halt_reason}")
            
            # Risk management information
            print(f"\n⚖️ RISK MANAGEMENT DATA:")
            risk_fields = [
                'margin_initial', 'margin_maintenance', 'margin_overnight',
                'position_limit_long', 'position_limit_short', 'concentration_limit'
            ]
            
            for field in risk_fields:
                if hasattr(response, field):
                    value = getattr(response, field)
                    if value:
                        field_name = field.replace('_', ' ').title()
                        print(f"{field_name}: {value}")
            
            # CSV output
            if output_format == "csv":
                print(f"\n📊 CSV FORMAT:")
                csv_fields = ['symbol', 'exchange', 'market_maker_spread', 'min_lot_size', 
                             'max_lot_size', 'price_limit_upper', 'price_limit_lower', 
                             'trading_status', 'market_state']
                
                # Header
                headers = [f.replace('_', ' ').title() for f in csv_fields]
                print(",".join(headers))
                
                # Data
                values = []
                for field in csv_fields:
                    if hasattr(response, field):
                        value = getattr(response, field, '')
                    else:
                        value = aux_data.get(field, '')
                    values.append(str(value))
                print(",".join(values))
            
            # Interpretation
            print(f"\n💡 INTERPRETATION:")
            print(f"Auxiliary data provides enhanced trading and risk information for {symbol}")
            
            if hasattr(response, 'min_lot_size') and response.min_lot_size:
                print(f"Minimum trade size: {response.min_lot_size} units")
            if hasattr(response, 'price_limit_upper') and response.price_limit_upper:
                print(f"Daily price limits help manage volatility risk")
            if hasattr(response, 'trading_status') and response.trading_status:
                print(f"Current trading status indicates market accessibility")
        
        else:
            print("❌ Auxiliary reference data request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify symbol and exchange are correct")
            print("   • Check if auxiliary data is available for this instrument")
            print("   • Ensure proper market data permissions")
            print("   • Try with a different symbol that supports auxiliary data")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing auxiliary reference data: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_multiple_auxiliary_reference_data(symbols: List[tuple],
                                               output_format: str = "human") -> bool:
    """
    Test auxiliary reference data for multiple symbols.
    
    Args:
        symbols: List of (symbol, exchange) tuples
        output_format: Output format
        
    Returns:
        bool: True if all tests successful
    """
    print("=" * 60)
    print("🔧 TESTING MULTIPLE AUXILIARY REFERENCE DATA REQUESTS")
    print("=" * 60)
    print(f"Symbols: {', '.join([f'{s}@{e}' for s, e in symbols])}")
    print("=" * 60)
    
    results = []
    
    for i, (symbol, exchange) in enumerate(symbols, 1):
        print(f"\n{'='*20} REQUEST {i}/{len(symbols)} {'='*20}")
        success = await test_auxiliary_reference_data(symbol, exchange, output_format)
        results.append((f"{symbol}@{exchange}", success))
        
        if i < len(symbols):
            print("\n⏳ Waiting 3 seconds before next request...")
            await asyncio.sleep(3)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 MULTIPLE AUXILIARY REFERENCE DATA SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for symbol_exchange, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{symbol_exchange:20s}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL AUXILIARY REFERENCE DATA REQUESTS PASSED!")
    else:
        print("⚠️  SOME AUXILIARY REFERENCE DATA REQUESTS FAILED!")
    
    return all_passed

async def main():
    """Main function for auxiliary reference data testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Auxiliary Reference Data endpoint (Templates 121/122)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_auxiliary_reference_data.py --symbol ESH5 --exchange CME
  python test_auxiliary_reference_data.py --symbol AAPL --exchange NASDAQ
  python test_auxiliary_reference_data.py --multiple ESH5,CME NQH5,CME
  python test_auxiliary_reference_data.py --symbol ESH5 --exchange CME --format json

Safety:
  This script performs READ-ONLY auxiliary reference data requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays comprehensive auxiliary instrument data including:
  - Market making and liquidity information
  - Trading session and settlement details
  - Price and volume limits
  - Risk management parameters
  - Trading status and market state
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        help="Trading symbol to get auxiliary reference data for (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--multiple",
        nargs="+",
        help="Test multiple symbols in format SYMBOL,EXCHANGE"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.symbol and not args.multiple:
        print("❌ Either --symbol and --exchange, or --multiple must be specified")
        return 1
    
    if args.symbol and not args.exchange:
        print("❌ --exchange is required when using --symbol")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC AUXILIARY REFERENCE DATA ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY AUXILIARY REFERENCE DATA OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test(s)
    if args.multiple:
        # Parse multiple symbols
        symbols = []
        for item in args.multiple:
            try:
                symbol, exchange = item.split(',')
                symbols.append((symbol.strip(), exchange.strip()))
            except ValueError:
                print(f"❌ Invalid format for {item}. Use SYMBOL,EXCHANGE format.")
                return 1
        
        success = await test_multiple_auxiliary_reference_data(symbols, args.format)
    else:
        success = await test_auxiliary_reference_data(args.symbol, args.exchange, args.format)
    
    if success:
        print("\n✅ Auxiliary reference data test completed successfully!")
        return 0
    else:
        print("\n❌ Auxiliary reference data test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)