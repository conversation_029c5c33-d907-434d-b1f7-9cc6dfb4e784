#!/usr/bin/env python3

"""
Test Rithmic Get Instrument by Underlying endpoints (Templates 102/103/104).

This script demonstrates how to retrieve instruments by underlying symbol
from the Rithmic API, providing related contract information.

SAFETY: This script only performs READ-ONLY instrument by underlying requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_get_instrument_by_underlying_pb2
import response_get_instrument_by_underlying_pb2
import response_get_instrument_by_underlying_keys_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_instrument_by_underlying(underlying_symbol: str, exchange: str,
                                      output_format: str = "human") -> bool:
    """
    Test instrument by underlying retrieval functionality.
    
    Args:
        underlying_symbol: Underlying symbol to search for (e.g., "ES", "AAPL")
        exchange: Exchange name
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🔍 TESTING INSTRUMENT BY UNDERLYING ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.GET_INSTRUMENT_BY_UNDERLYING_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.GET_INSTRUMENT_BY_UNDERLYING_RESPONSE} (Response)")
    print(f"Template ID: {TemplateIDs.GET_INSTRUMENT_BY_UNDERLYING_KEYS_RESPONSE} (Keys Response)")
    print(f"Underlying Symbol: {underlying_symbol}")
    print(f"Exchange: {exchange}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "instrument_by_underlying_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create instrument by underlying request
        request = request_get_instrument_by_underlying_pb2.RequestGetInstrumentByUnderlying()
        request.template_id = TemplateIDs.GET_INSTRUMENT_BY_UNDERLYING_REQUEST
        request.user_msg.append(f"Instrument by underlying request for {underlying_symbol}")
        
        request.underlying_symbol = underlying_symbol
        request.exchange = exchange
        
        # Send instrument by underlying request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending instrument by underlying request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send instrument by underlying request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for responses (may receive multiple)
        print("⏳ Waiting for instrument by underlying responses...")
        instruments = []
        response_count = 0
        max_responses = 50  # Limit to prevent infinite loop
        
        while response_count < max_responses:
            response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
            
            if not response_bytes:
                print(f"📥 No more responses received (got {response_count} responses)")
                break
                
            response_count += 1
            print(f"📥 Received response {response_count} ({len(response_bytes)} bytes)")
            
            # Parse response to determine type
            response_info = parse_message(response_bytes)
            if not response_info:
                continue
                
            # Handle different response types
            if response_info.template_id == TemplateIDs.GET_INSTRUMENT_BY_UNDERLYING_RESPONSE:
                # Main instrument response
                response = response_get_instrument_by_underlying_pb2.ResponseGetInstrumentByUnderlying()
                response.ParseFromString(response_bytes)
                
                # Check if this is the final response
                is_final = len(response.rp_code) == 1 and response.rp_code[0] == "0"
                
                if is_final:
                    print(f"✅ Received final response (Template {response.template_id})")
                    
                    # Parse and format the final response
                    if output_format != "human":
                        print("\n📋 FINAL RESPONSE DETAILS:")
                        print(message_handler_obj.format_message(response_info, output_format))
                    break
                else:
                    # Continue processing - this may contain instrument data
                    print(f"📊 Processing intermediate response (Template {response.template_id})")
                    
            elif response_info.template_id == TemplateIDs.GET_INSTRUMENT_BY_UNDERLYING_KEYS_RESPONSE:
                # Keys response containing instrument data
                keys_response = response_get_instrument_by_underlying_keys_pb2.ResponseGetInstrumentByUnderlyingKeys()
                keys_response.ParseFromString(response_bytes)
                
                print(f"🔑 Processing keys response (Template {keys_response.template_id})")
                
                # Extract instrument information
                if hasattr(keys_response, 'symbol') and keys_response.symbol:
                    for i, symbol in enumerate(keys_response.symbol):
                        instrument = {
                            "symbol": symbol,
                            "exchange": "",
                            "instrument_type": "",
                            "expiry_date": "",
                            "strike_price": "",
                            "option_type": "",
                            "underlying": underlying_symbol
                        }
                        
                        # Get corresponding data from other fields
                        if hasattr(keys_response, 'exchange') and i < len(keys_response.exchange):
                            instrument["exchange"] = keys_response.exchange[i]
                        
                        if hasattr(keys_response, 'instrument_type') and i < len(keys_response.instrument_type):
                            instrument["instrument_type"] = keys_response.instrument_type[i]
                        
                        if hasattr(keys_response, 'expiry_date') and i < len(keys_response.expiry_date):
                            instrument["expiry_date"] = keys_response.expiry_date[i]
                        
                        if hasattr(keys_response, 'strike_price') and i < len(keys_response.strike_price):
                            instrument["strike_price"] = keys_response.strike_price[i]
                        
                        if hasattr(keys_response, 'option_type') and i < len(keys_response.option_type):
                            instrument["option_type"] = keys_response.option_type[i]
                        
                        instruments.append(instrument)
                
                # Parse and format the keys response
                if output_format != "human":
                    print(f"\n📋 KEYS RESPONSE {response_count} DETAILS:")
                    print(message_handler_obj.format_message(response_info, output_format))
            
            else:
                print(f"📊 Received unexpected response type: Template {response_info.template_id}")
        
        # Analyze results
        print(f"\n📊 INSTRUMENT BY UNDERLYING ANALYSIS:")
        print(f"Underlying Symbol: {underlying_symbol}")
        print(f"Exchange: {exchange}")
        print(f"Total Responses: {response_count}")
        print(f"Instruments Found: {len(instruments)}")
        
        success = len(instruments) > 0 or response_count > 0
        
        if success and instruments:
            print("✅ Instrument by underlying request successful!")
            
            # Display instrument information
            print(f"\n🔍 RELATED INSTRUMENTS ({len(instruments)}):")
            print("-" * 80)
            
            # Group instruments by type
            instrument_types = {}
            for instrument in instruments:
                inst_type = instrument["instrument_type"] or "Unknown"
                if inst_type not in instrument_types:
                    instrument_types[inst_type] = []
                instrument_types[inst_type].append(instrument)
            
            # Display by instrument type
            for inst_type, type_instruments in instrument_types.items():
                print(f"\n📋 {inst_type.upper()} INSTRUMENTS ({len(type_instruments)}):")
                
                for i, instrument in enumerate(type_instruments[:20], 1):  # Limit display
                    print(f"{i:3d}. Symbol: {instrument['symbol']}")
                    if instrument['exchange']:
                        print(f"     Exchange: {instrument['exchange']}")
                    if instrument['expiry_date']:
                        print(f"     Expiry: {instrument['expiry_date']}")
                    if instrument['strike_price']:
                        print(f"     Strike: {instrument['strike_price']}")
                    if instrument['option_type']:
                        print(f"     Option Type: {instrument['option_type']}")
                    print()
                
                if len(type_instruments) > 20:
                    print(f"     ... and {len(type_instruments) - 20} more {inst_type} instruments")
            
            # CSV output
            if output_format == "csv":
                print(f"\n📊 CSV FORMAT:")
                csv_headers = ["Symbol", "Exchange", "Type", "Expiry_Date", 
                              "Strike_Price", "Option_Type", "Underlying"]
                print(",".join(csv_headers))
                
                for instrument in instruments:
                    values = [
                        instrument['symbol'],
                        instrument['exchange'],
                        instrument['instrument_type'],
                        instrument['expiry_date'],
                        str(instrument['strike_price']),
                        instrument['option_type'],
                        instrument['underlying']
                    ]
                    print(",".join(values))
            
            # Summary statistics
            print(f"\n📈 INSTRUMENT SUMMARY:")
            print(f"Total Instruments: {len(instruments)}")
            print(f"Instrument Types: {len(instrument_types)}")
            
            # Show exchanges
            exchanges = set(i['exchange'] for i in instruments if i['exchange'])
            if exchanges:
                print(f"Exchanges: {', '.join(sorted(exchanges))}")
            
            # Show expiry date range for futures/options
            expiry_dates = [i['expiry_date'] for i in instruments if i['expiry_date']]
            if expiry_dates:
                expiry_dates.sort()
                print(f"Expiry Range: {expiry_dates[0]} to {expiry_dates[-1]}")
            
            # Interpretation
            print(f"\n💡 INTERPRETATION:")
            print(f"Found {len(instruments)} instruments related to underlying '{underlying_symbol}'")
            
            if instrument_types:
                type_summary = ", ".join([f"{len(instruments)} {t}" for t, instruments in instrument_types.items()])
                print(f"Breakdown: {type_summary}")
            
            print(f"These instruments can be used for:")
            print(f"• Market data subscriptions")
            print(f"• Reference data requests")
            print(f"• Historical data analysis")
        
        elif success:
            print("✅ Request completed but no instruments found")
            print(f"This could indicate:")
            print(f"• No instruments available for underlying '{underlying_symbol}'")
            print(f"• Exchange filter may be too restrictive")
            print(f"• Underlying symbol may not exist or be active")
        
        else:
            print("❌ Instrument by underlying request failed!")
            print("• No responses received within timeout")
            print("• Check connection and authentication")
            print("• Verify underlying symbol and exchange are correct")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing instrument by underlying: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_multiple_instrument_by_underlying(symbols: List[tuple],
                                               output_format: str = "human") -> bool:
    """
    Test instrument by underlying for multiple symbols.
    
    Args:
        symbols: List of (underlying_symbol, exchange) tuples
        output_format: Output format
        
    Returns:
        bool: True if all tests successful
    """
    print("=" * 60)
    print("🔍 TESTING MULTIPLE INSTRUMENT BY UNDERLYING REQUESTS")
    print("=" * 60)
    print(f"Underlying Symbols: {', '.join([f'{s}@{e}' for s, e in symbols])}")
    print("=" * 60)
    
    results = []
    
    for i, (underlying_symbol, exchange) in enumerate(symbols, 1):
        print(f"\n{'='*20} REQUEST {i}/{len(symbols)} {'='*20}")
        success = await test_instrument_by_underlying(underlying_symbol, exchange, output_format)
        results.append((f"{underlying_symbol}@{exchange}", success))
        
        if i < len(symbols):
            print("\n⏳ Waiting 5 seconds before next request...")
            await asyncio.sleep(5)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 MULTIPLE INSTRUMENT BY UNDERLYING SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for symbol_exchange, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{symbol_exchange:20s}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL INSTRUMENT BY UNDERLYING REQUESTS PASSED!")
    else:
        print("⚠️  SOME INSTRUMENT BY UNDERLYING REQUESTS FAILED!")
    
    return all_passed

async def main():
    """Main function for instrument by underlying testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Get Instrument by Underlying endpoint (Templates 102/103/104)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_instrument_by_underlying.py --underlying ES --exchange CME
  python test_instrument_by_underlying.py --underlying AAPL --exchange NASDAQ
  python test_instrument_by_underlying.py --multiple ES,CME AAPL,NASDAQ NQ,CME
  python test_instrument_by_underlying.py --underlying ES --exchange CME --format csv

Common Underlying Symbols:
  ES (E-mini S&P 500), NQ (E-mini NASDAQ), YM (E-mini Dow)
  CL (Crude Oil), GC (Gold), SI (Silver)
  AAPL, MSFT, GOOGL (Equity options)

Safety:
  This script performs READ-ONLY instrument by underlying requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays related instruments for the underlying symbol including:
  - Futures contracts with different expiry dates
  - Options contracts with strikes and expiries
  - Instrument type classification
  - Exchange and trading information
  - Summary statistics and analysis
        """
    )
    
    parser.add_argument(
        "--underlying",
        type=str,
        help="Underlying symbol to search for (e.g., ES, AAPL, NQ)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--multiple",
        nargs="+",
        help="Test multiple underlying symbols in format SYMBOL,EXCHANGE"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.underlying and not args.multiple:
        print("❌ Either --underlying and --exchange, or --multiple must be specified")
        return 1
    
    if args.underlying and not args.exchange:
        print("❌ --exchange is required when using --underlying")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC INSTRUMENT BY UNDERLYING ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY INSTRUMENT BY UNDERLYING OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test(s)
    if args.multiple:
        # Parse multiple symbols
        symbols = []
        for item in args.multiple:
            try:
                underlying_symbol, exchange = item.split(',')
                symbols.append((underlying_symbol.strip(), exchange.strip()))
            except ValueError:
                print(f"❌ Invalid format for {item}. Use SYMBOL,EXCHANGE format.")
                return 1
        
        success = await test_multiple_instrument_by_underlying(symbols, args.format)
    else:
        success = await test_instrument_by_underlying(args.underlying, args.exchange, args.format)
    
    if success:
        print("\n✅ Instrument by underlying test completed successfully!")
        return 0
    else:
        print("\n❌ Instrument by underlying test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)