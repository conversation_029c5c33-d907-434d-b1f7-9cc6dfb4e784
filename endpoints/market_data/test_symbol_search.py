#!/usr/bin/env python3

"""
Test Rithmic Symbol Search endpoints (Templates 109/110).

This script demonstrates how to search for trading symbols using various
patterns and filters through the Rithmic API.

SAFETY: This script only performs READ-ONLY symbol search operations.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_search_symbols_pb2
import response_search_symbols_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_symbol_search(search_pattern: str,
                           exchange: Optional[str] = None,
                           max_results: int = 50,
                           output_format: str = "human") -> bool:
    """
    Test symbol search functionality.
    
    Args:
        search_pattern: Pattern to search for (e.g., "ES*", "AAPL", "*USD*")
        exchange: Optional exchange filter
        max_results: Maximum number of results to return
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("🔍 TESTING SYMBOL SEARCH ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.SEARCH_SYMBOLS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.SEARCH_SYMBOLS_RESPONSE} (Response)")
    print(f"Search Pattern: {search_pattern}")
    print(f"Exchange Filter: {exchange or 'None'}")
    print(f"Max Results: {max_results}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "symbol_search_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create symbol search request
        request = request_search_symbols_pb2.RequestSearchSymbols()
        request.template_id = TemplateIDs.SEARCH_SYMBOLS_REQUEST
        request.user_msg.append(f"Symbol search for pattern: {search_pattern}")
        
        # Set search criteria
        request.search_text = search_pattern  # Fixed: use correct field name
        
        # Set pattern type for the search
        if "*" in search_pattern or "?" in search_pattern:
            request.pattern = request_search_symbols_pb2.RequestSearchSymbols.Pattern.CONTAINS
        else:
            request.pattern = request_search_symbols_pb2.RequestSearchSymbols.Pattern.EQUALS
            
        if exchange:
            request.exchange = exchange
        
        # Send search request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending symbol search request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send symbol search request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for symbol search response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_search_symbols_pb2.ResponseSearchSymbols()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 SEARCH RESULTS ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Symbol search successful!")
            
            # Display search results
            if hasattr(response, 'symbol') and response.symbol:
                symbols = list(response.symbol)
                exchanges = list(response.exchange) if hasattr(response, 'exchange') else []
                
                # Ensure exchanges list is same length as symbols
                while len(exchanges) < len(symbols):
                    exchanges.append('N/A')
                
                print(f"\n🎯 FOUND {len(symbols)} SYMBOLS:")
                print("-" * 60)
                
                # Display results (limit to max_results)
                display_count = min(len(symbols), max_results)
                
                if output_format == "human":
                    for i in range(display_count):
                        symbol = symbols[i]
                        exchange_name = exchanges[i] if i < len(exchanges) else 'N/A'
                        print(f"{i+1:3d}. {symbol:20s} @ {exchange_name}")
                    
                    if len(symbols) > max_results:
                        print(f"... and {len(symbols) - max_results} more results (use --max-results to see more)")
                
                elif output_format == "csv":
                    print("Number,Symbol,Exchange")
                    for i in range(display_count):
                        symbol = symbols[i]
                        exchange_name = exchanges[i] if i < len(exchanges) else 'N/A'
                        print(f"{i+1},{symbol},{exchange_name}")
                
                # Summary statistics
                print(f"\n📈 SEARCH STATISTICS:")
                print(f"Total Results: {len(symbols)}")
                print(f"Displayed: {display_count}")
                
                # Analyze exchanges
                if exchanges and exchanges != ['N/A'] * len(exchanges):
                    unique_exchanges = set(ex for ex in exchanges if ex != 'N/A')
                    print(f"Unique Exchanges: {len(unique_exchanges)}")
                    if len(unique_exchanges) <= 10:
                        print(f"Exchanges: {', '.join(sorted(unique_exchanges))}")
                
                # Pattern analysis
                if search_pattern.endswith('*'):
                    prefix = search_pattern[:-1]
                    matching_count = sum(1 for s in symbols if s.startswith(prefix))
                    print(f"Pattern Matches: {matching_count}/{len(symbols)}")
                
                # Show sample matches
                if len(symbols) > 5:
                    print(f"\n🔍 SAMPLE RESULTS:")
                    sample_size = min(5, len(symbols))
                    for i in range(sample_size):
                        symbol = symbols[i]
                        exchange_name = exchanges[i] if i < len(exchanges) else 'N/A'
                        print(f"   {symbol} @ {exchange_name}")
            
            else:
                print("\n⚠️  No symbols found matching the search criteria")
                print("   Try using wildcards (* or ?) or a broader pattern")
        
        else:
            print("❌ Symbol search failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Try using wildcards: ES* for ES futures, *USD* for USD pairs")
            print("   • Check if the exchange filter is valid")
            print("   • Ensure the search pattern is not too restrictive")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing symbol search: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_multiple_searches(patterns: List[str],
                               exchange: Optional[str] = None,
                               output_format: str = "human") -> bool:
    """
    Test multiple symbol search patterns.
    
    Args:
        patterns: List of search patterns to test
        exchange: Optional exchange filter
        output_format: Output format
        
    Returns:
        bool: True if all tests successful
    """
    print("=" * 60)
    print("🔍 TESTING MULTIPLE SYMBOL SEARCHES")
    print("=" * 60)
    print(f"Patterns: {', '.join(patterns)}")
    print(f"Exchange Filter: {exchange or 'None'}")
    print("=" * 60)
    
    results = []
    
    for i, pattern in enumerate(patterns, 1):
        print(f"\n{'='*20} SEARCH {i}/{len(patterns)} {'='*20}")
        success = await test_symbol_search(pattern, exchange, 10, output_format)
        results.append((pattern, success))
        
        if i < len(patterns):
            print("\n⏳ Waiting 2 seconds before next search...")
            await asyncio.sleep(2)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 MULTIPLE SEARCH SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for pattern, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{pattern:20s}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL SYMBOL SEARCHES PASSED!")
    else:
        print("⚠️  SOME SYMBOL SEARCHES FAILED!")
    
    return all_passed

async def main():
    """Main function for symbol search testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Symbol Search endpoint (Templates 109/110)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_symbol_search.py --pattern "ES*"
  python test_symbol_search.py --pattern "AAPL" --exchange NASDAQ
  python test_symbol_search.py --pattern "*USD*" --max-results 20
  python test_symbol_search.py --multiple ES* NQ* ZN* --exchange CME
  python test_symbol_search.py --pattern "ES*" --format json

Search Patterns:
  ES*      - All symbols starting with "ES"
  *USD*    - All symbols containing "USD"
  ??H5     - Two characters followed by "H5"
  AAPL     - Exact match for "AAPL"

Safety:
  This script performs READ-ONLY symbol search operations only.
  Requires authentication but no account modifications performed.
        """
    )
    
    parser.add_argument(
        "--pattern",
        type=str,
        help="Search pattern (e.g., ES*, AAPL, *USD*)"
    )
    
    parser.add_argument(
        "--multiple",
        nargs="+",
        help="Test multiple search patterns"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange filter (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--max-results",
        type=int,
        default=50,
        help="Maximum number of results to display (default: 50)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.pattern and not args.multiple:
        print("❌ Either --pattern or --multiple must be specified")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC SYMBOL SEARCH ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY SYMBOL SEARCH OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test(s)
    if args.multiple:
        success = await test_multiple_searches(
            patterns=args.multiple,
            exchange=args.exchange,
            output_format=args.format
        )
    else:
        success = await test_symbol_search(
            search_pattern=args.pattern,
            exchange=args.exchange,
            max_results=args.max_results,
            output_format=args.format
        )
    
    if success:
        print("\n✅ Symbol search test completed successfully!")
        return 0
    else:
        print("\n❌ Symbol search test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)