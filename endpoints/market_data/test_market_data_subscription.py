#!/usr/bin/env python3

"""
Test Rithmic Market Data Subscription endpoints (Templates 100/101).

This script demonstrates how to subscribe to real-time market data updates
including Last Trade, Best Bid/Offer, and other market data types.

SAFETY: This script only performs READ-ONLY market data subscriptions.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
import time
from typing import Optional, Set, Dict, Any

# Add the proto_generated directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'proto_generated'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

import request_market_data_update_pb2
import response_market_data_update_pb2
import last_trade_pb2
import best_bid_offer_pb2

from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class MarketDataSubscriber:
    """Manages market data subscriptions and real-time updates."""
    
    def __init__(self, connection, authenticator):
        self.connection = connection
        self.authenticator = authenticator
        self.message_handler = get_message_handler()
        self.subscriptions: Set[tuple] = set()  # (symbol, exchange) pairs
        self.update_counts: Dict[int, int] = {}  # template_id -> count
        self.last_trade_data: Dict[str, Dict] = {}  # symbol -> last trade
        self.bbo_data: Dict[str, Dict] = {}  # symbol -> best bid/offer
        
    async def subscribe(self, symbol: str, exchange: str, 
                       update_types: Optional[list] = None) -> bool:
        """
        Subscribe to market data updates for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., "ESH5")
            exchange: Exchange name (e.g., "CME")
            update_types: List of update types to subscribe to
            
        Returns:
            bool: True if subscription successful
        """
        if update_types is None:
            # Default to Last Trade and Best Bid/Offer
            update_types = ["LAST_TRADE", "BBO"]
        
        print(f"\n📊 SUBSCRIBING TO MARKET DATA")
        print(f"Symbol: {symbol}")
        print(f"Exchange: {exchange}")
        print(f"Update Types: {', '.join(update_types)}")
        
        try:
            # Create subscription request
            request = request_market_data_update_pb2.RequestMarketDataUpdate()
            request.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
            request.user_msg.append(f"Subscribe to {symbol} on {exchange}")
            
            request.symbol = symbol
            request.exchange = exchange
            request.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.SUBSCRIBE
            
            # Set update bits based on requested types
            update_bits = 0
            update_bit_map = {
                "LAST_TRADE": request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.LAST_TRADE,
                "BBO": request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.BBO,
                "ORDER_BOOK": request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.ORDER_BOOK,
                "MARKET_MODE": request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.MARKET_MODE,
                "OPEN_INTEREST": request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.OPEN_INTEREST
                # Fixed: Removed TRADE_STATISTICS and QUOTE_STATISTICS (not in enum)
            }
            
            for update_type in update_types:
                if update_type in update_bit_map:
                    update_bits |= update_bit_map[update_type]
                else:
                    print(f"⚠️  Unknown update type: {update_type}")
            
            request.update_bits = update_bits
            
            # Send subscription request
            serialized = request.SerializeToString()
            print(f"📤 Sending subscription request ({len(serialized)} bytes)")
            
            if not await self.connection.send_message(serialized):
                print("❌ Failed to send subscription request")
                return False
            
            # Wait for subscription response
            print("⏳ Waiting for subscription response...")
            response_bytes = await self.connection.receive_message()
            
            if not response_bytes:
                print("❌ No subscription response received")
                return False
            
            # Parse response
            response = response_market_data_update_pb2.ResponseMarketDataUpdate()
            response.ParseFromString(response_bytes)
            
            print(f"📥 Received subscription response")
            print(f"Response Code: {list(response.rp_code)}")
            
            # Check if successful - handle both explicit success codes and empty response codes
            explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
            
            # Check for error indicators
            has_error = False
            if len(response.rp_code) >= 2:
                has_error = True  # Error code and message present
            elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
                has_error = True  # Non-zero error code
            
            # Consider empty response codes as success if no explicit error
            empty_response_success = len(response.rp_code) == 0 and not has_error
            
            success = explicit_success or empty_response_success
            
            if success:
                print("✅ Market data subscription successful!")
                self.subscriptions.add((symbol, exchange))
                return True
            else:
                print("❌ Market data subscription failed!")
                if len(response.rp_code) >= 2:
                    print(f"Error: {response.rp_code[0]} - {response.rp_code[1]}")
                return False
                
        except Exception as e:
            logger.error(f"Error subscribing to market data: {e}")
            print(f"❌ Subscription failed: {e}")
            return False
    
    async def unsubscribe(self, symbol: str, exchange: str) -> bool:
        """
        Unsubscribe from market data updates for a symbol.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            
        Returns:
            bool: True if unsubscription successful
        """
        print(f"\n🚫 UNSUBSCRIBING FROM MARKET DATA")
        print(f"Symbol: {symbol}")
        print(f"Exchange: {exchange}")
        
        try:
            # Create unsubscription request
            request = request_market_data_update_pb2.RequestMarketDataUpdate()
            request.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
            request.user_msg.append(f"Unsubscribe from {symbol} on {exchange}")
            
            request.symbol = symbol
            request.exchange = exchange
            request.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.UNSUBSCRIBE
            request.update_bits = (
                request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.LAST_TRADE |
                request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.BBO
            )
            
            # Send unsubscription request
            serialized = request.SerializeToString()
            print(f"📤 Sending unsubscription request ({len(serialized)} bytes)")
            
            if not await self.connection.send_message(serialized):
                print("❌ Failed to send unsubscription request")
                return False
            
            # Wait for response (optional for unsubscribe)
            try:
                response_bytes = await self.connection.receive_message(timeout=5.0)
                if response_bytes:
                    response = response_market_data_update_pb2.ResponseMarketDataUpdate()
                    response.ParseFromString(response_bytes)
                    print(f"📥 Unsubscription response: {list(response.rp_code)}")
            except:
                pass  # Unsubscribe responses are not always sent
            
            print("✅ Unsubscription request sent")
            self.subscriptions.discard((symbol, exchange))
            return True
                
        except Exception as e:
            logger.error(f"Error unsubscribing from market data: {e}")
            print(f"❌ Unsubscription failed: {e}")
            return False
    
    async def monitor_updates(self, duration: int = 60, max_updates: int = 100) -> Dict[str, Any]:
        """
        Monitor real-time market data updates.
        
        Args:
            duration: Maximum duration to monitor in seconds
            max_updates: Maximum number of updates to process
            
        Returns:
            dict: Summary statistics
        """
        print(f"\n📈 MONITORING MARKET DATA UPDATES")
        print(f"Duration: {duration} seconds")
        print(f"Max Updates: {max_updates}")
        print("Press Ctrl+C to stop early")
        print("-" * 50)
        
        start_time = time.time()
        update_count = 0
        
        try:
            while (time.time() - start_time < duration and 
                   update_count < max_updates):
                
                # Wait for market data update
                message_bytes = await self.connection.receive_message(timeout=1.0)
                
                if not message_bytes:
                    continue
                
                # Parse message to get template ID
                message_info = parse_message(message_bytes)
                if not message_info:
                    continue
                
                template_id = message_info.template_id
                self.update_counts[template_id] = self.update_counts.get(template_id, 0) + 1
                update_count += 1
                
                # Process specific message types
                if template_id == TemplateIDs.LAST_TRADE:
                    await self._process_last_trade(message_bytes)
                elif template_id == TemplateIDs.BEST_BID_OFFER:
                    await self._process_best_bid_offer(message_bytes)
                elif template_id == TemplateIDs.HEARTBEAT_RESPONSE:
                    print("💓 Heartbeat received")
                else:
                    print(f"📨 Received {message_info.template_name} update")
                
                # Progress indicator
                if update_count % 10 == 0:
                    elapsed = time.time() - start_time
                    print(f"📊 {update_count} updates received in {elapsed:.1f}s")
        
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Error during monitoring: {e}")
        
        elapsed_time = time.time() - start_time
        
        # Return summary statistics
        return {
            "duration": elapsed_time,
            "total_updates": update_count,
            "update_counts": dict(self.update_counts),
            "last_trade_symbols": list(self.last_trade_data.keys()),
            "bbo_symbols": list(self.bbo_data.keys()),
            "updates_per_second": update_count / elapsed_time if elapsed_time > 0 else 0
        }
    
    async def _process_last_trade(self, message_bytes: bytes) -> None:
        """Process Last Trade update."""
        try:
            trade = last_trade_pb2.LastTrade()
            trade.ParseFromString(message_bytes)
            
            symbol_key = f"{trade.symbol}@{trade.exchange}"
            
            # Update last trade data
            self.last_trade_data[symbol_key] = {
                "symbol": trade.symbol,
                "exchange": trade.exchange,
                "trade_price": getattr(trade, 'trade_price', None),
                "trade_size": getattr(trade, 'trade_size', None),
                "volume": getattr(trade, 'volume', None),
                "timestamp": time.time()
            }
            
            # Display trade information
            price = getattr(trade, 'trade_price', 'N/A')
            size = getattr(trade, 'trade_size', 'N/A')
            volume = getattr(trade, 'volume', 'N/A')
            
            print(f"🔄 {trade.symbol}: Trade @ {price} x {size}, Volume: {volume}")
            
        except Exception as e:
            logger.error(f"Error processing last trade: {e}")
    
    async def _process_best_bid_offer(self, message_bytes: bytes) -> None:
        """Process Best Bid/Offer update."""
        try:
            bbo = best_bid_offer_pb2.BestBidOffer()
            bbo.ParseFromString(message_bytes)
            
            symbol_key = f"{bbo.symbol}@{bbo.exchange}"
            
            # Update BBO data
            self.bbo_data[symbol_key] = {
                "symbol": bbo.symbol,
                "exchange": bbo.exchange,
                "bid_price": getattr(bbo, 'bid_price', None),
                "bid_size": getattr(bbo, 'bid_size', None),
                "ask_price": getattr(bbo, 'ask_price', None),
                "ask_size": getattr(bbo, 'ask_size', None),
                "timestamp": time.time()
            }
            
            # Display BBO information
            bid_price = getattr(bbo, 'bid_price', 'N/A')
            bid_size = getattr(bbo, 'bid_size', 'N/A')
            ask_price = getattr(bbo, 'ask_price', 'N/A')
            ask_size = getattr(bbo, 'ask_size', 'N/A')
            
            print(f"📊 {bbo.symbol}: {bid_price} x {bid_size} | {ask_price} x {ask_size}")
            
        except Exception as e:
            logger.error(f"Error processing BBO: {e}")

async def test_market_data_subscription(symbol: str, exchange: str,
                                      duration: int = 60,
                                      update_types: Optional[list] = None,
                                      output_format: str = "human") -> bool:
    """
    Test market data subscription functionality.
    
    Args:
        symbol: Trading symbol to subscribe to
        exchange: Exchange name
        duration: Duration to monitor updates
        update_types: Types of updates to subscribe to
        output_format: Output format
        
    Returns:
        bool: True if test successful
    """
    config = get_config()
    
    print("=" * 60)
    print("📊 TESTING MARKET DATA SUBSCRIPTION")
    print("=" * 60)
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Duration: {duration} seconds")
    print(f"Update Types: {update_types or ['LAST_TRADE', 'BBO']}")
    print(f"Paper Trading: {'✅ ENABLED' if config.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "market_data_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create market data subscriber
        subscriber = MarketDataSubscriber(connection, authenticator)
        
        # Subscribe to market data
        success = await subscriber.subscribe(symbol, exchange, update_types)
        if not success:
            return False
        
        # Monitor updates
        stats = await subscriber.monitor_updates(duration)
        
        # Display summary
        print("\n" + "=" * 60)
        print("📋 MARKET DATA SUMMARY")
        print("=" * 60)
        print(f"Duration: {stats['duration']:.1f} seconds")
        print(f"Total Updates: {stats['total_updates']}")
        print(f"Updates/Second: {stats['updates_per_second']:.1f}")
        
        if stats['update_counts']:
            print("\nUpdate Counts by Type:")
            for template_id, count in stats['update_counts'].items():
                template_name = {
                    TemplateIDs.LAST_TRADE: "Last Trade",
                    TemplateIDs.BEST_BID_OFFER: "Best Bid/Offer",
                    TemplateIDs.HEARTBEAT_RESPONSE: "Heartbeat"
                }.get(template_id, f"Template {template_id}")
                print(f"  {template_name}: {count}")
        
        if stats['last_trade_symbols']:
            print(f"\nLast Trade Symbols: {', '.join(stats['last_trade_symbols'])}")
        
        if stats['bbo_symbols']:
            print(f"BBO Symbols: {', '.join(stats['bbo_symbols'])}")
        
        # Unsubscribe
        await subscriber.unsubscribe(symbol, exchange)
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return stats['total_updates'] > 0
        
    except Exception as e:
        logger.error(f"Error in market data subscription test: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for market data subscription testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Market Data Subscription (Templates 100/101)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_market_data_subscription.py --symbol ESH5 --exchange CME
  python test_market_data_subscription.py --symbol AAPL --exchange NASDAQ --duration 30
  python test_market_data_subscription.py --symbol ESH5 --exchange CME --types LAST_TRADE BBO
  python test_market_data_subscription.py --symbol ESH5 --exchange CME --format json

Available Update Types:
  LAST_TRADE, BBO, TRADE_STATISTICS, QUOTE_STATISTICS, ORDER_BOOK, MARKET_MODE, OPEN_INTEREST

Safety:
  This script performs READ-ONLY market data subscriptions only.
  Requires authentication but no account modifications performed.
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Trading symbol to subscribe to (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        required=True,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=60,
        help="Duration to monitor updates in seconds (default: 60)"
    )
    
    parser.add_argument(
        "--types",
        nargs="+",
        default=["LAST_TRADE", "BBO"],
        help="Update types to subscribe to (default: LAST_TRADE BBO)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config = get_config()
        config.save_responses = True
    
    print("🧪 RITHMIC MARKET DATA SUBSCRIPTION TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY MARKET DATA OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_market_data_subscription(
        symbol=args.symbol,
        exchange=args.exchange,
        duration=args.duration,
        update_types=args.types,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Market data subscription test completed successfully!")
        return 0
    else:
        print("\n❌ Market data subscription test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)