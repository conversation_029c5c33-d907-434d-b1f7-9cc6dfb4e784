#!/usr/bin/env python3

"""
Test Rithmic Streaming Order Book Data endpoints.

This script demonstrates how to subscribe to real-time order book streams
from the Rithmic API, processing Order Book (Template 156) and Best Bid Offer (Template 151) updates.

SAFETY: This script only performs READ-ONLY streaming order book data requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime
from collections import defaultdict

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_market_data_update_pb2
import response_market_data_update_pb2
import order_book_pb2
import best_bid_offer_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class OrderBookAnalyzer:
    """Analyzes streaming order book data for market insights."""
    
    def __init__(self):
        self.order_book_updates = []
        self.bbo_updates = []
        self.symbols_seen = set()
        self.spread_history = defaultdict(list)
        self.liquidity_history = defaultdict(list)
        self.imbalance_history = defaultdict(list)
        
    def add_order_book_update(self, update_data: Dict[str, Any]):
        """Add an order book update to the analysis."""
        self.order_book_updates.append(update_data)
        symbol = update_data.get('symbol', '')
        self.symbols_seen.add(symbol)
        
        # Calculate liquidity metrics if we have bid/ask data
        bid_liquidity = sum(update_data.get('bid_quantities', []))
        ask_liquidity = sum(update_data.get('ask_quantities', []))
        total_liquidity = bid_liquidity + ask_liquidity
        
        if total_liquidity > 0:
            self.liquidity_history[symbol].append(total_liquidity)
            
            # Calculate imbalance
            imbalance = (bid_liquidity - ask_liquidity) / total_liquidity
            self.imbalance_history[symbol].append(imbalance)
    
    def add_bbo_update(self, bbo_data: Dict[str, Any]):
        """Add a best bid/offer update to the analysis."""
        self.bbo_updates.append(bbo_data)
        symbol = bbo_data.get('symbol', '')
        self.symbols_seen.add(symbol)
        
        # Calculate spread if we have both bid and ask
        bid_price = bbo_data.get('bid_price', 0)
        ask_price = bbo_data.get('ask_price', 0)
        
        if bid_price > 0 and ask_price > 0:
            spread = ask_price - bid_price
            self.spread_history[symbol].append(spread)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get order book analysis summary."""
        summary = {
            "total_order_book_updates": len(self.order_book_updates),
            "total_bbo_updates": len(self.bbo_updates),
            "unique_symbols": len(self.symbols_seen),
            "symbols": list(self.symbols_seen),
            "spread_analysis": {},
            "liquidity_analysis": {},
            "imbalance_analysis": {}
        }
        
        # Spread analysis per symbol
        for symbol, spreads in self.spread_history.items():
            if spreads:
                summary["spread_analysis"][symbol] = {
                    "avg_spread": sum(spreads) / len(spreads),
                    "min_spread": min(spreads),
                    "max_spread": max(spreads),
                    "latest_spread": spreads[-1],
                    "spread_updates": len(spreads)
                }
        
        # Liquidity analysis per symbol
        for symbol, liquidity in self.liquidity_history.items():
            if liquidity:
                summary["liquidity_analysis"][symbol] = {
                    "avg_liquidity": sum(liquidity) / len(liquidity),
                    "min_liquidity": min(liquidity),
                    "max_liquidity": max(liquidity),
                    "latest_liquidity": liquidity[-1],
                    "liquidity_updates": len(liquidity)
                }
        
        # Imbalance analysis per symbol
        for symbol, imbalances in self.imbalance_history.items():
            if imbalances:
                summary["imbalance_analysis"][symbol] = {
                    "avg_imbalance": sum(imbalances) / len(imbalances),
                    "latest_imbalance": imbalances[-1],
                    "imbalance_updates": len(imbalances)
                }
        
        return summary

async def test_order_book_streaming(symbol: str, exchange: str, duration: int = 60,
                                  output_format: str = "human") -> bool:
    """
    Test streaming order book data functionality.
    
    Args:
        symbol: Trading symbol to subscribe to
        exchange: Exchange name
        duration: How long to listen for order book updates (seconds)
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    analyzer = OrderBookAnalyzer()
    
    print("=" * 60)
    print("📚 TESTING STREAMING ORDER BOOK DATA ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.MARKET_DATA_UPDATE_REQUEST} (Subscription)")
    print(f"Template ID: {TemplateIDs.ORDER_BOOK} (Order Book Updates)")
    print(f"Template ID: {TemplateIDs.BEST_BID_OFFER} (Best Bid/Offer Updates)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Duration: {duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "order_book_streaming_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create market data subscription request for order book updates
        request = request_market_data_update_pb2.RequestMarketDataUpdate()
        request.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
        request.user_msg.append(f"Streaming order book data subscription for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        request.request = 1  # SUBSCRIBE (1 = SUBSCRIBE, 2 = UNSUBSCRIBE)
        
        # Set update bits for order book and best bid/offer data
        UPDATE_BITS_BEST_BID_OFFER = 2
        UPDATE_BITS_ORDER_BOOK = 4
        request.update_bits = UPDATE_BITS_BEST_BID_OFFER | UPDATE_BITS_ORDER_BOOK
        
        # Send subscription request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending order book data subscription ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send order book data subscription")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for subscription response
        print("⏳ Waiting for subscription response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No subscription response received within timeout")
            return False
        
        print(f"📥 Received subscription response ({len(response_bytes)} bytes)")
        
        # Parse subscription response
        response = response_market_data_update_pb2.ResponseMarketDataUpdate()
        response.ParseFromString(response_bytes)
        
        # Check subscription success
        subscription_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if not subscription_success:
            print("❌ Order book data subscription failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            return False
        
        print("✅ Order book data subscription successful!")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Listen for order book updates
        print(f"\n📚 LISTENING FOR ORDER BOOK UPDATES ({duration} seconds)...")
        print("=" * 60)
        
        update_count = 0
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < duration:
            try:
                # Wait for order book updates
                update_bytes = await connection.receive_message(timeout=5.0)
                
                if not update_bytes:
                    continue  # Timeout, keep listening
                
                # Parse update to determine type
                update_info = parse_message(update_bytes)
                if not update_info:
                    continue
                
                template_id = update_info.template_id
                update_count += 1
                
                if template_id == TemplateIDs.ORDER_BOOK:
                    # Order book update
                    order_book = order_book_pb2.OrderBook()
                    order_book.ParseFromString(update_bytes)
                    
                    # Extract order book data
                    update_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "symbol": getattr(order_book, 'symbol', ''),
                        "exchange": getattr(order_book, 'exchange', ''),
                        "bid_prices": [],
                        "bid_quantities": [],
                        "ask_prices": [],
                        "ask_quantities": [],
                        "book_depth": 0
                    }
                    
                    # Extract bid side data
                    if hasattr(order_book, 'bid_price'):
                        update_data["bid_prices"] = list(order_book.bid_price)
                    if hasattr(order_book, 'bid_quantity'):
                        update_data["bid_quantities"] = list(order_book.bid_quantity)
                    
                    # Extract ask side data
                    if hasattr(order_book, 'ask_price'):
                        update_data["ask_prices"] = list(order_book.ask_price)
                    if hasattr(order_book, 'ask_quantity'):
                        update_data["ask_quantities"] = list(order_book.ask_quantity)
                    
                    # Calculate book depth
                    update_data["book_depth"] = max(len(update_data["bid_prices"]), len(update_data["ask_prices"]))
                    
                    # Add to analyzer
                    analyzer.add_order_book_update(update_data)
                    
                    # Display order book update
                    print(f"📚 ORDER BOOK #{update_count} [{update_data['timestamp']}]")
                    print(f"    Symbol: {update_data['symbol']}@{update_data['exchange']}")
                    print(f"    Book Depth: {update_data['book_depth']} levels")
                    
                    # Show top 3 levels
                    max_levels = min(3, update_data["book_depth"])
                    if max_levels > 0:
                        print(f"    Top {max_levels} Levels:")
                        
                        for i in range(max_levels):
                            bid_price = update_data["bid_prices"][i] if i < len(update_data["bid_prices"]) else 0
                            bid_qty = update_data["bid_quantities"][i] if i < len(update_data["bid_quantities"]) else 0
                            ask_price = update_data["ask_prices"][i] if i < len(update_data["ask_prices"]) else 0
                            ask_qty = update_data["ask_quantities"][i] if i < len(update_data["ask_quantities"]) else 0
                            
                            print(f"      {i+1}. Bid: {bid_price:8.2f} x {bid_qty:6d} | Ask: {ask_price:8.2f} x {ask_qty:6d}")
                    
                    # Calculate and display spread
                    if update_data["bid_prices"] and update_data["ask_prices"]:
                        best_bid = update_data["bid_prices"][0]
                        best_ask = update_data["ask_prices"][0]
                        spread = best_ask - best_bid
                        print(f"    Spread: {spread:6.2f}")
                
                elif template_id == TemplateIDs.BEST_BID_OFFER:
                    # Best bid/offer update
                    bbo = best_bid_offer_pb2.BestBidOffer()
                    bbo.ParseFromString(update_bytes)
                    
                    # Extract BBO data
                    bbo_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "symbol": getattr(bbo, 'symbol', ''),
                        "exchange": getattr(bbo, 'exchange', ''),
                        "bid_price": getattr(bbo, 'bid_price', 0),
                        "bid_size": getattr(bbo, 'bid_size', 0),
                        "ask_price": getattr(bbo, 'ask_price', 0),
                        "ask_size": getattr(bbo, 'ask_size', 0)
                    }
                    
                    # Add to analyzer
                    analyzer.add_bbo_update(bbo_data)
                    
                    # Display BBO update
                    print(f"📈 BBO #{update_count} [{bbo_data['timestamp']}]")
                    print(f"    Symbol: {bbo_data['symbol']}@{bbo_data['exchange']}")
                    
                    if bbo_data['bid_price'] and bbo_data['ask_price']:
                        spread = bbo_data['ask_price'] - bbo_data['bid_price']
                        print(f"    Bid: {bbo_data['bid_price']:8.2f} x {bbo_data['bid_size']:6d}")
                        print(f"    Ask: {bbo_data['ask_price']:8.2f} x {bbo_data['ask_size']:6d}")
                        print(f"    Spread: {spread:6.2f}")
                
                else:
                    # Other update types
                    print(f"📡 OTHER UPDATE: Template {template_id}")
                
                # Display detailed message if requested
                if output_format != "human":
                    print(f"\n📋 UPDATE DETAILS (Template {template_id}):")
                    print(message_handler_obj.format_message(update_info, output_format))
                    print("-" * 40)
                
                print()  # Blank line for readability
                
            except asyncio.TimeoutError:
                # No updates received in the timeout period
                continue
            except Exception as e:
                logger.warning(f"Error processing order book update: {e}")
                continue
        
        # Get analysis summary
        summary = analyzer.get_summary()
        
        print(f"\n📚 STREAMING ORDER BOOK DATA SUMMARY:")
        print("=" * 60)
        print(f"Symbol: {symbol}")
        print(f"Exchange: {exchange}")
        print(f"Duration: {duration} seconds")
        print(f"Total Updates: {update_count}")
        print(f"Order Book Updates: {summary['total_order_book_updates']}")
        print(f"BBO Updates: {summary['total_bbo_updates']}")
        print(f"Unique Symbols: {summary['unique_symbols']}")
        
        if update_count > 0:
            avg_frequency = update_count / duration
            print(f"Average Update Frequency: {avg_frequency:.2f} updates/second")
        
        # Spread analysis
        if summary['spread_analysis']:
            print(f"\n📊 SPREAD ANALYSIS:")
            for sym, analysis in summary['spread_analysis'].items():
                print(f"  {sym}:")
                print(f"    Average Spread: {analysis['avg_spread']:6.2f}")
                print(f"    Spread Range: {analysis['min_spread']:6.2f} - {analysis['max_spread']:6.2f}")
                print(f"    Latest Spread: {analysis['latest_spread']:6.2f}")
                print(f"    Spread Updates: {analysis['spread_updates']}")
        
        # Liquidity analysis
        if summary['liquidity_analysis']:
            print(f"\n💧 LIQUIDITY ANALYSIS:")
            for sym, analysis in summary['liquidity_analysis'].items():
                print(f"  {sym}:")
                print(f"    Average Liquidity: {analysis['avg_liquidity']:,.0f}")
                print(f"    Liquidity Range: {analysis['min_liquidity']:,.0f} - {analysis['max_liquidity']:,.0f}")
                print(f"    Latest Liquidity: {analysis['latest_liquidity']:,.0f}")
                print(f"    Liquidity Updates: {analysis['liquidity_updates']}")
        
        # Imbalance analysis
        if summary['imbalance_analysis']:
            print(f"\n⚖️ IMBALANCE ANALYSIS:")
            for sym, analysis in summary['imbalance_analysis'].items():
                imbalance = analysis['latest_imbalance']
                print(f"  {sym}:")
                print(f"    Latest Imbalance: {imbalance:+6.2f}")
                
                if imbalance > 0.1:
                    print(f"    Bias: BID-HEAVY (buying pressure)")
                elif imbalance < -0.1:
                    print(f"    Bias: ASK-HEAVY (selling pressure)")
                else:
                    print(f"    Bias: BALANCED")
                
                print(f"    Average Imbalance: {analysis['avg_imbalance']:+6.2f}")
                print(f"    Imbalance Updates: {analysis['imbalance_updates']}")
        
        # CSV output
        if output_format == "csv" and analyzer.bbo_updates:
            print(f"\n📊 CSV FORMAT (BBO DATA):")
            csv_headers = ["Timestamp", "Symbol", "Exchange", "Bid_Price", "Bid_Size", 
                          "Ask_Price", "Ask_Size", "Spread"]
            print(",".join(csv_headers))
            
            for bbo in analyzer.bbo_updates[-50:]:  # Last 50 BBO updates
                spread = bbo['ask_price'] - bbo['bid_price'] if bbo['ask_price'] and bbo['bid_price'] else 0
                values = [
                    bbo['timestamp'],
                    bbo['symbol'],
                    bbo['exchange'],
                    str(bbo['bid_price']),
                    str(bbo['bid_size']),
                    str(bbo['ask_price']),
                    str(bbo['ask_size']),
                    str(spread)
                ]
                print(",".join(values))
        
        # Analysis and insights
        print(f"\n💡 ORDER BOOK STREAM ANALYSIS:")
        if update_count > 0:
            print(f"✅ Successfully received {update_count} order book updates")
            
            ob_pct = (summary['total_order_book_updates'] / update_count) * 100
            bbo_pct = (summary['total_bbo_updates'] / update_count) * 100
            
            print(f"• Order Book Updates: {ob_pct:.1f}%")
            print(f"• Best Bid/Offer Updates: {bbo_pct:.1f}%")
            
            # Market activity assessment
            if avg_frequency > 5:
                print(f"• High frequency market: {avg_frequency:.2f} updates/sec")
            elif avg_frequency > 1:
                print(f"• Active market: {avg_frequency:.2f} updates/sec")
            else:
                print(f"• Quiet market: {avg_frequency:.2f} updates/sec")
            
            # Spread analysis insights
            for sym, analysis in summary['spread_analysis'].items():
                if analysis['latest_spread'] < 0.01:
                    print(f"• {sym}: Very tight spread (high liquidity)")
                elif analysis['latest_spread'] > 1.0:
                    print(f"• {sym}: Wide spread (lower liquidity)")
                else:
                    print(f"• {sym}: Normal spread")
        else:
            print("⚠️  No order book updates received")
            print("• Market may be closed")
            print("• Symbol may have limited order book activity")
            print("• Check market hours and symbol liquidity")
        
        # Unsubscribe
        print(f"\n📤 Unsubscribing from order book data...")
        request.request = 2  # UNSUBSCRIBE
        serialized_unsub = request.SerializeToString()
        
        if await connection.send_message(serialized_unsub):
            # Wait for unsubscribe response
            unsub_response = await connection.receive_message(timeout=5.0)
            if unsub_response:
                print("✅ Successfully unsubscribed")
            else:
                print("⚠️  Unsubscribe response not received")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return update_count > 0  # Success if we received any updates
        
    except Exception as e:
        logger.error(f"Error testing streaming order book data: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for streaming order book data testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Streaming Order Book Data endpoint",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_order_book_streaming.py --symbol ESH5 --exchange CME
  python test_order_book_streaming.py --symbol AAPL --exchange NASDAQ --duration 120
  python test_order_book_streaming.py --symbol ESH5 --exchange CME --format csv

Safety:
  This script performs READ-ONLY streaming order book data requests only.
  Requires authentication but no account modifications performed.
  Automatically unsubscribes at the end of the test.
  
Output:
  Displays real-time order book updates including:
  - Full order book depth with bid/ask levels
  - Best bid/offer updates with spreads
  - Liquidity analysis and market depth
  - Order imbalance and market bias detection
  - Summary statistics and market insights
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Trading symbol to subscribe to (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        required=True,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=60,
        help="How long to listen for order book updates in seconds (default: 60)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC STREAMING ORDER BOOK DATA ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY STREAMING ORDER BOOK DATA OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_order_book_streaming(
        args.symbol,
        args.exchange,
        args.duration,
        args.format
    )
    
    if success:
        print("\n✅ Streaming order book data test completed successfully!")
        return 0
    else:
        print("\n❌ Streaming order book data test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)