#!/usr/bin/env python3

"""
Test Rithmic Streaming Market Statistics Data endpoints.

This script demonstrates how to subscribe to real-time market statistics streams
from the Rithmic API, processing Trade Statistics (Template 152), Quote Statistics (Template 153),
and other market statistics updates.

SAFETY: This script only performs READ-ONLY streaming market statistics requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime
from collections import defaultdict

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_market_data_update_pb2
import response_market_data_update_pb2
import trade_statistics_pb2
import quote_statistics_pb2
import open_interest_pb2
import market_mode_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

class MarketStatisticsAnalyzer:
    """Analyzes streaming market statistics for comprehensive market insights."""
    
    def __init__(self):
        self.trade_stats_updates = []
        self.quote_stats_updates = []
        self.open_interest_updates = []
        self.market_mode_updates = []
        self.symbols_seen = set()
        self.volume_history = defaultdict(list)
        self.volatility_history = defaultdict(list)
        self.trend_history = defaultdict(list)
        
    def add_trade_statistics(self, stats_data: Dict[str, Any]):
        """Add trade statistics to the analysis."""
        self.trade_stats_updates.append(stats_data)
        symbol = stats_data.get('symbol', '')
        self.symbols_seen.add(symbol)
        
        # Track volume trends
        volume = stats_data.get('volume', 0)
        if volume > 0:
            self.volume_history[symbol].append(volume)
        
        # Track price volatility indicators
        high = stats_data.get('high_price', 0)
        low = stats_data.get('low_price', 0)
        if high > 0 and low > 0:
            volatility = (high - low) / low * 100
            self.volatility_history[symbol].append(volatility)
    
    def add_quote_statistics(self, stats_data: Dict[str, Any]):
        """Add quote statistics to the analysis."""
        self.quote_stats_updates.append(stats_data)
        symbol = stats_data.get('symbol', '')
        self.symbols_seen.add(symbol)
    
    def add_open_interest(self, oi_data: Dict[str, Any]):
        """Add open interest data to the analysis."""
        self.open_interest_updates.append(oi_data)
        symbol = oi_data.get('symbol', '')
        self.symbols_seen.add(symbol)
    
    def add_market_mode(self, mode_data: Dict[str, Any]):
        """Add market mode data to the analysis."""
        self.market_mode_updates.append(mode_data)
        symbol = mode_data.get('symbol', '')
        self.symbols_seen.add(symbol)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive market statistics analysis."""
        summary = {
            "total_trade_stats": len(self.trade_stats_updates),
            "total_quote_stats": len(self.quote_stats_updates),
            "total_open_interest": len(self.open_interest_updates),
            "total_market_mode": len(self.market_mode_updates),
            "unique_symbols": len(self.symbols_seen),
            "symbols": list(self.symbols_seen),
            "volume_analysis": {},
            "volatility_analysis": {},
            "market_health": {}
        }
        
        # Volume analysis per symbol
        for symbol, volumes in self.volume_history.items():
            if volumes:
                summary["volume_analysis"][symbol] = {
                    "latest_volume": volumes[-1],
                    "avg_volume": sum(volumes) / len(volumes),
                    "max_volume": max(volumes),
                    "volume_trend": "increasing" if len(volumes) > 1 and volumes[-1] > volumes[0] else "stable",
                    "volume_updates": len(volumes)
                }
        
        # Volatility analysis per symbol
        for symbol, volatilities in self.volatility_history.items():
            if volatilities:
                summary["volatility_analysis"][symbol] = {
                    "latest_volatility": volatilities[-1],
                    "avg_volatility": sum(volatilities) / len(volatilities),
                    "max_volatility": max(volatilities),
                    "volatility_trend": "increasing" if len(volatilities) > 1 and volatilities[-1] > volatilities[0] else "stable",
                    "volatility_updates": len(volatilities)
                }
        
        # Market health assessment
        total_updates = sum([
            summary["total_trade_stats"],
            summary["total_quote_stats"],
            summary["total_open_interest"],
            summary["total_market_mode"]
        ])
        
        summary["market_health"]["total_updates"] = total_updates
        summary["market_health"]["update_diversity"] = len([x for x in [
            summary["total_trade_stats"],
            summary["total_quote_stats"],
            summary["total_open_interest"],
            summary["total_market_mode"]
        ] if x > 0])
        
        return summary

async def test_market_statistics_streaming(symbol: str, exchange: str, duration: int = 60,
                                         output_format: str = "human") -> bool:
    """
    Test streaming market statistics functionality.
    
    Args:
        symbol: Trading symbol to subscribe to
        exchange: Exchange name
        duration: How long to listen for statistics updates (seconds)
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    analyzer = MarketStatisticsAnalyzer()
    
    print("=" * 60)
    print("📈 TESTING STREAMING MARKET STATISTICS ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.MARKET_DATA_UPDATE_REQUEST} (Subscription)")
    print(f"Template ID: {TemplateIDs.TRADE_STATISTICS} (Trade Statistics)")
    print(f"Template ID: {TemplateIDs.QUOTE_STATISTICS} (Quote Statistics)")
    print(f"Template ID: {TemplateIDs.OPEN_INTEREST} (Open Interest)")
    print(f"Template ID: {TemplateIDs.MARKET_MODE} (Market Mode)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Duration: {duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "market_statistics_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create market data subscription request for statistics
        request = request_market_data_update_pb2.RequestMarketDataUpdate()
        request.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
        request.user_msg.append(f"Streaming market statistics subscription for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        request.request = 1  # SUBSCRIBE (1 = SUBSCRIBE, 2 = UNSUBSCRIBE)
        
        # Set update bits for various statistics
        UPDATE_BITS_TRADE_STATISTICS = 8
        UPDATE_BITS_QUOTE_STATISTICS = 16
        UPDATE_BITS_MARKET_MODE = 32
        UPDATE_BITS_OPEN_INTEREST = 64
        
        request.update_bits = (UPDATE_BITS_TRADE_STATISTICS | 
                              UPDATE_BITS_QUOTE_STATISTICS | 
                              UPDATE_BITS_MARKET_MODE |
                              UPDATE_BITS_OPEN_INTEREST)
        
        # Send subscription request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending market statistics subscription ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send market statistics subscription")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for subscription response
        print("⏳ Waiting for subscription response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No subscription response received within timeout")
            return False
        
        print(f"📥 Received subscription response ({len(response_bytes)} bytes)")
        
        # Parse subscription response
        response = response_market_data_update_pb2.ResponseMarketDataUpdate()
        response.ParseFromString(response_bytes)
        
        # Check subscription success
        subscription_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if not subscription_success:
            print("❌ Market statistics subscription failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            return False
        
        print("✅ Market statistics subscription successful!")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Listen for market statistics updates
        print(f"\n📈 LISTENING FOR MARKET STATISTICS UPDATES ({duration} seconds)...")
        print("=" * 60)
        
        update_count = 0
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < duration:
            try:
                # Wait for statistics updates
                update_bytes = await connection.receive_message(timeout=5.0)
                
                if not update_bytes:
                    continue  # Timeout, keep listening
                
                # Parse update to determine type
                update_info = parse_message(update_bytes)
                if not update_info:
                    continue
                
                template_id = update_info.template_id
                update_count += 1
                
                if template_id == TemplateIDs.TRADE_STATISTICS:
                    # Trade statistics update
                    trade_stats = trade_statistics_pb2.TradeStatistics()
                    trade_stats.ParseFromString(update_bytes)
                    
                    # Extract trade statistics data
                    stats_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "symbol": getattr(trade_stats, 'symbol', ''),
                        "exchange": getattr(trade_stats, 'exchange', ''),
                        "volume": getattr(trade_stats, 'volume', 0),
                        "high_price": getattr(trade_stats, 'high_price', 0),
                        "low_price": getattr(trade_stats, 'low_price', 0),
                        "open_price": getattr(trade_stats, 'open_price', 0),
                        "close_price": getattr(trade_stats, 'close_price', 0),
                        "vwap": getattr(trade_stats, 'vwap', 0),
                        "settlement_price": getattr(trade_stats, 'settlement_price', 0)
                    }
                    
                    # Add to analyzer
                    analyzer.add_trade_statistics(stats_data)
                    
                    # Display trade statistics
                    print(f"📊 TRADE STATS #{update_count} [{stats_data['timestamp']}]")
                    print(f"    Symbol: {stats_data['symbol']}@{stats_data['exchange']}")
                    
                    if stats_data['volume']:
                        print(f"    Volume: {stats_data['volume']:,}")
                    
                    if stats_data['high_price'] and stats_data['low_price']:
                        range_pct = ((stats_data['high_price'] - stats_data['low_price']) / stats_data['low_price']) * 100
                        print(f"    High: {stats_data['high_price']:8.2f}")
                        print(f"    Low:  {stats_data['low_price']:8.2f}")
                        print(f"    Range: {range_pct:5.2f}%")
                    
                    if stats_data['open_price']:
                        print(f"    Open: {stats_data['open_price']:8.2f}")
                    
                    if stats_data['close_price']:
                        print(f"    Close: {stats_data['close_price']:8.2f}")
                    
                    if stats_data['vwap']:
                        print(f"    VWAP: {stats_data['vwap']:8.2f}")
                
                elif template_id == TemplateIDs.QUOTE_STATISTICS:
                    # Quote statistics update
                    quote_stats = quote_statistics_pb2.QuoteStatistics()
                    quote_stats.ParseFromString(update_bytes)
                    
                    # Extract quote statistics data
                    stats_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "symbol": getattr(quote_stats, 'symbol', ''),
                        "exchange": getattr(quote_stats, 'exchange', ''),
                        "bid_count": getattr(quote_stats, 'bid_count', 0),
                        "ask_count": getattr(quote_stats, 'ask_count', 0),
                        "quote_count": getattr(quote_stats, 'quote_count', 0)
                    }
                    
                    # Add to analyzer
                    analyzer.add_quote_statistics(stats_data)
                    
                    # Display quote statistics
                    print(f"💬 QUOTE STATS #{update_count} [{stats_data['timestamp']}]")
                    print(f"    Symbol: {stats_data['symbol']}@{stats_data['exchange']}")
                    
                    if stats_data['bid_count']:
                        print(f"    Bid Count: {stats_data['bid_count']:,}")
                    
                    if stats_data['ask_count']:
                        print(f"    Ask Count: {stats_data['ask_count']:,}")
                    
                    if stats_data['quote_count']:
                        print(f"    Total Quotes: {stats_data['quote_count']:,}")
                
                elif template_id == TemplateIDs.OPEN_INTEREST:
                    # Open interest update
                    open_interest = open_interest_pb2.OpenInterest()
                    open_interest.ParseFromString(update_bytes)
                    
                    # Extract open interest data
                    oi_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "symbol": getattr(open_interest, 'symbol', ''),
                        "exchange": getattr(open_interest, 'exchange', ''),
                        "open_interest": getattr(open_interest, 'open_interest', 0),
                        "open_interest_change": getattr(open_interest, 'open_interest_change', 0)
                    }
                    
                    # Add to analyzer
                    analyzer.add_open_interest(oi_data)
                    
                    # Display open interest
                    print(f"🔓 OPEN INTEREST #{update_count} [{oi_data['timestamp']}]")
                    print(f"    Symbol: {oi_data['symbol']}@{oi_data['exchange']}")
                    
                    if oi_data['open_interest']:
                        print(f"    Open Interest: {oi_data['open_interest']:,}")
                    
                    if oi_data['open_interest_change']:
                        change_symbol = "+" if oi_data['open_interest_change'] > 0 else ""
                        print(f"    Change: {change_symbol}{oi_data['open_interest_change']:,}")
                
                elif template_id == TemplateIDs.MARKET_MODE:
                    # Market mode update
                    market_mode = market_mode_pb2.MarketMode()
                    market_mode.ParseFromString(update_bytes)
                    
                    # Extract market mode data
                    mode_data = {
                        "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                        "symbol": getattr(market_mode, 'symbol', ''),
                        "exchange": getattr(market_mode, 'exchange', ''),
                        "market_mode": getattr(market_mode, 'market_mode', ''),
                        "trading_status": getattr(market_mode, 'trading_status', '')
                    }
                    
                    # Add to analyzer
                    analyzer.add_market_mode(mode_data)
                    
                    # Display market mode
                    print(f"🏛️  MARKET MODE #{update_count} [{mode_data['timestamp']}]")
                    print(f"    Symbol: {mode_data['symbol']}@{mode_data['exchange']}")
                    
                    if mode_data['market_mode']:
                        print(f"    Market Mode: {mode_data['market_mode']}")
                    
                    if mode_data['trading_status']:
                        print(f"    Trading Status: {mode_data['trading_status']}")
                
                else:
                    # Other update types
                    print(f"📡 OTHER STATISTICS: Template {template_id}")
                
                # Display detailed message if requested
                if output_format != "human":
                    print(f"\n📋 STATISTICS UPDATE DETAILS (Template {template_id}):")
                    print(message_handler_obj.format_message(update_info, output_format))
                    print("-" * 40)
                
                print()  # Blank line for readability
                
            except asyncio.TimeoutError:
                # No updates received in the timeout period
                continue
            except Exception as e:
                logger.warning(f"Error processing market statistics update: {e}")
                continue
        
        # Get analysis summary
        summary = analyzer.get_summary()
        
        print(f"\n📈 STREAMING MARKET STATISTICS SUMMARY:")
        print("=" * 60)
        print(f"Symbol: {symbol}")
        print(f"Exchange: {exchange}")
        print(f"Duration: {duration} seconds")
        print(f"Total Updates: {update_count}")
        print(f"Trade Statistics: {summary['total_trade_stats']}")
        print(f"Quote Statistics: {summary['total_quote_stats']}")
        print(f"Open Interest: {summary['total_open_interest']}")
        print(f"Market Mode: {summary['total_market_mode']}")
        print(f"Unique Symbols: {summary['unique_symbols']}")
        
        if update_count > 0:
            avg_frequency = update_count / duration
            print(f"Average Update Frequency: {avg_frequency:.2f} updates/second")
        
        # Volume analysis
        if summary['volume_analysis']:
            print(f"\n📊 VOLUME ANALYSIS:")
            for sym, analysis in summary['volume_analysis'].items():
                print(f"  {sym}:")
                print(f"    Latest Volume: {analysis['latest_volume']:,}")
                print(f"    Average Volume: {analysis['avg_volume']:,.0f}")
                print(f"    Max Volume: {analysis['max_volume']:,}")
                print(f"    Trend: {analysis['volume_trend'].title()}")
                print(f"    Updates: {analysis['volume_updates']}")
        
        # Volatility analysis
        if summary['volatility_analysis']:
            print(f"\n📈 VOLATILITY ANALYSIS:")
            for sym, analysis in summary['volatility_analysis'].items():
                print(f"  {sym}:")
                print(f"    Latest Volatility: {analysis['latest_volatility']:5.2f}%")
                print(f"    Average Volatility: {analysis['avg_volatility']:5.2f}%")
                print(f"    Max Volatility: {analysis['max_volatility']:5.2f}%")
                print(f"    Trend: {analysis['volatility_trend'].title()}")
                print(f"    Updates: {analysis['volatility_updates']}")
        
        # Market health assessment
        health = summary['market_health']
        print(f"\n🏥 MARKET HEALTH ASSESSMENT:")
        print(f"Total Updates: {health['total_updates']}")
        print(f"Update Diversity: {health['update_diversity']}/4 types")
        
        if health['update_diversity'] >= 3:
            print("Market Health: EXCELLENT (high data diversity)")
        elif health['update_diversity'] >= 2:
            print("Market Health: GOOD (moderate data diversity)")
        else:
            print("Market Health: LIMITED (low data diversity)")
        
        # CSV output
        if output_format == "csv" and analyzer.trade_stats_updates:
            print(f"\n📊 CSV FORMAT (TRADE STATISTICS):")
            csv_headers = ["Timestamp", "Symbol", "Exchange", "Volume", "High", "Low", 
                          "Open", "Close", "VWAP"]
            print(",".join(csv_headers))
            
            for stats in analyzer.trade_stats_updates[-20:]:  # Last 20 updates
                values = [
                    stats['timestamp'],
                    stats['symbol'],
                    stats['exchange'],
                    str(stats['volume']),
                    str(stats['high_price']),
                    str(stats['low_price']),
                    str(stats['open_price']),
                    str(stats['close_price']),
                    str(stats['vwap'])
                ]
                print(",".join(values))
        
        # Analysis and insights
        print(f"\n💡 MARKET STATISTICS ANALYSIS:")
        if update_count > 0:
            print(f"✅ Successfully received {update_count} market statistics updates")
            
            # Distribution analysis
            total = update_count
            if summary['total_trade_stats'] > 0:
                pct = (summary['total_trade_stats'] / total) * 100
                print(f"• Trade Statistics: {pct:.1f}% of updates")
            
            if summary['total_quote_stats'] > 0:
                pct = (summary['total_quote_stats'] / total) * 100
                print(f"• Quote Statistics: {pct:.1f}% of updates")
            
            if summary['total_open_interest'] > 0:
                pct = (summary['total_open_interest'] / total) * 100
                print(f"• Open Interest: {pct:.1f}% of updates")
            
            if summary['total_market_mode'] > 0:
                pct = (summary['total_market_mode'] / total) * 100
                print(f"• Market Mode: {pct:.1f}% of updates")
            
            # Market activity assessment
            if avg_frequency > 2:
                print(f"• High activity market with frequent statistics updates")
            elif avg_frequency > 0.5:
                print(f"• Moderate activity market")
            else:
                print(f"• Low activity market")
        else:
            print("⚠️  No market statistics updates received")
            print("• Market may be closed")
            print("• Symbol may have limited statistical reporting")
            print("• Check market hours and data availability")
        
        # Unsubscribe
        print(f"\n📤 Unsubscribing from market statistics...")
        request.request = 2  # UNSUBSCRIBE
        serialized_unsub = request.SerializeToString()
        
        if await connection.send_message(serialized_unsub):
            # Wait for unsubscribe response
            unsub_response = await connection.receive_message(timeout=5.0)
            if unsub_response:
                print("✅ Successfully unsubscribed")
            else:
                print("⚠️  Unsubscribe response not received")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return update_count > 0  # Success if we received any updates
        
    except Exception as e:
        logger.error(f"Error testing streaming market statistics: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for streaming market statistics testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Streaming Market Statistics endpoint",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_market_statistics.py --symbol ESH5 --exchange CME
  python test_market_statistics.py --symbol AAPL --exchange NASDAQ --duration 120
  python test_market_statistics.py --symbol ESH5 --exchange CME --format csv

Safety:
  This script performs READ-ONLY streaming market statistics requests only.
  Requires authentication but no account modifications performed.
  Automatically unsubscribes at the end of the test.
  
Output:
  Displays real-time market statistics including:
  - Trade statistics (volume, OHLC, VWAP)
  - Quote statistics (bid/ask counts)
  - Open interest changes
  - Market mode and trading status
  - Volatility and trend analysis
  - Market health assessment
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Trading symbol to subscribe to (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        required=True,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=60,
        help="How long to listen for statistics updates in seconds (default: 60)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC STREAMING MARKET STATISTICS ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY STREAMING MARKET STATISTICS OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_market_statistics_streaming(
        args.symbol,
        args.exchange,
        args.duration,
        args.format
    )
    
    if success:
        print("\n✅ Streaming market statistics test completed successfully!")
        return 0
    else:
        print("\n❌ Streaming market statistics test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)