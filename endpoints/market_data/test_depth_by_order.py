#!/usr/bin/env python3

"""
Test Rithmic Depth by Order endpoints (Templates 115/116, 117/118).

This script demonstrates how to retrieve depth by order snapshots and subscribe
to depth by order updates from the Rithmic API, providing detailed order book data.

SAFETY: This script only performs READ-ONLY depth by order requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_depth_by_order_snapshot_pb2
import response_depth_by_order_snapshot_pb2
import request_depth_by_order_updates_pb2
import response_depth_by_order_updates_pb2

# Depth by order update messages
import depth_by_order_pb2
import depth_by_order_end_event_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_depth_by_order_snapshot(symbol: str, exchange: str,
                                     output_format: str = "human") -> bool:
    """
    Test depth by order snapshot functionality.
    
    Args:
        symbol: Trading symbol to get depth by order snapshot for
        exchange: Exchange name
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📚 TESTING DEPTH BY ORDER SNAPSHOT ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_RESPONSE} (Response)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "depth_by_order_snapshot_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create depth by order snapshot request
        request = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
        request.template_id = TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_REQUEST
        request.user_msg.append(f"Depth by order snapshot request for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        
        # Send depth by order snapshot request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending depth by order snapshot request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send depth by order snapshot request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for depth by order snapshot response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_depth_by_order_snapshot_pb2.ResponseDepthByOrderSnapshot()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 DEPTH BY ORDER SNAPSHOT ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Depth by order snapshot request successful!")
            
            # Display depth by order information
            print(f"\n📚 DEPTH BY ORDER SNAPSHOT:")
            print("-" * 80)
            
            # Basic symbol information
            if hasattr(response, 'symbol') and response.symbol:
                print(f"Symbol: {response.symbol}")
            if hasattr(response, 'exchange') and response.exchange:
                print(f"Exchange: {response.exchange}")
            
            # Order book depth data
            bid_orders = []
            ask_orders = []
            
            # Process bid side orders
            if hasattr(response, 'bid_price') and response.bid_price:
                print(f"\n📈 BID SIDE ORDERS ({len(response.bid_price)} levels):")
                print("-" * 60)
                
                for i, price in enumerate(response.bid_price):
                    order = {
                        "side": "BID",
                        "price": price,
                        "quantity": 0,
                        "orders": 0,
                        "order_id": "",
                        "timestamp": ""
                    }
                    
                    # Get corresponding order data
                    if hasattr(response, 'bid_quantity') and i < len(response.bid_quantity):
                        order["quantity"] = response.bid_quantity[i]
                    
                    if hasattr(response, 'bid_order_count') and i < len(response.bid_order_count):
                        order["orders"] = response.bid_order_count[i]
                    
                    if hasattr(response, 'bid_order_id') and i < len(response.bid_order_id):
                        order["order_id"] = response.bid_order_id[i]
                    
                    if hasattr(response, 'bid_timestamp') and i < len(response.bid_timestamp):
                        order["timestamp"] = response.bid_timestamp[i]
                    
                    bid_orders.append(order)
                    
                    # Display bid order
                    print(f"{i+1:3d}. Price: {price:8.2f} | Qty: {order['quantity']:6d} | Orders: {order['orders']:3d}")
                    if order["order_id"]:
                        print(f"     Order ID: {order['order_id']}")
            
            # Process ask side orders
            if hasattr(response, 'ask_price') and response.ask_price:
                print(f"\n📉 ASK SIDE ORDERS ({len(response.ask_price)} levels):")
                print("-" * 60)
                
                for i, price in enumerate(response.ask_price):
                    order = {
                        "side": "ASK",
                        "price": price,
                        "quantity": 0,
                        "orders": 0,
                        "order_id": "",
                        "timestamp": ""
                    }
                    
                    # Get corresponding order data
                    if hasattr(response, 'ask_quantity') and i < len(response.ask_quantity):
                        order["quantity"] = response.ask_quantity[i]
                    
                    if hasattr(response, 'ask_order_count') and i < len(response.ask_order_count):
                        order["orders"] = response.ask_order_count[i]
                    
                    if hasattr(response, 'ask_order_id') and i < len(response.ask_order_id):
                        order["order_id"] = response.ask_order_id[i]
                    
                    if hasattr(response, 'ask_timestamp') and i < len(response.ask_timestamp):
                        order["timestamp"] = response.ask_timestamp[i]
                    
                    ask_orders.append(order)
                    
                    # Display ask order
                    print(f"{i+1:3d}. Price: {price:8.2f} | Qty: {order['quantity']:6d} | Orders: {order['orders']:3d}")
                    if order["order_id"]:
                        print(f"     Order ID: {order['order_id']}")
            
            # Market depth statistics
            if bid_orders and ask_orders:
                best_bid = max(bid_orders, key=lambda x: x["price"])
                best_ask = min(ask_orders, key=lambda x: x["price"])
                spread = best_ask["price"] - best_bid["price"]
                
                print(f"\n📊 MARKET DEPTH STATISTICS:")
                print(f"Best Bid: {best_bid['price']:8.2f} ({best_bid['quantity']} qty)")
                print(f"Best Ask: {best_ask['price']:8.2f} ({best_ask['quantity']} qty)")
                print(f"Spread: {spread:8.2f}")
                
                # Calculate total liquidity
                total_bid_qty = sum(order["quantity"] for order in bid_orders)
                total_ask_qty = sum(order["quantity"] for order in ask_orders)
                total_bid_orders = sum(order["orders"] for order in bid_orders)
                total_ask_orders = sum(order["orders"] for order in ask_orders)
                
                print(f"Total Bid Liquidity: {total_bid_qty:,} ({total_bid_orders} orders)")
                print(f"Total Ask Liquidity: {total_ask_qty:,} ({total_ask_orders} orders)")
                print(f"Total Depth Levels: {len(bid_orders)} bids, {len(ask_orders)} asks")
            
            # CSV output
            if output_format == "csv":
                print(f"\n📊 CSV FORMAT (ORDER BOOK):")
                print("Side,Price,Quantity,Orders,Order_ID,Timestamp")
                
                # Combine and sort orders
                all_orders = bid_orders + ask_orders
                for order in all_orders:
                    values = [
                        order["side"],
                        str(order["price"]),
                        str(order["quantity"]),
                        str(order["orders"]),
                        str(order["order_id"]),
                        str(order["timestamp"])
                    ]
                    print(",".join(values))
            
            # Market analysis
            print(f"\n💡 MARKET DEPTH ANALYSIS:")
            if bid_orders and ask_orders:
                print(f"• Order book shows {len(bid_orders)} bid levels and {len(ask_orders)} ask levels")
                print(f"• Spread of {spread:.2f} indicates market tightness")
                
                # Liquidity imbalance
                if total_bid_qty > total_ask_qty * 1.1:
                    print(f"• Bid-heavy liquidity (buying pressure)")
                elif total_ask_qty > total_bid_qty * 1.1:
                    print(f"• Ask-heavy liquidity (selling pressure)")
                else:
                    print(f"• Balanced liquidity")
                
                # Order concentration
                if bid_orders:
                    avg_bid_size = total_bid_qty / len(bid_orders)
                    print(f"• Average bid size: {avg_bid_size:.1f}")
                if ask_orders:
                    avg_ask_size = total_ask_qty / len(ask_orders)
                    print(f"• Average ask size: {avg_ask_size:.1f}")
            else:
                print("• Limited or no depth by order data available")
        
        else:
            print("❌ Depth by order snapshot request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify symbol and exchange are correct")
            print("   • Check if depth by order data is available for this instrument")
            print("   • Ensure proper market data permissions")
            print("   • Try with a more liquid symbol (e.g., ESH5)")
            print("   • Check if market is currently active")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing depth by order snapshot: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_depth_by_order_updates(symbol: str, exchange: str, duration: int = 30,
                                    output_format: str = "human") -> bool:
    """
    Test depth by order updates subscription functionality.
    
    Args:
        symbol: Trading symbol to subscribe to depth by order updates for
        exchange: Exchange name
        duration: How long to listen for updates (seconds)
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📡 TESTING DEPTH BY ORDER UPDATES ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.DEPTH_BY_ORDER_UPDATES_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.DEPTH_BY_ORDER_UPDATES_RESPONSE} (Response)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Duration: {duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "depth_by_order_updates_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create depth by order updates subscription request
        request = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates()
        request.template_id = TemplateIDs.DEPTH_BY_ORDER_UPDATES_REQUEST
        request.user_msg.append(f"Depth by order updates subscription for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        request.request = 1  # SUBSCRIBE (1 = SUBSCRIBE, 2 = UNSUBSCRIBE)
        
        # Send depth by order updates subscription request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending depth by order updates subscription ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send depth by order updates subscription")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for subscription response
        print("⏳ Waiting for subscription response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No subscription response received within timeout")
            return False
        
        print(f"📥 Received subscription response ({len(response_bytes)} bytes)")
        
        # Parse subscription response
        response = response_depth_by_order_updates_pb2.ResponseDepthByOrderUpdates()
        response.ParseFromString(response_bytes)
        
        # Check subscription success - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        subscription_success = explicit_success or empty_response_success
        
        if not subscription_success:
            print("❌ Depth by order updates subscription failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            return False
        
        print("✅ Depth by order updates subscription successful!")
        
        # Listen for depth by order updates
        print(f"\n📡 LISTENING FOR DEPTH BY ORDER UPDATES ({duration} seconds)...")
        print("=" * 60)
        
        update_count = 0
        end_event_count = 0
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < duration:
            try:
                # Wait for depth by order updates
                update_bytes = await connection.receive_message(timeout=5.0)
                
                if not update_bytes:
                    continue  # Timeout, keep listening
                
                # Parse update to determine type
                update_info = parse_message(update_bytes)
                if not update_info:
                    continue
                
                template_id = update_info.template_id
                
                if template_id == TemplateIDs.DEPTH_BY_ORDER:
                    # Depth by order update
                    depth_update = depth_by_order_pb2.DepthByOrder()
                    depth_update.ParseFromString(update_bytes)
                    
                    update_count += 1
                    print(f"📚 DEPTH UPDATE #{update_count}: {depth_update.symbol}@{depth_update.exchange}")
                    
                    if hasattr(depth_update, 'side'):
                        side = "BID" if depth_update.side == 1 else "ASK"
                        print(f"    Side: {side}")
                    
                    if hasattr(depth_update, 'price') and depth_update.price:
                        print(f"    Price: {depth_update.price}")
                    
                    if hasattr(depth_update, 'quantity') and depth_update.quantity:
                        print(f"    Quantity: {depth_update.quantity}")
                    
                    if hasattr(depth_update, 'order_id') and depth_update.order_id:
                        print(f"    Order ID: {depth_update.order_id}")
                    
                    # Display detailed message if requested
                    if output_format != "human":
                        print(f"\n📋 DEPTH UPDATE DETAILS:")
                        print(message_handler_obj.format_message(update_info, output_format))
                        print("-" * 40)
                
                elif template_id == TemplateIDs.DEPTH_BY_ORDER_END_EVENT:
                    # Depth by order end event
                    end_event = depth_by_order_end_event_pb2.DepthByOrderEndEvent()
                    end_event.ParseFromString(update_bytes)
                    
                    end_event_count += 1
                    print(f"🔚 DEPTH END EVENT #{end_event_count}: {end_event.symbol}@{end_event.exchange}")
                
                else:
                    print(f"📡 OTHER UPDATE: Template {template_id}")
                
            except asyncio.TimeoutError:
                # No updates received in the timeout period
                continue
            except Exception as e:
                logger.warning(f"Error processing depth by order update: {e}")
                continue
        
        print(f"\n📊 DEPTH BY ORDER UPDATES SUMMARY:")
        print("=" * 60)
        print(f"Symbol: {symbol}")
        print(f"Exchange: {exchange}")
        print(f"Duration: {duration} seconds")
        print(f"Depth Updates: {update_count}")
        print(f"End Events: {end_event_count}")
        
        # Analysis
        if update_count > 0:
            print(f"\n💡 ANALYSIS:")
            print(f"✅ Successfully received {update_count} depth by order updates")
            print(f"• Average updates per second: {update_count/duration:.1f}")
            print(f"• Market shows active order book changes")
        else:
            print(f"\n⚠️  No depth by order updates received")
            print("• Market may be closed or inactive")
            print("• Symbol may have limited order book activity")
        
        # Unsubscribe
        print(f"\n📤 Unsubscribing from depth by order updates...")
        request.request = 2  # UNSUBSCRIBE
        serialized_unsub = request.SerializeToString()
        
        if await connection.send_message(serialized_unsub):
            # Wait for unsubscribe response
            unsub_response = await connection.receive_message(timeout=5.0)
            if unsub_response:
                print("✅ Successfully unsubscribed")
            else:
                print("⚠️  Unsubscribe response not received")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return update_count > 0  # Success if we received any updates
        
    except Exception as e:
        logger.error(f"Error testing depth by order updates: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for depth by order testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Depth by Order endpoints (Templates 115/116, 117/118)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_depth_by_order.py --symbol ESH5 --exchange CME --snapshot
  python test_depth_by_order.py --symbol ESH5 --exchange CME --updates --duration 60
  python test_depth_by_order.py --symbol ESH5 --exchange CME --both
  python test_depth_by_order.py --symbol ESH5 --exchange CME --snapshot --format csv

Safety:
  This script performs READ-ONLY depth by order requests only.
  Requires authentication but no account modifications performed.
  Automatically unsubscribes from updates at the end of the test.
  
Output:
  Displays comprehensive depth by order information including:
  - Order book snapshot with individual orders
  - Real-time depth by order updates
  - Market depth statistics and analysis
  - Liquidity distribution and order concentration
  - Spread analysis and market insights
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Trading symbol to get depth by order for (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        required=True,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--snapshot",
        action="store_true",
        help="Test depth by order snapshot"
    )
    
    parser.add_argument(
        "--updates",
        action="store_true",
        help="Test depth by order updates subscription"
    )
    
    parser.add_argument(
        "--both",
        action="store_true",
        help="Test both snapshot and updates"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=30,
        help="How long to listen for updates in seconds (default: 30)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not (args.snapshot or args.updates or args.both):
        print("❌ Must specify --snapshot, --updates, or --both")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC DEPTH BY ORDER ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY DEPTH BY ORDER OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the tests
    results = []
    
    if args.snapshot or args.both:
        print("\n" + "="*60)
        print("TESTING DEPTH BY ORDER SNAPSHOT")
        print("="*60)
        snapshot_success = await test_depth_by_order_snapshot(args.symbol, args.exchange, args.format)
        results.append(("Snapshot", snapshot_success))
        
        if args.both:
            print("\n⏳ Waiting 5 seconds before updates test...")
            await asyncio.sleep(5)
    
    if args.updates or args.both:
        print("\n" + "="*60)
        print("TESTING DEPTH BY ORDER UPDATES")
        print("="*60)
        updates_success = await test_depth_by_order_updates(args.symbol, args.exchange, args.duration, args.format)
        results.append(("Updates", updates_success))
    
    # Summary
    print("\n" + "="*60)
    print("📋 DEPTH BY ORDER TEST SUMMARY")
    print("="*60)
    
    all_passed = True
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:15s}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\n✅ All depth by order tests completed successfully!")
        return 0
    else:
        print("\n❌ Some depth by order tests failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)