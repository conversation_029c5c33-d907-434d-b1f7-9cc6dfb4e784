#!/usr/bin/env python3

"""
Test Rithmic Get Volume at Price endpoints (Templates 119/120).

This script demonstrates how to retrieve volume at price information
from the Rithmic API, providing market depth and liquidity data.

SAFETY: This script only performs READ-ONLY volume at price requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_get_volume_at_price_pb2
import response_get_volume_at_price_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_volume_at_price(symbol: str, exchange: str, price_level: Optional[float] = None,
                             output_format: str = "human") -> bool:
    """
    Test volume at price retrieval functionality.
    
    Args:
        symbol: Trading symbol to get volume at price for
        exchange: Exchange name
        price_level: Optional specific price level to query
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📊 TESTING VOLUME AT PRICE ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.GET_VOLUME_AT_PRICE_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.GET_VOLUME_AT_PRICE_RESPONSE} (Response)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Price Level: {price_level or 'All levels'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "volume_at_price_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create volume at price request
        request = request_get_volume_at_price_pb2.RequestGetVolumeAtPrice()
        request.template_id = TemplateIDs.GET_VOLUME_AT_PRICE_REQUEST
        request.user_msg.append(f"Volume at price request for {symbol}")
        
        request.symbol = symbol
        request.exchange = exchange
        
        # Note: Volume at price requests typically don't take a specific price parameter
        # The API returns volume data across all available price levels
        
        # Send volume at price request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending volume at price request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send volume at price request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for volume at price response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_get_volume_at_price_pb2.ResponseGetVolumeAtPrice()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 VOLUME AT PRICE ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Volume at price request successful!")
            
            # Display volume at price information
            print(f"\n📊 VOLUME AT PRICE DATA:")
            print("-" * 80)
            
            # Basic symbol information
            if hasattr(response, 'symbol') and response.symbol:
                print(f"Symbol: {response.symbol}")
            if hasattr(response, 'exchange') and response.exchange:
                print(f"Exchange: {response.exchange}")
            
            # Volume at price data
            volume_levels = []
            if hasattr(response, 'price') and response.price:
                print(f"\n📈 VOLUME LEVELS ({len(response.price)} entries):")
                print("-" * 60)
                
                for i, price in enumerate(response.price):
                    level_info = {
                        "index": i + 1,
                        "price": price,
                        "volume": 0,
                        "bid_volume": 0,
                        "ask_volume": 0,
                        "total_orders": 0,
                        "timestamp": ""
                    }
                    
                    # Get corresponding volume data
                    if hasattr(response, 'volume') and i < len(response.volume):
                        level_info["volume"] = response.volume[i]
                    
                    if hasattr(response, 'bid_volume') and i < len(response.bid_volume):
                        level_info["bid_volume"] = response.bid_volume[i]
                    
                    if hasattr(response, 'ask_volume') and i < len(response.ask_volume):
                        level_info["ask_volume"] = response.ask_volume[i]
                    
                    if hasattr(response, 'order_count') and i < len(response.order_count):
                        level_info["total_orders"] = response.order_count[i]
                    
                    if hasattr(response, 'timestamp') and i < len(response.timestamp):
                        level_info["timestamp"] = response.timestamp[i]
                    
                    volume_levels.append(level_info)
                    
                    # Display volume level
                    print(f"{i+1:3d}. Price: {price}")
                    if level_info["volume"]:
                        print(f"     Total Volume: {level_info['volume']}")
                    if level_info["bid_volume"]:
                        print(f"     Bid Volume: {level_info['bid_volume']}")
                    if level_info["ask_volume"]:
                        print(f"     Ask Volume: {level_info['ask_volume']}")
                    if level_info["total_orders"]:
                        print(f"     Order Count: {level_info['total_orders']}")
                    if level_info["timestamp"]:
                        print(f"     Timestamp: {level_info['timestamp']}")
                    print()
            
            # Summary statistics
            if volume_levels:
                total_volume = sum(level["volume"] for level in volume_levels)
                total_bid_volume = sum(level["bid_volume"] for level in volume_levels)
                total_ask_volume = sum(level["ask_volume"] for level in volume_levels)
                total_orders = sum(level["total_orders"] for level in volume_levels)
                
                print(f"\n📈 VOLUME SUMMARY:")
                print(f"Price Levels: {len(volume_levels)}")
                if total_volume > 0:
                    print(f"Total Volume: {total_volume:,}")
                if total_bid_volume > 0:
                    print(f"Total Bid Volume: {total_bid_volume:,}")
                if total_ask_volume > 0:
                    print(f"Total Ask Volume: {total_ask_volume:,}")
                if total_orders > 0:
                    print(f"Total Orders: {total_orders:,}")
                
                # Find price range
                prices = [level["price"] for level in volume_levels if level["price"]]
                if prices:
                    min_price = min(prices)
                    max_price = max(prices)
                    print(f"Price Range: {min_price} - {max_price}")
                
                # Find highest volume level
                if total_volume > 0:
                    max_volume_level = max(volume_levels, key=lambda x: x["volume"])
                    print(f"Highest Volume: {max_volume_level['volume']:,} at ${max_volume_level['price']}")
            
            # Market depth analysis
            if hasattr(response, 'market_depth') and response.market_depth:
                print(f"\nMarket Depth: {response.market_depth}")
            
            if hasattr(response, 'liquidity_score') and response.liquidity_score:
                print(f"Liquidity Score: {response.liquidity_score}")
            
            # Additional volume metrics
            volume_metrics = [
                'average_trade_size', 'volume_weighted_average_price', 'participation_rate',
                'market_impact', 'spread_at_volume', 'time_weighted_volume'
            ]
            
            print(f"\n📊 VOLUME METRICS:")
            for metric in volume_metrics:
                if hasattr(response, metric):
                    value = getattr(response, metric)
                    if value:
                        metric_name = metric.replace('_', ' ').title()
                        print(f"{metric_name}: {value}")
            
            # CSV output
            if output_format == "csv":
                print(f"\n📊 CSV FORMAT (VOLUME LEVELS):")
                
                if volume_levels:
                    # Header
                    csv_headers = ["Index", "Price", "Volume", "Bid_Volume", 
                                  "Ask_Volume", "Order_Count", "Timestamp"]
                    print(",".join(csv_headers))
                    
                    # Data rows
                    for level in volume_levels:
                        values = [
                            str(level["index"]),
                            str(level["price"]),
                            str(level["volume"]),
                            str(level["bid_volume"]),
                            str(level["ask_volume"]),
                            str(level["total_orders"]),
                            str(level["timestamp"])
                        ]
                        print(",".join(values))
                else:
                    print("No volume at price data available for CSV output")
            
            # Analysis and interpretation
            print(f"\n💡 VOLUME AT PRICE INTERPRETATION:")
            print(f"This data shows trading activity distribution across price levels for {symbol}")
            
            if volume_levels:
                print(f"\n📈 TRADING INSIGHTS:")
                print(f"• Volume distribution helps identify support/resistance levels")
                print(f"• High volume areas indicate significant price levels")
                print(f"• Bid/ask volume imbalance suggests directional pressure")
                
                if total_bid_volume > 0 and total_ask_volume > 0:
                    bid_ask_ratio = total_bid_volume / total_ask_volume
                    if bid_ask_ratio > 1.1:
                        print(f"• Bullish bias: More bid volume ({bid_ask_ratio:.2f}:1 ratio)")
                    elif bid_ask_ratio < 0.9:
                        print(f"• Bearish bias: More ask volume ({1/bid_ask_ratio:.2f}:1 ratio)")
                    else:
                        print(f"• Balanced: Similar bid/ask volumes")
        
        else:
            print("❌ Volume at price request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify symbol and exchange are correct")
            print("   • Check if volume at price data is available for this instrument")
            print("   • Ensure proper market data permissions")
            print("   • Try with a more liquid symbol (e.g., ESH5)")
            print("   • Check if market is currently active")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing volume at price: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_multiple_volume_at_price(symbols: List[tuple],
                                      output_format: str = "human") -> bool:
    """
    Test volume at price for multiple symbols.
    
    Args:
        symbols: List of (symbol, exchange, price_level) tuples
        output_format: Output format
        
    Returns:
        bool: True if all tests successful
    """
    print("=" * 60)
    print("📊 TESTING MULTIPLE VOLUME AT PRICE REQUESTS")
    print("=" * 60)
    symbol_list = []
    for s, e, p in symbols:
        if p:
            symbol_list.append(f"{s}@{e}@{p}")
        else:
            symbol_list.append(f"{s}@{e}")
    print(f"Symbols: {', '.join(symbol_list)}")
    print("=" * 60)
    
    results = []
    
    for i, (symbol, exchange, price_level) in enumerate(symbols, 1):
        print(f"\n{'='*20} REQUEST {i}/{len(symbols)} {'='*20}")
        success = await test_volume_at_price(symbol, exchange, price_level, output_format)
        symbol_desc = f"{symbol}@{exchange}" + (f"@{price_level}" if price_level else "")
        results.append((symbol_desc, success))
        
        if i < len(symbols):
            print("\n⏳ Waiting 3 seconds before next request...")
            await asyncio.sleep(3)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 MULTIPLE VOLUME AT PRICE SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for symbol_desc, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{symbol_desc:25s}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL VOLUME AT PRICE REQUESTS PASSED!")
    else:
        print("⚠️  SOME VOLUME AT PRICE REQUESTS FAILED!")
    
    return all_passed

async def main():
    """Main function for volume at price testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Get Volume at Price endpoint (Templates 119/120)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_volume_at_price.py --symbol ESH5 --exchange CME
  python test_volume_at_price.py --symbol AAPL --exchange NASDAQ --price 150.00
  python test_volume_at_price.py --multiple ESH5,CME, NQH5,CME, AAPL,NASDAQ,150.00
  python test_volume_at_price.py --symbol ESH5 --exchange CME --format csv

Safety:
  This script performs READ-ONLY volume at price requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays comprehensive volume at price information including:
  - Volume distribution across price levels
  - Bid and ask volume breakdown
  - Order count and liquidity metrics
  - Market depth and trading insights
  - Summary statistics and analysis
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        help="Trading symbol to get volume at price for (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--price",
        type=float,
        help="Specific price level to query (optional)"
    )
    
    parser.add_argument(
        "--multiple",
        nargs="+",
        help="Test multiple symbols in format SYMBOL,EXCHANGE[,PRICE]"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.symbol and not args.multiple:
        print("❌ Either --symbol and --exchange, or --multiple must be specified")
        return 1
    
    if args.symbol and not args.exchange:
        print("❌ --exchange is required when using --symbol")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC VOLUME AT PRICE ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY VOLUME AT PRICE OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test(s)
    if args.multiple:
        # Parse multiple symbols
        symbols = []
        for item in args.multiple:
            parts = item.split(',')
            if len(parts) >= 2:
                symbol = parts[0].strip()
                exchange = parts[1].strip()
                price_level = float(parts[2].strip()) if len(parts) > 2 and parts[2].strip() else None
                symbols.append((symbol, exchange, price_level))
            else:
                print(f"❌ Invalid format for {item}. Use SYMBOL,EXCHANGE[,PRICE] format.")
                return 1
        
        success = await test_multiple_volume_at_price(symbols, args.format)
    else:
        success = await test_volume_at_price(args.symbol, args.exchange, args.price, args.format)
    
    if success:
        print("\n✅ Volume at price test completed successfully!")
        return 0
    else:
        print("\n❌ Volume at price test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)