#!/usr/bin/env python3

"""
Test Rithmic Market Data Update by Underlying endpoints (Templates 105/106).

This script demonstrates how to subscribe to market data updates by underlying symbol
from the Rithmic API, providing real-time data for multiple related instruments.

SAFETY: This script only performs READ-ONLY market data subscription requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_market_data_update_by_underlying_pb2
import response_market_data_update_by_underlying_pb2

# Market data update messages
import last_trade_pb2
import best_bid_offer_pb2
import order_book_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_market_data_by_underlying(underlying_symbol: str, exchange: str,
                                       duration: int = 30,
                                       output_format: str = "human") -> bool:
    """
    Test market data by underlying subscription functionality.
    
    Args:
        underlying_symbol: Underlying symbol to subscribe to (e.g., "ES", "AAPL")
        exchange: Exchange name
        duration: How long to listen for updates (seconds)
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📡 TESTING MARKET DATA BY UNDERLYING ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.MARKET_DATA_UPDATE_BY_UNDERLYING_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.MARKET_DATA_UPDATE_BY_UNDERLYING_RESPONSE} (Response)")
    print(f"Underlying Symbol: {underlying_symbol}")
    print(f"Exchange: {exchange}")
    print(f"Duration: {duration} seconds")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "market_data_by_underlying_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create market data by underlying subscription request
        request = request_market_data_update_by_underlying_pb2.RequestMarketDataUpdateByUnderlying()
        request.template_id = TemplateIDs.MARKET_DATA_UPDATE_BY_UNDERLYING_REQUEST
        request.user_msg.append(f"Market data by underlying subscription for {underlying_symbol}")
        
        request.underlying_symbol = underlying_symbol
        request.exchange = exchange
        request.request = request_market_data_update_by_underlying_pb2.RequestMarketDataUpdateByUnderlying.Request.SUBSCRIBE
        
        # Set update bits for the types of data we want using proper enum values
        request.update_bits = (
            request_market_data_update_by_underlying_pb2.RequestMarketDataUpdateByUnderlying.UpdateBits.LAST_TRADE |
            request_market_data_update_by_underlying_pb2.RequestMarketDataUpdateByUnderlying.UpdateBits.BBO |
            request_market_data_update_by_underlying_pb2.RequestMarketDataUpdateByUnderlying.UpdateBits.ORDER_BOOK
        )
        
        # Send market data by underlying subscription request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending market data by underlying subscription ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send market data by underlying subscription")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for subscription response
        print("⏳ Waiting for subscription response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No subscription response received within timeout")
            return False
        
        print(f"📥 Received subscription response ({len(response_bytes)} bytes)")
        
        # Parse subscription response
        response = response_market_data_update_by_underlying_pb2.ResponseMarketDataUpdateByUnderlying()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 SUBSCRIPTION RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Check subscription success
        subscription_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        if not subscription_success:
            print("❌ Market data by underlying subscription failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            return False
        
        print("✅ Market data by underlying subscription successful!")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Listen for market data updates
        print(f"\n📡 LISTENING FOR MARKET DATA UPDATES ({duration} seconds)...")
        print("=" * 60)
        
        update_counts = {
            "last_trade": 0,
            "best_bid_offer": 0,
            "order_book": 0,
            "trade_statistics": 0,
            "other": 0
        }
        
        symbols_seen = set()
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < duration:
            try:
                # Wait for market data updates
                update_bytes = await connection.receive_message(timeout=5.0)
                
                if not update_bytes:
                    continue  # Timeout, keep listening
                
                # Parse update to determine type
                update_info = parse_message(update_bytes)
                if not update_info:
                    continue
                
                template_id = update_info.template_id
                
                # Handle different types of market data updates
                if template_id == TemplateIDs.LAST_TRADE:
                    # Last trade update
                    last_trade = last_trade_pb2.LastTrade()
                    last_trade.ParseFromString(update_bytes)
                    
                    update_counts["last_trade"] += 1
                    symbols_seen.add(f"{last_trade.symbol}@{last_trade.exchange}")
                    
                    print(f"📊 LAST TRADE: {last_trade.symbol}@{last_trade.exchange}")
                    if hasattr(last_trade, 'trade_price') and last_trade.trade_price:
                        print(f"    Price: {last_trade.trade_price}")
                    if hasattr(last_trade, 'trade_size') and last_trade.trade_size:
                        print(f"    Size: {last_trade.trade_size}")
                    if hasattr(last_trade, 'volume') and last_trade.volume:
                        print(f"    Volume: {last_trade.volume}")
                    
                elif template_id == TemplateIDs.BEST_BID_OFFER:
                    # Best bid/offer update
                    bbo = best_bid_offer_pb2.BestBidOffer()
                    bbo.ParseFromString(update_bytes)
                    
                    update_counts["best_bid_offer"] += 1
                    symbols_seen.add(f"{bbo.symbol}@{bbo.exchange}")
                    
                    print(f"📈 BID/OFFER: {bbo.symbol}@{bbo.exchange}")
                    if hasattr(bbo, 'bid_price') and bbo.bid_price:
                        print(f"    Bid: {bbo.bid_price}")
                    if hasattr(bbo, 'ask_price') and bbo.ask_price:
                        print(f"    Ask: {bbo.ask_price}")
                    if hasattr(bbo, 'bid_size') and bbo.bid_size:
                        print(f"    Bid Size: {bbo.bid_size}")
                    if hasattr(bbo, 'ask_size') and bbo.ask_size:
                        print(f"    Ask Size: {bbo.ask_size}")
                
                elif template_id == TemplateIDs.ORDER_BOOK:
                    # Order book update
                    order_book = order_book_pb2.OrderBook()
                    order_book.ParseFromString(update_bytes)
                    
                    update_counts["order_book"] += 1
                    symbols_seen.add(f"{order_book.symbol}@{order_book.exchange}")
                    
                    print(f"📚 ORDER BOOK: {order_book.symbol}@{order_book.exchange}")
                    # Order book details would be complex to display fully
                    
                elif template_id == TemplateIDs.TRADE_STATISTICS:
                    # Trade statistics update
                    update_counts["trade_statistics"] += 1
                    print(f"📊 TRADE STATISTICS: Template {template_id}")
                    
                else:
                    # Other update types
                    update_counts["other"] += 1
                    print(f"📡 OTHER UPDATE: Template {template_id}")
                
                # Display detailed message if requested
                if output_format != "human":
                    print(f"\n📋 UPDATE DETAILS (Template {template_id}):")
                    print(message_handler_obj.format_message(update_info, output_format))
                    print("-" * 40)
                
            except asyncio.TimeoutError:
                # No updates received in the timeout period
                continue
            except Exception as e:
                logger.warning(f"Error processing market data update: {e}")
                continue
        
        print(f"\n📊 MARKET DATA BY UNDERLYING SUMMARY:")
        print("=" * 60)
        print(f"Underlying Symbol: {underlying_symbol}")
        print(f"Exchange: {exchange}")
        print(f"Duration: {duration} seconds")
        print(f"Symbols Seen: {len(symbols_seen)}")
        
        # Display update counts
        total_updates = sum(update_counts.values())
        print(f"\nUpdate Counts (Total: {total_updates}):")
        for update_type, count in update_counts.items():
            if count > 0:
                print(f"  {update_type.replace('_', ' ').title()}: {count}")
        
        # Display symbols seen
        if symbols_seen:
            print(f"\nSymbols with Updates:")
            for symbol in sorted(symbols_seen):
                print(f"  • {symbol}")
        
        # CSV output
        if output_format == "csv":
            print(f"\n📊 CSV FORMAT (UPDATE SUMMARY):")
            print("Update_Type,Count")
            for update_type, count in update_counts.items():
                print(f"{update_type},{count}")
        
        # Analysis
        print(f"\n💡 ANALYSIS:")
        if total_updates > 0:
            print(f"✅ Successfully received {total_updates} market data updates")
            print(f"• Average updates per second: {total_updates/duration:.1f}")
            
            if len(symbols_seen) > 1:
                print(f"• Multi-symbol updates: Found {len(symbols_seen)} related instruments")
            else:
                print(f"• Single symbol updates: Only {underlying_symbol} related data")
                
        else:
            print("⚠️  No market data updates received")
            print("• Market may be closed")
            print("• Underlying symbol may have no active instruments")
            print("• Check subscription parameters")
        
        # Unsubscribe
        print(f"\n📤 Unsubscribing from market data by underlying...")
        request.request = request_market_data_update_by_underlying_pb2.RequestMarketDataUpdateByUnderlying.Request.UNSUBSCRIBE
        serialized_unsub = request.SerializeToString()
        
        if await connection.send_message(serialized_unsub):
            # Wait for unsubscribe response
            unsub_response = await connection.receive_message(timeout=5.0)
            if unsub_response:
                print("✅ Successfully unsubscribed")
            else:
                print("⚠️  Unsubscribe response not received")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return total_updates > 0  # Success if we received any updates
        
    except Exception as e:
        logger.error(f"Error testing market data by underlying: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for market data by underlying testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Market Data Update by Underlying endpoint (Templates 105/106)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_market_data_by_underlying.py --underlying ES --exchange CME
  python test_market_data_by_underlying.py --underlying AAPL --exchange NASDAQ --duration 60
  python test_market_data_by_underlying.py --underlying ES --exchange CME --format json

Common Underlying Symbols:
  ES (E-mini S&P 500), NQ (E-mini NASDAQ), YM (E-mini Dow)
  CL (Crude Oil), GC (Gold), SI (Silver)
  AAPL, MSFT, GOOGL (Equity options)

Safety:
  This script performs READ-ONLY market data subscription requests only.
  Requires authentication but no account modifications performed.
  Automatically unsubscribes at the end of the test.
  
Output:
  Displays real-time market data updates for instruments related to the underlying:
  - Last trade updates with price, size, volume
  - Best bid/offer updates with spreads
  - Order book depth information
  - Trade statistics and market data
  - Summary statistics and analysis
        """
    )
    
    parser.add_argument(
        "--underlying",
        type=str,
        required=True,
        help="Underlying symbol to subscribe to (e.g., ES, AAPL, NQ)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        required=True,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--duration",
        type=int,
        default=30,
        help="How long to listen for updates in seconds (default: 30)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC MARKET DATA BY UNDERLYING ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY MARKET DATA SUBSCRIPTION OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test
    success = await test_market_data_by_underlying(
        args.underlying,
        args.exchange,
        args.duration,
        args.format
    )
    
    if success:
        print("\n✅ Market data by underlying test completed successfully!")
        return 0
    else:
        print("\n❌ Market data by underlying test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)