#!/usr/bin/env python3

"""
Test Rithmic Tick Size Table endpoints (Templates 107/108).

This script demonstrates how to retrieve tick size table information
from the Rithmic API, providing pricing increment specifications.

SAFETY: This script only performs READ-ONLY tick size table requests.
No account modifications or order operations are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_give_tick_size_type_table_pb2
import response_give_tick_size_type_table_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message

logger = logging.getLogger(__name__)

async def test_tick_size_table(symbol: str, exchange: str,
                             output_format: str = "human") -> bool:
    """
    Test tick size table retrieval functionality.
    
    Args:
        symbol: Trading symbol to get tick size table for
        exchange: Exchange name
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📏 TESTING TICK SIZE TABLE ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.TICK_SIZE_TABLE_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.TICK_SIZE_TABLE_RESPONSE} (Response)")
    print(f"Symbol: {symbol}")
    print(f"Exchange: {exchange}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Ticker Plant
        print("🔐 Establishing connection to Ticker Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.TICKER_PLANT, "tick_size_table_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create tick size table request
        request = request_give_tick_size_type_table_pb2.RequestGiveTickSizeTypeTable()
        request.template_id = TemplateIDs.TICK_SIZE_TABLE_REQUEST
        request.user_msg.append(f"Tick size table request")
        
        # Set tick size type (required field) - common type for futures
        request.tick_size_type = "1"  # Standard tick size type
        
        # Send tick size table request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending tick size table request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send tick size table request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for tick size table response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_give_tick_size_type_table_pb2.ResponseGiveTickSizeTypeTable()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response content
        print("\n📊 TICK SIZE TABLE ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Check if successful - handle both explicit success codes and empty response codes
        explicit_success = len(response.rp_code) == 1 and response.rp_code[0] == "0"
        
        # Check for error indicators
        has_error = False
        if len(response.rp_code) >= 2:
            has_error = True  # Error code and message present
        elif len(response.rp_code) == 1 and response.rp_code[0] != "0":
            has_error = True  # Non-zero error code
        
        # Consider empty response codes as success if no explicit error
        empty_response_success = len(response.rp_code) == 0 and not has_error
        
        success = explicit_success or empty_response_success
        
        if success:
            print("✅ Tick size table request successful!")
            
            # Display tick size table information
            print(f"\n📏 TICK SIZE TABLE:")
            print("-" * 80)
            
            # Basic symbol information
            if hasattr(response, 'symbol') and response.symbol:
                print(f"Symbol: {response.symbol}")
            if hasattr(response, 'exchange') and response.exchange:
                print(f"Exchange: {response.exchange}")
            
            # Tick size table data
            tick_sizes = []
            if hasattr(response, 'tick_size') and response.tick_size:
                print(f"\n📊 TICK SIZE DETAILS ({len(response.tick_size)} entries):")
                print("-" * 60)
                
                for i, tick_size in enumerate(response.tick_size):
                    tick_info = {
                        "index": i + 1,
                        "tick_size": tick_size,
                        "price_from": "",
                        "price_to": "",
                        "increment": "",
                        "description": ""
                    }
                    
                    # Get corresponding price ranges if available
                    if hasattr(response, 'price_from') and i < len(response.price_from):
                        tick_info["price_from"] = response.price_from[i]
                    
                    if hasattr(response, 'price_to') and i < len(response.price_to):
                        tick_info["price_to"] = response.price_to[i]
                    
                    if hasattr(response, 'increment') and i < len(response.increment):
                        tick_info["increment"] = response.increment[i]
                    
                    if hasattr(response, 'description') and i < len(response.description):
                        tick_info["description"] = response.description[i]
                    
                    tick_sizes.append(tick_info)
                    
                    # Display tick size entry
                    print(f"{i+1:3d}. Tick Size: {tick_size}")
                    if tick_info["price_from"]:
                        print(f"     Price From: {tick_info['price_from']}")
                    if tick_info["price_to"]:
                        print(f"     Price To: {tick_info['price_to']}")
                    if tick_info["increment"]:
                        print(f"     Increment: {tick_info['increment']}")
                    if tick_info["description"]:
                        print(f"     Description: {tick_info['description']}")
                    print()
            
            # Display minimum tick size information
            if hasattr(response, 'minimum_tick_size') and response.minimum_tick_size:
                print(f"Minimum Tick Size: {response.minimum_tick_size}")
            
            if hasattr(response, 'tick_value') and response.tick_value:
                print(f"Tick Value: {response.tick_value}")
            
            # Display tick size rules
            if hasattr(response, 'tick_size_rule') and response.tick_size_rule:
                print(f"\n📋 TICK SIZE RULES:")
                for i, rule in enumerate(response.tick_size_rule):
                    print(f"{i+1}. {rule}")
            
            # Additional tick size specifications
            additional_fields = [
                'tick_size_type', 'tick_table_version', 'effective_date',
                'currency', 'lot_size', 'contract_multiplier'
            ]
            
            print(f"\n📋 ADDITIONAL TICK SPECIFICATIONS:")
            for field in additional_fields:
                if hasattr(response, field):
                    value = getattr(response, field)
                    if value:
                        field_name = field.replace('_', ' ').title()
                        print(f"{field_name}: {value}")
            
            # CSV output
            if output_format == "csv":
                print(f"\n📊 CSV FORMAT (TICK SIZE TABLE):")
                
                if tick_sizes:
                    # Header
                    csv_headers = ["Index", "Tick_Size", "Price_From", "Price_To", 
                                  "Increment", "Description"]
                    print(",".join(csv_headers))
                    
                    # Data rows
                    for tick in tick_sizes:
                        values = [
                            str(tick["index"]),
                            str(tick["tick_size"]),
                            str(tick["price_from"]),
                            str(tick["price_to"]),
                            str(tick["increment"]),
                            str(tick["description"]).replace(',', ';')  # Escape commas
                        ]
                        print(",".join(values))
                else:
                    print("No tick size data available for CSV output")
            
            # Analysis and interpretation
            print(f"\n💡 TICK SIZE TABLE INTERPRETATION:")
            print(f"This table defines the minimum price increments for {symbol}")
            
            if tick_sizes:
                print(f"Total tick size rules: {len(tick_sizes)}")
                
                # Find the most common tick size
                tick_values = [float(t["tick_size"]) for t in tick_sizes if t["tick_size"]]
                if tick_values:
                    min_tick = min(tick_values)
                    max_tick = max(tick_values)
                    print(f"Tick size range: {min_tick} to {max_tick}")
                    
                    if len(set(tick_values)) == 1:
                        print(f"Uniform tick size: {tick_values[0]}")
                    else:
                        print(f"Variable tick sizes based on price ranges")
                
                # Show trading implications
                print(f"\n📈 TRADING IMPLICATIONS:")
                print(f"• Orders must be placed in multiples of the appropriate tick size")
                print(f"• Tick size determines minimum profit/loss increments")
                print(f"• Different price ranges may have different tick sizes")
        
        else:
            print("❌ Tick size table request failed!")
            if len(response.rp_code) >= 2:
                error_code = response.rp_code[0]
                error_message = response.rp_code[1]
                print(f"   Error Code: {error_code}")
                print(f"   Error Message: {error_message}")
            
            # Suggest troubleshooting steps
            print("\n🔧 TROUBLESHOOTING:")
            print("   • Verify symbol and exchange are correct")
            print("   • Check if tick size table is available for this instrument")
            print("   • Ensure proper market data permissions")
            print("   • Try with a more common symbol (e.g., ESH5)")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing tick size table: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def test_multiple_tick_size_tables(symbols: List[tuple],
                                       output_format: str = "human") -> bool:
    """
    Test tick size tables for multiple symbols.
    
    Args:
        symbols: List of (symbol, exchange) tuples
        output_format: Output format
        
    Returns:
        bool: True if all tests successful
    """
    print("=" * 60)
    print("📏 TESTING MULTIPLE TICK SIZE TABLE REQUESTS")
    print("=" * 60)
    print(f"Symbols: {', '.join([f'{s}@{e}' for s, e in symbols])}")
    print("=" * 60)
    
    results = []
    
    for i, (symbol, exchange) in enumerate(symbols, 1):
        print(f"\n{'='*20} REQUEST {i}/{len(symbols)} {'='*20}")
        success = await test_tick_size_table(symbol, exchange, output_format)
        results.append((f"{symbol}@{exchange}", success))
        
        if i < len(symbols):
            print("\n⏳ Waiting 3 seconds before next request...")
            await asyncio.sleep(3)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 MULTIPLE TICK SIZE TABLE SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for symbol_exchange, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{symbol_exchange:20s}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL TICK SIZE TABLE REQUESTS PASSED!")
    else:
        print("⚠️  SOME TICK SIZE TABLE REQUESTS FAILED!")
    
    return all_passed

async def main():
    """Main function for tick size table testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Tick Size Table endpoint (Templates 107/108)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_tick_size_table.py --symbol ESH5 --exchange CME
  python test_tick_size_table.py --symbol AAPL --exchange NASDAQ
  python test_tick_size_table.py --multiple ESH5,CME NQH5,CME AAPL,NASDAQ
  python test_tick_size_table.py --symbol ESH5 --exchange CME --format csv

Safety:
  This script performs READ-ONLY tick size table requests only.
  Requires authentication but no account modifications performed.
  
Output:
  Displays comprehensive tick size table information including:
  - Tick size values and price ranges
  - Price increment rules
  - Trading specifications
  - Currency and contract details
  - Analysis and trading implications
        """
    )
    
    parser.add_argument(
        "--symbol",
        type=str,
        help="Trading symbol to get tick size table for (e.g., ESH5, AAPL)"
    )
    
    parser.add_argument(
        "--exchange",
        type=str,
        help="Exchange name (e.g., CME, NASDAQ, NYSE)"
    )
    
    parser.add_argument(
        "--multiple",
        nargs="+",
        help="Test multiple symbols in format SYMBOL,EXCHANGE"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.symbol and not args.multiple:
        print("❌ Either --symbol and --exchange, or --multiple must be specified")
        return 1
    
    if args.symbol and not args.exchange:
        print("❌ --exchange is required when using --symbol")
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC TICK SIZE TABLE ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY TICK SIZE TABLE OPERATIONS ONLY")
    print("=" * 50)
    
    # Run the test(s)
    if args.multiple:
        # Parse multiple symbols
        symbols = []
        for item in args.multiple:
            try:
                symbol, exchange = item.split(',')
                symbols.append((symbol.strip(), exchange.strip()))
            except ValueError:
                print(f"❌ Invalid format for {item}. Use SYMBOL,EXCHANGE format.")
                return 1
        
        success = await test_multiple_tick_size_tables(symbols, args.format)
    else:
        success = await test_tick_size_table(args.symbol, args.exchange, args.format)
    
    if success:
        print("\n✅ Tick size table test completed successfully!")
        return 0
    else:
        print("\n❌ Tick size table test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)