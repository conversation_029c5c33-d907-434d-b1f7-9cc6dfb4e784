# DANGEROUS ENDPOINTS - DO NOT TEST

## ⚠️ CRITICAL WARNING ⚠️

**The endpoints listed in this document are STRICTLY FORBIDDEN from testing. These endpoints can:**
- ✅ Create, modify, or cancel orders
- ✅ Execute trades
- ✅ Modify account settings
- ✅ Accept legal agreements
- ✅ Change risk management parameters

**Testing these endpoints, even on paper trading accounts, could result in:**
- ❌ Unintended order creation
- ❌ Position changes
- ❌ Account modifications
- ❌ Legal agreement acceptance
- ❌ Compliance violations

---

## Order Management Endpoints (DANGEROUS)

### New Order Creation
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 312 | New Order Request | Client→Server | 🔴 CRITICAL | **Creates new orders!** Can result in actual trades |
| 313 | New Order Response | Server→Client | 🔴 CRITICAL | Response to order creation |
| 328 | OCO Order Request | Client→Server | 🔴 CRITICAL | **Creates One-Cancels-Other orders!** |
| 329 | OCO Order Response | Server→Client | 🔴 CRITICAL | Response to OCO order creation |
| 330 | Bracket Order Request | Client→Server | 🔴 CRITICAL | **Creates bracket orders with stops/targets!** |
| 331 | Bracket Order Response | Server→Client | 🔴 CRITICAL | Response to bracket order creation |

**Why Dangerous:** These endpoints create actual orders in the market that can be filled, resulting in unintended positions and financial exposure.

### Order Modification
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 314 | Modify Order Request | Client→Server | 🔴 CRITICAL | **Modifies existing orders!** Can change price, quantity, or type |
| 315 | Modify Order Response | Server→Client | 🔴 CRITICAL | Response to order modification |
| 332 | Update Target Bracket Level Request | Client→Server | 🔴 CRITICAL | **Modifies profit target levels!** |
| 333 | Update Target Bracket Level Response | Server→Client | 🔴 CRITICAL | Response to target modification |
| 334 | Update Stop Bracket Level Request | Client→Server | 🔴 CRITICAL | **Modifies stop loss levels!** |
| 335 | Update Stop Bracket Level Response | Server→Client | 🔴 CRITICAL | Response to stop modification |
| 3500 | Modify Order Reference Data Request | Client→Server | 🔴 CRITICAL | **Modifies order reference data!** |
| 3501 | Modify Order Reference Data Response | Server→Client | 🔴 CRITICAL | Response to reference data modification |

**Why Dangerous:** These endpoints can modify existing orders, potentially changing stop losses, profit targets, or order parameters in ways that affect risk management.

### Order Cancellation
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 316 | Cancel Order Request | Client→Server | 🔴 CRITICAL | **Cancels individual orders!** |
| 317 | Cancel Order Response | Server→Client | 🔴 CRITICAL | Response to order cancellation |
| 346 | Cancel All Orders Request | Client→Server | 🔴 CRITICAL | **CANCELS ALL ORDERS!** Extremely dangerous! |
| 347 | Cancel All Orders Response | Server→Client | 🔴 CRITICAL | Response to cancel all orders |

**Why Dangerous:** Canceling orders can remove protective stops or exit strategies, potentially leaving positions unprotected.

### Position Management
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 3504 | Exit Position Request | Client→Server | 🔴 CRITICAL | **Creates market orders to exit positions!** |
| 3505 | Exit Position Response | Server→Client | 🔴 CRITICAL | Response to position exit |

**Why Dangerous:** This endpoint creates market orders to close positions, potentially resulting in immediate trades at unfavorable prices.

### Order Linking
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 344 | Link Orders Request | Client→Server | 🔴 CRITICAL | **Links orders together!** Changes order behavior |
| 345 | Link Orders Response | Server→Client | 🔴 CRITICAL | Response to order linking |

**Why Dangerous:** Linking orders can create dependencies between orders that affect execution and risk management.

---

## Subscription Management (POTENTIALLY DANGEROUS)

### Order Update Subscriptions
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 308 | Subscribe For Order Updates Request | Client→Server | 🟡 MODERATE | **Can trigger unwanted notifications** |
| 309 | Subscribe For Order Updates Response | Server→Client | 🟡 MODERATE | Response to order update subscription |
| 336 | Subscribe To Bracket Updates Request | Client→Server | 🟡 MODERATE | **Can trigger unwanted bracket notifications** |
| 337 | Subscribe To Bracket Updates Response | Server→Client | 🟡 MODERATE | Response to bracket update subscription |

**Why Potentially Dangerous:** While these don't directly modify orders, they can trigger notification flows that might interfere with production systems or cause confusion.

---

## Account Settings (DANGEROUS)

### Agreement Management
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 504 | Accept Agreement Request | Client→Server | 🔴 CRITICAL | **Accepts legal agreements!** |
| 505 | Accept Agreement Response | Server→Client | 🔴 CRITICAL | Response to agreement acceptance |

**Why Dangerous:** Accepting agreements can modify account permissions, enable new features, or create legal obligations.

### Market Data Certification
| Template ID | Name | Direction | Danger Level | Description |
|-------------|------|-----------|--------------|-------------|
| 508 | Set Rithmic MarketData Self Certification Status Request | Client→Server | 🔴 CRITICAL | **Changes account settings!** |
| 509 | Set Rithmic MarketData Self Certification Status Response | Server→Client | 🔴 CRITICAL | Response to certification status change |

**Why Dangerous:** This endpoint modifies account-level market data settings and certifications, which can affect compliance status and data access.

---

## Implementation Safeguards

### Configuration Level Protection
The `shared/config.py` file includes these dangerous templates in the `DANGEROUS_TEMPLATES` dictionary:

```python
DANGEROUS_TEMPLATES = {
    308: "Subscribe For Order Updates Request - Can trigger unwanted notifications",
    312: "New Order Request - CREATES ORDERS!",
    314: "Modify Order Request - MODIFIES ORDERS!",
    316: "Cancel Order Request - CANCELS ORDERS!",
    # ... complete list
}
```

### Runtime Validation
All scripts include validation to prevent accidental use of dangerous endpoints:

```python
def is_dangerous_template(template_id: int) -> bool:
    """Check if a template ID corresponds to a dangerous endpoint."""
    return template_id in DANGEROUS_TEMPLATES
```

### Message Handler Protection
The `message_handler.py` module automatically flags dangerous messages:

```python
is_dangerous = is_dangerous_template(template_id)
if is_dangerous:
    logger.warning(f"⚠️  DANGEROUS endpoint detected: {template_id}")
```

---

## Testing Guidelines

### ✅ SAFE for Testing (Read-Only Operations):
- System information and discovery
- Market data subscriptions and queries
- Reference data requests
- Account information retrieval (without modification)
- Order history viewing
- Position and P&L inquiries
- Historical data requests
- Agreement viewing (without acceptance)

### ❌ NEVER Test (Account-Modifying Operations):
- Any order creation, modification, or cancellation
- Position exit requests
- Account setting changes
- Agreement acceptance
- Risk management parameter modifications

### 🟡 Use Caution (Notification Subscriptions):
- Order update subscriptions (can cause notification floods)
- Bracket update subscriptions
- Real-time position updates (high frequency)

---

## Emergency Procedures

### If You Accidentally Execute a Dangerous Endpoint:

1. **IMMEDIATELY STOP** the script
2. **Log into your trading platform** to check for unintended orders
3. **Cancel any unwanted orders** manually through the platform
4. **Contact your broker** if there are position or account issues
5. **Document the incident** for review and prevention

### Prevention Checklist:

- [ ] Always use paper trading accounts for testing
- [ ] Never modify the `DANGEROUS_TEMPLATES` list in config.py
- [ ] Always review script code before execution
- [ ] Use read-only endpoints only
- [ ] Monitor logs for dangerous endpoint warnings
- [ ] Keep trading platform open during testing for monitoring

---

## Compliance Notes

### Legal Considerations:
- Testing these endpoints may violate terms of service
- Even paper trading accounts may have restrictions
- Some endpoints may trigger compliance alerts
- Legal agreements should only be accepted deliberately

### Best Practices:
- Always coordinate with compliance before testing order-related endpoints
- Use dedicated testing accounts separate from production
- Maintain audit trails of all testing activities
- Follow your organization's API testing policies

---

**Remember: When in doubt, don't test it. It's better to be safe than sorry when dealing with financial systems.**