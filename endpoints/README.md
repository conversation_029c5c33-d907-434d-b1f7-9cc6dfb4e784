# Rithmic API Endpoint Testing Framework

## ⚠️ CRITICAL SAFETY WARNING ⚠️

**This testing framework is designed EXCLUSIVELY for READ-ONLY operations on PAPER TRADING accounts only.**

### STRICTLY FORBIDDEN OPERATIONS:
- ❌ **Order Creation** (Templates 312, 328, 330)
- ❌ **Order Modification** (Templates 314, 332, 334, 3500)
- ❌ **Order Cancellation** (Templates 316, 346)
- ❌ **Position Exit** (Template 3504)
- ❌ **Account Settings Changes** (Template 508)
- ❌ **Agreement Acceptance** (Template 504)

**See [DANGEROUS_ENDPOINTS.md](DANGEROUS_ENDPOINTS.md) for complete list of forbidden endpoints.**

---

## Overview

This comprehensive testing framework provides scripts to demonstrate and test all **safe, read-only** Rithmic API endpoints across all infrastructure plants. The framework uses the paper trading credentials specified in the project's CLAUDE.md file.

## Framework Structure

```
endpoints/
├── README.md                     # This file - usage and safety guide
├── DANGEROUS_ENDPOINTS.md        # Complete list of forbidden endpoints
├── shared/                       # Common utilities
│   ├── config.py                 # Configuration and safety settings
│   ├── connection.py             # WebSocket connection management  
│   ├── auth.py                   # Authentication handling
│   └── message_handler.py        # Message parsing and formatting
├── system_discovery/             # System information and discovery
├── market_data/                  # Market data subscriptions and queries
├── account_info/                 # Account information (read-only)
├── historical_data/              # Historical time series data
├── pnl_data/                     # Position and P&L information
└── repository/                   # Repository data (read-only)
```

## Quick Start

### Prerequisites

Ensure you have the required dependencies:
```bash
pip install websockets protobuf
```

### Basic Usage

1. **Test System Discovery:**
   ```bash
   cd endpoints/system_discovery/
   python test_system_info.py
   ```

2. **Test Market Data:**
   ```bash
   cd endpoints/market_data/
   python test_market_data_subscription.py --symbol ESH5 --exchange CME
   ```

3. **Test Account Information:**
   ```bash
   cd endpoints/account_info/
   python test_account_list.py
   ```

### Configuration

All scripts use the paper trading credentials from the project's CLAUDE.md:
- **Username:** PP-013155
- **Password:** b7neA8k6JA
- **System:** Rithmic Paper Trading
- **Gateway:** Chicago Area

## Infrastructure Plants

The Rithmic API is organized into separate infrastructure plants:

### 1. Ticker Plant (Market Data)
**Purpose:** Real-time market data, symbol information, reference data  
**Login Required:** Yes (infra_type = 1)  
**Templates:** 100-163  

**Available Scripts:**
- `test_market_data_subscription.py` - Subscribe to real-time data
- `test_symbol_search.py` - Search for symbols
- `test_reference_data.py` - Get instrument reference data
- `test_depth_by_order.py` - Level 3 order book data
- `test_front_month_contract.py` - Front month contract information

### 2. Order Plant (Account Information - READ-ONLY)
**Purpose:** Account data, order history, RMS information  
**Login Required:** Yes (infra_type = 2)  
**Templates:** 300-349, 3500-3509 (SAFE ones only)  

**Available Scripts:**
- `test_account_list.py` - List available accounts
- `test_account_rms.py` - Account risk management settings
- `test_order_history.py` - Historical order information
- `test_trade_routes.py` - Available trade routes

### 3. History Plant
**Purpose:** Historical time series data  
**Login Required:** Yes (infra_type = 3)  
**Templates:** 200-251  

**Available Scripts:**
- `test_time_bars.py` - Historical time bar data
- `test_tick_bars.py` - Historical tick bar data
- `test_volume_profile.py` - Volume profile data

### 4. PnL Plant
**Purpose:** Position and profit/loss information  
**Login Required:** Yes (infra_type = 4)  
**Templates:** 400-451  

**Available Scripts:**
- `test_pnl_position_snapshot.py` - Current position snapshot
- `test_pnl_position_updates.py` - Real-time position updates

### 5. Repository Plant (READ-ONLY)
**Purpose:** User agreements and permissions  
**Login Required:** Yes (infra_type = 5)  
**Templates:** 500-509 (SAFE ones only)  

**Available Scripts:**
- `test_list_agreements.py` - List user agreements
- `test_show_agreement.py` - View agreement details

## Safety Features

### Built-in Safety Mechanisms:
1. **Paper Trading Only:** All scripts default to paper trading credentials
2. **Template Validation:** Dangerous templates are blocked at the configuration level
3. **Confirmation Prompts:** Any potentially risky operations require explicit confirmation
4. **Comprehensive Logging:** All API interactions are logged for audit
5. **Response Validation:** All responses are checked for success/error codes

### Configuration Safety:
```python
# From shared/config.py
paper_trading_only: bool = True      # Enforced
require_confirmation: bool = True    # Enabled for safety
log_all_messages: bool = True       # Full audit trail
```

## Output Formats

All scripts support multiple output formats:

### Human-Readable (Default)
```bash
python test_account_list.py --format human
```

### JSON (Programmatic)
```bash
python test_account_list.py --format json
```

### CSV (Data Analysis)
```bash
python test_account_list.py --format csv
```

## Common Command-Line Options

Most scripts support these common options:

```bash
--format {human,json,csv}    # Output format
--save-responses            # Save responses to files
--log-level {DEBUG,INFO,WARN,ERROR}  # Logging level
--timeout SECONDS           # Message timeout
--system-name NAME          # Override system name
--help                      # Show help information
```

## Error Handling

All scripts include comprehensive error handling:

1. **Connection Failures:** Automatic retry with exponential backoff
2. **Authentication Errors:** Clear error messages with troubleshooting tips
3. **API Errors:** Detailed error code and message display
4. **Timeout Handling:** Configurable timeouts with graceful failure
5. **SSL Issues:** Certificate validation and troubleshooting

## Performance Monitoring

Scripts automatically track and report:
- **Connection Time:** Time to establish WebSocket connection
- **Authentication Time:** Time to complete login process
- **Response Time:** Time to receive API responses
- **Message Throughput:** Messages per second for streaming data
- **Error Rates:** Success/failure statistics

## Troubleshooting

### Common Issues:

1. **Connection Refused:**
   - Check network connectivity
   - Verify SSL certificate path
   - Try different system/gateway

2. **Authentication Failed:**
   - Verify paper trading credentials in CLAUDE.md
   - Check system name spelling
   - Ensure account is active

3. **Permission Denied:**
   - Verify account has required permissions
   - Check exchange permissions
   - Try different infrastructure plant

4. **SSL Certificate Issues:**
   - Verify certificate file exists: `etc/rithmic_ssl_cert_auth_params`
   - Check file permissions
   - Update certificate if expired

### Debug Mode:
```bash
python test_script.py --log-level DEBUG
```

## Data Storage

### Response Saving:
When enabled, all API responses are saved to:
```
endpoint_responses/
├── 20250702_143052_123_System_Info_Response_17.json
├── 20250702_143053_456_Login_Response_11.json
└── ...
```

### Log Files:
Detailed execution logs are written to:
```
logs/
├── endpoint_testing_20250702.log
└── ...
```

## Example Workflows

### 1. Basic System Discovery:
```bash
cd endpoints/system_discovery/
python test_system_info.py          # List available systems
python test_gateway_info.py         # Get gateway information
python test_heartbeat.py            # Test heartbeat mechanism
```

### 2. Market Data Exploration:
```bash
cd endpoints/market_data/
python test_symbol_search.py --pattern "ES*"
python test_front_month_contract.py --symbol ES
python test_market_data_subscription.py --symbol ESH5 --exchange CME
```

### 3. Account Analysis:
```bash
cd endpoints/account_info/
python test_account_list.py
python test_account_rms.py --account PP-013155
python test_order_history.py --account PP-013155 --days 7
```

### 4. Historical Data Analysis:
```bash
cd endpoints/historical_data/
python test_time_bars.py --symbol ESH5 --exchange CME --interval 1m --days 1
python test_tick_bars.py --symbol ESH5 --exchange CME --count 1000
```

## Support and Documentation

- **API Reference:** `docs/developer-guide/api-reference.md`
- **Sample Code:** `samples/samples.py/`
- **Web Dashboard:** `web_dashboard/`
- **Project Documentation:** `README.md`

## Legal and Compliance

This testing framework is provided for:
- ✅ Educational purposes
- ✅ API exploration and learning
- ✅ Paper trading and simulation
- ✅ Read-only data analysis

**NOT for:**
- ❌ Live trading without explicit authorization
- ❌ Production order management
- ❌ Account modifications
- ❌ Automated trading systems

**Always comply with your broker's terms of service and applicable regulations.**