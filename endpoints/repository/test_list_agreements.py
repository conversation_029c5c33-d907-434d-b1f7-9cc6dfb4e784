#!/usr/bin/env python3

"""
Test Rithmic List Agreements endpoints (Templates 500/501, 502/503).

This script demonstrates how to retrieve user agreement information
from the Rithmic API, including both accepted and unaccepted agreements.

SAFETY: This script only performs READ-ONLY agreement listing operations.
No agreement acceptance or account modifications are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_list_unaccepted_agreements_pb2
import response_list_unaccepted_agreements_pb2
import request_list_accepted_agreements_pb2
import response_list_accepted_agreements_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message
from repository_utils import create_repository_response_handler

logger = logging.getLogger(__name__)

async def test_list_unaccepted_agreements(output_format: str = "human") -> tuple[bool, List[Dict]]:
    """
    Test listing unaccepted agreements.
    
    Args:
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        tuple: (success, list of unaccepted agreements)
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("\n" + "=" * 60)
    print("📋 TESTING UNACCEPTED AGREEMENTS LIST")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.LIST_UNACCEPTED_AGREEMENTS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.LIST_UNACCEPTED_AGREEMENTS_RESPONSE} (Response)")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Repository Plant
        print("🔐 Establishing connection to Repository Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.REPOSITORY_PLANT, "unaccepted_agreements_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False, []
        
        print("✅ Authentication successful")
        
        # Create unaccepted agreements list request
        request = request_list_unaccepted_agreements_pb2.RequestListUnacceptedAgreements()
        request.template_id = TemplateIDs.LIST_UNACCEPTED_AGREEMENTS_REQUEST
        request.user_msg.append("List unaccepted agreements request")
        
        # Send request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending unaccepted agreements list request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send request")
            return False, []
        
        # Wait for response
        print("⏳ Waiting for unaccepted agreements response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False, []
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_list_unaccepted_agreements_pb2.ResponseListUnacceptedAgreements()
        response.ParseFromString(response_bytes)
        
        # Analyze response using intelligent Repository Plant handler
        config_obj = get_config()
        repository_handler = create_repository_response_handler(config_obj)
        response_result = repository_handler.interpret_response(list(response.rp_code), "List Unaccepted Agreements")
        
        print(f"Response Code: {list(response.rp_code)}")
        
        # Display intelligent status interpretation
        repository_handler.display_repository_status(response_result, "Unaccepted Agreements List")
        
        success = response_result["success"]
        
        agreements = []
        
        if success:
            # Extract agreement information only if we have actual data
            if hasattr(response, 'agreement_name') and response.agreement_name:
                print(f"\n⚠️  UNACCEPTED AGREEMENTS ({len(response.agreement_name)}):")
                print("-" * 60)
                
                for i, agreement_name in enumerate(response.agreement_name):
                    agreement = {
                        "name": agreement_name,
                        "type": "unaccepted",
                        "description": "",
                        "version": ""
                    }
                    
                    # Get additional fields if available
                    if hasattr(response, 'agreement_text') and i < len(response.agreement_text):
                        agreement["description"] = response.agreement_text[i][:100] + "..." if len(response.agreement_text[i]) > 100 else response.agreement_text[i]
                    
                    if hasattr(response, 'agreement_version') and i < len(response.agreement_version):
                        agreement["version"] = response.agreement_version[i]
                    
                    agreements.append(agreement)
                    
                    # Display agreement info
                    if output_format == "human":
                        print(f"{i+1:2d}. {agreement_name}")
                        if agreement["version"]:
                            print(f"    Version: {agreement['version']}")
                        if agreement["description"]:
                            print(f"    Description: {agreement['description']}")
                        print()
            else:
                # No agreements found - this is handled by the repository handler
                if not response_result.get("paper_trading_limitation"):
                    print("\n✅ No unaccepted agreements found")
                    print("   All required agreements have been accepted")
        
        # Cleanup
        await authenticator.logout()
        await connection.disconnect()
        
        return success, agreements
        
    except Exception as e:
        logger.error(f"Error testing unaccepted agreements list: {e}")
        print(f"❌ Test failed: {e}")
        return False, []

async def test_list_accepted_agreements(output_format: str = "human") -> tuple[bool, List[Dict]]:
    """
    Test listing accepted agreements.
    
    Args:
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        tuple: (success, list of accepted agreements)
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("\n" + "=" * 60)
    print("✅ TESTING ACCEPTED AGREEMENTS LIST")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.LIST_ACCEPTED_AGREEMENTS_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.LIST_ACCEPTED_AGREEMENTS_RESPONSE} (Response)")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Repository Plant
        print("🔐 Establishing connection to Repository Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.REPOSITORY_PLANT, "accepted_agreements_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False, []
        
        print("✅ Authentication successful")
        
        # Create accepted agreements list request
        request = request_list_accepted_agreements_pb2.RequestListAcceptedAgreements()
        request.template_id = TemplateIDs.LIST_ACCEPTED_AGREEMENTS_REQUEST
        request.user_msg.append("List accepted agreements request")
        
        # Send request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending accepted agreements list request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send request")
            return False, []
        
        # Wait for response
        print("⏳ Waiting for accepted agreements response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False, []
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_list_accepted_agreements_pb2.ResponseListAcceptedAgreements()
        response.ParseFromString(response_bytes)
        
        # Analyze response using intelligent Repository Plant handler
        config_obj = get_config()
        repository_handler = create_repository_response_handler(config_obj)
        response_result = repository_handler.interpret_response(list(response.rp_code), "List Accepted Agreements")
        
        print(f"Response Code: {list(response.rp_code)}")
        
        # Display intelligent status interpretation
        repository_handler.display_repository_status(response_result, "Accepted Agreements List")
        
        success = response_result["success"]
        
        agreements = []
        
        if success:
            # Extract agreement information only if we have actual data
            if hasattr(response, 'agreement_name') and response.agreement_name:
                print(f"\n📜 ACCEPTED AGREEMENTS ({len(response.agreement_name)}):")
                print("-" * 60)
                
                for i, agreement_name in enumerate(response.agreement_name):
                    agreement = {
                        "name": agreement_name,
                        "type": "accepted",
                        "description": "",
                        "version": "",
                        "acceptance_date": ""
                    }
                    
                    # Get additional fields if available
                    if hasattr(response, 'agreement_text') and i < len(response.agreement_text):
                        agreement["description"] = response.agreement_text[i][:100] + "..." if len(response.agreement_text[i]) > 100 else response.agreement_text[i]
                    
                    if hasattr(response, 'agreement_version') and i < len(response.agreement_version):
                        agreement["version"] = response.agreement_version[i]
                    
                    if hasattr(response, 'acceptance_date') and i < len(response.acceptance_date):
                        agreement["acceptance_date"] = response.acceptance_date[i]
                    
                    agreements.append(agreement)
                    
                    # Display agreement info
                    if output_format == "human":
                        print(f"{i+1:2d}. {agreement_name}")
                        if agreement["version"]:
                            print(f"    Version: {agreement['version']}")
                        if agreement["acceptance_date"]:
                            print(f"    Accepted: {agreement['acceptance_date']}")
                        if agreement["description"]:
                            print(f"    Description: {agreement['description']}")
                        print()
            else:
                # No agreements found - this is handled by the repository handler
                if not response_result.get("paper_trading_limitation"):
                    print("\n📋 No accepted agreements found")
                    print("   No agreements have been accepted yet")
        
        # Cleanup
        await authenticator.logout()
        await connection.disconnect()
        
        return success, agreements
        
    except Exception as e:
        logger.error(f"Error testing accepted agreements list: {e}")
        print(f"❌ Test failed: {e}")
        return False, []

async def test_list_all_agreements(output_format: str = "human") -> bool:
    """
    Test listing both accepted and unaccepted agreements.
    
    Args:
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if both tests successful
    """
    config_obj = get_config()
    
    print("=" * 60)
    print("📜 TESTING AGREEMENT LISTING ENDPOINTS")
    print("=" * 60)
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    # Test unaccepted agreements
    unaccepted_success, unaccepted_agreements = await test_list_unaccepted_agreements(output_format)
    
    # Wait between requests
    print("\n⏳ Waiting 3 seconds before next request...")
    await asyncio.sleep(3)
    
    # Test accepted agreements
    accepted_success, accepted_agreements = await test_list_accepted_agreements(output_format)
    
    # Combine results and display summary
    all_agreements = unaccepted_agreements + accepted_agreements
    
    print("\n" + "=" * 60)
    print("📋 AGREEMENT SUMMARY")
    print("=" * 60)
    
    if unaccepted_success or accepted_success:
        print(f"Total Agreements: {len(all_agreements)}")
        print(f"Accepted: {len(accepted_agreements)}")
        print(f"Unaccepted: {len(unaccepted_agreements)}")
        
        if unaccepted_agreements:
            print(f"\n⚠️  ATTENTION: {len(unaccepted_agreements)} agreements require acceptance:")
            for agreement in unaccepted_agreements:
                print(f"   • {agreement['name']}")
            print("\n   ⚠️  These agreements must be accepted to enable full functionality")
        
        if accepted_agreements:
            print(f"\n✅ {len(accepted_agreements)} agreements are already accepted")
        
        # Display in requested format
        if output_format == "csv" and all_agreements:
            print(f"\n📊 CSV FORMAT:")
            print("Name,Type,Version,Description")
            for agreement in all_agreements:
                description = agreement.get('description', '').replace(',', ';')
                print(f"{agreement['name']},{agreement['type']},{agreement.get('version', '')},{description}")
        
        elif output_format == "json" and all_agreements:
            import json
            print(f"\n📊 JSON FORMAT:")
            print(json.dumps(all_agreements, indent=2))
    
    else:
        print("❌ Failed to retrieve agreement information")
        print("\n🔧 TROUBLESHOOTING:")
        print("   • Verify credentials are correct")
        print("   • Check Repository Plant connectivity")
        print("   • Ensure account has proper permissions")
    
    return unaccepted_success and accepted_success

async def main():
    """Main function for agreement listing testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic List Agreements endpoints (Templates 500/501, 502/503)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_list_agreements.py
  python test_list_agreements.py --format json
  python test_list_agreements.py --format csv > agreements.csv
  python test_list_agreements.py --accepted-only
  python test_list_agreements.py --unaccepted-only

Safety:
  This script performs READ-ONLY agreement listing operations only.
  Requires authentication but no agreement acceptance performed.
  NO AGREEMENTS WILL BE AUTOMATICALLY ACCEPTED.
  
Output:
  The script will display:
  - List of accepted agreements with acceptance dates
  - List of unaccepted agreements requiring attention
  - Summary statistics and compliance status
        """
    )
    
    parser.add_argument(
        "--accepted-only",
        action="store_true",
        help="Test accepted agreements only"
    )
    
    parser.add_argument(
        "--unaccepted-only", 
        action="store_true",
        help="Test unaccepted agreements only"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC AGREEMENT LISTING ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY AGREEMENT LISTING ONLY")
    print("⚠️  NO AGREEMENTS WILL BE ACCEPTED")
    print("=" * 50)
    
    # Run the appropriate test(s)
    if args.accepted_only:
        success, _ = await test_list_accepted_agreements(args.format)
    elif args.unaccepted_only:
        success, _ = await test_list_unaccepted_agreements(args.format)
    else:
        success = await test_list_all_agreements(args.format)
    
    if success:
        print("\n✅ Agreement listing test completed successfully!")
        return 0
    else:
        print("\n❌ Agreement listing test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)