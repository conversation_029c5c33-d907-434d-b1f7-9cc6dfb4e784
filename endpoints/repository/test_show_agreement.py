#!/usr/bin/env python3

"""
Test Rithmic Show Agreement endpoints (Templates 506/507).

This script demonstrates how to retrieve agreement details from the Rithmic API,
showing legal agreements and their terms with comprehensive analysis.

SAFETY: This script only performs READ-ONLY agreement viewing requests.
No account modifications or agreement acceptances are performed.
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add the required paths
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(os.path.join(project_root, 'proto_generated'))
sys.path.append(os.path.join(project_root, 'endpoints', 'shared'))

import request_show_agreement_pb2
import response_show_agreement_pb2

import config
from config import get_config, TemplateIDs, InfraType, validate_config
from auth import create_authenticated_connection
import message_handler
from message_handler import get_message_handler, parse_message
from repository_utils import create_repository_response_handler

logger = logging.getLogger(__name__)

class AgreementAnalyzer:
    """Analyzes agreement data and provides comprehensive compliance insights."""
    
    def __init__(self):
        self.agreements = []
        self.agreement_types = set()
        self.agreement_versions = {}
        self.compliance_requirements = []
        self.risk_disclosures = []
        self.trading_permissions = []
        self.liability_terms = []
        self.data_usage_terms = []
        self.termination_clauses = []
        self.update_history = []
        self.total_sections = 0
        self.total_characters = 0
        self.keyword_analysis = {}
    
    def add_agreement(self, agreement_info: Dict[str, Any]):
        """Add an agreement to the analysis."""
        self.agreements.append(agreement_info)
        
        # Track agreement types and versions
        if agreement_info['agreement_type']:
            self.agreement_types.add(agreement_info['agreement_type'])
            
            # Track versions per type
            agreement_type = agreement_info['agreement_type']
            if agreement_type not in self.agreement_versions:
                self.agreement_versions[agreement_type] = []
            
            if agreement_info['version']:
                self.agreement_versions[agreement_type].append(agreement_info['version'])
        
        # Analyze agreement content
        if agreement_info['agreement_text']:
            self._analyze_agreement_content(agreement_info)
        
        # Track metadata
        if agreement_info['sections']:
            self.total_sections += len(agreement_info['sections'])
        
        if agreement_info['character_count']:
            self.total_characters += agreement_info['character_count']
        
        # Track update history
        if agreement_info['last_updated']:
            self.update_history.append({
                'agreement_id': agreement_info['agreement_id'],
                'agreement_type': agreement_info['agreement_type'],
                'last_updated': agreement_info['last_updated'],
                'version': agreement_info['version']
            })
    
    def _analyze_agreement_content(self, agreement_info: Dict[str, Any]):
        """Analyze agreement content for key terms and clauses."""
        text = agreement_info['agreement_text'].lower()
        
        # Define key terms to search for
        compliance_keywords = [
            'compliance', 'regulation', 'regulatory', 'finra', 'sec', 'cftc',
            'audit', 'examination', 'reporting', 'disclosure'
        ]
        
        risk_keywords = [
            'risk', 'loss', 'liability', 'disclaimer', 'warning',
            'volatile', 'market risk', 'credit risk', 'operational risk'
        ]
        
        trading_keywords = [
            'trading', 'execution', 'order', 'position', 'margin',
            'leverage', 'settlement', 'delivery', 'exercise'
        ]
        
        data_keywords = [
            'data', 'information', 'confidential', 'privacy', 'usage',
            'distribution', 'redistribution', 'market data', 'real-time'
        ]
        
        termination_keywords = [
            'terminate', 'termination', 'cancel', 'suspension', 'breach',
            'default', 'notice', 'effective date'
        ]
        
        # Count keyword occurrences
        for keyword in compliance_keywords:
            if keyword in text:
                self.compliance_requirements.append({
                    'agreement_id': agreement_info['agreement_id'],
                    'keyword': keyword,
                    'occurrences': text.count(keyword)
                })
        
        for keyword in risk_keywords:
            if keyword in text:
                self.risk_disclosures.append({
                    'agreement_id': agreement_info['agreement_id'],
                    'keyword': keyword,
                    'occurrences': text.count(keyword)
                })
        
        for keyword in trading_keywords:
            if keyword in text:
                self.trading_permissions.append({
                    'agreement_id': agreement_info['agreement_id'],
                    'keyword': keyword,
                    'occurrences': text.count(keyword)
                })
        
        for keyword in data_keywords:
            if keyword in text:
                self.data_usage_terms.append({
                    'agreement_id': agreement_info['agreement_id'],
                    'keyword': keyword,
                    'occurrences': text.count(keyword)
                })
        
        for keyword in termination_keywords:
            if keyword in text:
                self.termination_clauses.append({
                    'agreement_id': agreement_info['agreement_id'],
                    'keyword': keyword,
                    'occurrences': text.count(keyword)
                })
    
    def get_analysis(self) -> Dict[str, Any]:
        """Get comprehensive analysis of agreement data."""
        analysis = {
            'total_agreements': len(self.agreements),
            'agreement_types': list(self.agreement_types),
            'content_analysis': None,
            'compliance_analysis': None,
            'risk_analysis': None,
            'trading_analysis': None,
            'data_usage_analysis': None,
            'version_analysis': None,
            'agreement_insights': []
        }
        
        # Content analysis
        analysis['content_analysis'] = {
            'total_sections': self.total_sections,
            'total_characters': self.total_characters,
            'average_sections_per_agreement': self.total_sections / len(self.agreements) if self.agreements else 0,
            'average_characters_per_agreement': self.total_characters / len(self.agreements) if self.agreements else 0
        }
        
        # Compliance analysis
        compliance_count = len(self.compliance_requirements)
        unique_compliance_terms = len(set(cr['keyword'] for cr in self.compliance_requirements))
        
        analysis['compliance_analysis'] = {
            'total_compliance_references': compliance_count,
            'unique_compliance_terms': unique_compliance_terms,
            'compliance_density': compliance_count / len(self.agreements) if self.agreements else 0,
            'top_compliance_terms': self._get_top_terms(self.compliance_requirements)
        }
        
        # Risk analysis
        risk_count = len(self.risk_disclosures)
        unique_risk_terms = len(set(rd['keyword'] for rd in self.risk_disclosures))
        
        analysis['risk_analysis'] = {
            'total_risk_references': risk_count,
            'unique_risk_terms': unique_risk_terms,
            'risk_density': risk_count / len(self.agreements) if self.agreements else 0,
            'top_risk_terms': self._get_top_terms(self.risk_disclosures),
            'risk_coverage_assessment': self._assess_risk_coverage()
        }
        
        # Trading analysis
        trading_count = len(self.trading_permissions)
        unique_trading_terms = len(set(tp['keyword'] for tp in self.trading_permissions))
        
        analysis['trading_analysis'] = {
            'total_trading_references': trading_count,
            'unique_trading_terms': unique_trading_terms,
            'trading_density': trading_count / len(self.agreements) if self.agreements else 0,
            'top_trading_terms': self._get_top_terms(self.trading_permissions)
        }
        
        # Data usage analysis
        data_count = len(self.data_usage_terms)
        unique_data_terms = len(set(dut['keyword'] for dut in self.data_usage_terms))
        
        analysis['data_usage_analysis'] = {
            'total_data_references': data_count,
            'unique_data_terms': unique_data_terms,
            'data_density': data_count / len(self.agreements) if self.agreements else 0,
            'top_data_terms': self._get_top_terms(self.data_usage_terms)
        }
        
        # Version analysis
        analysis['version_analysis'] = {
            'agreement_types_count': len(self.agreement_types),
            'versions_per_type': {k: len(set(v)) for k, v in self.agreement_versions.items()},
            'most_recent_updates': self._get_recent_updates(),
            'version_compliance': self._assess_version_compliance()
        }
        
        # Generate insights
        analysis['agreement_insights'] = self._generate_agreement_insights(analysis)
        
        return analysis
    
    def _get_top_terms(self, term_list: List[Dict[str, Any]], limit: int = 5) -> List[Dict[str, Any]]:
        """Get top terms by total occurrences."""
        term_counts = {}
        for item in term_list:
            keyword = item['keyword']
            if keyword not in term_counts:
                term_counts[keyword] = 0
            term_counts[keyword] += item['occurrences']
        
        sorted_terms = sorted(term_counts.items(), key=lambda x: x[1], reverse=True)
        return [{'term': term, 'total_occurrences': count} for term, count in sorted_terms[:limit]]
    
    def _assess_risk_coverage(self) -> str:
        """Assess the comprehensiveness of risk disclosures."""
        essential_risks = ['market risk', 'credit risk', 'operational risk', 'liquidity risk', 'volatility']
        covered_risks = set(rd['keyword'] for rd in self.risk_disclosures)
        
        coverage_count = sum(1 for risk in essential_risks if any(risk in covered for covered in covered_risks))
        coverage_pct = (coverage_count / len(essential_risks)) * 100
        
        if coverage_pct >= 80:
            return "Comprehensive"
        elif coverage_pct >= 60:
            return "Good"
        elif coverage_pct >= 40:
            return "Adequate"
        else:
            return "Limited"
    
    def _get_recent_updates(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get most recent agreement updates."""
        sorted_updates = sorted(self.update_history, 
                               key=lambda x: x['last_updated'], 
                               reverse=True)
        return sorted_updates[:limit]
    
    def _assess_version_compliance(self) -> str:
        """Assess version management compliance."""
        if not self.agreement_versions:
            return "No version data"
        
        # Check if all agreement types have proper versioning
        properly_versioned = sum(1 for versions in self.agreement_versions.values() if len(versions) > 0)
        total_types = len(self.agreement_versions)
        
        if properly_versioned == total_types:
            return "Fully Compliant"
        elif properly_versioned >= total_types * 0.8:
            return "Mostly Compliant"
        else:
            return "Non-Compliant"
    
    def _generate_agreement_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate insights from agreement analysis."""
        insights = []
        
        if analysis['total_agreements'] > 0:
            insights.append(f"Analyzed {analysis['total_agreements']} legal agreements")
            
            if analysis['content_analysis']:
                ca = analysis['content_analysis']
                if ca['total_sections'] > 50:
                    insights.append("Comprehensive agreement structure with detailed sections")
                
                avg_chars = ca['average_characters_per_agreement']
                if avg_chars > 10000:
                    insights.append("Detailed agreements with extensive terms and conditions")
                elif avg_chars < 2000:
                    insights.append("Concise agreements with essential terms only")
            
            if analysis['compliance_analysis']:
                comp = analysis['compliance_analysis']
                if comp['compliance_density'] > 5:
                    insights.append("High regulatory compliance focus")
                
                if comp['unique_compliance_terms'] > 10:
                    insights.append("Comprehensive regulatory coverage")
            
            if analysis['risk_analysis']:
                risk = analysis['risk_analysis']
                coverage = risk['risk_coverage_assessment']
                if coverage == 'Comprehensive':
                    insights.append("Excellent risk disclosure coverage")
                elif coverage == 'Limited':
                    insights.append("Limited risk disclosure - review may be needed")
                
                if risk['risk_density'] > 10:
                    insights.append("Extensive risk warnings and disclosures")
            
            if analysis['trading_analysis']:
                trading = analysis['trading_analysis']
                if trading['trading_density'] > 8:
                    insights.append("Detailed trading terms and conditions")
            
            if analysis['data_usage_analysis']:
                data = analysis['data_usage_analysis']
                if data['data_density'] > 5:
                    insights.append("Comprehensive data usage and privacy terms")
            
            if analysis['version_analysis']:
                version = analysis['version_analysis']
                compliance = version['version_compliance']
                if compliance == 'Fully Compliant':
                    insights.append("Proper version management across all agreement types")
                elif compliance == 'Non-Compliant':
                    insights.append("Version management needs improvement")
        
        return insights

async def test_show_agreement(agreement_id: Optional[str] = None, 
                             agreement_type: Optional[str] = None,
                             output_format: str = "human") -> bool:
    """
    Test show agreement functionality.
    
    Args:
        agreement_id: Optional specific agreement ID to retrieve
        agreement_type: Optional agreement type filter
        output_format: Output format ("human", "json", "csv")
        
    Returns:
        bool: True if test successful
    """
    config_obj = get_config()
    message_handler_obj = get_message_handler()
    
    print("=" * 60)
    print("📜 TESTING SHOW AGREEMENT ENDPOINT")
    print("=" * 60)
    print(f"Template ID: {TemplateIDs.SHOW_AGREEMENT_REQUEST} (Request)")
    print(f"Template ID: {TemplateIDs.SHOW_AGREEMENT_RESPONSE} (Response)")
    print(f"Agreement ID: {agreement_id or 'All agreements'}")
    print(f"Agreement Type: {agreement_type or 'All types'}")
    print(f"Paper Trading: {'✅ ENABLED' if config_obj.paper_trading_only else '❌ DISABLED'}")
    print("=" * 60)
    
    try:
        # Create authenticated connection to Repository Plant
        print("🔐 Establishing connection to Repository Plant...")
        connection, authenticator = await create_authenticated_connection(
            InfraType.REPOSITORY_PLANT, "show_agreement_test")
        
        if not connection or not authenticator:
            print("❌ Failed to establish authenticated connection")
            return False
        
        print("✅ Authentication successful")
        
        # Create show agreement request
        request = request_show_agreement_pb2.RequestShowAgreement()
        request.template_id = TemplateIDs.SHOW_AGREEMENT_REQUEST
        request.user_msg.append("Show agreement request from endpoint tester")
        
        # Set filters if provided
        if agreement_id:
            request.agreement_id = agreement_id
        
        if agreement_type:
            request.agreement_type = agreement_type
        
        # Send show agreement request
        serialized_request = request.SerializeToString()
        print(f"📤 Sending show agreement request ({len(serialized_request)} bytes)")
        
        if not await connection.send_message(serialized_request):
            print("❌ Failed to send show agreement request")
            return False
        
        # Parse and log the request
        request_info = parse_message(serialized_request)
        if request_info and output_format != "human":
            print("\n📋 REQUEST DETAILS:")
            print(message_handler_obj.format_message(request_info, output_format))
        
        # Wait for response
        print("⏳ Waiting for show agreement response...")
        response_bytes = await connection.receive_message(timeout=config_obj.message_timeout)
        
        if not response_bytes:
            print("❌ No response received within timeout")
            return False
        
        print(f"📥 Received response ({len(response_bytes)} bytes)")
        
        # Parse response
        response = response_show_agreement_pb2.ResponseShowAgreement()
        response.ParseFromString(response_bytes)
        
        # Parse and format the response
        response_info = parse_message(response_bytes)
        if response_info and output_format != "human":
            print("\n📋 RESPONSE DETAILS:")
            print(message_handler_obj.format_message(response_info, output_format))
        
        # Analyze response using intelligent Repository Plant handler
        config_obj = get_config()
        repository_handler = create_repository_response_handler(config_obj)
        response_result = repository_handler.interpret_response(list(response.rp_code), "Show Agreement")
        
        # Analyze response content
        print("\n📊 SHOW AGREEMENT ANALYSIS:")
        print(f"Template ID: {response.template_id}")
        print(f"User Messages: {list(response.user_msg)}")
        print(f"Response Code: {list(response.rp_code)}")
        
        # Display intelligent status interpretation
        repository_handler.display_repository_status(response_result, "Show Agreement")
        
        success = response_result["success"]
        
        if success:
            print("✅ Show agreement request successful!")
            
            # Create analyzer for agreement data
            analyzer = AgreementAnalyzer()
            
            # Display agreement information
            if hasattr(response, 'agreement_id') and response.agreement_id:
                print(f"\n📜 LEGAL AGREEMENTS ({len(response.agreement_id)} agreements):")
                print("-" * 100)
                
                for i, agr_id in enumerate(response.agreement_id):
                    agreement_info = {
                        "agreement_id": agr_id,
                        "agreement_type": "",
                        "agreement_title": "",
                        "agreement_text": "",
                        "version": "",
                        "effective_date": "",
                        "last_updated": "",
                        "status": "",
                        "acceptance_required": False,
                        "sections": [],
                        "character_count": 0,
                        "word_count": 0
                    }
                    
                    # Get corresponding agreement data
                    if hasattr(response, 'agreement_type') and i < len(response.agreement_type):
                        agreement_info["agreement_type"] = response.agreement_type[i]
                    
                    if hasattr(response, 'agreement_title') and i < len(response.agreement_title):
                        agreement_info["agreement_title"] = response.agreement_title[i]
                    
                    if hasattr(response, 'agreement_text') and i < len(response.agreement_text):
                        agreement_info["agreement_text"] = response.agreement_text[i]
                        agreement_info["character_count"] = len(response.agreement_text[i])
                        agreement_info["word_count"] = len(response.agreement_text[i].split())
                    
                    if hasattr(response, 'version') and i < len(response.version):
                        agreement_info["version"] = response.version[i]
                    
                    if hasattr(response, 'effective_date') and i < len(response.effective_date):
                        agreement_info["effective_date"] = response.effective_date[i]
                    
                    if hasattr(response, 'last_updated') and i < len(response.last_updated):
                        agreement_info["last_updated"] = response.last_updated[i]
                    
                    if hasattr(response, 'status') and i < len(response.status):
                        agreement_info["status"] = response.status[i]
                    
                    if hasattr(response, 'acceptance_required') and i < len(response.acceptance_required):
                        agreement_info["acceptance_required"] = response.acceptance_required[i]
                    
                    # Extract sections (simplified)
                    if agreement_info["agreement_text"]:
                        # Simple section detection based on numbering or headers
                        text = agreement_info["agreement_text"]
                        sections = []
                        lines = text.split('\n')
                        current_section = ""
                        
                        for line in lines:
                            line = line.strip()
                            if line.startswith(('1.', '2.', '3.', '4.', '5.', 'Section', 'Article')):
                                if current_section:
                                    sections.append(current_section.strip())
                                current_section = line
                            else:
                                current_section += " " + line
                        
                        if current_section:
                            sections.append(current_section.strip())
                        
                        agreement_info["sections"] = sections
                    
                    analyzer.add_agreement(agreement_info)
                    
                    # Display agreement summary
                    print(f"{i+1:3d}. Agreement ID: {agreement_info['agreement_id']}")
                    print(f"     Type: {agreement_info['agreement_type']}")
                    
                    if agreement_info['agreement_title']:
                        print(f"     Title: {agreement_info['agreement_title']}")
                    
                    if agreement_info['version']:
                        print(f"     Version: {agreement_info['version']}")
                    
                    if agreement_info['effective_date']:
                        print(f"     Effective Date: {agreement_info['effective_date']}")
                    
                    if agreement_info['last_updated']:
                        print(f"     Last Updated: {agreement_info['last_updated']}")
                    
                    if agreement_info['status']:
                        print(f"     Status: {agreement_info['status']}")
                    
                    acceptance_status = "✅ REQUIRED" if agreement_info['acceptance_required'] else "❌ NOT REQUIRED"
                    print(f"     Acceptance Required: {acceptance_status}")
                    
                    # Content summary
                    if agreement_info['agreement_text']:
                        print(f"     📊 CONTENT SUMMARY:")
                        print(f"       Character Count: {agreement_info['character_count']:,}")
                        print(f"       Word Count: {agreement_info['word_count']:,}")
                        print(f"       Sections: {len(agreement_info['sections'])}")
                        
                        # Show first few sections
                        if agreement_info['sections']:
                            print(f"       📋 SECTIONS:")
                            for j, section in enumerate(agreement_info['sections'][:3]):
                                preview = section[:100] + "..." if len(section) > 100 else section
                                print(f"         {j+1}. {preview}")
                            
                            if len(agreement_info['sections']) > 3:
                                print(f"         ... and {len(agreement_info['sections']) - 3} more sections")
                        
                        # Show text preview
                        text_preview = agreement_info['agreement_text'][:300]
                        if len(agreement_info['agreement_text']) > 300:
                            text_preview += "..."
                        
                        print(f"     📄 TEXT PREVIEW:")
                        print(f"       {text_preview}")
                    
                    print()
                
                # Display comprehensive analysis
                analysis = analyzer.get_analysis()
                
                print(f"📈 AGREEMENT COMPREHENSIVE ANALYSIS:")
                print(f"Total Agreements: {analysis['total_agreements']}")
                print(f"Agreement Types: {', '.join(analysis['agreement_types'])}")
                
                if analysis['content_analysis']:
                    ca = analysis['content_analysis']
                    print(f"Total Sections: {ca['total_sections']}")
                    print(f"Total Characters: {ca['total_characters']:,}")
                    print(f"Average Sections/Agreement: {ca['average_sections_per_agreement']:.1f}")
                    print(f"Average Characters/Agreement: {ca['average_characters_per_agreement']:,.0f}")
                
                if analysis['compliance_analysis']:
                    comp = analysis['compliance_analysis']
                    print(f"Compliance References: {comp['total_compliance_references']}")
                    print(f"Unique Compliance Terms: {comp['unique_compliance_terms']}")
                    print(f"Compliance Density: {comp['compliance_density']:.1f} per agreement")
                    
                    if comp['top_compliance_terms']:
                        print(f"Top Compliance Terms:")
                        for term_info in comp['top_compliance_terms'][:3]:
                            print(f"  • {term_info['term']}: {term_info['total_occurrences']} occurrences")
                
                if analysis['risk_analysis']:
                    risk = analysis['risk_analysis']
                    print(f"Risk References: {risk['total_risk_references']}")
                    print(f"Risk Coverage: {risk['risk_coverage_assessment']}")
                    print(f"Risk Density: {risk['risk_density']:.1f} per agreement")
                
                if analysis['trading_analysis']:
                    trading = analysis['trading_analysis']
                    print(f"Trading References: {trading['total_trading_references']}")
                    print(f"Trading Density: {trading['trading_density']:.1f} per agreement")
                
                if analysis['data_usage_analysis']:
                    data = analysis['data_usage_analysis']
                    print(f"Data Usage References: {data['total_data_references']}")
                    print(f"Data Density: {data['data_density']:.1f} per agreement")
                
                if analysis['version_analysis']:
                    version = analysis['version_analysis']
                    print(f"Version Compliance: {version['version_compliance']}")
                    
                    if version['most_recent_updates']:
                        print(f"Recent Updates:")
                        for update in version['most_recent_updates'][:3]:
                            print(f"  • {update['agreement_type']}: {update['last_updated']}")
                
                # Display insights
                if analysis['agreement_insights']:
                    print("\n💡 AGREEMENT INSIGHTS:")
                    for insight in analysis['agreement_insights']:
                        print(f"• {insight}")
                
                # CSV output
                if output_format == "csv":
                    print(f"\n📊 CSV FORMAT:")
                    csv_headers = ["Agreement_ID", "Agreement_Type", "Title", "Version", 
                                  "Effective_Date", "Character_Count", "Section_Count", 
                                  "Acceptance_Required", "Status"]
                    print(",".join(csv_headers))
                    
                    for agreement in analyzer.agreements:
                        values = [
                            agreement['agreement_id'],
                            agreement['agreement_type'],
                            agreement['agreement_title'][:50] + "..." if len(agreement['agreement_title']) > 50 else agreement['agreement_title'],
                            agreement['version'],
                            agreement['effective_date'],
                            str(agreement['character_count']),
                            str(len(agreement['sections'])),
                            str(agreement['acceptance_required']),
                            agreement['status']
                        ]
                        print(",".join(values))
                
                # Legal compliance recommendations
                print(f"\n💡 LEGAL COMPLIANCE INSIGHTS:")
                print("✅ Agreement review provides essential legal and compliance information")
                
                if analysis['compliance_analysis']['compliance_density'] > 5:
                    print("• High regulatory compliance focus ensures proper oversight")
                
                if analysis['risk_analysis']['risk_coverage_assessment'] == 'Comprehensive':
                    print("• Excellent risk disclosure coverage protects all parties")
                elif analysis['risk_analysis']['risk_coverage_assessment'] == 'Limited':
                    print("• Consider reviewing risk disclosure completeness")
                
                acceptance_required = sum(1 for agr in analyzer.agreements if agr['acceptance_required'])
                if acceptance_required > 0:
                    print(f"• {acceptance_required} agreements require formal acceptance")
                
                if analysis['version_analysis']['version_compliance'] != 'Fully Compliant':
                    print("• Review version management for compliance consistency")
                
                print("• Regular agreement review ensures ongoing compliance")
                print("• Monitor agreement updates for regulatory changes")
                print("• Maintain records of agreement acceptances and versions")
            
            else:
                # No agreements found - this is handled by the repository handler
                if not response_result.get("paper_trading_limitation"):
                    print(f"\n📋 No agreements found")
                    print("   This could indicate:")
                    print("   • No agreements configured for this account")
                    print("   • Agreement ID/type filter too restrictive")
                    print("   • Insufficient permissions to view agreements")
                    print("   • Agreements may be managed at different level")
        
        # Cleanup
        print("\n🔄 Cleaning up...")
        await authenticator.logout()
        await connection.disconnect()
        print("✅ Cleanup completed")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing show agreement: {e}")
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main function for show agreement testing."""
    parser = argparse.ArgumentParser(
        description="Test Rithmic Show Agreement endpoint (Templates 506/507)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_show_agreement.py
  python test_show_agreement.py --agreement-id "AGR001"
  python test_show_agreement.py --agreement-type "Trading Agreement"
  python test_show_agreement.py --format csv

Safety:
  This script performs READ-ONLY agreement viewing requests only.
  Requires authentication but no account modifications performed.
  NEVER accepts agreements automatically.
  
Output:
  Displays agreement details including:
  - Agreement content, versions, and effective dates
  - Compliance and risk disclosure analysis
  - Trading and data usage terms
  - Legal compliance insights and recommendations
        """
    )
    
    parser.add_argument(
        "--agreement-id",
        type=str,
        help="Specific agreement ID to retrieve (optional)"
    )
    
    parser.add_argument(
        "--agreement-type",
        type=str,
        help="Agreement type filter (optional)"
    )
    
    parser.add_argument(
        "--format",
        choices=["human", "json", "csv"],
        default="human",
        help="Output format (default: human)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--save-responses",
        action="store_true",
        help="Save responses to files"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate configuration
    if not validate_config():
        print("❌ Configuration validation failed")
        return 1
    
    # Update config if needed
    if args.save_responses:
        config_obj = get_config()
        config_obj.save_responses = True
    
    print("🧪 RITHMIC SHOW AGREEMENT ENDPOINT TESTER")
    print("=" * 50)
    print("⚠️  SAFETY: READ-ONLY AGREEMENT VIEWING OPERATIONS ONLY")
    print("⚠️  NO AUTOMATIC AGREEMENT ACCEPTANCE PERFORMED")
    print("=" * 50)
    
    # Run the test
    success = await test_show_agreement(
        agreement_id=args.agreement_id,
        agreement_type=args.agreement_type,
        output_format=args.format
    )
    
    if success:
        print("\n✅ Show agreement test completed successfully!")
        return 0
    else:
        print("\n❌ Show agreement test failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)