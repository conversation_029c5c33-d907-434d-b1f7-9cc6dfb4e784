#!/usr/bin/env python3

"""
Quick test check to identify which endpoint tests are working properly.
"""

import asyncio
import subprocess
import sys
import time

# Quick tests that should work with minimal arguments
quick_tests = [
    # System Discovery (should all work)
    ("system_discovery/test_system_info.py", []),
    ("system_discovery/test_gateway_info.py", []),
    
    # Account Info (some improvements expected)
    ("account_info/test_account_list.py", []),
    ("account_info/test_account_rms_info.py", []),
    
    # Market Data (with minimal args)
    ("market_data/test_product_codes.py", []),
    ("market_data/test_front_month_contract.py", []),
    
    # Repository (with improvements)
    ("repository/test_list_agreements.py", []),
]

async def run_quick_test(test_path, args):
    """Run a single test quickly."""
    cmd = [sys.executable, test_path, "--log-level", "ERROR"] + args
    print(f"Testing {test_path}...")
    
    try:
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await asyncio.wait_for(
            process.communicate(), 
            timeout=20  # 20 second timeout
        )
        
        success = process.returncode == 0
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status}")
        
        if not success:
            error_msg = stderr.decode('utf-8', errors='ignore')[-200:]
            if error_msg.strip():
                print(f"    Error: {error_msg.strip()}")
        
        return success
        
    except asyncio.TimeoutError:
        print("  ⏰ TIMEOUT")
        return False
    except Exception as e:
        print(f"  💥 ERROR: {e}")
        return False

async def main():
    """Run quick tests."""
    print("🚀 Quick Test Check")
    print("=" * 40)
    
    passed = 0
    total = len(quick_tests)
    
    for test_path, args in quick_tests:
        success = await run_quick_test(test_path, args)
        if success:
            passed += 1
        
        # Brief pause
        await asyncio.sleep(1)
    
    print("\n" + "=" * 40)
    print(f"📊 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed >= total * 0.7:  # 70% success rate
        print("🎉 Good progress! Most core tests are working.")
    elif passed >= total * 0.5:  # 50% success rate
        print("📈 Moderate progress. Some improvements visible.")
    else:
        print("🔧 More work needed on basic connectivity.")

if __name__ == "__main__":
    asyncio.run(main())