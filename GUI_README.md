# ES Futures Data Collection GUI Controller

## 🎯 Overview

The **ES Futures GUI Controller** is a comprehensive graphical interface for managing and monitoring ES futures market data collection. It provides intuitive control over data collection scripts, real-time monitoring, and comprehensive statistics tracking.

## ✨ Features

### 🎮 Control Panel
- **Symbol Selection**: Choose from all available ES futures contracts (ESU5, ESZ5, ESH6, ESM6, etc.)
- **Data Type Configuration**: Select Level 1, Level 3, Continuous, or Historical data collection
- **Quick Actions**: Pre-configured setups for common collection scenarios
- **Collection Controls**: Start, stop, restart, and emergency stop capabilities

### 📊 Real-time Monitoring
- **Process Status**: Live monitoring of all collection processes
- **Collection Metrics**: Real-time statistics and performance data
- **Health Monitoring**: Automatic process health checks and restart capabilities
- **Data Validation**: File and database collection verification

### 📋 Live Logs
- **Real-time Log Viewing**: Stream output from all collection processes
- **Log Filtering**: Filter by log level (INFO, WARNING, ERROR)
- **Auto-scroll**: Automatically follow new log entries
- **Log Export**: Save logs to file for analysis

### 📈 Statistics Dashboard
- **File Statistics**: Monitor data file sizes, counts, and recent activity
- **Database Statistics**: Track database table status and record counts
- **Collection Metrics**: Performance statistics and collection rates
- **Export Capabilities**: Export statistics for analysis

### ⚙️ Configuration Management
- **Settings Persistence**: Save and load collection configurations
- **Quick Setup**: Pre-defined configurations for common scenarios
- **Export/Import**: Share configurations between systems
- **Reset Options**: Restore default settings

## 🚀 Getting Started

### Prerequisites
- Python 3.8 or higher
- tkinter (usually included with Python)
- Working Rithmic API environment
- Required collection scripts in place

### Installation
1. Ensure all project files are in place
2. Run the launcher script:
   ```bash
   python3 launch_gui.py
   ```

### First-Time Setup
1. **Launch the GUI**: Run `python3 launch_gui.py`
2. **Configure Symbols**: In the Control Panel, select desired ES contracts
3. **Select Data Types**: Choose Level 1, Level 3, or both
4. **Save Configuration**: Use "Save Configuration" to persist settings
5. **Start Collection**: Click "🚀 Start Collection"

## 📖 User Guide

### Control Panel Tab

#### Symbol Selection
- **Select All**: Choose all available ES contracts
- **Select None**: Clear all contract selections
- **Select Front Month**: Choose only front month contracts (ESU5, ESZ5, ESH6)
- **Individual Selection**: Use checkboxes for specific contracts

#### Data Type Selection
- **Level 1**: Best Bid/Offer and Last Trade data
- **Level 3**: Full order book depth-by-order data
- **Continuous**: Proven continuous collection system
- **Historical**: Historical time bars and tick data

#### Collection Controls
- **🚀 Start Collection**: Begin data collection with selected settings
- **🛑 Stop Collection**: Gracefully stop all collection processes
- **🔄 Restart Collection**: Stop and restart all processes
- **⚠️ Emergency Stop**: Immediately terminate all processes

#### Quick Actions
- **📊 ESU5 Level 1 Only**: Quick setup for ESU5 Level 1 data only
- **🎯 All ES Level 1+3**: Comprehensive collection for all contracts
- **🔄 Continuous ESU5**: Continuous collection for ESU5
- **💾 Save Configuration**: Save current settings

### Monitoring Tab

#### Process Status
- View all active collection processes
- Monitor process IDs and uptime
- Check process health status
- Automatic failure detection

#### Collection Metrics
- Real-time performance statistics
- Data file monitoring
- Database status tracking
- Collection rate metrics

### Statistics Tab

#### File Statistics
- Data file counts and sizes
- Recent file activity
- File modification timestamps
- Storage usage tracking

#### Database Statistics
- Table status and record counts
- Connection health monitoring
- Query performance metrics
- Data integrity checks

### Live Logs Tab

#### Log Display
- Real-time streaming of all process output
- Color-coded log levels
- Timestamp information
- Process identification

#### Log Controls
- **🧹 Clear Logs**: Clear the log display
- **💾 Save Logs**: Export logs to file
- **Auto-scroll**: Automatically follow new entries
- **Filter**: Show only specific log levels

### Configuration Tab

#### Configuration Display
- View current settings in JSON format
- Real-time configuration updates
- Setting validation and verification

#### Configuration Controls
- **💾 Save Configuration**: Persist current settings
- **📂 Load Configuration**: Load saved settings
- **🔄 Reset to Defaults**: Restore default configuration
- **📤 Export Configuration**: Export settings to file

## 🎯 Quick Start Scenarios

### Scenario 1: ESU5 Level 1 Data Only
1. Click "📊 ESU5 Level 1 Only" in Quick Actions
2. Click "🚀 Start Collection"
3. Monitor in the Monitoring tab

### Scenario 2: Comprehensive ES Collection
1. Click "🎯 All ES Level 1+3" in Quick Actions
2. Verify all desired contracts are selected
3. Click "🚀 Start Collection"
4. Monitor performance in Statistics tab

### Scenario 3: Continuous 24/7 Collection
1. Select desired contracts
2. Enable "Continuous Collection" data type
3. Set Collection Duration to 24+ hours
4. Enable "Auto-restart on failure"
5. Click "🚀 Start Collection"

## 🔧 Advanced Features

### Auto-restart
- Automatically restart failed processes
- Configurable restart attempts
- Intelligent failure detection
- Graceful recovery mechanisms

### Process Monitoring
- Real-time health checks
- Memory and CPU monitoring
- Network connection status
- Data flow verification

### Data Validation
- File integrity checks
- Database consistency verification
- Real-time data flow monitoring
- Collection completeness tracking

## ⚠️ Troubleshooting

### GUI Won't Start
- Check Python version (3.8+ required)
- Verify tkinter installation: `python3 -c "import tkinter"`
- Run launcher: `python3 launch_gui.py`

### Collection Processes Won't Start
- Verify script paths in project structure
- Check database connectivity
- Verify Rithmic API credentials
- Check log output in Live Logs tab

### No Data Being Collected
- Verify market hours (ES futures trade 23 hours/day)
- Check network connectivity
- Verify Rithmic API login
- Monitor process status in Monitoring tab

### Performance Issues
- Reduce number of simultaneous contracts
- Check system resources (CPU, memory, disk)
- Monitor database performance
- Review log files for errors

## 📁 File Structure

```
ES_GUI_Controller/
├── es_futures_gui_controller.py    # Main GUI application
├── launch_gui.py                   # GUI launcher script
├── GUI_README.md                   # This documentation
├── es_collection_config.json       # Saved configuration
└── data/
    ├── logs/                       # Log files
    ├── exports/                    # Exported data
    └── config/                     # Configuration backups
```

## 🔄 Integration

### With Existing Scripts
The GUI integrates with existing collection scripts:
- `src/scripts/subscribe_level1_data.py`
- `src/scripts/subscribe_depth_by_order.py`
- `continuous_es_data_collection.py`

### Database Integration
- Full MySQL database support
- Real-time data persistence
- Query performance monitoring
- Data integrity validation

### File System Integration
- JSON Lines format for streaming data
- Organized directory structure
- Automatic file rotation
- Export capabilities

## 📊 Default Configuration

By default, the GUI is configured to:
- ✅ Select all available ES contracts
- ✅ Enable Level 1 and Level 3 data collection
- ✅ Enable auto-restart on failure
- ✅ Set 24-hour collection duration
- ✅ Monitor all processes every 5 seconds
- ✅ Auto-scroll logs
- ✅ Save configuration on changes

## 🎖️ Best Practices

1. **Start Small**: Begin with a single contract and data type
2. **Monitor Actively**: Use the Monitoring tab during initial setup
3. **Save Configurations**: Always save working configurations
4. **Check Logs**: Review logs regularly for errors or warnings
5. **Plan Resources**: Monitor system resources during peak collection
6. **Backup Data**: Regularly export and backup collected data
7. **Test Recovery**: Verify auto-restart functionality works
8. **Document Setup**: Keep notes on successful configurations

## 🆘 Support

For issues or questions:
1. Check the Live Logs tab for error messages
2. Review the Statistics tab for system health
3. Verify configuration in Configuration tab
4. Check project documentation and scripts
5. Monitor system resources and network connectivity

---

**🎯 The ES Futures GUI Controller provides comprehensive, user-friendly management of market data collection with real-time monitoring, automated recovery, and detailed analytics - making complex data collection operations simple and reliable.**