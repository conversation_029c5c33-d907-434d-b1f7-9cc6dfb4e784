#!/usr/bin/env python3
"""
Unified Analysis Tool for Rithmic API

This tool consolidates all analysis and exploration functionality into a single interface
with multiple subcommands for different types of analysis.

Consolidates:
- explore_available_symbols.py
- explore_product_codes.py
- get_product_codes.py
- analyze_discovery_results.py
- comprehensive_product_discovery.py
- comprehensive_offline_test.py
- maximum_capacity_test.py

Usage:
    python3 tools/analysis.py <command> [options]

Commands:
    symbols     - Explore available symbols
    products    - Explore product codes  
    discovery   - Run comprehensive discovery
    analyze     - Analyze discovery results
    capacity    - Test maximum capacity
    offline     - Run offline analysis
    help        - Show detailed help
"""

import asyncio
import sys
import os
import json
import argparse
import logging
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / 'src'))
sys.path.append(str(project_root / 'proto_generated'))

try:
    from rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
    from rithmic_api.config import config
    from utils.contract_cache import ContractCache
    RITHMIC_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Rithmic components not available: {e}")
    RITHMIC_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SymbolExplorer:
    """Explore available symbols from the API."""
    
    def __init__(self):
        self.client = None
        self.discovered_symbols = []
    
    async def explore_symbols(self, patterns=None, exchanges=None, max_results=1000):
        """Explore symbols with given patterns and exchanges."""
        if not RITHMIC_AVAILABLE:
            print("❌ Rithmic components not available")
            return []
        
        print("🔍 Starting symbol exploration...")
        
        # Default patterns if none provided
        if not patterns:
            patterns = ['*', 'ES*', 'NQ*', 'YM*', 'CL*', 'GC*', 'SI*']
        
        # Default exchanges if none provided
        if not exchanges:
            exchanges = ['CME', 'NYMEX', 'ICE', 'EUREX']
        
        self.client = RithmicWebSocketClient()
        
        try:
            # System discovery and login
            await self._authenticate()
            
            # Search for symbols
            total_found = 0
            for exchange in exchanges:
                for pattern in patterns:
                    print(f"🔍 Searching {pattern} on {exchange}...")
                    
                    try:
                        results = await self.client.search_symbols(
                            text=pattern,
                            exchange=exchange,
                            type='FUTURE'
                        )
                        
                        if results:
                            print(f"  ✅ Found {len(results)} symbols")
                            self.discovered_symbols.extend(results)
                            total_found += len(results)
                        else:
                            print(f"  ℹ️ No symbols found")
                        
                        if total_found >= max_results:
                            print(f"🛑 Reached maximum results limit ({max_results})")
                            break
                            
                    except Exception as e:
                        print(f"  ❌ Error searching {pattern} on {exchange}: {e}")
                
                if total_found >= max_results:
                    break
        
        except Exception as e:
            print(f"❌ Error during symbol exploration: {e}")
        
        finally:
            if self.client and self.client.is_connected:
                await self.client.disconnect()
        
        print(f"📊 Total symbols discovered: {len(self.discovered_symbols)}")
        return self.discovered_symbols
    
    async def _authenticate(self):
        """Authenticate with the API."""
        # System discovery
        systems = await self.client.discover_systems()
        if not systems:
            raise Exception("No systems available")
        
        # Login to ticker plant
        system_name = "Rithmic Paper Trading"
        if system_name not in systems:
            system_name = systems[0]
        
        await self.client.login(system_name, infrastructure_type="TICKER_PLANT")
    
    def save_results(self, filename=None):
        """Save discovered symbols to file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"symbol_exploration_{timestamp}.json"
        
        output_path = project_root / 'data' / filename
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(self.discovered_symbols, f, indent=2, default=str)
        
        print(f"💾 Results saved to {output_path}")


class ProductExplorer:
    """Explore product codes from the API."""
    
    def __init__(self):
        self.client = None
        self.product_codes = []
    
    async def explore_products(self):
        """Explore available product codes."""
        if not RITHMIC_AVAILABLE:
            print("❌ Rithmic components not available")
            return []
        
        print("🔍 Starting product code exploration...")
        
        self.client = RithmicWebSocketClient()
        
        try:
            # System discovery and login
            await self._authenticate()
            
            # Request product codes
            print("📋 Requesting product codes...")
            results = await self.client.get_product_codes()
            
            if results:
                self.product_codes = results
                print(f"✅ Found {len(self.product_codes)} product codes")
            else:
                print("❌ No product codes found")
        
        except Exception as e:
            print(f"❌ Error during product exploration: {e}")
        
        finally:
            if self.client and self.client.is_connected:
                await self.client.disconnect()
        
        return self.product_codes
    
    async def _authenticate(self):
        """Authenticate with the API."""
        # System discovery
        systems = await self.client.discover_systems()
        if not systems:
            raise Exception("No systems available")
        
        # Login to ticker plant
        system_name = "Rithmic Paper Trading"
        if system_name not in systems:
            system_name = systems[0]
        
        await self.client.login(system_name, infrastructure_type="TICKER_PLANT")
    
    def save_results(self, filename=None):
        """Save product codes to file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"product_codes_{timestamp}.json"
        
        output_path = project_root / 'data' / filename
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(self.product_codes, f, indent=2, default=str)
        
        print(f"💾 Results saved to {output_path}")


class DiscoveryAnalyzer:
    """Analyze discovery results from saved data."""
    
    def load_data(self, filename):
        """Load discovery data from JSON file."""
        try:
            data_file = project_root / 'data' / filename
            with open(data_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading data from {filename}: {e}")
            return None
    
    def analyze_contracts(self, contracts):
        """Analyze discovered contracts."""
        if not contracts:
            print("❌ No contracts to analyze")
            return
        
        print(f"📊 Analyzing {len(contracts)} contracts...")
        print("=" * 60)
        
        # Basic statistics
        total_contracts = len(contracts)
        print(f"📋 Total Contracts: {total_contracts:,}")
        
        # Group by exchange
        by_exchange = defaultdict(list)
        for contract in contracts:
            exchange = contract.get('exchange', 'Unknown')
            by_exchange[exchange].append(contract)
        
        print(f"\n🏢 By Exchange:")
        for exchange, exchange_contracts in sorted(by_exchange.items()):
            count = len(exchange_contracts)
            percentage = (count / total_contracts) * 100
            print(f"  {exchange}: {count:,} contracts ({percentage:.1f}%)")
        
        # Group by product
        by_product = defaultdict(list)
        for contract in contracts:
            product = contract.get('product_code', contract.get('product', 'Unknown'))
            by_product[product].append(contract)
        
        print(f"\n📈 Top 20 Products by Contract Count:")
        sorted_products = sorted(by_product.items(), key=lambda x: len(x[1]), reverse=True)
        for i, (product, product_contracts) in enumerate(sorted_products[:20]):
            count = len(product_contracts)
            percentage = (count / total_contracts) * 100
            print(f"  {i+1:2d}. {product}: {count:,} contracts ({percentage:.1f}%)")
        
        # Instrument types
        by_type = defaultdict(list)
        for contract in contracts:
            inst_type = contract.get('instrument_type', 'Unknown')
            by_type[inst_type].append(contract)
        
        print(f"\n📋 By Instrument Type:")
        for inst_type, type_contracts in sorted(by_type.items()):
            count = len(type_contracts)
            percentage = (count / total_contracts) * 100
            print(f"  {inst_type}: {count:,} contracts ({percentage:.1f}%)")
        
        # Symbol patterns analysis
        print(f"\n🔤 Symbol Analysis:")
        symbol_lengths = [len(contract.get('symbol', '')) for contract in contracts]
        if symbol_lengths:
            avg_length = sum(symbol_lengths) / len(symbol_lengths)
            print(f"  Average symbol length: {avg_length:.1f} characters")
            print(f"  Shortest symbol: {min(symbol_lengths)} characters")
            print(f"  Longest symbol: {max(symbol_lengths)} characters")
        
        # Month/year analysis for futures
        futures = [c for c in contracts if c.get('instrument_type') == 'FUTURE']
        if futures:
            print(f"\n📅 Futures Analysis ({len(futures):,} contracts):")
            
            # Extract month/year patterns
            years = set()
            months = set()
            for contract in futures:
                symbol = contract.get('symbol', '')
                if len(symbol) >= 3:
                    # Common pattern: XXXMYYor XXXMYY where M=month, Y=year
                    month_char = symbol[-3] if len(symbol) >= 3 else None
                    if month_char and month_char.isalpha():
                        months.add(month_char)
                    
                    # Try to extract year
                    year_part = symbol[-2:] if len(symbol) >= 2 else None
                    if year_part and year_part.isdigit():
                        full_year = 2000 + int(year_part) if int(year_part) < 50 else 1900 + int(year_part)
                        years.add(full_year)
            
            if months:
                print(f"  Month codes found: {sorted(months)}")
            if years:
                print(f"  Years found: {min(years)} - {max(years)}")
    
    def generate_report(self, contracts, output_file=None):
        """Generate detailed analysis report."""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"analysis_report_{timestamp}.txt"
        
        output_path = project_root / 'data' / output_file
        output_path.parent.mkdir(exist_ok=True)
        
        # Redirect stdout to capture analysis
        import io
        from contextlib import redirect_stdout
        
        f = io.StringIO()
        with redirect_stdout(f):
            self.analyze_contracts(contracts)
        
        report_content = f.getvalue()
        
        with open(output_path, 'w') as file:
            file.write(f"Rithmic API Discovery Analysis Report\n")
            file.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write("=" * 80 + "\n\n")
            file.write(report_content)
        
        print(f"📊 Detailed report saved to {output_path}")


class CapacityTester:
    """Test maximum capacity and performance."""
    
    def __init__(self):
        self.client = None
        self.results = {}
    
    async def test_capacity(self, max_contracts=1000, max_duration=300):
        """Test API capacity and performance limits."""
        if not RITHMIC_AVAILABLE:
            print("❌ Rithmic components not available")
            return
        
        print(f"🚀 Starting capacity test (max {max_contracts} contracts, {max_duration}s)")
        
        self.client = RithmicWebSocketClient()
        start_time = datetime.now()
        
        try:
            # Authenticate
            await self._authenticate()
            
            # Test 1: Symbol search performance
            await self._test_search_performance(max_contracts // 10)
            
            # Test 2: Market data subscription capacity
            await self._test_subscription_capacity(min(100, max_contracts // 10))
            
            # Test 3: Concurrent request handling
            await self._test_concurrent_requests()
            
        except Exception as e:
            print(f"❌ Capacity test error: {e}")
        
        finally:
            if self.client and self.client.is_connected:
                await self.client.disconnect()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"📊 Capacity test completed in {duration:.1f} seconds")
        self._print_results()
    
    async def _authenticate(self):
        """Authenticate with the API."""
        systems = await self.client.discover_systems()
        if not systems:
            raise Exception("No systems available")
        
        system_name = "Rithmic Paper Trading"
        if system_name not in systems:
            system_name = systems[0]
        
        await self.client.login(system_name, infrastructure_type="TICKER_PLANT")
    
    async def _test_search_performance(self, max_searches):
        """Test symbol search performance."""
        print(f"📋 Testing search performance ({max_searches} searches)...")
        
        search_patterns = ['ES*', 'NQ*', 'YM*', 'CL*', 'GC*']
        exchanges = ['CME', 'NYMEX']
        
        start_time = datetime.now()
        total_results = 0
        search_count = 0
        
        for exchange in exchanges:
            for pattern in search_patterns:
                if search_count >= max_searches:
                    break
                
                try:
                    results = await self.client.search_symbols(
                        text=pattern,
                        exchange=exchange,
                        type='FUTURE'
                    )
                    
                    if results:
                        total_results += len(results)
                    
                    search_count += 1
                    
                except Exception as e:
                    print(f"  ❌ Search error: {e}")
            
            if search_count >= max_searches:
                break
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.results['search_performance'] = {
            'searches_completed': search_count,
            'total_results': total_results,
            'duration_seconds': duration,
            'searches_per_second': search_count / duration if duration > 0 else 0,
            'results_per_second': total_results / duration if duration > 0 else 0
        }
        
        print(f"  ✅ {search_count} searches, {total_results} results in {duration:.1f}s")
        print(f"  📊 {search_count/duration:.1f} searches/sec, {total_results/duration:.1f} results/sec")
    
    async def _test_subscription_capacity(self, max_subscriptions):
        """Test market data subscription capacity."""
        print(f"📡 Testing subscription capacity ({max_subscriptions} subscriptions)...")
        
        # This is a simplified test - in practice would need actual symbols
        self.results['subscription_capacity'] = {
            'max_attempted': max_subscriptions,
            'successful': 0,  # Would be populated by actual test
            'failed': 0
        }
        
        print(f"  ℹ️ Subscription capacity test (placeholder implementation)")
    
    async def _test_concurrent_requests(self):
        """Test concurrent request handling."""
        print(f"🔄 Testing concurrent request handling...")
        
        # Test concurrent heartbeats
        start_time = datetime.now()
        
        tasks = []
        for i in range(5):
            tasks.append(self.client.send_heartbeat())
        
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
            success_count = 5
        except Exception as e:
            success_count = 0
            print(f"  ❌ Concurrent request error: {e}")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.results['concurrent_requests'] = {
            'requests_sent': 5,
            'successful': success_count,
            'duration_seconds': duration
        }
        
        print(f"  ✅ {success_count}/5 concurrent requests in {duration:.1f}s")
    
    def _print_results(self):
        """Print capacity test results."""
        print("\n📊 Capacity Test Results:")
        print("=" * 40)
        
        for test_name, results in self.results.items():
            print(f"\n{test_name.replace('_', ' ').title()}:")
            for key, value in results.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.2f}")
                else:
                    print(f"  {key}: {value}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Unified Analysis Tool for Rithmic API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Commands:
  symbols     Explore available symbols
  products    Explore product codes
  discovery   Run comprehensive discovery
  analyze     Analyze discovery results
  capacity    Test maximum capacity
  offline     Run offline analysis
  help        Show detailed help

Examples:
  python3 tools/analysis.py symbols --patterns "ES*" "NQ*" --exchanges CME
  python3 tools/analysis.py products --save products.json
  python3 tools/analysis.py analyze --file discovery_results.json
  python3 tools/analysis.py capacity --max-contracts 500
        """
    )
    
    parser.add_argument('command', choices=['symbols', 'products', 'discovery', 'analyze', 'capacity', 'offline', 'help'],
                       help='Analysis command to run')
    
    # Symbol exploration options
    parser.add_argument('--patterns', nargs='*', default=['*', 'ES*', 'NQ*', 'YM*'],
                       help='Search patterns for symbols')
    parser.add_argument('--exchanges', nargs='*', default=['CME', 'NYMEX'],
                       help='Exchanges to search')
    parser.add_argument('--max-results', type=int, default=1000,
                       help='Maximum results to collect')
    
    # Analysis options
    parser.add_argument('--file', type=str,
                       help='Input file for analysis')
    parser.add_argument('--save', type=str,
                       help='Save results to file')
    
    # Capacity test options
    parser.add_argument('--max-contracts', type=int, default=1000,
                       help='Maximum contracts for capacity test')
    parser.add_argument('--max-duration', type=int, default=300,
                       help='Maximum duration for capacity test (seconds)')
    
    args = parser.parse_args()
    
    if args.command == 'help':
        parser.print_help()
        return
    
    # Run the appropriate command
    if args.command == 'symbols':
        explorer = SymbolExplorer()
        results = asyncio.run(explorer.explore_symbols(
            patterns=args.patterns,
            exchanges=args.exchanges,
            max_results=args.max_results
        ))
        if args.save:
            explorer.save_results(args.save)
    
    elif args.command == 'products':
        explorer = ProductExplorer()
        results = asyncio.run(explorer.explore_products())
        if args.save:
            explorer.save_results(args.save)
    
    elif args.command == 'analyze':
        analyzer = DiscoveryAnalyzer()
        if not args.file:
            print("❌ --file parameter required for analyze command")
            return
        
        data = analyzer.load_data(args.file)
        if data:
            analyzer.analyze_contracts(data)
            if args.save:
                analyzer.generate_report(data, args.save)
    
    elif args.command == 'capacity':
        tester = CapacityTester()
        asyncio.run(tester.test_capacity(
            max_contracts=args.max_contracts,
            max_duration=args.max_duration
        ))
    
    elif args.command == 'discovery':
        print("🔍 Comprehensive discovery mode")
        print("This would run symbol exploration followed by analysis")
        explorer = SymbolExplorer()
        results = asyncio.run(explorer.explore_symbols(
            patterns=args.patterns,
            exchanges=args.exchanges,
            max_results=args.max_results
        ))
        if results:
            analyzer = DiscoveryAnalyzer()
            analyzer.analyze_contracts(results)
    
    elif args.command == 'offline':
        print("📊 Offline analysis mode")
        print("This would analyze existing cache/data files")
        # Placeholder for offline analysis
        cache = ContractCache(use_database=False) if RITHMIC_AVAILABLE else None
        if cache:
            status = cache.get_cache_status()
            print(f"Cache status: {status}")


if __name__ == "__main__":
    main()