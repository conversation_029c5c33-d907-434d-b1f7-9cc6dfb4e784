#!/usr/bin/env python3
"""
ES Futures Market Data Deployment Script

Deploys comprehensive market data collection for ES futures contracts:
- Level 1 market data (Best Bid/Offer, Last Trade)
- Level 3 depth-by-order data (Full order book)
- Continuous operation with database persistence
- Real-time monitoring and logging
"""

import asyncio
import signal
import sys
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
import os
env_file = project_root / '.env'
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()

from src.rithmic_api.config import config
from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.database.database_manager import get_database_manager
from src.database.models import create_all_tables
from src.utils.data_utils import initialize_message_logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('es_market_data_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ESMarketDataCollector:
    """Comprehensive ES Futures Market Data Collector"""
    
    def __init__(self):
        self.clients: Dict[str, RithmicWebSocketClient] = {}
        self.running = False
        self.contracts = ["ESU5"]  # Start with discovered front month
        self.data_stats = {
            'level1_updates': 0,
            'level3_updates': 0,
            'last_update': None,
            'start_time': None
        }
        
    async def initialize(self):
        """Initialize the market data collection system"""
        logger.info("🚀 Initializing ES Market Data Collector")
        
        # Create database tables
        logger.info("📊 Setting up database...")
        create_all_tables()
        
        # Setup data directories
        data_dir = Path(config.data_directory)
        data_dir.mkdir(exist_ok=True)
        initialize_message_logging(data_dir)
        
        logger.info("✅ Initialization complete")
        
    async def create_level1_client(self) -> RithmicWebSocketClient:
        """Create and configure Level 1 market data client"""
        logger.info("📈 Setting up Level 1 market data client...")
        
        client = RithmicWebSocketClient()
        
        # Set up Level 1 data handlers
        def on_best_bid_offer(data):
            self.data_stats['level1_updates'] += 1
            self.data_stats['last_update'] = datetime.now()
            if self.data_stats['level1_updates'] % 100 == 0:
                logger.info(f"📊 Level 1 Updates: {self.data_stats['level1_updates']}")
        
        def on_last_trade(data):
            self.data_stats['level1_updates'] += 1
            self.data_stats['last_update'] = datetime.now()
        
        # Register callbacks (would be implemented in actual client)
        # client.on_best_bid_offer = on_best_bid_offer
        # client.on_last_trade = on_last_trade
        
        return client
    
    async def create_level3_client(self) -> RithmicWebSocketClient:
        """Create and configure Level 3 depth-by-order client"""
        logger.info("📊 Setting up Level 3 depth-by-order client...")
        
        client = RithmicWebSocketClient()
        
        # Set up Level 3 data handlers
        def on_depth_update(data):
            self.data_stats['level3_updates'] += 1
            self.data_stats['last_update'] = datetime.now()
            if self.data_stats['level3_updates'] % 50 == 0:
                logger.info(f"📊 Level 3 Updates: {self.data_stats['level3_updates']}")
        
        # Register callbacks (would be implemented in actual client)
        # client.on_depth_update = on_depth_update
        
        return client
    
    async def start_level1_collection(self):
        """Start Level 1 market data collection"""
        logger.info("🔄 Starting Level 1 market data collection...")
        
        try:
            client = await self.create_level1_client()
            self.clients['level1'] = client
            
            # Connect and authenticate
            await client.connect()
            await client.login("TICKER_PLANT")
            
            # Subscribe to Level 1 data for ES contracts
            for contract in self.contracts:
                logger.info(f"📈 Subscribing to Level 1 data for {contract}")
                await client.subscribe_to_market_data(contract, "CME")
            
            logger.info("✅ Level 1 collection active")
            
        except Exception as e:
            logger.error(f"❌ Level 1 collection failed: {e}")
            raise
    
    async def start_level3_collection(self):
        """Start Level 3 depth-by-order collection"""
        logger.info("🔄 Starting Level 3 depth-by-order collection...")
        
        try:
            client = await self.create_level3_client()
            self.clients['level3'] = client
            
            # Connect and authenticate
            await client.connect()
            await client.login("TICKER_PLANT")
            
            # Subscribe to Level 3 data for ES contracts
            for contract in self.contracts:
                logger.info(f"📊 Subscribing to Level 3 data for {contract}")
                await client.subscribe_to_depth_by_order(contract, "CME")
            
            logger.info("✅ Level 3 collection active")
            
        except Exception as e:
            logger.error(f"❌ Level 3 collection failed: {e}")
            raise
    
    async def monitor_data_flow(self):
        """Monitor and report data collection statistics"""
        logger.info("👁️ Starting data flow monitoring...")
        
        while self.running:
            try:
                await asyncio.sleep(60)  # Report every minute
                
                now = datetime.now()
                uptime = now - self.data_stats['start_time'] if self.data_stats['start_time'] else None
                
                logger.info("📊 DATA COLLECTION STATISTICS")
                logger.info(f"  ⏱️ Uptime: {uptime}")
                logger.info(f"  📈 Level 1 Updates: {self.data_stats['level1_updates']}")
                logger.info(f"  📊 Level 3 Updates: {self.data_stats['level3_updates']}")
                logger.info(f"  🕐 Last Update: {self.data_stats['last_update']}")
                logger.info(f"  🎯 Contracts: {', '.join(self.contracts)}")
                
                # Check database health
                try:
                    db = get_database_manager()
                    with db.get_cursor() as (cursor, conn):
                        cursor.execute("SELECT COUNT(*) as count FROM best_bid_offer WHERE DATE(received_at) = CURDATE()")
                        bbo_count = cursor.fetchone()['count']
                        
                        cursor.execute("SELECT COUNT(*) as count FROM last_trades WHERE DATE(received_at) = CURDATE()")
                        trade_count = cursor.fetchone()['count']
                        
                        cursor.execute("SELECT COUNT(*) as count FROM depth_by_order_updates WHERE DATE(received_at) = CURDATE()")
                        depth_count = cursor.fetchone()['count']
                    
                    logger.info("💾 DATABASE RECORDS TODAY")
                    logger.info(f"  📈 Best Bid/Offer: {bbo_count}")
                    logger.info(f"  💰 Trades: {trade_count}")
                    logger.info(f"  📊 Depth Updates: {depth_count}")
                    
                except Exception as e:
                    logger.warning(f"⚠️ Database health check failed: {e}")
                
            except Exception as e:
                logger.error(f"❌ Monitoring error: {e}")
    
    async def deploy(self):
        """Deploy the complete ES market data collection system"""
        logger.info("🚀 DEPLOYING ES FUTURES MARKET DATA COLLECTION")
        logger.info("=" * 60)
        
        try:
            # Initialize system
            await self.initialize()
            
            # Set running flag and start time
            self.running = True
            self.data_stats['start_time'] = datetime.now()
            
            # Start both Level 1 and Level 3 collection concurrently
            logger.info("🔄 Starting concurrent data collection...")
            
            tasks = [
                asyncio.create_task(self.start_level1_collection()),
                asyncio.create_task(self.start_level3_collection()),
                asyncio.create_task(self.monitor_data_flow())
            ]
            
            # Run all tasks concurrently
            await asyncio.gather(*tasks)
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Gracefully shutdown the data collection system"""
        logger.info("🔄 Shutting down ES market data collection...")
        
        self.running = False
        
        # Disconnect all clients
        for name, client in self.clients.items():
            try:
                logger.info(f"📡 Disconnecting {name} client...")
                await client.disconnect()
            except Exception as e:
                logger.warning(f"⚠️ Error disconnecting {name} client: {e}")
        
        # Final statistics
        total_updates = self.data_stats['level1_updates'] + self.data_stats['level3_updates']
        uptime = datetime.now() - self.data_stats['start_time'] if self.data_stats['start_time'] else None
        
        logger.info("📊 FINAL COLLECTION STATISTICS")
        logger.info(f"  📈 Total Updates: {total_updates}")
        logger.info(f"  ⏱️ Total Uptime: {uptime}")
        logger.info("✅ Shutdown complete")

async def main():
    """Main deployment function"""
    
    # Set up signal handlers for graceful shutdown
    collector = ESMarketDataCollector()
    
    def signal_handler(signum, frame):
        logger.info(f"🛑 Received signal {signum}, initiating shutdown...")
        asyncio.create_task(collector.shutdown())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        logger.info("🎯 ES FUTURES MARKET DATA DEPLOYMENT")
        logger.info(f"📅 Start Time: {datetime.now()}")
        logger.info(f"🎯 Target Contracts: ESU5 (ES front month)")
        logger.info(f"📊 Data Types: Level 1 + Level 3")
        logger.info(f"💾 Storage: MySQL Database")
        logger.info("=" * 60)
        
        # Deploy the system
        await collector.deploy()
        
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted by user")
    except Exception as e:
        logger.error(f"💥 Deployment error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        logger.info("🏁 ES Market Data Deployment Complete")

if __name__ == "__main__":
    # Verify system readiness
    print("🔍 Pre-deployment System Check...")
    
    try:
        # Test database connection
        db = get_database_manager()
        with db.get_cursor() as (cursor, conn):
            cursor.execute('SELECT VERSION()')
            version = cursor.fetchone()['VERSION()']
        print(f"✅ Database: MySQL {version}")
        
        # Test configuration
        print(f"✅ User: {config.user}")
        print(f"✅ System: {config.system_name}")
        print(f"✅ URI: {config.uri}")
        
        # Test SSL certificate
        ssl_path = Path(config.ssl_cert_path)
        if ssl_path.exists():
            print(f"✅ SSL Certificate: {ssl_path}")
        else:
            print(f"⚠️ SSL Certificate missing: {ssl_path}")
        
        print("🎯 System ready for deployment!")
        print("🚀 Starting ES Futures Market Data Collection...")
        print()
        
        # Run the deployment
        asyncio.run(main())
        
    except Exception as e:
        print(f"❌ Pre-deployment check failed: {e}")
        sys.exit(1)