#!/usr/bin/env python3
"""
Contract Resolution Utility

This module provides functionality to resolve various contract specifications
into actual tradeable contract symbols, including:
- Continuous contracts (@ES -> ESU5.CME)
- Wildcard patterns (ES* -> [ESU5.CME, ESZ5.CME, ...])
- Specific contracts (ESU5.CME -> ESU5.CME)
"""

import logging
import re
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass

from .contract_cache import contract_cache, ContractInfo


@dataclass
class ResolvedContract:
    """Represents a resolved contract with metadata."""
    original_spec: str
    resolved_symbol: str
    exchange: str
    product_code: str
    is_continuous: bool = False
    is_wildcard: bool = False
    resolution_method: str = ""
    

class ContractResolver:
    """
    Resolves contract specifications into actual contract symbols.
    
    Supports:
    - Continuous contracts: @ES, @NQ
    - Wildcard patterns: *, *.CME, ES*, NQ*
    - Specific contracts: ESU5.CME, NQU5.CME
    """
    
    def __init__(self, client=None):
        """
        Initialize contract resolver.
        
        Args:
            client: RithmicWebSocketClient for live resolution (optional)
        """
        self.client = client
        self.logger = logging.getLogger(__name__)
    
    async def resolve_contract_list(self, contract_specs: List[str], 
                                  max_contracts: Optional[int] = None) -> List[ResolvedContract]:
        """
        Resolve a list of contract specifications into actual contracts.
        
        Args:
            contract_specs: List of contract specifications
            max_contracts: Maximum number of contracts to return
            
        Returns:
            List of resolved contracts
        """
        self.logger.info(f"Resolving {len(contract_specs)} contract specifications")
        
        all_resolved = []
        
        for spec in contract_specs:
            try:
                resolved = await self.resolve_single_contract(spec)
                all_resolved.extend(resolved)
                
            except Exception as e:
                self.logger.error(f"Error resolving contract spec '{spec}': {e}")
                continue
        
        # Remove duplicates while preserving order
        seen = set()
        unique_resolved = []
        for contract in all_resolved:
            if contract.resolved_symbol not in seen:
                seen.add(contract.resolved_symbol)
                unique_resolved.append(contract)
        
        # Apply max contracts limit
        if max_contracts and len(unique_resolved) > max_contracts:
            self.logger.warning(f"Limiting to {max_contracts} contracts out of {len(unique_resolved)} resolved")
            unique_resolved = unique_resolved[:max_contracts]
        
        self.logger.info(f"Resolved to {len(unique_resolved)} unique contracts")
        return unique_resolved
    
    async def resolve_single_contract(self, spec: str) -> List[ResolvedContract]:
        """
        Resolve a single contract specification.
        
        Args:
            spec: Contract specification string
            
        Returns:
            List of resolved contracts (may be multiple for wildcards)
        """
        spec = spec.strip()
        
        if not spec:
            return []
        
        self.logger.debug(f"Resolving contract spec: '{spec}'")
        
        # Continuous contract (@ES)
        if spec.startswith('@'):
            return await self._resolve_continuous_contract(spec)
        
        # Wildcard pattern (*, *.CME, ES*)
        elif '*' in spec:
            return await self._resolve_wildcard_pattern(spec)
        
        # Specific contract (ESU5.CME)
        else:
            return await self._resolve_specific_contract(spec)
    
    async def _resolve_continuous_contract(self, spec: str) -> List[ResolvedContract]:
        """Resolve continuous contract (@ES -> ESU5.CME)."""
        product_code = spec[1:]  # Remove @ prefix
        
        self.logger.info(f"Resolving continuous contract: @{product_code}")
        
        # Try to resolve using client first
        if self.client:
            try:
                resolved_symbol = await self.client.resolve_continuous_contract(product_code)
                if resolved_symbol:
                    return [ResolvedContract(
                        original_spec=spec,
                        resolved_symbol=resolved_symbol,
                        exchange="CME",  # Default exchange
                        product_code=product_code,
                        is_continuous=True,
                        resolution_method="client_api"
                    )]
            except Exception as e:
                self.logger.warning(f"Client resolution failed for @{product_code}: {e}")
        
        # Fallback to cache
        contracts = contract_cache.get_contracts_by_product(product_code)
        if contracts:
            # Sort by symbol to get front month (simplified approach)
            front_month = min(contracts, key=lambda c: c.symbol)
            
            return [ResolvedContract(
                original_spec=spec,
                resolved_symbol=front_month.symbol,
                exchange=front_month.exchange,
                product_code=product_code,
                is_continuous=True,
                resolution_method="cache_lookup"
            )]
        
        self.logger.error(f"Could not resolve continuous contract: {spec}")
        return []
    
    async def _resolve_wildcard_pattern(self, spec: str) -> List[ResolvedContract]:
        """Resolve wildcard pattern (ES*, *.CME, *)."""
        self.logger.info(f"Resolving wildcard pattern: {spec}")
        
        # Parse pattern components
        exchange = None
        product_pattern = None
        
        if '.' in spec:
            # Pattern like "*.CME" or "ES*.CME"
            parts = spec.split('.')
            if len(parts) == 2:
                product_pattern = parts[0]
                exchange = parts[1] if parts[1] != '*' else None
        else:
            # Pattern like "ES*" or "*"
            product_pattern = spec
        
        # Search using cache
        contracts = contract_cache.search_contracts(
            pattern=product_pattern,
            exchange=exchange,
            instrument_type="FUTURE"
        )
        
        # If cache search fails or returns nothing, try client
        if not contracts and self.client:
            try:
                api_contracts = await self.client.search_contracts_by_pattern(spec)
                contracts = [ContractInfo.from_dict(c) for c in api_contracts]
            except Exception as e:
                self.logger.warning(f"Client wildcard search failed for {spec}: {e}")
        
        resolved = []
        for contract in contracts:
            resolved.append(ResolvedContract(
                original_spec=spec,
                resolved_symbol=contract.symbol,
                exchange=contract.exchange,
                product_code=contract.product_code,
                is_wildcard=True,
                resolution_method="wildcard_search"
            ))
        
        self.logger.info(f"Wildcard pattern '{spec}' resolved to {len(resolved)} contracts")
        return resolved
    
    async def _resolve_specific_contract(self, spec: str) -> List[ResolvedContract]:
        """Resolve specific contract (ESU5.CME)."""
        self.logger.debug(f"Resolving specific contract: {spec}")
        
        # Parse contract components
        if '.' in spec:
            symbol, exchange = spec.rsplit('.', 1)
        else:
            symbol = spec
            exchange = "CME"  # Default exchange
        
        # Extract product code (letters only)
        product_code = ''.join(c for c in symbol if c.isalpha())
        
        # Check if contract exists in cache
        contract = contract_cache.get_contract(f"{symbol}.{exchange}")
        if not contract:
            # Try without exchange suffix
            contract = contract_cache.get_contract(symbol)
        
        # If not in cache, try to validate with client
        if not contract and self.client:
            try:
                is_valid = await self.client.validate_symbol(symbol, exchange)
                if is_valid:
                    # Create a basic contract info
                    contract = ContractInfo(
                        symbol=f"{symbol}.{exchange}",
                        exchange=exchange,
                        product_code=product_code,
                        instrument_type="FUTURE"
                    )
            except Exception as e:
                self.logger.warning(f"Client validation failed for {spec}: {e}")
        
        if contract:
            return [ResolvedContract(
                original_spec=spec,
                resolved_symbol=contract.symbol,
                exchange=contract.exchange,
                product_code=contract.product_code,
                is_continuous=False,
                is_wildcard=False,
                resolution_method="specific_lookup"
            )]
        
        # If all else fails, assume it's valid and return as-is
        self.logger.warning(f"Could not validate contract {spec}, assuming valid")
        return [ResolvedContract(
            original_spec=spec,
            resolved_symbol=f"{symbol}.{exchange}",
            exchange=exchange,
            product_code=product_code,
            is_continuous=False,
            is_wildcard=False,
            resolution_method="assumed_valid"
        )]
    
    def get_resolution_summary(self, resolved_contracts: List[ResolvedContract]) -> Dict[str, Any]:
        """Get a summary of contract resolution results."""
        summary = {
            'total_contracts': len(resolved_contracts),
            'by_method': {},
            'by_exchange': {},
            'by_product': {},
            'continuous_contracts': 0,
            'wildcard_contracts': 0,
            'specific_contracts': 0
        }
        
        for contract in resolved_contracts:
            # Count by resolution method
            method = contract.resolution_method
            summary['by_method'][method] = summary['by_method'].get(method, 0) + 1
            
            # Count by exchange
            exchange = contract.exchange
            summary['by_exchange'][exchange] = summary['by_exchange'].get(exchange, 0) + 1
            
            # Count by product
            product = contract.product_code
            summary['by_product'][product] = summary['by_product'].get(product, 0) + 1
            
            # Count by type
            if contract.is_continuous:
                summary['continuous_contracts'] += 1
            elif contract.is_wildcard:
                summary['wildcard_contracts'] += 1
            else:
                summary['specific_contracts'] += 1
        
        return summary


# Global resolver instance
contract_resolver = ContractResolver()