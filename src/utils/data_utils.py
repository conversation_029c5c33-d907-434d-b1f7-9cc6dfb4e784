"""
Data management utilities for Rithmic API client.
Handles data directory management and file operations.
Now integrates with database logging for improved data persistence.
"""

import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import json
import os

# Import database logging infrastructure
try:
    from .database_logger import get_database_logger, LogCategory
    DATABASE_LOGGING_AVAILABLE = True
except ImportError:
    DATABASE_LOGGING_AVAILABLE = False
    print("Warning: Database logging not available, using file-based fallback")

logger = logging.getLogger(__name__)

# Initialize database logger for data utilities
db_logger = None
if DATABASE_LOGGING_AVAILABLE:
    try:
        db_logger = get_database_logger('data_utils')
        db_logger.info("Data utilities initialized with database logging", category=LogCategory.SYSTEM)
    except Exception as e:
        logger.warning(f"Failed to initialize database logging: {e}")
        db_logger = None

# Configuration for logging behavior
USE_DATABASE_LOGGING = os.getenv('ENABLE_DATABASE_LOGGING', 'true').lower() == 'true'
USE_FILE_LOGGING_FALLBACK = os.getenv('ENABLE_FILE_LOGGING_FALLBACK', 'true').lower() == 'true'

def clear_data_directory(data_dir: Path) -> bool:
    """
    Clear the data directory to start fresh.
    
    Args:
        data_dir: Path to data directory
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if data_dir.exists():
            # Count files before deletion
            file_count = len(list(data_dir.glob('*')))
            
            # Remove all contents
            shutil.rmtree(data_dir)
            logger.info(f"Cleared data directory: removed {file_count} files")
        
        # Recreate the directory
        data_dir.mkdir(exist_ok=True)
        logger.info(f"Data directory ready: {data_dir}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to clear data directory: {e}")
        return False

def save_text_data(data_dir: Path, filename: str, content: str) -> Path:
    """
    Save text content to a file in the data directory.
    
    Args:
        data_dir: Path to data directory
        filename: Name of file to save
        content: Text content to save
        
    Returns:
        Path to saved file
    """
    file_path = data_dir / filename
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    except Exception as e:
        logger.error(f"Failed to save text data to {file_path}: {e}")
        raise

def save_json_data(data_dir: Path, filename: str, data: Dict[str, Any]) -> Path:
    """
    Save JSON data to a file in the data directory.
    
    Args:
        data_dir: Path to data directory  
        filename: Name of file to save
        data: Dictionary data to save as JSON
        
    Returns:
        Path to saved file
    """
    file_path = data_dir / filename
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return file_path
    except Exception as e:
        logger.error(f"Failed to save JSON data to {file_path}: {e}")
        raise

def generate_timestamp_filename(prefix: str, suffix: str = ".txt") -> str:
    """
    Generate a timestamped filename.
    
    Args:
        prefix: Filename prefix
        suffix: File extension (default: .txt)
        
    Returns:
        Timestamped filename
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
    return f"{prefix}_{timestamp}{suffix}"

def protobuf_message_to_text(message) -> str:
    """
    Convert a protobuf message to human-readable text format.
    
    Args:
        message: Protobuf message object
        
    Returns:
        Text representation of the message
    """
    try:
        # Get the message as a string representation
        message_str = str(message)
        
        # Add metadata
        message_type = type(message).__name__
        timestamp = datetime.now().isoformat()
        
        text_output = f"""# Rithmic API Message
# Timestamp: {timestamp}
# Message Type: {message_type}
# Raw Message Length: {len(message.SerializeToString())} bytes

{message_str}

# Serialized Data (hex):
{message.SerializeToString().hex()}
"""
        return text_output
        
    except Exception as e:
        logger.error(f"Failed to convert protobuf message to text: {e}")
        return f"Error converting message: {e}"


def append_tx_message(data_dir: Path, message_type: str, template_id: int, message_data: Dict[str, Any], raw_content: str) -> Optional[Path]:
    """
    Log a transmitted message using database logging with optional file fallback.
    
    Args:
        data_dir: Path to data directory (used for file fallback)
        message_type: Message type name (e.g., 'RequestMarketDataUpdate')
        template_id: Template ID of the message
        message_data: Message data to append
        raw_content: Raw text content to append
        
    Returns:
        Path to the file if file logging was used, None if database logging was used
    """
    # Try database logging first
    if USE_DATABASE_LOGGING and db_logger and db_logger.is_enabled():
        try:
            # Extract symbol and exchange from message data if available
            symbol = message_data.get('symbol')
            exchange = message_data.get('exchange')
            
            # Log to database with structured data
            additional_data = {
                'template_id': template_id,
                'message_type': message_type,
                'direction': 'SENT',
                'raw_content': raw_content,
                'message_data': message_data,
                'content_length': len(raw_content)
            }
            
            db_logger.info(
                f"Transmitted message: {message_type}",
                category=LogCategory.API,
                symbol=symbol,
                exchange=exchange,
                **additional_data
            )
            
            logger.debug(f"Logged Tx message {message_type} (template {template_id}) to database")
            return None  # No file path when using database
            
        except Exception as e:
            logger.error(f"Database logging failed for Tx message: {e}")
            # Fall through to file logging if enabled
    
    # File logging fallback (original behavior)
    if USE_FILE_LOGGING_FALLBACK:
        logs_dir = data_dir / "logs" / "Tx"
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        txt_file = logs_dir / f"{message_type}.txt"
        try:
            with open(txt_file, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*80}\n")
                f.write(f"# Message Entry: {datetime.now().isoformat()}\n")
                f.write(f"# Template ID: {template_id}\n")
                f.write(f"{'='*80}\n\n")
                f.write(raw_content)
                f.write('\n')
            
            logger.debug(f"Logged Tx message {message_type} to file fallback: {txt_file}")
            return txt_file
            
        except Exception as e:
            logger.error(f"Failed to append Tx message to {txt_file}: {e}")
            raise
    
    # If both database and file logging are disabled, just log the attempt
    logger.warning(f"No logging mechanism available for Tx message: {message_type}")
    return None

def append_rx_message(data_dir: Path, message_type: str, template_id: int, message_data: Dict[str, Any], raw_content: str) -> Optional[Path]:
    """
    Log a received message using database logging with optional file fallback.
    
    Args:
        data_dir: Path to data directory (used for file fallback)
        message_type: Message type name (e.g., 'LastTrade', 'BestBidOffer')
        template_id: Template ID of the message
        message_data: Message data to append
        raw_content: Raw text content to append
        
    Returns:
        Path to the file if file logging was used, None if database logging was used
    """
    # Try database logging first
    if USE_DATABASE_LOGGING and db_logger and db_logger.is_enabled():
        try:
            # Extract symbol and exchange from message data if available
            symbol = message_data.get('symbol')
            exchange = message_data.get('exchange')
            
            # Determine the appropriate log category based on message type
            category = LogCategory.MARKET_DATA
            if 'order' in message_type.lower() or 'trade' in message_type.lower():
                category = LogCategory.ORDER_MANAGEMENT
            elif 'auth' in message_type.lower() or 'login' in message_type.lower():
                category = LogCategory.AUTHENTICATION
            elif 'search' in message_type.lower():
                category = LogCategory.SEARCH
            
            # Log to database with structured data
            additional_data = {
                'template_id': template_id,
                'message_type': message_type,
                'direction': 'RECEIVED',
                'raw_content': raw_content,
                'message_data': message_data,
                'content_length': len(raw_content)
            }
            
            db_logger.info(
                f"Received message: {message_type}",
                category=category,
                symbol=symbol,
                exchange=exchange,
                **additional_data
            )
            
            logger.debug(f"Logged Rx message {message_type} (template {template_id}) to database")
            return None  # No file path when using database
            
        except Exception as e:
            logger.error(f"Database logging failed for Rx message: {e}")
            # Fall through to file logging if enabled
    
    # File logging fallback (original behavior)
    if USE_FILE_LOGGING_FALLBACK:
        logs_dir = data_dir / "logs" / "Rx"
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Log to .txt file
        txt_file = logs_dir / f"{message_type}.txt"
        try:
            with open(txt_file, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*80}\n")
                f.write(f"# Message Entry: {datetime.now().isoformat()}\n")
                f.write(f"# Template ID: {template_id}\n")
                f.write(f"{'='*80}\n\n")
                f.write(raw_content)
                f.write('\n')
        except Exception as e:
            logger.error(f"Failed to append Rx message to {txt_file}: {e}")
            raise
        
        # Log to .jsonl file
        jsonl_file = logs_dir / f"{message_type}.jsonl"
        try:
            message_entry = {
                "timestamp": datetime.now().isoformat(),
                "template_id": template_id,
                "message_data": message_data
            }
            
            with open(jsonl_file, 'a', encoding='utf-8') as f:
                json.dump(message_entry, f, ensure_ascii=False)
                f.write('\n')
        except Exception as e:
            logger.error(f"Failed to append Rx JSONL message to {jsonl_file}: {e}")
            raise
        
        logger.debug(f"Logged Rx message {message_type} to file fallback: {txt_file}")
        return txt_file
    
    # If both database and file logging are disabled, just log the attempt
    logger.warning(f"No logging mechanism available for Rx message: {message_type}")
    return None


def initialize_message_logging(data_dir: Path) -> None:
    """
    Initialize message logging system (database + optional file fallback).
    
    Args:
        data_dir: Path to data directory (used for file fallback)
    """
    try:
        # Initialize database logging if available
        if USE_DATABASE_LOGGING and db_logger and db_logger.is_enabled():
            db_logger.info(
                "Message logging system initialized with database persistence",
                category=LogCategory.STARTUP,
                data_directory=str(data_dir)
            )
            logger.info("Message logging initialized with database persistence")
        
        # Initialize file logging directories if file fallback is enabled
        if USE_FILE_LOGGING_FALLBACK:
            tx_dir = data_dir / "logs" / "Tx"
            rx_dir = data_dir / "logs" / "Rx"
            tx_dir.mkdir(parents=True, exist_ok=True)
            rx_dir.mkdir(parents=True, exist_ok=True)
            
            if db_logger and db_logger.is_enabled():
                db_logger.info(
                    f"File logging fallback directories created: {tx_dir}, {rx_dir}",
                    category=LogCategory.SYSTEM
                )
            
            logger.info(f"File logging fallback directories: {tx_dir}, {rx_dir}")
        
        # Log configuration status
        config_status = {
            'database_logging': USE_DATABASE_LOGGING and db_logger and db_logger.is_enabled(),
            'file_fallback': USE_FILE_LOGGING_FALLBACK,
            'data_directory': str(data_dir)
        }
        
        if db_logger and db_logger.is_enabled():
            db_logger.info(
                "Message logging configuration initialized",
                category=LogCategory.SYSTEM,
                **config_status
            )
        
        logger.info(f"Message logging initialized: {config_status}")
        
    except Exception as e:
        logger.error(f"Failed to initialize message logging system: {e}")
        if db_logger and db_logger.is_enabled():
            db_logger.error(
                "Failed to initialize message logging system",
                error=e,
                category=LogCategory.ERROR
            )
        raise

def save_cache_file(data_dir: Path, filename: str, content: str) -> Path:
    """
    Save content to a cache file.
    
    Args:
        data_dir: Path to data directory
        filename: Name of cache file to save
        content: Content to save
        
    Returns:
        Path to saved cache file
    """
    cache_dir = data_dir / "cache"
    cache_dir.mkdir(exist_ok=True)
    file_path = cache_dir / filename
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    except Exception as e:
        logger.error(f"Failed to save cache file {file_path}: {e}")
        raise

def read_cache_file(data_dir: Path, filename: str) -> str:
    """
    Read content from a cache file.
    
    Args:
        data_dir: Path to data directory
        filename: Name of cache file to read
        
    Returns:
        Content of the cache file, or None if file doesn't exist
    """
    cache_dir = data_dir / "cache"
    file_path = cache_dir / filename
    
    try:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        return None
    except Exception as e:
        logger.error(f"Failed to read cache file {file_path}: {e}")
        raise

def cache_file_exists(data_dir: Path, filename: str) -> bool:
    """
    Check if a cache file exists.
    
    Args:
        data_dir: Path to data directory
        filename: Name of cache file to check
        
    Returns:
        True if cache file exists, False otherwise
    """
    cache_dir = data_dir / "cache"
    file_path = cache_dir / filename
    return file_path.exists()