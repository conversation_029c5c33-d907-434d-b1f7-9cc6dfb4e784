"""
Data management utilities for Rithmic API client.
Handles data directory management and file operations.
"""

import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
import json

logger = logging.getLogger(__name__)

def clear_data_directory(data_dir: Path) -> bool:
    """
    Clear the data directory to start fresh.
    
    Args:
        data_dir: Path to data directory
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if data_dir.exists():
            # Count files before deletion
            file_count = len(list(data_dir.glob('*')))
            
            # Remove all contents
            shutil.rmtree(data_dir)
            logger.info(f"Cleared data directory: removed {file_count} files")
        
        # Recreate the directory
        data_dir.mkdir(exist_ok=True)
        logger.info(f"Data directory ready: {data_dir}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to clear data directory: {e}")
        return False

def save_text_data(data_dir: Path, filename: str, content: str) -> Path:
    """
    Save text content to a file in the data directory.
    
    Args:
        data_dir: Path to data directory
        filename: Name of file to save
        content: Text content to save
        
    Returns:
        Path to saved file
    """
    file_path = data_dir / filename
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    except Exception as e:
        logger.error(f"Failed to save text data to {file_path}: {e}")
        raise

def save_json_data(data_dir: Path, filename: str, data: Dict[str, Any]) -> Path:
    """
    Save JSON data to a file in the data directory.
    
    Args:
        data_dir: Path to data directory  
        filename: Name of file to save
        data: Dictionary data to save as JSON
        
    Returns:
        Path to saved file
    """
    file_path = data_dir / filename
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return file_path
    except Exception as e:
        logger.error(f"Failed to save JSON data to {file_path}: {e}")
        raise

def generate_timestamp_filename(prefix: str, suffix: str = ".txt") -> str:
    """
    Generate a timestamped filename.
    
    Args:
        prefix: Filename prefix
        suffix: File extension (default: .txt)
        
    Returns:
        Timestamped filename
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
    return f"{prefix}_{timestamp}{suffix}"

def protobuf_message_to_text(message) -> str:
    """
    Convert a protobuf message to human-readable text format.
    
    Args:
        message: Protobuf message object
        
    Returns:
        Text representation of the message
    """
    try:
        # Get the message as a string representation
        message_str = str(message)
        
        # Add metadata
        message_type = type(message).__name__
        timestamp = datetime.now().isoformat()
        
        text_output = f"""# Rithmic API Message
# Timestamp: {timestamp}
# Message Type: {message_type}
# Raw Message Length: {len(message.SerializeToString())} bytes

{message_str}

# Serialized Data (hex):
{message.SerializeToString().hex()}
"""
        return text_output
        
    except Exception as e:
        logger.error(f"Failed to convert protobuf message to text: {e}")
        return f"Error converting message: {e}"


def append_tx_message(data_dir: Path, message_type: str, template_id: int, message_data: Dict[str, Any], raw_content: str) -> Path:
    """
    Log a transmitted message to Tx directory by message type.
    
    Args:
        data_dir: Path to data directory
        message_type: Message type name (e.g., 'RequestMarketDataUpdate')
        template_id: Template ID of the message
        message_data: Message data to append
        raw_content: Raw text content to append
        
    Returns:
        Path to the file
    """
    logs_dir = data_dir / "logs" / "Tx"
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Log to .txt file
    txt_file = logs_dir / f"{message_type}.txt"
    try:
        with open(txt_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"# Message Entry: {datetime.now().isoformat()}\n")
            f.write(f"# Template ID: {template_id}\n")
            f.write(f"{'='*80}\n\n")
            f.write(raw_content)
            f.write('\n')
    except Exception as e:
        logger.error(f"Failed to append Tx message to {txt_file}: {e}")
        raise
        
    return txt_file

def append_rx_message(data_dir: Path, message_type: str, template_id: int, message_data: Dict[str, Any], raw_content: str) -> Path:
    """
    Log a received message to Rx directory by message type.
    
    Args:
        data_dir: Path to data directory
        message_type: Message type name (e.g., 'LastTrade', 'BestBidOffer')
        template_id: Template ID of the message
        message_data: Message data to append
        raw_content: Raw text content to append
        
    Returns:
        Path to the file
    """
    logs_dir = data_dir / "logs" / "Rx"
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Log to .txt file
    txt_file = logs_dir / f"{message_type}.txt"
    try:
        with open(txt_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"# Message Entry: {datetime.now().isoformat()}\n")
            f.write(f"# Template ID: {template_id}\n")
            f.write(f"{'='*80}\n\n")
            f.write(raw_content)
            f.write('\n')
    except Exception as e:
        logger.error(f"Failed to append Rx message to {txt_file}: {e}")
        raise
    
    # Log to .jsonl file
    jsonl_file = logs_dir / f"{message_type}.jsonl"
    try:
        message_entry = {
            "timestamp": datetime.now().isoformat(),
            "template_id": template_id,
            "message_data": message_data
        }
        
        with open(jsonl_file, 'a', encoding='utf-8') as f:
            json.dump(message_entry, f, ensure_ascii=False)
            f.write('\n')
    except Exception as e:
        logger.error(f"Failed to append Rx JSONL message to {jsonl_file}: {e}")
        raise
        
    return txt_file


def initialize_message_logging(data_dir: Path) -> None:
    """
    Initialize message logging directories.
    
    Args:
        data_dir: Path to data directory
    """
    try:
        # Ensure Tx and Rx directories exist
        tx_dir = data_dir / "logs" / "Tx"
        rx_dir = data_dir / "logs" / "Rx"
        tx_dir.mkdir(parents=True, exist_ok=True)
        rx_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized message logging directories: {tx_dir}, {rx_dir}")
        
    except Exception as e:
        logger.error(f"Failed to initialize message logging directories: {e}")
        raise

def save_cache_file(data_dir: Path, filename: str, content: str) -> Path:
    """
    Save content to a cache file.
    
    Args:
        data_dir: Path to data directory
        filename: Name of cache file to save
        content: Content to save
        
    Returns:
        Path to saved cache file
    """
    cache_dir = data_dir / "cache"
    cache_dir.mkdir(exist_ok=True)
    file_path = cache_dir / filename
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    except Exception as e:
        logger.error(f"Failed to save cache file {file_path}: {e}")
        raise

def read_cache_file(data_dir: Path, filename: str) -> str:
    """
    Read content from a cache file.
    
    Args:
        data_dir: Path to data directory
        filename: Name of cache file to read
        
    Returns:
        Content of the cache file, or None if file doesn't exist
    """
    cache_dir = data_dir / "cache"
    file_path = cache_dir / filename
    
    try:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        return None
    except Exception as e:
        logger.error(f"Failed to read cache file {file_path}: {e}")
        raise

def cache_file_exists(data_dir: Path, filename: str) -> bool:
    """
    Check if a cache file exists.
    
    Args:
        data_dir: Path to data directory
        filename: Name of cache file to check
        
    Returns:
        True if cache file exists, False otherwise
    """
    cache_dir = data_dir / "cache"
    file_path = cache_dir / filename
    return file_path.exists()