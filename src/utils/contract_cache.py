#!/usr/bin/env python3
"""
Contract Caching System for Rithmic API

This module provides functionality to cache and manage contract information
from the Rithmic API, including wildcard searches and bulk operations.
"""

import json
import pickle
import logging
from datetime import datetime, timedelta, date
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
import asyncio

# Import database components
try:
    from ..database.database_manager import get_database_manager
    from ..database.models import Symbol, SymbolDAO, get_database_stats
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    # Create dummy Symbol class for type hints when database not available
    class Symbol:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)


@dataclass
class ContractInfo:
    """Data class to represent contract information."""
    symbol: str
    symbol_name: str = ""
    exchange: str = ""
    product_code: str = ""
    instrument_type: str = ""
    expiration_date: str = ""
    strike_price: str = ""
    put_call_indicator: str = ""
    cached_at: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContractInfo':
        """Create from dictionary."""
        return cls(**data)


class ContractCache:
    """
    Manages caching of contract information with support for:
    - Bulk contract caching
    - Wildcard pattern matching
    - Cache expiration and refresh
    - JSON and binary serialization
    """
    
    def __init__(self, cache_dir: str = "data/cache", ttl_hours: int = 24, use_database: bool = True):
        """
        Initialize contract cache.
        
        Args:
            cache_dir: Directory to store cache files (fallback when database not available)
            ttl_hours: Time-to-live for cache entries in hours
            use_database: Whether to use database backend (defaults to True)
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.ttl_hours = ttl_hours
        self.use_database = use_database and DATABASE_AVAILABLE
        self.logger = logging.getLogger(__name__)
        
        # Database components
        self.db_manager = None
        self.symbol_dao = None
        
        # In-memory cache
        self._contracts: Dict[str, ContractInfo] = {}
        self._contracts_by_exchange: Dict[str, Set[str]] = {}
        self._contracts_by_product: Dict[str, Set[str]] = {}
        self._last_updated: Optional[datetime] = None
        
        # Cache file paths (fallback storage)
        self.contracts_json_file = self.cache_dir / "contracts.json"
        self.contracts_pickle_file = self.cache_dir / "contracts.pkl"
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        
        # Initialize storage backend
        if self.use_database:
            self._init_database()
        else:
            self.logger.warning("Database not available, falling back to file-based cache")
        
        # Load existing cache
        self._load_cache()
    
    def _init_database(self):
        """Initialize database connection."""
        try:
            self.db_manager = get_database_manager()
            self.symbol_dao = SymbolDAO()
            
            # Ensure tables exist
            self.symbol_dao.create_table()
            
            self.logger.info("Database backend initialized for contract cache")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database backend: {e}")
            self.use_database = False
    
    def _load_cache(self):
        """Load cache from database or disk."""
        try:
            # Load from database if available
            if self.use_database and self.symbol_dao:
                self._load_from_database()
                return
                
            # Fallback to file-based loading
            # Try to load from pickle first (faster)
            if self.contracts_pickle_file.exists():
                with open(self.contracts_pickle_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    self._contracts = cache_data.get('contracts', {})
                    self._contracts_by_exchange = cache_data.get('by_exchange', {})
                    self._contracts_by_product = cache_data.get('by_product', {})
                    self._last_updated = cache_data.get('last_updated')
                    
                self.logger.info(f"Loaded {len(self._contracts)} contracts from pickle cache")
                return
                
            # Fallback to JSON
            elif self.contracts_json_file.exists():
                with open(self.contracts_json_file, 'r') as f:
                    cache_data = json.load(f)
                    
                # Convert dictionaries back to ContractInfo objects
                self._contracts = {
                    symbol: ContractInfo.from_dict(data) 
                    for symbol, data in cache_data.get('contracts', {}).items()
                }
                self._rebuild_indices()
                
                # Load metadata
                if self.metadata_file.exists():
                    with open(self.metadata_file, 'r') as f:
                        metadata = json.load(f)
                        if 'last_updated' in metadata:
                            self._last_updated = datetime.fromisoformat(metadata['last_updated'])
                
                self.logger.info(f"Loaded {len(self._contracts)} contracts from JSON cache")
                return
                
        except Exception as e:
            self.logger.error(f"Error loading cache: {e}")
            
        # Initialize empty cache if loading failed
        self._contracts = {}
        self._contracts_by_exchange = {}
        self._contracts_by_product = {}
        self._last_updated = None
    
    def _save_cache(self):
        """Save cache to database or disk."""
        try:
            # Save to database if available
            if self.use_database:
                self._save_to_database()
                return
                
            # Fallback to file-based saving
            # Ensure cache directory exists
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            
            # Save as pickle (faster loading)
            cache_data = {
                'contracts': self._contracts,
                'by_exchange': self._contracts_by_exchange,
                'by_product': self._contracts_by_product,
                'last_updated': self._last_updated
            }
            
            with open(self.contracts_pickle_file, 'wb') as f:
                pickle.dump(cache_data, f)
                
            # Save as JSON (human readable)
            json_data = {
                'contracts': {
                    symbol: contract.to_dict() 
                    for symbol, contract in self._contracts.items()
                }
            }
            
            with open(self.contracts_json_file, 'w') as f:
                json.dump(json_data, f, indent=2)
                
            # Save metadata
            metadata = {
                'last_updated': self._last_updated.isoformat() if self._last_updated else None,
                'total_contracts': len(self._contracts),
                'exchanges': list(self._contracts_by_exchange.keys()),
                'products': list(self._contracts_by_product.keys())
            }
            
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
                
            self.logger.info(f"Saved {len(self._contracts)} contracts to cache")
            
        except Exception as e:
            self.logger.error(f"Error saving cache: {e}")
    
    def _rebuild_indices(self):
        """Rebuild internal indices for fast lookups."""
        self._contracts_by_exchange = {}
        self._contracts_by_product = {}
        
        for symbol, contract in self._contracts.items():
            # Index by exchange
            if contract.exchange:
                if contract.exchange not in self._contracts_by_exchange:
                    self._contracts_by_exchange[contract.exchange] = set()
                self._contracts_by_exchange[contract.exchange].add(symbol)
            
            # Index by product code
            if contract.product_code:
                if contract.product_code not in self._contracts_by_product:
                    self._contracts_by_product[contract.product_code] = set()
                self._contracts_by_product[contract.product_code].add(symbol)
    
    def _load_from_database(self):
        """Load contracts from database."""
        try:
            if not self.symbol_dao:
                return
                
            # Get all symbols from database
            exchanges = self.symbol_dao.get_all_exchanges()
            total_loaded = 0
            
            for exchange in exchanges:
                symbols = self.symbol_dao.find_by_exchange(exchange)
                for symbol in symbols:
                    contract_info = self._symbol_to_contract_info(symbol)
                    self._contracts[f"{symbol.symbol}@{symbol.exchange}"] = contract_info
                    total_loaded += 1
            
            # Rebuild indices
            self._rebuild_indices()
            self._last_updated = datetime.now()
            
            self.logger.info(f"Loaded {total_loaded} contracts from database")
            
        except Exception as e:
            self.logger.error(f"Error loading from database: {e}")
    
    def _save_to_database(self):
        """Save contracts to database."""
        try:
            if not self.symbol_dao:
                return
                
            symbols_to_save = []
            for contract in self._contracts.values():
                symbol = self._contract_info_to_symbol(contract)
                symbols_to_save.append(symbol)
            
            if symbols_to_save:
                # Use bulk insert for efficiency
                rows_inserted = self.symbol_dao.bulk_insert(symbols_to_save)
                self.logger.info(f"Saved {rows_inserted} contracts to database")
                
        except Exception as e:
            self.logger.error(f"Error saving to database: {e}")
    
    def _symbol_to_contract_info(self, symbol: Symbol) -> ContractInfo:
        """Convert Symbol model to ContractInfo."""
        return ContractInfo(
            symbol=symbol.symbol,
            symbol_name=symbol.symbol_name or "",
            exchange=symbol.exchange,
            product_code=symbol.product_code or "",
            instrument_type=symbol.instrument_type or "",
            expiration_date=symbol.expiration_date.strftime('%Y%m%d') if symbol.expiration_date else "",
            strike_price=str(symbol.strike_price) if symbol.strike_price else "",
            put_call_indicator=symbol.put_call_indicator or "",
            cached_at=symbol.created_at.isoformat() if symbol.created_at else datetime.now().isoformat()
        )
    
    def _contract_info_to_symbol(self, contract: ContractInfo) -> Symbol:
        """Convert ContractInfo to Symbol model."""
        symbol = Symbol(
            symbol=contract.symbol,
            symbol_name=contract.symbol_name,
            exchange=contract.exchange,
            product_code=contract.product_code,
            instrument_type=contract.instrument_type,
            put_call_indicator=contract.put_call_indicator if contract.put_call_indicator in ['PUT', 'CALL'] else None,
            raw_response={'contract_info': contract.to_dict()}
        )
        
        # Parse expiration date
        if contract.expiration_date:
            try:
                symbol.expiration_date = datetime.strptime(contract.expiration_date, '%Y%m%d').date()
            except:
                pass
        
        # Parse strike price
        if contract.strike_price:
            try:
                from decimal import Decimal
                symbol.strike_price = Decimal(contract.strike_price)
            except:
                pass
        
        return symbol
    
    def is_cache_expired(self) -> bool:
        """Check if cache is expired based on TTL."""
        if not self._last_updated:
            return True
            
        return datetime.now() - self._last_updated > timedelta(hours=self.ttl_hours)
    
    def add_contracts(self, contracts: List[Dict[str, Any]]):
        """
        Add multiple contracts to cache.
        
        Args:
            contracts: List of contract dictionaries from API
        """
        current_time = datetime.now().isoformat()
        
        for contract_data in contracts:
            # Add timestamp
            contract_data['cached_at'] = current_time
            
            # Create ContractInfo object
            contract = ContractInfo.from_dict(contract_data)
            
            # Add to main cache
            self._contracts[contract.symbol] = contract
            
            # Update indices
            if contract.exchange:
                if contract.exchange not in self._contracts_by_exchange:
                    self._contracts_by_exchange[contract.exchange] = set()
                self._contracts_by_exchange[contract.exchange].add(contract.symbol)
            
            if contract.product_code:
                if contract.product_code not in self._contracts_by_product:
                    self._contracts_by_product[contract.product_code] = set()
                self._contracts_by_product[contract.product_code].add(contract.symbol)
        
        self._last_updated = datetime.now()
        self._save_cache()
        
        self.logger.info(f"Added {len(contracts)} contracts to cache")
    
    def get_contract(self, symbol: str) -> Optional[ContractInfo]:
        """Get contract by symbol."""
        return self._contracts.get(symbol)
    
    def get_contracts_by_exchange(self, exchange: str) -> List[ContractInfo]:
        """Get all contracts for a specific exchange."""
        if exchange not in self._contracts_by_exchange:
            return []
            
        symbols = self._contracts_by_exchange[exchange]
        return [self._contracts[symbol] for symbol in symbols]
    
    def get_contracts_by_product(self, product_code: str) -> List[ContractInfo]:
        """Get all contracts for a specific product code."""
        if product_code not in self._contracts_by_product:
            return []
            
        symbols = self._contracts_by_product[product_code]
        return [self._contracts[symbol] for symbol in symbols]
    
    def search_contracts(self, 
                        pattern: str = None,
                        exchange: str = None,
                        product_code: str = None,
                        instrument_type: str = None) -> List[ContractInfo]:
        """
        Search contracts with various filters.
        
        Args:
            pattern: Wildcard pattern for symbol matching
            exchange: Filter by exchange
            product_code: Filter by product code
            instrument_type: Filter by instrument type
            
        Returns:
            List of matching contracts
        """
        results = []
        
        for symbol, contract in self._contracts.items():
            # Apply filters
            if exchange and contract.exchange != exchange:
                continue
            if product_code and contract.product_code != product_code:
                continue
            if instrument_type and contract.instrument_type != instrument_type:
                continue
                
            # Apply pattern matching
            if pattern:
                if not self._matches_pattern(symbol, pattern):
                    continue
            
            results.append(contract)
        
        return results
    
    def _matches_pattern(self, symbol: str, pattern: str) -> bool:
        """Check if symbol matches wildcard pattern."""
        import re
        
        # Convert wildcard pattern to regex
        # Replace * with .*, ? with ., and escape other special characters
        regex_pattern = pattern.replace('*', '.*').replace('?', '.')
        regex_pattern = f'^{regex_pattern}$'
        
        try:
            return bool(re.match(regex_pattern, symbol, re.IGNORECASE))
        except re.error:
            # If regex is invalid, fall back to simple string matching
            return pattern.lower() in symbol.lower()
    
    def get_all_contracts(self) -> List[ContractInfo]:
        """Get all cached contracts."""
        return list(self._contracts.values())
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            'total_contracts': len(self._contracts),
            'exchanges': list(self._contracts_by_exchange.keys()),
            'products': list(self._contracts_by_product.keys()),
            'last_updated': self._last_updated.isoformat() if self._last_updated else None,
            'is_expired': self.is_cache_expired(),
            'cache_files': {
                'json_exists': self.contracts_json_file.exists(),
                'pickle_exists': self.contracts_pickle_file.exists(),
                'metadata_exists': self.metadata_file.exists()
            }
        }
    
    def clear_cache(self):
        """Clear all cache data."""
        self._contracts = {}
        self._contracts_by_exchange = {}
        self._contracts_by_product = {}
        self._last_updated = None
        
        # Remove cache files
        for cache_file in [self.contracts_json_file, self.contracts_pickle_file, self.metadata_file]:
            if cache_file.exists():
                cache_file.unlink()
                
        self.logger.info("Cache cleared")
    
    async def refresh_from_client(self, client):
        """
        Refresh cache using data from RithmicWebSocketClient.
        
        Args:
            client: RithmicWebSocketClient instance (must be authenticated)
        """
        self.logger.info("Refreshing contract cache from API...")
        
        try:
            # Get all contracts using the improved alphanumeric search
            all_contracts = await client.get_all_contracts()
            
            if all_contracts:
                # Clear existing cache
                self.clear_cache()
                
                # Add new contracts
                self.add_contracts(all_contracts)
                
                self.logger.info(f"Cache refreshed with {len(all_contracts)} contracts")
                return True
            else:
                self.logger.warning("No contracts received from API")
                return False
                
        except Exception as e:
            self.logger.error(f"Error refreshing cache: {e}")
            return False
    
    async def refresh_comprehensive_data(self, client, exchanges: List[str] = None):
        """
        Refresh cache with comprehensive reference data from all available sources.
        
        Args:
            client: RithmicWebSocketClient instance (must be authenticated)
            exchanges: Optional list of exchanges to focus on
        """
        self.logger.info("Refreshing cache with comprehensive reference data...")
        
        try:
            # Get comprehensive reference data
            reference_data = await client.get_comprehensive_reference_data(exchanges)
            
            if reference_data and reference_data.get('symbols'):
                # Clear existing cache
                self.clear_cache()
                
                # Convert symbols dict to list for add_contracts
                all_contracts = list(reference_data['symbols'].values())
                
                # Add new contracts
                self.add_contracts(all_contracts)
                
                # Save additional reference data
                self._save_reference_data(reference_data)
                
                stats = reference_data.get('statistics', {})
                self.logger.info(f"Cache refreshed with comprehensive data:")
                self.logger.info(f"  - {stats.get('total_contracts', 0)} contracts")
                self.logger.info(f"  - {stats.get('total_product_codes', 0)} product codes")
                self.logger.info(f"  - {stats.get('total_exchanges', 0)} exchanges")
                
                return True
            else:
                self.logger.warning("No reference data received from API")
                return False
                
        except Exception as e:
            self.logger.error(f"Error refreshing comprehensive cache: {e}")
            return False
    
    def _save_reference_data(self, reference_data: Dict[str, Any]):
        """Save additional reference data to separate files."""
        try:
            # Save reference data summary
            ref_data_file = self.cache_dir / "reference_data.json"
            with open(ref_data_file, 'w') as f:
                json.dump(reference_data, f, indent=2)
            
            # Save product codes separately for quick access
            if 'product_codes' in reference_data:
                product_codes_file = self.cache_dir / "product_codes.json"
                with open(product_codes_file, 'w') as f:
                    json.dump(reference_data['product_codes'], f, indent=2)
            
            # Save exchange statistics
            if 'exchanges' in reference_data:
                exchanges_file = self.cache_dir / "exchanges_summary.json"
                exchange_summary = {
                    exchange: {
                        'total_symbols': data['statistics']['total_symbols'],
                        'total_product_codes': data['statistics']['total_product_codes'],
                        'total_contracts': data['statistics']['total_contracts'],
                        'product_codes': data['product_codes']
                    }
                    for exchange, data in reference_data['exchanges'].items()
                }
                with open(exchanges_file, 'w') as f:
                    json.dump(exchange_summary, f, indent=2)
                    
            self.logger.info("Additional reference data saved to cache directory")
            
        except Exception as e:
            self.logger.error(f"Error saving reference data: {e}")
    
    def get_product_codes(self) -> List[str]:
        """Get all available product codes from cache."""
        try:
            product_codes_file = self.cache_dir / "product_codes.json"
            if product_codes_file.exists():
                with open(product_codes_file, 'r') as f:
                    data = json.load(f)
                    return data.get('all_codes', [])
        except Exception as e:
            self.logger.error(f"Error loading product codes: {e}")
        
        # Fallback: extract from cached contracts
        product_codes = set()
        for contract in self._contracts.values():
            if contract.product_code:
                product_codes.add(contract.product_code)
        return sorted(list(product_codes))
    
    def get_exchanges(self) -> List[str]:
        """Get all available exchanges from cache."""
        try:
            exchanges_file = self.cache_dir / "exchanges_summary.json"
            if exchanges_file.exists():
                with open(exchanges_file, 'r') as f:
                    data = json.load(f)
                    return list(data.keys())
        except Exception as e:
            self.logger.error(f"Error loading exchanges: {e}")
        
        # Fallback: extract from cached contracts
        return list(self._contracts_by_exchange.keys())
    
    def get_reference_data_summary(self) -> Dict[str, Any]:
        """Get comprehensive reference data summary if available."""
        try:
            ref_data_file = self.cache_dir / "reference_data.json"
            if ref_data_file.exists():
                with open(ref_data_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading reference data: {e}")
        
        return {}


# Global cache instance
contract_cache = ContractCache()