#!/usr/bin/env python3
"""
Database Logger Utility

Provides centralized database logging functionality to replace file-based logging
throughout the Rithmic API project. This module offers a standardized interface
for logging system events, errors, and performance metrics to the MySQL database.

Features:
- Centralized database logging interface
- Structured logging with categorization
- Performance metrics tracking
- Error logging with stack traces
- System event logging
- Session management
- Thread-safe operations

Usage:
    from src.utils.database_logger import get_database_logger, LogLevel, LogCategory
    
    logger = get_database_logger('my_component')
    logger.info('Application started', category=LogCategory.SYSTEM)
    logger.error('Connection failed', category=LogCategory.NETWORK, error=e)
    logger.performance('Processing time', duration_ms=1250, data_size=1024)
"""

import os
import sys
import logging
import traceback
import threading
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Union
from enum import Enum
import uuid

# Add project root to path for database imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from src.database.database_manager import get_database_manager, DatabaseManager
except ImportError as e:
    print(f"Warning: Could not import database modules: {e}")
    print("Database logging functionality will not be available.")
    get_database_manager = None
    DatabaseManager = None


class LogLevel(Enum):
    """Standard logging levels for database logging."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(Enum):
    """Categories for organizing database log entries."""
    SYSTEM = "SYSTEM"
    NETWORK = "NETWORK"
    DATABASE = "DATABASE"
    AUTHENTICATION = "AUTHENTICATION"
    MARKET_DATA = "MARKET_DATA"
    ORDER_MANAGEMENT = "ORDER_MANAGEMENT"
    HISTORICAL_DATA = "HISTORICAL_DATA"
    SEARCH = "SEARCH"
    GUI = "GUI"
    API = "API"
    PERFORMANCE = "PERFORMANCE"
    STARTUP = "STARTUP"
    SHUTDOWN = "SHUTDOWN"
    ERROR = "ERROR"


class DatabaseLogger:
    """
    Database-backed logger for structured logging and analytics.
    
    Provides methods for logging system events, errors, and performance metrics
    to the MySQL database instead of files. Thread-safe and session-aware.
    """
    
    def __init__(self, component_name: str, session_id: str = None):
        """
        Initialize database logger for a specific component.
        
        Args:
            component_name: Name of the component using this logger
            session_id: Unique session identifier (auto-generated if not provided)
        """
        self.component_name = component_name
        self.session_id = session_id or f"{component_name}-{uuid.uuid4().hex[:8]}"
        self.enabled = get_database_manager is not None
        self.db_manager = None
        self._lock = threading.Lock()
        
        # Set up fallback console logger
        self.console_logger = logging.getLogger(f"DatabaseLogger.{component_name}")
        self.console_logger.setLevel(logging.INFO)
        
        if not self.console_logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.console_logger.addHandler(handler)
        
        if self.enabled:
            try:
                self.db_manager = get_database_manager()
                self._log_startup()
            except Exception as e:
                self.console_logger.warning(f"Database initialization failed: {e}")
                self.enabled = False
        else:
            self.console_logger.info("Database logging disabled - using console fallback")
    
    def _log_startup(self):
        """Log component startup event."""
        try:
            self.info(
                f"Component {self.component_name} initialized with database logging",
                category=LogCategory.STARTUP
            )
        except Exception as e:
            self.console_logger.error(f"Failed to log startup event: {e}")
    
    def is_enabled(self) -> bool:
        """Check if database logging is enabled."""
        return self.enabled
    
    def _insert_log_entry(self, level: LogLevel, message: str, category: LogCategory,
                         additional_data: Dict[str, Any] = None, error: Exception = None):
        """
        Insert a log entry into the database.
        
        Args:
            level: Log level
            message: Log message
            category: Log category
            additional_data: Additional structured data
            error: Exception object for error logging
        """
        if not self.enabled:
            # Fallback to console logging
            console_level = getattr(logging, level.value, logging.INFO)
            self.console_logger.log(console_level, f"[{category.value}] {message}")
            if error:
                self.console_logger.error(f"Error details: {str(error)}")
            return
        
        try:
            with self._lock:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Prepare additional data with error information
                    log_data = additional_data.copy() if additional_data else {}
                    if error:
                        log_data.update({
                            'error_type': type(error).__name__,
                            'error_message': str(error),
                            'stack_trace': traceback.format_exc() if level == LogLevel.ERROR else None
                        })
                    
                    # Add component and session context
                    log_data.update({
                        'component': self.component_name,
                        'session_id': self.session_id,
                        'log_level': level.value,
                        'log_category': category.value
                    })
                    
                    insert_query = """
                        INSERT INTO message_log (
                            template_id, message_type, direction, symbol, exchange,
                            session_id, message_timestamp, parsed_data
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    cursor.execute(insert_query, (
                        0,  # template_id (0 for log entries)
                        level.value,  # message_type (log level)
                        'INTERNAL',  # direction (internal log)
                        log_data.get('symbol'),  # symbol (if applicable)
                        log_data.get('exchange'),  # exchange (if applicable)
                        self.session_id,
                        datetime.now(timezone.utc),
                        self._serialize_data(log_data)
                    ))
                    
                    conn.commit()
                    
        except Exception as e:
            # Fallback to console logging if database fails
            self.console_logger.error(f"Database logging failed: {e}")
            self.console_logger.log(
                getattr(logging, level.value, logging.INFO),
                f"[{category.value}] {message}"
            )
            if error:
                self.console_logger.error(f"Original error: {str(error)}")
    
    def _serialize_data(self, data: Dict[str, Any]) -> str:
        """Serialize data for database storage."""
        try:
            import json
            return json.dumps(data, default=str, ensure_ascii=False)
        except Exception:
            return str(data)
    
    def debug(self, message: str, category: LogCategory = LogCategory.SYSTEM,
             symbol: str = None, exchange: str = None, **kwargs):
        """Log debug message."""
        additional_data = kwargs.copy()
        if symbol:
            additional_data['symbol'] = symbol
        if exchange:
            additional_data['exchange'] = exchange
        
        self._insert_log_entry(LogLevel.DEBUG, message, category, additional_data)
    
    def info(self, message: str, category: LogCategory = LogCategory.SYSTEM,
            symbol: str = None, exchange: str = None, **kwargs):
        """Log info message."""
        additional_data = kwargs.copy()
        if symbol:
            additional_data['symbol'] = symbol
        if exchange:
            additional_data['exchange'] = exchange
        
        self._insert_log_entry(LogLevel.INFO, message, category, additional_data)
    
    def warning(self, message: str, category: LogCategory = LogCategory.SYSTEM,
               symbol: str = None, exchange: str = None, **kwargs):
        """Log warning message."""
        additional_data = kwargs.copy()
        if symbol:
            additional_data['symbol'] = symbol
        if exchange:
            additional_data['exchange'] = exchange
        
        self._insert_log_entry(LogLevel.WARNING, message, category, additional_data)
    
    def error(self, message: str, error: Exception = None, category: LogCategory = LogCategory.ERROR,
             symbol: str = None, exchange: str = None, **kwargs):
        """Log error message with optional exception details."""
        additional_data = kwargs.copy()
        if symbol:
            additional_data['symbol'] = symbol
        if exchange:
            additional_data['exchange'] = exchange
        
        self._insert_log_entry(LogLevel.ERROR, message, category, additional_data, error)
    
    def critical(self, message: str, error: Exception = None, category: LogCategory = LogCategory.ERROR,
                symbol: str = None, exchange: str = None, **kwargs):
        """Log critical error message."""
        additional_data = kwargs.copy()
        if symbol:
            additional_data['symbol'] = symbol
        if exchange:
            additional_data['exchange'] = exchange
        
        self._insert_log_entry(LogLevel.CRITICAL, message, category, additional_data, error)
    
    def performance(self, operation: str, duration_ms: float = None, data_size: int = None,
                   symbol: str = None, exchange: str = None, **kwargs):
        """Log performance metrics."""
        additional_data = kwargs.copy()
        additional_data.update({
            'operation': operation,
            'duration_ms': duration_ms,
            'data_size': data_size
        })
        if symbol:
            additional_data['symbol'] = symbol
        if exchange:
            additional_data['exchange'] = exchange
        
        message = f"Performance: {operation}"
        if duration_ms is not None:
            message += f" took {duration_ms:.2f}ms"
        if data_size is not None:
            message += f" (processed {data_size} bytes)"
        
        self._insert_log_entry(LogLevel.INFO, message, LogCategory.PERFORMANCE, additional_data)
    
    def market_data(self, message: str, symbol: str, exchange: str, data_type: str = None, **kwargs):
        """Log market data events."""
        additional_data = kwargs.copy()
        additional_data.update({
            'symbol': symbol,
            'exchange': exchange,
            'data_type': data_type
        })
        
        self._insert_log_entry(LogLevel.INFO, message, LogCategory.MARKET_DATA, additional_data)
    
    def network(self, message: str, url: str = None, status_code: int = None, **kwargs):
        """Log network operations."""
        additional_data = kwargs.copy()
        if url:
            additional_data['url'] = url
        if status_code:
            additional_data['status_code'] = status_code
        
        self._insert_log_entry(LogLevel.INFO, message, LogCategory.NETWORK, additional_data)
    
    def authentication(self, message: str, user: str = None, system: str = None, success: bool = None, **kwargs):
        """Log authentication events."""
        additional_data = kwargs.copy()
        additional_data.update({
            'user': user,
            'system': system,
            'success': success
        })
        
        level = LogLevel.INFO if success is not False else LogLevel.WARNING
        self._insert_log_entry(level, message, LogCategory.AUTHENTICATION, additional_data)
    
    def shutdown(self):
        """Log component shutdown and cleanup."""
        try:
            self.info(
                f"Component {self.component_name} shutting down",
                category=LogCategory.SHUTDOWN
            )
        except Exception as e:
            self.console_logger.error(f"Failed to log shutdown event: {e}")


# Global logger instances registry
_logger_instances = {}
_logger_lock = threading.Lock()


def get_database_logger(component_name: str, session_id: str = None) -> DatabaseLogger:
    """
    Get or create a database logger instance for a component.
    
    Args:
        component_name: Name of the component
        session_id: Unique session identifier (optional)
    
    Returns:
        DatabaseLogger instance
    """
    global _logger_instances, _logger_lock
    
    with _logger_lock:
        key = f"{component_name}_{session_id or 'default'}"
        
        if key not in _logger_instances:
            _logger_instances[key] = DatabaseLogger(component_name, session_id)
        
        return _logger_instances[key]


def create_file_replacement_logger(component_name: str, log_file_path: str = None) -> DatabaseLogger:
    """
    Create a database logger specifically for replacing file-based logging.
    
    Args:
        component_name: Name of the component
        log_file_path: Original log file path (for reference/migration)
    
    Returns:
        DatabaseLogger instance configured for file replacement
    """
    logger = get_database_logger(component_name)
    
    if log_file_path:
        logger.info(
            f"Migrated from file-based logging to database logging",
            category=LogCategory.SYSTEM,
            original_log_file=log_file_path
        )
    
    return logger


# Convenience function for quick migration from file logging
def replace_file_logger(component_name: str, original_log_path: str = None) -> DatabaseLogger:
    """
    Quick replacement function for migrating from file-based to database logging.
    
    Args:
        component_name: Component name
        original_log_path: Path to original log file being replaced
    
    Returns:
        DatabaseLogger instance
    """
    return create_file_replacement_logger(component_name, original_log_path)


# Context manager for operation logging
class LoggedOperation:
    """Context manager for logging operations with automatic timing."""
    
    def __init__(self, logger: DatabaseLogger, operation_name: str, 
                 category: LogCategory = LogCategory.SYSTEM, **kwargs):
        self.logger = logger
        self.operation_name = operation_name
        self.category = category
        self.kwargs = kwargs
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(
            f"Starting operation: {self.operation_name}",
            category=self.category,
            **self.kwargs
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds() * 1000
        
        if exc_type is None:
            self.logger.performance(
                self.operation_name,
                duration_ms=duration,
                **self.kwargs
            )
        else:
            self.logger.error(
                f"Operation failed: {self.operation_name}",
                error=exc_val,
                category=LogCategory.ERROR,
                duration_ms=duration,
                **self.kwargs
            )