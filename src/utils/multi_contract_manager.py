#!/usr/bin/env python3
"""
Multi-Contract Subscription Manager

This module provides functionality to manage subscriptions to multiple contracts
simultaneously, including contract resolution, validation, batching, and
subscription management.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Callable, Set
from dataclasses import dataclass
from datetime import datetime

from .contract_resolver import ContractResolver, ResolvedContract
from .contract_cache import contract_cache
from ..rithmic_api.config import config


@dataclass
class SubscriptionResult:
    """Result of a contract subscription attempt."""
    contract: ResolvedContract
    success: bool
    error_message: Optional[str] = None
    subscription_time: Optional[datetime] = None


class MultiContractManager:
    """
    Manages multiple contract subscriptions with support for:
    - Contract resolution and validation
    - Batched subscriptions with rate limiting
    - Error handling and retry logic
    - Subscription status tracking
    """
    
    def __init__(self, client, resolver: Optional[ContractResolver] = None):
        """
        Initialize multi-contract manager.
        
        Args:
            client: RithmicWebSocketClient instance
            resolver: Contract resolver (creates new one if None)
        """
        self.client = client
        self.resolver = resolver or ContractResolver(client)
        self.logger = logging.getLogger(__name__)
        
        # Subscription tracking
        self.active_subscriptions: Set[str] = set()
        self.subscription_results: List[SubscriptionResult] = []
        self.subscription_callbacks: Dict[str, List[Callable]] = {}
        
        # Configuration
        self.batch_size = config.batch_subscription_size
        self.delay_ms = config.subscription_delay_ms
        self.max_contracts = config.max_concurrent_contracts
        self.validate_before_subscribe = config.validate_contracts_before_subscribe
        self.continue_on_error = config.continue_on_invalid_contract
    
    async def subscribe_to_configured_contracts(self) -> List[SubscriptionResult]:
        """
        Subscribe to contracts specified in configuration.
        
        Returns:
            List of subscription results
        """
        contract_specs = config.contracts_to_subscribe
        self.logger.info(f"Subscribing to configured contracts: {contract_specs}")
        
        return await self.subscribe_to_contracts(contract_specs)
    
    async def subscribe_to_contracts(self, contract_specs: List[str]) -> List[SubscriptionResult]:
        """
        Subscribe to a list of contract specifications.
        
        Args:
            contract_specs: List of contract specifications to subscribe to
            
        Returns:
            List of subscription results
        """
        self.logger.info(f"Starting subscription to {len(contract_specs)} contract specifications")
        
        # Step 1: Resolve contract specifications
        self.logger.info("=== STEP 1: RESOLVING CONTRACTS ===")
        resolved_contracts = await self.resolver.resolve_contract_list(
            contract_specs, 
            max_contracts=self.max_contracts
        )
        
        if not resolved_contracts:
            self.logger.error("No contracts resolved. Cannot proceed with subscriptions.")
            return []
        
        # Log resolution summary
        summary = self.resolver.get_resolution_summary(resolved_contracts)
        self.logger.info(f"Resolution summary: {summary}")
        
        # Step 2: Validate contracts (if enabled)
        if self.validate_before_subscribe:
            self.logger.info("=== STEP 2: VALIDATING CONTRACTS ===")
            resolved_contracts = await self._validate_contracts(resolved_contracts)
        
        # Step 3: Subscribe to contracts
        self.logger.info("=== STEP 3: SUBSCRIBING TO CONTRACTS ===")
        results = await self._subscribe_to_resolved_contracts(resolved_contracts)
        
        # Step 4: Report results
        self._log_subscription_results(results)
        
        return results
    
    async def _validate_contracts(self, resolved_contracts: List[ResolvedContract]) -> List[ResolvedContract]:
        """Validate resolved contracts before subscription."""
        valid_contracts = []
        
        for contract in resolved_contracts:
            try:
                # Skip validation for contracts resolved from cache or API
                if contract.resolution_method in ['client_api', 'cache_lookup', 'wildcard_search']:
                    valid_contracts.append(contract)
                    continue
                
                # Validate using client
                is_valid = await self.client.validate_symbol(
                    contract.resolved_symbol.split('.')[0],  # Remove exchange suffix
                    contract.exchange
                )
                
                if is_valid:
                    valid_contracts.append(contract)
                    self.logger.info(f"✅ Contract validated: {contract.resolved_symbol}")
                else:
                    self.logger.warning(f"❌ Contract invalid: {contract.resolved_symbol}")
                    if not self.continue_on_error:
                        break
                        
            except Exception as e:
                self.logger.error(f"Error validating {contract.resolved_symbol}: {e}")
                if not self.continue_on_error:
                    break
        
        self.logger.info(f"Validated {len(valid_contracts)} out of {len(resolved_contracts)} contracts")
        return valid_contracts
    
    async def _subscribe_to_resolved_contracts(self, resolved_contracts: List[ResolvedContract]) -> List[SubscriptionResult]:
        """Subscribe to resolved and validated contracts."""
        results = []
        
        # Process contracts in batches
        for i in range(0, len(resolved_contracts), self.batch_size):
            batch = resolved_contracts[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (len(resolved_contracts) + self.batch_size - 1) // self.batch_size
            
            self.logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} contracts)")
            
            batch_results = await self._subscribe_batch(batch)
            results.extend(batch_results)
            
            # Add delay between batches (except for last batch)
            if i + self.batch_size < len(resolved_contracts):
                await asyncio.sleep(self.delay_ms / 1000.0)
        
        return results
    
    async def _subscribe_batch(self, batch: List[ResolvedContract]) -> List[SubscriptionResult]:
        """Subscribe to a batch of contracts concurrently."""
        tasks = []
        
        for contract in batch:
            task = self._subscribe_single_contract(contract)
            tasks.append(task)
        
        # Execute subscriptions concurrently
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        results = []
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                # Handle exception
                error_result = SubscriptionResult(
                    contract=batch[i],
                    success=False,
                    error_message=str(result)
                )
                results.append(error_result)
            else:
                results.append(result)
        
        return results
    
    async def _subscribe_single_contract(self, contract: ResolvedContract) -> SubscriptionResult:
        """Subscribe to a single contract."""
        symbol = contract.resolved_symbol.split('.')[0]  # Remove exchange suffix if present
        exchange = contract.exchange
        
        try:
            # Check if already subscribed
            subscription_key = f"{symbol}.{exchange}"
            if subscription_key in self.active_subscriptions:
                self.logger.info(f"Already subscribed to {subscription_key}")
                return SubscriptionResult(
                    contract=contract,
                    success=True,
                    subscription_time=datetime.now()
                )
            
            # Attempt subscription
            success = await self.client.subscribe_to_market_data(symbol, exchange)
            
            if success:
                self.active_subscriptions.add(subscription_key)
                self.logger.info(f"✅ Subscribed to {subscription_key}")
                
                return SubscriptionResult(
                    contract=contract,
                    success=True,
                    subscription_time=datetime.now()
                )
            else:
                self.logger.error(f"❌ Failed to subscribe to {subscription_key}")
                return SubscriptionResult(
                    contract=contract,
                    success=False,
                    error_message="Subscription request failed"
                )
                
        except Exception as e:
            self.logger.error(f"❌ Exception subscribing to {symbol}.{exchange}: {e}")
            return SubscriptionResult(
                contract=contract,
                success=False,
                error_message=str(e)
            )
    
    def _log_subscription_results(self, results: List[SubscriptionResult]):
        """Log detailed subscription results."""
        successful = [r for r in results if r.success]
        failed = [r for r in results if not r.success]
        
        self.logger.info("=== SUBSCRIPTION RESULTS ===")
        self.logger.info(f"Total contracts processed: {len(results)}")
        self.logger.info(f"Successful subscriptions: {len(successful)}")
        self.logger.info(f"Failed subscriptions: {len(failed)}")
        
        if successful:
            self.logger.info("✅ Successful subscriptions:")
            for result in successful:
                self.logger.info(f"  - {result.contract.resolved_symbol} ({result.contract.original_spec})")
        
        if failed:
            self.logger.warning("❌ Failed subscriptions:")
            for result in failed:
                self.logger.warning(f"  - {result.contract.resolved_symbol} ({result.contract.original_spec}): {result.error_message}")
        
        # Store results for later access
        self.subscription_results = results
    
    def get_active_subscriptions(self) -> List[str]:
        """Get list of active subscription keys."""
        return list(self.active_subscriptions)
    
    def get_subscription_count(self) -> int:
        """Get count of active subscriptions."""
        return len(self.active_subscriptions)
    
    def get_subscription_stats(self) -> Dict[str, Any]:
        """Get detailed subscription statistics."""
        successful = [r for r in self.subscription_results if r.success]
        failed = [r for r in self.subscription_results if not r.success]
        
        return {
            'total_processed': len(self.subscription_results),
            'successful_subscriptions': len(successful),
            'failed_subscriptions': len(failed),
            'active_subscriptions': len(self.active_subscriptions),
            'success_rate': len(successful) / len(self.subscription_results) if self.subscription_results else 0,
            'active_contracts': list(self.active_subscriptions),
            'failed_contracts': [r.contract.resolved_symbol for r in failed]
        }
    
    async def unsubscribe_all(self):
        """Unsubscribe from all active subscriptions."""
        self.logger.info(f"Unsubscribing from {len(self.active_subscriptions)} contracts")
        
        # Note: Rithmic API doesn't have explicit unsubscribe, disconnection handles it
        self.active_subscriptions.clear()
        self.subscription_results.clear()
        
        self.logger.info("All subscriptions cleared")
    
    def add_subscription_callback(self, contract_key: str, callback: Callable):
        """Add callback for specific contract updates."""
        if contract_key not in self.subscription_callbacks:
            self.subscription_callbacks[contract_key] = []
        self.subscription_callbacks[contract_key].append(callback)
    
    def process_market_update(self, template_id: int, message, contract_key: str = None):
        """Process market data update and trigger callbacks."""
        # Call any registered callbacks for this contract
        if contract_key and contract_key in self.subscription_callbacks:
            for callback in self.subscription_callbacks[contract_key]:
                try:
                    callback(template_id, message)
                except Exception as e:
                    self.logger.error(f"Error in subscription callback for {contract_key}: {e}")