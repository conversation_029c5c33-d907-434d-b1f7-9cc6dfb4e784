#!/usr/bin/env python3

# Add project imports
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

"""
Multi-Contract Level 1 Market Data Subscription Script

This script subscribes to real-time Level 1 market data for multiple contracts
simultaneously. It supports:
- Contract resolution from configuration or command line
- Continuous contracts (@ES, @NQ)
- Wildcard patterns (ES*, *.CME, *)
- Specific contracts (ESU5.CME, NQU5.CME)
- Batched subscriptions with rate limiting
- Comprehensive error handling and reporting

All incoming data is continuously written to timestamped files for later analysis.
"""

import asyncio
import logging
import json
import signal
from datetime import datetime
from pathlib import Path
from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config
from src.utils import MultiContractManager, contract_cache
from src.utils import save_json_data

# Global flag for graceful shutdown
shutdown_flag = False

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully."""
    global shutdown_flag
    print("\n🛑 Shutdown requested. Stopping data collection...")
    shutdown_flag = True

async def subscribe_to_multi_contract_level1_data(
    contract_specs: list = None, 
    duration_seconds: int = None
):
    """
    Subscribe to Level 1 market data for multiple contracts.
    
    Args:
        contract_specs: List of contract specifications. If None, uses config.
        duration_seconds: How long to collect data. If None, run until interrupted.
    """
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Initialize client
    client = RithmicWebSocketClient()
    
    # Initialize message logging
    client.initialize_data_collection()
    
    # Initialize multi-contract manager
    manager = MultiContractManager(client)
    
    try:
        # Step 1: System Discovery
        logger.info("=== STEP 1: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Cannot proceed.")
            return False
            
        
        # Step 2: Login to Ticker Plant
        logger.info("=== STEP 2: LOGIN TO TICKER PLANT ===")
        target_system = config.system_name if config.system_name in systems else systems[0]
        
        login_success = await client.login(
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="MultiContractLevel1Collector"
        )
        
        if not login_success:
            logger.error("Login failed. Cannot proceed.")
            return False
            
        
        # Step 3: Cache Preparation (if enabled)
        if config.cache_contracts and config.auto_refresh_cache:
            logger.info("=== STEP 3: CACHE PREPARATION ===")
            
            if contract_cache.is_cache_expired():
                logger.info("Cache is expired. Refreshing...")
                try:
                    await contract_cache.refresh_from_client(client)
                except Exception as e:
                    logger.warning(f"Cache refresh failed: {e}")
            else:
                logger.info("Cache is fresh. Using existing cache.")
        
        
        # Step 4: Contract Resolution and Subscription
        logger.info("=== STEP 4: MULTI-CONTRACT SUBSCRIPTION ===")
        
        # Use provided contract specs or fall back to configuration
        if contract_specs:
            logger.info(f"Using provided contract specifications: {contract_specs}")
        else:
            contract_specs = config.contracts_to_subscribe
            logger.info(f"Using configured contract specifications: {contract_specs}")
        
        # Subscribe to all contracts
        subscription_results = await manager.subscribe_to_contracts(contract_specs)
        
        if not subscription_results:
            logger.error("No successful subscriptions. Cannot proceed.")
            return False
        
        # Display subscription statistics
        stats = manager.get_subscription_stats()
        logger.info("=== SUBSCRIPTION STATISTICS ===")
        logger.info(f"Total contracts processed: {stats['total_processed']}")
        logger.info(f"Successful subscriptions: {stats['successful_subscriptions']}")
        logger.info(f"Failed subscriptions: {stats['failed_subscriptions']}")
        logger.info(f"Success rate: {stats['success_rate']:.1%}")
        logger.info(f"Active subscriptions: {stats['active_subscriptions']}")
        
        if stats['failed_subscriptions'] > 0:
            logger.warning(f"Failed contracts: {stats['failed_contracts']}")
        
        
        # Step 5: Listen for Market Data Updates
        logger.info("=== STEP 5: COLLECTING MULTI-CONTRACT MARKET DATA ===")
        
        active_contracts = manager.get_active_subscriptions()
        logger.info(f"📊 Collecting Level 1 data for {len(active_contracts)} contracts...")
        
        if duration_seconds:
            logger.info(f"⏱️  Data collection duration: {duration_seconds} seconds")
        else:
            logger.info("⏱️  Data collection: indefinite (press Ctrl+C to stop)")
        
        logger.info("💾 Data will be saved to timestamped files in the 'data' directory")
        logger.info("📈 Active contracts:")
        for contract in active_contracts:
            logger.info(f"  - {contract}")
        
        # Enhanced message processing for multi-contract data
        update_counts = {contract: 0 for contract in active_contracts}
        last_log_time = datetime.now()
        log_interval = 30  # Log statistics every 30 seconds
        
        # Create custom message handler
        original_process_market_data = client.process_market_data_update
        
        def enhanced_multi_contract_processor(template_id, message):
            """Enhanced message processor for multi-contract Level 1 data."""
            nonlocal update_counts, last_log_time
            
            # Determine contract from message
            contract_symbol = getattr(message, 'symbol', 'Unknown')
            
            # Update statistics
            if contract_symbol in update_counts:
                update_counts[contract_symbol] += 1
            
            # Periodic logging
            current_time = datetime.now()
            if (current_time - last_log_time).seconds >= log_interval:
                total_updates = sum(update_counts.values())
                logger.info(f"📊 Update summary ({total_updates} total updates in last {log_interval}s):")
                for contract, count in sorted(update_counts.items()):
                    if count > 0:
                        logger.info(f"  - {contract}: {count} updates")
                
                # Reset counters
                update_counts = {contract: 0 for contract in active_contracts}
                last_log_time = current_time
            
            # Log specific update types
            if template_id == client.TEMPLATE_IDS['LAST_TRADE']:
                logger.debug(f"📈 Last Trade: {contract_symbol} - "
                           f"Price: {getattr(message, 'trade_price', 'N/A')} - "
                           f"Size: {getattr(message, 'trade_size', 'N/A')}")
            elif template_id == client.TEMPLATE_IDS['BEST_BID_OFFER']:
                logger.debug(f"📊 BBO: {contract_symbol}")
            elif template_id == client.TEMPLATE_IDS['ORDER_BOOK']:
                logger.debug(f"📋 Order Book: {contract_symbol}")
            
            # Call original processor for data saving
            original_process_market_data(template_id, message)
            
            # Process through manager for any callbacks
            manager.process_market_update(template_id, message, contract_symbol)
        
        # Replace the message processor
        client.process_market_data_update = enhanced_multi_contract_processor
        
        # Start listening for updates
        try:
            await client.listen_for_updates(duration=duration_seconds)
        except KeyboardInterrupt:
            logger.info("🛑 Data collection interrupted by user")
        
        # Final statistics
        total_updates = sum(update_counts.values())
        logger.info("=== FINAL STATISTICS ===")
        logger.info(f"📊 Total updates received: {total_updates}")
        logger.info(f"📈 Updates per contract:")
        for contract, count in sorted(update_counts.items()):
            logger.info(f"  - {contract}: {count} updates")
        
        logger.info("📊 Multi-contract Level 1 data collection completed")
        
        return True
        
    except Exception as e:
        logger.error(f"Multi-contract Level 1 data collection error: {e}")
        return False
    finally:
        # Cleanup
        if manager:
            await manager.unsubscribe_all()
        await client.disconnect()

async def main():
    """Main function to run multi-contract Level 1 data collection."""
    
    # Setup signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    # Parse command line arguments
    contract_specs = None
    duration = None
    
    args = sys.argv[1:]
    i = 0
    while i < len(args):
        arg = args[i]
        
        if arg in ['-d', '--duration']:
            if i + 1 < len(args):
                try:
                    duration = int(args[i + 1])
                    i += 1
                except ValueError:
                    print(f"❌ Invalid duration: {args[i + 1]}")
                    return False
            else:
                print("❌ Duration flag requires a value")
                return False
        
        elif arg in ['-c', '--contracts']:
            if i + 1 < len(args):
                # Parse space-separated contract list
                contracts_arg = args[i + 1]
                contract_specs = contracts_arg.split()
                i += 1
            else:
                print("❌ Contracts flag requires a value")
                return False
        
        elif arg in ['-h', '--help']:
            print("""
Multi-Contract Level 1 Data Collection

Usage:
    python3 subscribe_multi_contract_level1.py [options]

Options:
    -d, --duration SECONDS    Duration to collect data (default: indefinite)
    -c, --contracts "SPECS"   Space-separated contract specifications
    -h, --help               Show this help message

Contract Specification Examples:
    "@ES @NQ"                 Continuous contracts (front month)
    "ESU5.CME NQU5.CME"      Specific contracts
    "ES* NQ*"                Wildcard patterns
    "*.CME"                  All CME contracts
    "*"                      All contracts (use with caution!)

Examples:
    python3 subscribe_multi_contract_level1.py
    python3 subscribe_multi_contract_level1.py -d 300
    python3 subscribe_multi_contract_level1.py -c "@ES @NQ @YM"
    python3 subscribe_multi_contract_level1.py -c "ES* NQ*" -d 600
            """)
            return True
        else:
            print(f"❌ Unknown argument: {arg}")
            return False
        
        i += 1
    
    # Display configuration
    if contract_specs:
        print(f"📋 Contract specifications: {contract_specs}")
    else:
        print(f"📋 Using configured contracts: {config.contracts_to_subscribe}")
    
    if duration:
        print(f"⏱️  Collection duration: {duration} seconds")
    else:
        print("⏱️  Collection duration: indefinite")
    
    success = await subscribe_to_multi_contract_level1_data(
        contract_specs=contract_specs,
        duration_seconds=duration
    )
    
    if success:
        print("✅ Multi-contract Level 1 data collection completed successfully!")
        return True
    else:
        print("❌ Multi-contract Level 1 data collection failed!")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Data collection interrupted")
        exit(0)