#!/usr/bin/env python3
"""
Test script for Rithmic API authentication flow.
This script implements the two-step authentication process:
1. System Discovery
2. Login Authentication
"""

import asyncio
import logging
import sys
import os

# Add the parent directories to Python path for imports
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config

async def test_authentication():
    """Test the complete authentication flow."""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Initialize client
    client = RithmicWebSocketClient()
    
    # Initialize data collection for authentication testing
    client.initialize_data_collection()
    
    try:
        # Step 1: System Discovery
        logger.info("=== STEP 1: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Exiting.")
            return False
            
        logger.info(f"Discovered systems: {systems}")
        
        # Step 2: Login Authentication
        logger.info("=== STEP 2: LOGIN AUTHENTICATION ===")
        
        # Use the configured system or discovered system
        target_system = config.system_name if config.system_name in systems else systems[0]
        
        login_success = await client.login(
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="TestAuthenticationClient"
        )
        
        if login_success:
            logger.info("Authentication successful!")
            
            # Test heartbeat for a few seconds
            logger.info("Testing heartbeat mechanism for 10 seconds...")
            await asyncio.sleep(10)
            
            return True
        else:
            logger.error("Authentication failed!")
            return False
            
    except Exception as e:
        logger.error(f"Authentication test failed: {e}")
        return False
    finally:
        # Cleanup
        await client.disconnect()

if __name__ == "__main__":
    success = asyncio.run(test_authentication())
    if success:
        print("✅ Authentication test passed!")
    else:
        print("❌ Authentication test failed!")
        exit(1)