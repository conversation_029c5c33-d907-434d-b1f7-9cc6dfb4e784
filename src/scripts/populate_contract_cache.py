#!/usr/bin/env python3

# Add project imports
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

"""
Contract Cache Population Script

This script discovers and caches all available contracts from the Rithmic API.
It performs comprehensive symbol searches to build a complete contract database
that can be used for wildcard matching and multi-contract operations.
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Load environment variables for database configuration
from dotenv import load_dotenv
load_dotenv()

from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config
from src.utils import contract_cache


async def populate_contract_cache():
    """
    Discover and cache all available contracts from the Rithmic API.
    """
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Initialize client
    client = RithmicWebSocketClient()
    
    # Initialize message logging
    client.initialize_data_collection()
    
    try:
        # Step 1: System Discovery
        logger.info("=== STEP 1: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Cannot proceed.")
            return False
            
        
        # Step 2: Login to Ticker Plant
        logger.info("=== STEP 2: LOGIN TO TICKER PLANT ===")
        target_system = config.system_name if config.system_name in systems else systems[0]
        
        login_success = await client.login(
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="ContractCacheBuilder"
        )
        
        if not login_success:
            logger.error("Login failed. Cannot proceed.")
            return False
            
        
        # Step 3: Check if cache needs refresh
        logger.info("=== STEP 3: CHECKING CACHE STATUS ===")
        
        cache_stats = contract_cache.get_cache_stats()
        logger.info(f"Current cache status: {cache_stats}")
        
        if not contract_cache.is_cache_expired() and cache_stats['total_contracts'] > 0:
            logger.info("Cache is fresh and populated. Skipping refresh.")
            
            # Still populate with some popular contracts to ensure we have them
            await _populate_popular_contracts(client, logger)
            return True
        
        
        # Step 4: Dynamic Exchange Discovery
        logger.info("=== STEP 4: DYNAMIC EXCHANGE DISCOVERY ===")
        
        # Get user's permitted exchanges dynamically instead of hardcoded list
        try:
            permitted_exchanges = await client.get_exchange_permissions()
            logger.info(f"User has permission for {len(permitted_exchanges)} exchanges: {', '.join(permitted_exchanges)}")
        except Exception as e:
            logger.error(f"Error getting exchange permissions: {e}")
            logger.info("Using fallback exchange discovery...")
            permitted_exchanges = ["CME", "CBOT", "NYMEX", "COMEX", "ICE", "EUREX"]  # Fallback
        
        
        # Step 5: Exhaustive Contract Discovery Across All Instrument Types
        logger.info("=== STEP 5: EXHAUSTIVE CONTRACT DISCOVERY ===")
        logger.info("Searching ALL instrument types across ALL exchanges...")
        
        all_contracts_by_type = {}
        total_contracts = 0
        
        for exchange in permitted_exchanges:
            logger.info(f"\n--- PROCESSING EXCHANGE: {exchange} ---")
            
            try:
                # Search ALL instrument types for this exchange
                exchange_contracts = await client.search_all_instrument_types(
                    exchange=exchange,
                    verbose=True
                )
                
                # Merge results
                for instrument_type, contracts in exchange_contracts.items():
                    if instrument_type not in all_contracts_by_type:
                        all_contracts_by_type[instrument_type] = []
                    
                    # Add contracts with exchange info
                    for contract in contracts:
                        contract['discovered_exchange'] = exchange  # Mark where it was found
                        all_contracts_by_type[instrument_type].append(contract)
                
                exchange_total = sum(len(contracts) for contracts in exchange_contracts.values())
                total_contracts += exchange_total
                logger.info(f"Exchange {exchange} total: {exchange_total} contracts across {len(exchange_contracts)} instrument types")
                
            except Exception as e:
                logger.error(f"Error processing exchange {exchange}: {e}")
                continue
        
        
        # Step 6: Enhanced Reference Data Collection
        logger.info("=== STEP 6: ENHANCED REFERENCE DATA COLLECTION ===")
        
        # Collect additional reference data for discovered contracts
        enhanced_contracts = []
        sample_size = min(100, total_contracts // 10)  # Sample 10% or max 100 contracts
        
        logger.info(f"Collecting enhanced reference data for {sample_size} sample contracts...")
        
        contract_sample = []
        for instrument_type, contracts in all_contracts_by_type.items():
            if contracts:
                # Take a few samples from each instrument type
                sample_count = min(10, len(contracts))
                contract_sample.extend(contracts[:sample_count])
        
        for i, contract in enumerate(contract_sample[:sample_size]):
            try:
                symbol = contract.get('symbol', '')
                exchange = contract.get('exchange', '')
                
                logger.info(f"[{i+1}/{sample_size}] Enhancing data for {symbol}...")
                
                # Get instruments by underlying (for options/spreads related to this contract)
                try:
                    underlying_instruments = await client.get_instrument_by_underlying(
                        underlying_symbol=symbol,
                        exchange=exchange
                    )
                    if underlying_instruments:
                        contract['related_instruments'] = underlying_instruments
                        logger.info(f"  Found {len(underlying_instruments)} related instruments")
                except Exception as e:
                    logger.debug(f"  Could not get underlying instruments for {symbol}: {e}")
                
                # Get auxiliary reference data
                try:
                    aux_data = await client.get_auxiliary_reference_data(
                        symbol=symbol,
                        exchange=exchange
                    )
                    if aux_data:
                        contract['auxiliary_data'] = aux_data
                        logger.info(f"  Retrieved auxiliary reference data")
                except Exception as e:
                    logger.debug(f"  Could not get auxiliary data for {symbol}: {e}")
                
                enhanced_contracts.append(contract)
                
                # Small delay to avoid overwhelming the server
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error enhancing contract {contract.get('symbol', 'unknown')}: {e}")
                continue
        
        
        # Step 7: Comprehensive Cache Storage
        logger.info("=== STEP 7: COMPREHENSIVE CACHE STORAGE ===")
        
        # Flatten all contracts into single list
        all_contracts = []
        for instrument_type, contracts in all_contracts_by_type.items():
            all_contracts.extend(contracts)
        
        if all_contracts:
            # Clear existing cache
            contract_cache.clear_cache()
            
            # Add all discovered contracts
            contract_cache.add_contracts(all_contracts)
            
            # Create comprehensive reference data structure
            comprehensive_data = {
                'collection_timestamp': datetime.now().isoformat(),
                'total_contracts': len(all_contracts),
                'total_enhanced_contracts': len(enhanced_contracts),
                'contracts_by_instrument_type': {
                    instrument_type: len(contracts)
                    for instrument_type, contracts in all_contracts_by_type.items()
                },
                'exchanges_discovered': permitted_exchanges,
                'instrument_types_found': list(all_contracts_by_type.keys()),
                'enhanced_sample_contracts': enhanced_contracts,
                'discovery_method': 'exhaustive_all_instrument_types'
            }
            
            # Save comprehensive data
            contract_cache._save_reference_data(comprehensive_data)
            
            # Display final statistics
            final_stats = contract_cache.get_cache_stats()
            logger.info("=== FINAL CACHE STATISTICS ===")
            logger.info(f"Total contracts cached: {final_stats['total_contracts']}")
            logger.info(f"Exchanges: {', '.join(final_stats['exchanges'])}")
            logger.info(f"Products: {len(final_stats['products'])} unique product codes")
            logger.info(f"Instrument types discovered: {len(all_contracts_by_type)}")
            
            # Show breakdown by instrument type
            logger.info("=== BREAKDOWN BY INSTRUMENT TYPE ===")
            for instrument_type, count in comprehensive_data['contracts_by_instrument_type'].items():
                logger.info(f"  {instrument_type}: {count} contracts")
            
            # Show breakdown by exchange
            logger.info("=== BREAKDOWN BY EXCHANGE ===")
            exchange_counts = {}
            for contract in all_contracts:
                ex = contract.get('exchange', 'Unknown')
                exchange_counts[ex] = exchange_counts.get(ex, 0) + 1
            
            for exchange, count in sorted(exchange_counts.items()):
                logger.info(f"  {exchange}: {count} contracts")
            
            logger.info(f"Cache last updated: {final_stats['last_updated']}")
            logger.info(f"Enhanced contracts with additional data: {len(enhanced_contracts)}")
            
            return True
        else:
            logger.error("No contracts discovered across any instrument type or exchange.")
            return False
            
    except Exception as e:
        logger.error(f"Contract cache population error: {e}")
        return False
    finally:
        # Cleanup
        await client.disconnect()


async def _populate_popular_contracts(client, logger):
    """
    Populate cache with popular contracts as a fallback.
    """
    logger.info("Populating cache with popular contracts...")
    
    # Popular futures products
    popular_products = [
        "ES",   # S&P 500 E-mini
        "NQ",   # Nasdaq-100 E-mini
        "YM",   # Dow Jones E-mini
        "RTY",  # Russell 2000 E-mini
        "GC",   # Gold
        "SI",   # Silver
        "CL",   # Crude Oil
        "NG",   # Natural Gas
        "ZN",   # 10-Year Treasury Note
        "ZB",   # 30-Year Treasury Bond
        "ZF",   # 5-Year Treasury Note
        "ZC",   # Corn
        "ZS",   # Soybeans
        "ZW",   # Wheat
        "EUR",  # Euro FX
        "GBP",  # British Pound
        "JPY",  # Japanese Yen
        "6E",   # Euro FX (CME)
        "6B",   # British Pound (CME)
        "6J",   # Japanese Yen (CME)
    ]
    
    all_contracts = []
    
    for product in popular_products:
        try:
            # Get contract series for this product
            contracts = await client.get_contract_series(product)
            if contracts:
                all_contracts.extend(contracts)
                logger.info(f"Found {len(contracts)} contracts for {product}")
            else:
                logger.warning(f"No contracts found for {product}")
                
        except Exception as e:
            logger.error(f"Error getting contracts for {product}: {e}")
            continue
    
    if all_contracts:
        contract_cache.add_contracts(all_contracts)
        logger.info(f"Cached {len(all_contracts)} popular contracts")


async def main():
    """Main function to run contract cache population."""
    success = await populate_contract_cache()
    
    if success:
        print("✅ Contract cache populated successfully!")
        
        # Display final statistics
        stats = contract_cache.get_cache_stats()
        print(f"📊 Total contracts: {stats['total_contracts']}")
        print(f"🏢 Exchanges: {len(stats['exchanges'])}")
        print(f"📈 Products: {len(stats['products'])}")
        return True
    else:
        print("❌ Failed to populate contract cache!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)