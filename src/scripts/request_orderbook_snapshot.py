#!/usr/bin/env python3

# Add project imports
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

"""
Order Book Snapshot Request Script

This script requests a complete snapshot of the current order book state for the ES front month contract.
This should be executed IMMEDIATELY after subscribing to depth-by-order updates to prevent data gaps.

The snapshot provides the baseline state before processing incremental updates, ensuring complete
order book reconstruction without missing any data.
"""

import asyncio
import logging
import json
import sys
from datetime import datetime
from pathlib import Path
from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config
from src.utils import read_cache_file

async def request_order_book_snapshot(subscribe_first: bool = True):
    """
    Request a complete order book snapshot for the ES front month contract.
    
    Args:
        subscribe_first: Whether to subscribe to depth-by-order updates first.
                        This is recommended to prevent data gaps.
    """
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Initialize client
    client = RithmicWebSocketClient()
    
    # Initialize data collection for orderbook snapshot data
    client.initialize_data_collection()
    
    # Session info
    session_start = datetime.now()
    session_file = Path("data") / f"orderbook_snapshot_session_{session_start.strftime('%Y%m%d_%H%M%S')}.json"
    session_info = {
        "session_start": session_start.isoformat(),
        "session_type": "Order Book Snapshot",
        "target_product": "ES",
        "subscribe_first": subscribe_first,
        "files_created": [],
        "statistics": {
            "messages_received": 0,
            "snapshot_responses": 0,
            "depth_updates_before_snapshot": 0,
            "depth_updates_after_snapshot": 0
        }
    }
    
    try:
        # Step 1: Get the ES front month contract
        logger.info("=== STEP 1: GETTING ES FRONT MONTH CONTRACT ===")
        
        data_dir = Path("data")
        es_contract = read_cache_file(data_dir, "es_front_month_contract.txt")
        if es_contract:
            logger.info(f"Using previously discovered contract: {es_contract}")
        else:
            logger.info("No previously discovered contract found. Running discovery...")
            # Import and run the discovery script
            from src.scripts.find_front_month_contract import find_front_month_es_contract
            es_contract = await find_front_month_es_contract()
            
            if not es_contract:
                logger.error("Could not determine ES front month contract. Exiting.")
                return False
        
        session_info["contract_symbol"] = es_contract
        session_info["exchange"] = "CME"
        
        # Step 2: System Discovery
        logger.info("=== STEP 2: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Cannot proceed.")
            return False
            
        session_info["available_systems"] = systems
        
        # Step 3: Login to Ticker Plant
        logger.info("=== STEP 3: LOGIN TO TICKER PLANT ===")
        target_system = "Rithmic Paper Trading" if "Rithmic Paper Trading" in systems else systems[0]
        
        login_success = await client.login(
            user="PP-013155",
            password="b7neA8k6JA",
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="OrderBookSnapshotClient",
            app_version="1.0.0"
        )
        
        if not login_success:
            logger.error("Login failed. Cannot proceed.")
            return False
            
        session_info["login_system"] = target_system
        session_info["login_time"] = datetime.now().isoformat()
        
        # Step 4: Subscribe to Depth-by-Order Updates (if requested)
        if subscribe_first:
            logger.info("=== STEP 4: SUBSCRIBING TO DEPTH-BY-ORDER UPDATES ===")
            logger.info("⚠️  Subscribing first to prevent data gaps in order book reconstruction")
            
            subscription_success = await client.subscribe_to_depth_by_order(
                symbol=es_contract,
                exchange="CME"
            )
            
            if not subscription_success:
                logger.error("Failed to subscribe to depth-by-order data. Cannot proceed.")
                return False
                
            session_info["depth_subscription_time"] = datetime.now().isoformat()
            session_info["depth_subscription_success"] = True
            
            # Listen for a few depth updates to confirm subscription
            logger.info("🔍 Monitoring initial depth updates for 5 seconds...")
            
            start_monitor = datetime.now()
            monitor_duration = 5.0
            
            original_process_market_data = client.process_market_data_update
            
            def monitor_depth_updates(template_id, message):
                """Monitor depth updates before snapshot."""
                if template_id == client.TEMPLATE_IDS['DEPTH_BY_ORDER']:
                    session_info["statistics"]["depth_updates_before_snapshot"] += 1
                    logger.info(f"📋 Pre-snapshot depth update #{session_info['statistics']['depth_updates_before_snapshot']}")
                elif template_id == client.TEMPLATE_IDS['DEPTH_BY_ORDER_END_EVENT']:
                    logger.info(f"🏁 Pre-snapshot end event")
                
                original_process_market_data(template_id, message)
            
            client.process_market_data_update = monitor_depth_updates
            
            try:
                await client.listen_for_updates(duration=monitor_duration)
            except Exception as e:
                logger.warning(f"Monitoring stopped: {e}")
            
            logger.info(f"📊 Received {session_info['statistics']['depth_updates_before_snapshot']} depth updates before snapshot")
        
        # Step 5: Request Order Book Snapshot
        logger.info("=== STEP 5: REQUESTING ORDER BOOK SNAPSHOT ===")
        logger.info(f"Contract: {es_contract}")
        logger.info(f"Exchange: CME")
        logger.info("📸 Requesting complete order book snapshot...")
        
        snapshot_request_time = datetime.now()
        session_info["snapshot_request_time"] = snapshot_request_time.isoformat()
        
        snapshot_success = await client.request_order_book_snapshot(
            symbol=es_contract,
            exchange="CME"
        )
        
        if not snapshot_success:
            logger.error("Failed to request order book snapshot.")
            return False
            
        logger.info("✅ Order book snapshot request sent successfully")
        
        # Step 6: Listen for Snapshot Response and Subsequent Updates
        logger.info("=== STEP 6: COLLECTING SNAPSHOT AND SUBSEQUENT UPDATES ===")
        logger.info("📥 Waiting for snapshot response...")
        logger.info("🔍 Will also collect depth updates after snapshot for comparison")
        
        # Enhanced message processor to handle snapshot response
        def process_snapshot_and_updates(template_id, message):
            """Process snapshot response and subsequent depth updates."""
            session_info["statistics"]["messages_received"] += 1
            
            if template_id == client.TEMPLATE_IDS['RESPONSE_DEPTH_BY_ORDER_SNAPSHOT']:
                session_info["statistics"]["snapshot_responses"] += 1
                
                logger.info(f"📸 SNAPSHOT RESPONSE #{session_info['statistics']['snapshot_responses']} RECEIVED!")
                
                # Save detailed snapshot information
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                snapshot_file = Path("data") / f"order_book_snapshot_{timestamp}.json"
                
                snapshot_data = {
                    'timestamp': timestamp,
                    'request_time': session_info["snapshot_request_time"],
                    'response_time': datetime.now().isoformat(),
                    'contract': es_contract,
                    'exchange': 'CME',
                    'message_fields': {}
                }
                
                # Extract all fields from the snapshot response
                for field in message.DESCRIPTOR.fields:
                    if hasattr(message, field.name):
                        value = getattr(message, field.name)
                        snapshot_data['message_fields'][field.name] = str(value)
                
                # Snapshot data is now handled by WebSocket client's consolidated logging
                logger.info(f"💾 Snapshot data logged to consolidated files")
                
            elif template_id == client.TEMPLATE_IDS['DEPTH_BY_ORDER']:
                session_info["statistics"]["depth_updates_after_snapshot"] += 1
                
                logger.info(f"📋 Post-snapshot depth update #{session_info['statistics']['depth_updates_after_snapshot']}")
                
                # Post-snapshot depth updates are now handled by WebSocket client's consolidated logging
                
            elif template_id == client.TEMPLATE_IDS['DEPTH_BY_ORDER_END_EVENT']:
                logger.info(f"🏁 Post-snapshot end event")
            
            # Call original processor
            original_process_market_data(template_id, message)
        
        client.process_market_data_update = process_snapshot_and_updates
        
        # Listen for snapshot response and subsequent updates
        logger.info("⏳ Listening for snapshot response and subsequent updates for 30 seconds...")
        
        try:
            await client.listen_for_updates(duration=30.0)
        except KeyboardInterrupt:
            logger.info("🛑 Data collection interrupted by user")
        
        # Step 7: Session Summary
        logger.info("=== STEP 7: SESSION SUMMARY ===")
        session_info["session_end"] = datetime.now().isoformat()
        session_info["duration_seconds"] = (datetime.now() - session_start).total_seconds()
        
        logger.info(f"📊 Session Statistics:")
        logger.info(f"   Duration: {session_info['duration_seconds']:.1f} seconds")
        logger.info(f"   Total Messages: {session_info['statistics']['messages_received']}")
        logger.info(f"   Snapshot Responses: {session_info['statistics']['snapshot_responses']}")
        logger.info(f"   Depth Updates (Pre-snapshot): {session_info['statistics']['depth_updates_before_snapshot']}")
        logger.info(f"   Depth Updates (Post-snapshot): {session_info['statistics']['depth_updates_after_snapshot']}")
        
        # Analyze snapshot timing
        if session_info["statistics"]["snapshot_responses"] > 0:
            logger.info("✅ Order book snapshot successfully received!")
            logger.info("💡 Use the snapshot as the baseline state for order book reconstruction")
            logger.info("💡 Apply subsequent depth updates to maintain current order book state")
        else:
            logger.warning("⚠️  No snapshot response received - check API connectivity and permissions")
        
        # List created files
        data_files = list(Path("data").glob("received_*")) + list(Path("data").glob("order_book_*")) + list(Path("data").glob("post_snapshot_*"))
        session_info["files_created"] = [str(f) for f in data_files if f.stat().st_mtime > session_start.timestamp()]
        logger.info(f"📁 Data files created: {len(session_info['files_created'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"Order book snapshot request error: {e}")
        session_info["error"] = str(e)
        return False
    finally:
        # Save session info
        with open(session_file, 'w') as f:
            json.dump(session_info, f, indent=2)
        logger.info(f"📄 Session info saved to: {session_file}")
        
        # Cleanup
        await client.disconnect()

async def main():
    """Main function to run order book snapshot request."""
    
    # Parse command line arguments
    subscribe_first = True
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['false', 'no', '0']:
            subscribe_first = False
            print("🚫 Will NOT subscribe to depth updates first")
        else:
            print("✅ Will subscribe to depth updates first (recommended)")
    else:
        print("✅ Will subscribe to depth updates first (recommended)")
    
    success = await request_order_book_snapshot(subscribe_first=subscribe_first)
    
    if success:
        print("✅ Order book snapshot request completed successfully!")
        return True
    else:
        print("❌ Order book snapshot request failed!")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Snapshot request interrupted")
        exit(0)