#!/usr/bin/env python3

# Add project imports
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

"""
ES Futures Front Month Contract Discovery Script

This script implements dual approach contract discovery:
1. Primary Method: Use RequestFrontMonthContract to directly request the nearest ES futures contract
2. Fallback Method: Use RequestSearchSymbols to search for ES futures contracts and select nearest expiration

All responses are saved to timestamped files for later analysis.
"""

import asyncio
import logging
import json
from datetime import datetime
from pathlib import Path
from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config
from src.utils import save_json_data, save_cache_file

async def find_front_month_es_contract():
    """
    Find the front month ES futures contract using both direct and fallback methods.
    """
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Initialize client
    client = RithmicWebSocketClient()
    
    # Initialize message logging
    client.initialize_data_collection()
    
    final_contract = None
    
    try:
        # Step 1: System Discovery
        logger.info("=== STEP 1: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Cannot proceed.")
            return None
            
        
        # Step 2: Login to Ticker Plant
        logger.info("=== STEP 2: LOGIN TO TICKER PLANT ===")
        target_system = config.system_name if config.system_name in systems else systems[0]
        
        login_success = await client.login(
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="ESContractDiscovery"
        )
        
        if not login_success:
            logger.error("Login failed. Cannot proceed.")
            return None
            
        
        # Step 3: Primary Method - RequestFrontMonthContract
        logger.info("=== STEP 3: PRIMARY METHOD - FRONT MONTH CONTRACT REQUEST ===")
        
        try:
            front_month_contract = await client.find_front_month_contract("ES")
            
            if front_month_contract:
                logger.info(f"✅ Primary method successful: {front_month_contract}")
                final_contract = front_month_contract
            else:
                logger.warning("❌ Primary method failed or returned no contract")
                final_contract = None
                
        except Exception as e:
            logger.error(f"❌ Primary method error: {e}")
            final_contract = None
        
        # Step 4: Fallback Method - Symbol Search (if primary failed)
        if not final_contract:
            logger.info("=== STEP 4: FALLBACK METHOD - SYMBOL SEARCH ===")
            
            try:
                fallback_contract = await client.search_front_month_contract("ES")
                
                if fallback_contract:
                    logger.info(f"✅ Fallback method successful: {fallback_contract}")
                    final_contract = fallback_contract
                else:
                    logger.error("❌ Fallback method also failed")
                    
            except Exception as e:
                logger.error(f"❌ Fallback method error: {e}")
        
        # Final Result
        if final_contract:
            logger.info(f"🎉 SUCCESS: ES Front Month Contract = {final_contract}")
            
            # Save contract symbol to cache for other scripts to use
            data_dir = Path(config.data_directory)
            cache_file = save_cache_file(data_dir, "es_front_month_contract.txt", final_contract)
            logger.info(f"Contract symbol saved to cache: {cache_file}")
            
            return final_contract
        else:
            logger.error("❌ FAILED: Could not determine ES front month contract")
            return None
            
    except Exception as e:
        logger.error(f"Script error: {e}")
        return None
    finally:
        # Cleanup
        await client.disconnect()

async def main():
    """Main function to run the contract discovery."""
    contract = await find_front_month_es_contract()
    
    if contract:
        print(f"✅ ES Front Month Contract: {contract}")
        return True
    else:
        print("❌ Failed to find ES front month contract")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)