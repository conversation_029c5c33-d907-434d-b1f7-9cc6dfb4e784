#!/usr/bin/env python3

# Add project imports
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

"""
Subscription Limits Testing Script

This script tests the maximum number of concurrent market data subscriptions
supported by the Rithmic API. It progressively increases the number of 
subscriptions until it hits a limit or error.

The script will:
1. Discover available contracts
2. Attempt subscriptions in increasing batches
3. Monitor for errors or subscription failures
4. Report the maximum successful concurrent subscriptions
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path

from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config
from src.utils import MultiContractManager, contract_cache


async def test_subscription_limits():
    """
    Test the maximum number of concurrent subscriptions supported.
    """
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Initialize client
    client = RithmicWebSocketClient()
    
    # Initialize message logging
    client.initialize_data_collection()
    
    # Initialize multi-contract manager
    manager = MultiContractManager(client)
    
    test_results = {
        'max_successful': 0,
        'failed_at': None,
        'test_batches': [],
        'errors_encountered': [],
        'total_contracts_tested': 0
    }
    
    try:
        # Step 1: System Discovery
        logger.info("=== STEP 1: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Cannot proceed.")
            return test_results
            
        
        # Step 2: Login to Ticker Plant
        logger.info("=== STEP 2: LOGIN TO TICKER PLANT ===")
        target_system = config.system_name if config.system_name in systems else systems[0]
        
        login_success = await client.login(
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="SubscriptionLimitTester"
        )
        
        if not login_success:
            logger.error("Login failed. Cannot proceed.")
            return test_results
            
        
        # Step 3: Contract Discovery
        logger.info("=== STEP 3: CONTRACT DISCOVERY ===")
        
        # Refresh cache to get comprehensive contract list
        if contract_cache.is_cache_expired():
            logger.info("Refreshing contract cache...")
            try:
                await contract_cache.refresh_from_client(client)
            except Exception as e:
                logger.warning(f"Cache refresh failed: {e}")
        
        # Get test contracts from cache
        all_contracts = contract_cache.get_all_contracts()
        
        if not all_contracts:
            logger.warning("No contracts in cache. Using fallback list...")
            # Use a predefined list of known contracts
            test_contract_specs = [
                "@ES", "@NQ", "@YM", "@RTY",  # E-mini indices
                "@GC", "@SI", "@CL", "@NG",  # Commodities
                "@ZN", "@ZB", "@ZF",         # Treasuries
                "@6E", "@6B", "@6J",         # FX
                "@ZC", "@ZS", "@ZW"          # Grains
            ]
            
            # Resolve these to actual contracts
            resolved = await manager.resolver.resolve_contract_list(test_contract_specs)
            test_contracts = [c.resolved_symbol for c in resolved]
        else:
            # Use a diverse selection from cache
            # Group by product and take a few from each
            by_product = {}
            for contract in all_contracts:
                product = contract.product_code
                if product not in by_product:
                    by_product[product] = []
                by_product[product].append(contract.symbol)
            
            # Take up to 3 contracts per product for testing
            test_contracts = []
            for product, symbols in by_product.items():
                test_contracts.extend(symbols[:3])
        
        test_contracts = test_contracts[:100]  # Limit to 100 for testing
        logger.info(f"Testing with {len(test_contracts)} contracts")
        
        
        # Step 4: Progressive Subscription Testing
        logger.info("=== STEP 4: PROGRESSIVE SUBSCRIPTION TESTING ===")
        
        batch_sizes = [1, 5, 10, 20, 30, 50, 75, 100]
        successful_subscriptions = 0
        
        for batch_size in batch_sizes:
            if batch_size > len(test_contracts):
                batch_size = len(test_contracts)
            
            logger.info(f"--- Testing {batch_size} concurrent subscriptions ---")
            
            # Take contracts for this batch
            batch_contracts = test_contracts[:batch_size]
            
            # Clear previous subscriptions
            await manager.unsubscribe_all()
            
            try:
                # Attempt subscriptions
                start_time = datetime.now()
                results = await manager.subscribe_to_contracts(batch_contracts)
                end_time = datetime.now()
                
                # Analyze results
                successful = [r for r in results if r.success]
                failed = [r for r in results if not r.success]
                
                batch_result = {
                    'batch_size': batch_size,
                    'successful_count': len(successful),
                    'failed_count': len(failed),
                    'subscription_time_seconds': (end_time - start_time).total_seconds(),
                    'success_rate': len(successful) / len(results) if results else 0
                }
                
                test_results['test_batches'].append(batch_result)
                
                logger.info(f"Batch {batch_size}: {len(successful)}/{len(results)} successful")
                
                if len(successful) == batch_size:
                    # All subscriptions successful
                    successful_subscriptions = batch_size
                    test_results['max_successful'] = batch_size
                    logger.info(f"✅ {batch_size} concurrent subscriptions successful")
                    
                    # Test data reception for a short period
                    logger.info("Testing data reception for 10 seconds...")
                    await client.listen_for_updates(duration=10)
                    
                else:
                    # Some subscriptions failed
                    logger.warning(f"❌ Only {len(successful)}/{batch_size} subscriptions successful")
                    test_results['failed_at'] = batch_size
                    
                    # Log failure details
                    for failed_result in failed:
                        error_msg = f"Failed: {failed_result.contract.resolved_symbol} - {failed_result.error_message}"
                        logger.error(error_msg)
                        test_results['errors_encountered'].append(error_msg)
                    
                    # Stop testing if success rate is too low
                    if len(successful) / len(results) < 0.5:
                        logger.error(f"Success rate too low ({len(successful)}/{len(results)}). Stopping tests.")
                        break
                
                # Add delay between tests
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Error testing batch size {batch_size}: {e}")
                test_results['errors_encountered'].append(f"Batch {batch_size}: {str(e)}")
                test_results['failed_at'] = batch_size
                break
            
            # If we tested all available contracts, stop
            if batch_size >= len(test_contracts):
                break
        
        test_results['total_contracts_tested'] = len(test_contracts)
        
        
        # Step 5: Report Results
        logger.info("=== STEP 5: TEST RESULTS ===")
        
        logger.info(f"Maximum successful concurrent subscriptions: {test_results['max_successful']}")
        if test_results['failed_at']:
            logger.info(f"First failure occurred at: {test_results['failed_at']} subscriptions")
        
        logger.info("Batch test summary:")
        for batch in test_results['test_batches']:
            logger.info(f"  {batch['batch_size']} contracts: {batch['successful_count']}/{batch['batch_size']} "
                       f"successful ({batch['success_rate']:.1%}) in {batch['subscription_time_seconds']:.1f}s")
        
        if test_results['errors_encountered']:
            logger.warning(f"Errors encountered ({len(test_results['errors_encountered'])}):")
            for error in test_results['errors_encountered'][:5]:  # Show first 5
                logger.warning(f"  - {error}")
        
        return test_results
        
    except Exception as e:
        logger.error(f"Subscription limit testing error: {e}")
        test_results['errors_encountered'].append(str(e))
        return test_results
    finally:
        # Cleanup
        await manager.unsubscribe_all()
        await client.disconnect()


async def main():
    """Main function to run subscription limit testing."""
    print("🧪 Starting subscription limits testing...")
    print("⚠️  Note: This test may take several minutes to complete")
    print("⚠️  Market must be open for accurate results")
    print()
    
    results = await test_subscription_limits()
    
    print("\n" + "="*60)
    print("📊 SUBSCRIPTION LIMITS TEST RESULTS")
    print("="*60)
    
    if results['max_successful'] > 0:
        print(f"✅ Maximum concurrent subscriptions: {results['max_successful']}")
        
        if results['max_successful'] >= 50:
            print("🎉 Excellent! API supports high concurrent subscriptions")
        elif results['max_successful'] >= 20:
            print("👍 Good! API supports reasonable concurrent subscriptions")
        elif results['max_successful'] >= 10:
            print("⚠️  Moderate support for concurrent subscriptions")
        else:
            print("⚠️  Limited concurrent subscription support")
        
        print(f"\n📈 Recommendation: Set MAX_CONCURRENT_CONTRACTS to {results['max_successful'] - 5} for safety margin")
    else:
        print("❌ No successful subscriptions achieved")
    
    if results['failed_at']:
        print(f"❌ First failure at: {results['failed_at']} subscriptions")
    
    print(f"\n📋 Total contracts tested: {results['total_contracts_tested']}")
    print(f"🧪 Test batches completed: {len(results['test_batches'])}")
    
    if results['errors_encountered']:
        print(f"\n⚠️  Errors encountered: {len(results['errors_encountered'])}")
        print("   (Check logs for details)")
    
    print("\n💡 Configure .env file with appropriate MAX_CONCURRENT_CONTRACTS value")
    
    return results['max_successful'] > 0


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted")
        exit(0)