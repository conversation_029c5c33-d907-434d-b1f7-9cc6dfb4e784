#!/usr/bin/env python3

# Add project imports
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

"""
Enhanced Level 1 Market Data Subscription Script

This script supports both single-contract and multi-contract Level 1 data subscription.
It maintains backward compatibility with the original script while adding support for:
- Multi-contract subscriptions from configuration
- Contract resolution (continuous, wildcard, specific)
- Enhanced error handling and reporting
- Batched subscriptions with rate limiting

Mode Selection:
- Multi-contract mode: Uses CONTRACTS_TO_SUBSCRIBE from .env configuration
- Single-contract mode: Falls back to ES front month for compatibility

All incoming data is continuously written to timestamped files for later analysis.
"""

import asyncio
import logging
import json
import signal
from datetime import datetime
from pathlib import Path
from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config
from src.utils import MultiContractManager, read_cache_file, contract_cache
from src.utils import save_json_data

# Global flag for graceful shutdown
shutdown_flag = False

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully."""
    global shutdown_flag
    print("\n🛑 Shutdown requested. Stopping data collection...")
    shutdown_flag = True

async def subscribe_to_level1_market_data(duration_seconds: int = None, force_single_contract: bool = False):
    """
    Subscribe to Level 1 market data (single or multi-contract).
    
    Args:
        duration_seconds: How long to collect data. If None, run until interrupted.
        force_single_contract: Force single-contract mode for backward compatibility.
    """
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Initialize client
    client = RithmicWebSocketClient()
    
    # Initialize message logging
    client.initialize_data_collection()
    
    # Determine mode
    use_multi_contract = (
        not force_single_contract and 
        config.subscribe_on_startup and 
        len(config.contracts_to_subscribe) > 1
    )
    
    if use_multi_contract:
        logger.info("🔀 Using MULTI-CONTRACT mode")
        return await _subscribe_multi_contract_mode(client, duration_seconds, logger)
    else:
        logger.info("📈 Using SINGLE-CONTRACT mode (ES front month)")
        return await _subscribe_single_contract_mode(client, duration_seconds, logger)

async def _subscribe_multi_contract_mode(client, duration_seconds, logger):
    """Handle multi-contract subscription mode."""
    
    # Initialize multi-contract manager
    manager = MultiContractManager(client)
    
    try:
        # Step 1: System Discovery
        logger.info("=== STEP 1: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Cannot proceed.")
            return False
            
        # Step 2: Login to Ticker Plant
        logger.info("=== STEP 2: LOGIN TO TICKER PLANT ===")
        target_system = config.system_name if config.system_name in systems else systems[0]
        
        login_success = await client.login(
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="EnhancedLevel1DataCollector"
        )
        
        if not login_success:
            logger.error("Login failed. Cannot proceed.")
            return False
            
        # Step 3: Cache Preparation
        if config.cache_contracts and config.auto_refresh_cache:
            logger.info("=== STEP 3: CACHE PREPARATION ===")
            
            if contract_cache.is_cache_expired():
                logger.info("Cache is expired. Refreshing...")
                try:
                    await contract_cache.refresh_from_client(client)
                except Exception as e:
                    logger.warning(f"Cache refresh failed: {e}")
            else:
                logger.info("Cache is fresh.")
        
        # Step 4: Multi-Contract Subscription
        logger.info("=== STEP 4: MULTI-CONTRACT SUBSCRIPTION ===")
        contract_specs = config.contracts_to_subscribe
        logger.info(f"Contract specifications: {contract_specs}")
        
        subscription_results = await manager.subscribe_to_contracts(contract_specs)
        
        if not subscription_results:
            logger.error("No successful subscriptions. Cannot proceed.")
            return False
        
        # Display statistics
        stats = manager.get_subscription_stats()
        logger.info(f"📊 Successfully subscribed to {stats['successful_subscriptions']} contracts")
        
        # Step 5: Data Collection
        logger.info("=== STEP 5: MULTI-CONTRACT DATA COLLECTION ===")
        return await _collect_multi_contract_data(client, manager, duration_seconds, logger)
        
    except Exception as e:
        logger.error(f"Multi-contract mode error: {e}")
        return False
    finally:
        if manager:
            await manager.unsubscribe_all()

async def _subscribe_single_contract_mode(client, duration_seconds, logger):
    """Handle single-contract subscription mode (backward compatibility)."""
    
    try:
        # Step 1: Get the ES front month contract
        logger.info("=== STEP 1: GETTING ES FRONT MONTH CONTRACT ===")
        
        data_dir = Path(config.data_directory)
        es_contract = read_cache_file(data_dir, "es_front_month_contract.txt")
        if es_contract:
            logger.info(f"Using previously discovered contract: {es_contract}")
        else:
            logger.info("No previously discovered contract found. Running discovery...")
            # Import and run the discovery script (same as original)
            from src.scripts.find_front_month_contract import find_front_month_es_contract
            es_contract = await find_front_month_es_contract()
            
            if not es_contract:
                logger.error("Could not determine ES front month contract. Exiting.")
                return False
        
        # Step 2: System Discovery
        logger.info("=== STEP 2: SYSTEM DISCOVERY ===")
        systems = await client.discover_systems()
        
        if not systems:
            logger.error("No systems discovered. Cannot proceed.")
            return False
            
        # Step 3: Login to Ticker Plant
        logger.info("=== STEP 3: LOGIN TO TICKER PLANT ===")
        target_system = config.system_name if config.system_name in systems else systems[0]
        
        login_success = await client.login(
            system_name=target_system,
            infra_type="TICKER_PLANT",
            app_name="Level1DataCollector"
        )
        
        if not login_success:
            logger.error("Login failed. Cannot proceed.")
            return False
            
        # Step 4: Subscribe to Level 1 Market Data
        logger.info("=== STEP 4: SUBSCRIBING TO LEVEL 1 MARKET DATA ===")
        logger.info(f"Contract: {es_contract}")
        logger.info(f"Exchange: CME")
        
        subscription_success = await client.subscribe_to_market_data(
            symbol=es_contract,
            exchange="CME"
        )
        
        if not subscription_success:
            logger.error("Failed to subscribe to market data. Cannot proceed.")
            return False
            
        # Step 5: Data Collection
        logger.info("=== STEP 5: SINGLE-CONTRACT DATA COLLECTION ===")
        return await _collect_single_contract_data(client, es_contract, duration_seconds, logger)
        
    except Exception as e:
        logger.error(f"Single-contract mode error: {e}")
        return False

async def _collect_multi_contract_data(client, manager, duration_seconds, logger):
    """Collect data for multiple contracts."""
    
    active_contracts = manager.get_active_subscriptions()
    
    if duration_seconds:
        logger.info(f"📊 Collecting Level 1 data for {len(active_contracts)} contracts for {duration_seconds} seconds...")
    else:
        logger.info(f"📊 Collecting Level 1 data for {len(active_contracts)} contracts indefinitely...")
        logger.info("💡 Press Ctrl+C to stop data collection")
    
    logger.info("💡 Data will be saved to timestamped files in the 'data' directory")
    logger.info("📈 Active contracts:")
    for contract in active_contracts:
        logger.info(f"  - {contract}")
    
    # Enhanced message processing
    update_counts = {contract: 0 for contract in active_contracts}
    last_log_time = datetime.now()
    
    original_process_market_data = client.process_market_data_update
    
    def enhanced_processor(template_id, message):
        """Enhanced message processor for multi-contract data."""
        nonlocal update_counts, last_log_time
        
        contract_symbol = getattr(message, 'symbol', 'Unknown')
        if contract_symbol in update_counts:
            update_counts[contract_symbol] += 1
        
        # Periodic statistics
        current_time = datetime.now()
        if (current_time - last_log_time).seconds >= 30:
            total_updates = sum(update_counts.values())
            logger.info(f"📊 Total updates: {total_updates} (last 30s)")
            update_counts = {contract: 0 for contract in active_contracts}
            last_log_time = current_time
        
        # Call original processor
        original_process_market_data(template_id, message)
    
    client.process_market_data_update = enhanced_processor
    
    # Start listening
    try:
        await client.listen_for_updates(duration=duration_seconds)
    except KeyboardInterrupt:
        logger.info("🛑 Data collection interrupted by user")
    
    total_updates = sum(update_counts.values())
    logger.info(f"📊 Multi-contract data collection completed. Total updates: {total_updates}")
    
    return True

async def _collect_single_contract_data(client, es_contract, duration_seconds, logger):
    """Collect data for single contract (backward compatibility)."""
    
    if duration_seconds:
        logger.info(f"📊 Collecting Level 1 data for {duration_seconds} seconds...")
    else:
        logger.info("📊 Collecting Level 1 data indefinitely...")
        logger.info("💡 Press Ctrl+C to stop data collection")
    
    logger.info("💡 Data will be saved to timestamped files in the 'data' directory")
    
    # Create custom message handler for statistics
    original_process_market_data = client.process_market_data_update
    
    def enhanced_process_market_data(template_id, message):
        """Enhanced message processor for Level 1 data."""
        if template_id == client.TEMPLATE_IDS['LAST_TRADE']:
            logger.info(f"📈 Last Trade: {message.symbol if hasattr(message, 'symbol') else es_contract} - "
                       f"Price: {message.trade_price if hasattr(message, 'trade_price') else 'N/A'} - "
                       f"Size: {message.trade_size if hasattr(message, 'trade_size') else 'N/A'}")
        elif template_id == client.TEMPLATE_IDS['BEST_BID_OFFER']:
            logger.info(f"📊 Best Bid/Offer: {message.symbol if hasattr(message, 'symbol') else es_contract}")
        else:
            logger.info(f"📋 Other Update: Template {template_id} for {message.symbol if hasattr(message, 'symbol') else es_contract}")
        
        # Call original processor
        original_process_market_data(template_id, message)
    
    # Replace the message processor
    client.process_market_data_update = enhanced_process_market_data
    
    # Start listening for updates
    try:
        await client.listen_for_updates(duration=duration_seconds)
    except KeyboardInterrupt:
        logger.info("🛑 Data collection interrupted by user")
    
    logger.info("📊 Level 1 data collection completed")
    
    return True

async def main():
    """Main function to run Level 1 data collection."""
    
    # Setup signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    # Parse command line arguments
    duration = None
    force_single = False
    
    args = sys.argv[1:]
    i = 0
    while i < len(args):
        arg = args[i]
        
        if arg in ['-d', '--duration']:
            if i + 1 < len(args):
                try:
                    duration = int(args[i + 1])
                    i += 1
                except ValueError:
                    print(f"❌ Invalid duration: {args[i + 1]}")
                    return False
            else:
                print("❌ Duration flag requires a value")
                return False
        
        elif arg in ['-s', '--single']:
            force_single = True
        
        elif arg in ['-h', '--help']:
            print("""
Enhanced Level 1 Market Data Collection

Usage:
    python3 subscribe_level1_data_enhanced.py [options]

Options:
    -d, --duration SECONDS    Duration to collect data (default: indefinite)
    -s, --single             Force single-contract mode (ES only)
    -h, --help               Show this help message

Modes:
    Multi-contract: Uses CONTRACTS_TO_SUBSCRIBE from .env (default if > 1 contract)
    Single-contract: ES front month only (backward compatibility)

Examples:
    python3 subscribe_level1_data_enhanced.py
    python3 subscribe_level1_data_enhanced.py -d 300
    python3 subscribe_level1_data_enhanced.py --single
    python3 subscribe_level1_data_enhanced.py -s -d 600
            """)
            return True
        else:
            print(f"❌ Unknown argument: {arg}")
            return False
        
        i += 1
    
    # Display mode information
    contract_specs = config.contracts_to_subscribe
    if force_single:
        print("📈 Mode: Single-contract (ES front month)")
    elif len(contract_specs) > 1:
        print(f"🔀 Mode: Multi-contract ({contract_specs})")
    else:
        print("📈 Mode: Single-contract (backward compatibility)")
    
    if duration:
        print(f"⏱️  Duration: {duration} seconds")
    else:
        print("⏱️  Duration: indefinite")
    
    success = await subscribe_to_level1_market_data(
        duration_seconds=duration,
        force_single_contract=force_single
    )
    
    if success:
        print("✅ Level 1 data collection completed successfully!")
        return True
    else:
        print("❌ Level 1 data collection failed!")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Data collection interrupted")
        exit(0)