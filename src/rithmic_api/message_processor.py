#!/usr/bin/env python3
"""
Comprehensive Message Processing Pipeline for Rithmic API

Provides dynamic message processing, routing, and persistence for all Rithmic API template types.
Supports templates 10-3509 with automatic protobuf handling and database persistence.
"""

import logging
import time
import asyncio
import importlib
from typing import Dict, Any, List, Optional, Callable, Union, Type
from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from pathlib import Path
from collections import defaultdict
from enum import Enum

# Database imports
from ..database.database_manager import get_database_manager
from ..database.models import (
    # Core models and DAOs
    Symbol, SymbolDAO,
    BestBidOffer, BestBidOfferDAO,
    LastTrade, LastTradeDAO,
    UserSession, UserSessionDAO,
    # Level 3 Depth-by-Order
    DepthByOrderSnapshot, DepthByOrderSnapshotDAO,
    DepthByOrderUpdate, DepthByOrderUpdateDAO,
    OrderBookLevel, OrderBookLevelDAO,
    # Historical Data
    TimeBar, TimeBarDAO,
    TickBar, TickBarDAO,
    # Order Management
    OrderNotification, OrderNotificationDAO,
    OrderFill, OrderFillDAO,
    # Position & Account
    Position, PositionDAO,
    AccountInfo, AccountInfoDAO,
    # System Events & Statistics
    SystemEvent, SystemEventDAO,
    TradeStatistics, TradeStatisticsDAO,
    QuoteStatistics, QuoteStatisticsDAO,
    Heartbeat, HeartbeatDAO
)

logger = logging.getLogger(__name__)


# =============================================================================
# MESSAGE PROCESSING ENUMS AND DATA CLASSES
# =============================================================================

class MessageDirection(Enum):
    """Message direction classification."""
    FROM_CLIENT = "from_client"
    FROM_SERVER = "from_server"

class InfrastructureType(Enum):
    """Rithmic infrastructure plant types."""
    TICKER_PLANT = "ticker_plant"
    ORDER_PLANT = "order_plant"
    HISTORY_PLANT = "history_plant"
    PNL_PLANT = "pnl_plant"
    REPOSITORY_PLANT = "repository_plant"

@dataclass
class TemplateDefinition:
    """Template metadata for message processing."""
    template_id: int
    template_name: str
    direction: MessageDirection
    infrastructure_type: InfrastructureType
    protobuf_class_name: str
    protobuf_module_name: str
    database_model_class: Optional[Type] = None
    database_dao_class: Optional[Type] = None
    is_streaming: bool = False
    is_multi_response: bool = False
    priority: int = 1  # 1=high, 2=medium, 3=low

@dataclass
class ProcessedMessage:
    """Container for processed message data."""
    template_id: int
    template_name: str
    direction: MessageDirection
    infrastructure_type: InfrastructureType
    raw_data: bytes
    parsed_message: Any
    metadata: Dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    database_persisted: bool = False
    error: Optional[str] = None


# =============================================================================
# TEMPLATE REGISTRY
# =============================================================================

class TemplateRegistry:
    """Registry for all Rithmic API template definitions."""
    
    def __init__(self):
        self._templates: Dict[int, TemplateDefinition] = {}
        self._initialize_core_templates()
    
    def _initialize_core_templates(self):
        """Initialize core template definitions."""
        
        # Shared Infrastructure Templates (10-77)
        self._register_template(10, "LoginRequest", MessageDirection.FROM_CLIENT, 
                               InfrastructureType.TICKER_PLANT, "RequestLogin", "request_login_pb2")
        self._register_template(11, "LoginResponse", MessageDirection.FROM_SERVER, 
                               InfrastructureType.TICKER_PLANT, "ResponseLogin", "response_login_pb2",
                               UserSession, UserSessionDAO)
        
        self._register_template(16, "RithmicSystemInfoRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.TICKER_PLANT, "RequestRithmicSystemInfo", "request_rithmic_system_info_pb2")
        self._register_template(17, "RithmicSystemInfoResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "ResponseRithmicSystemInfo", "response_rithmic_system_info_pb2")
        
        self._register_template(18, "RequestHeartbeat", MessageDirection.FROM_CLIENT,
                               InfrastructureType.TICKER_PLANT, "RequestHeartbeat", "request_heartbeat_pb2")
        self._register_template(19, "ResponseHeartbeat", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "ResponseHeartbeat", "response_heartbeat_pb2",
                               Heartbeat, HeartbeatDAO)
        
        # Market Data Infrastructure Templates (100-163)
        self._register_template(100, "MarketDataUpdateRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.TICKER_PLANT, "RequestMarketDataUpdate", "request_market_data_update_pb2")
        self._register_template(101, "MarketDataUpdateResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "ResponseMarketDataUpdate", "response_market_data_update_pb2")
        
        self._register_template(109, "SearchSymbolsRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.TICKER_PLANT, "RequestSearchSymbols", "request_search_symbols_pb2")
        self._register_template(110, "SearchSymbolsResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "ResponseSearchSymbols", "response_search_symbols_pb2",
                               Symbol, SymbolDAO, is_multi_response=True)
        
        self._register_template(113, "FrontMonthContractRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.TICKER_PLANT, "RequestFrontMonthContract", "request_front_month_contract_pb2")
        self._register_template(114, "FrontMonthContractResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "ResponseFrontMonthContract", "response_front_month_contract_pb2",
                               Symbol, SymbolDAO)
        
        # Level 3 Depth-by-Order Templates (115-118)
        self._register_template(115, "DepthByOrderSnapshotRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.TICKER_PLANT, "RequestDepthByOrderSnapshot", "request_depth_by_order_snapshot_pb2")
        self._register_template(116, "DepthByOrderSnapshotResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "ResponseDepthByOrderSnapshot", "response_depth_by_order_snapshot_pb2",
                               DepthByOrderSnapshot, DepthByOrderSnapshotDAO, is_multi_response=True)
        
        self._register_template(117, "DepthByOrderUpdatesRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.TICKER_PLANT, "RequestDepthByOrderUpdates", "request_depth_by_order_updates_pb2")
        self._register_template(118, "DepthByOrderUpdatesResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "ResponseDepthByOrderUpdates", "response_depth_by_order_updates_pb2")
        
        # Streaming Market Data Templates (150-163)
        self._register_template(150, "LastTrade", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "LastTrade", "last_trade_pb2",
                               LastTrade, LastTradeDAO, is_streaming=True)
        self._register_template(151, "BestBidOffer", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "BestBidOffer", "best_bid_offer_pb2",
                               BestBidOffer, BestBidOfferDAO, is_streaming=True)
        self._register_template(152, "TradeStatistics", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "TradeStatistics", "trade_statistics_pb2",
                               TradeStatistics, TradeStatisticsDAO, is_streaming=True)
        self._register_template(153, "QuoteStatistics", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "QuoteStatistics", "quote_statistics_pb2",
                               QuoteStatistics, QuoteStatisticsDAO, is_streaming=True)
        self._register_template(156, "OrderBook", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "OrderBook", "order_book_pb2",
                               OrderBookLevel, OrderBookLevelDAO, is_streaming=True)
        self._register_template(160, "DepthByOrder", MessageDirection.FROM_SERVER,
                               InfrastructureType.TICKER_PLANT, "DepthByOrder", "depth_by_order_pb2",
                               DepthByOrderUpdate, DepthByOrderUpdateDAO, is_streaming=True)
        
        # Historical Data Templates (200-251)
        self._register_template(200, "TimeBarUpdateRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.HISTORY_PLANT, "RequestTimeBarUpdate", "request_time_bar_update_pb2")
        self._register_template(201, "TimeBarUpdateResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.HISTORY_PLANT, "ResponseTimeBarUpdate", "response_time_bar_update_pb2")
        self._register_template(204, "TickBarUpdateRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.HISTORY_PLANT, "RequestTickBarUpdate", "request_tick_bar_update_pb2")
        self._register_template(205, "TickBarUpdateResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.HISTORY_PLANT, "ResponseTickBarUpdate", "response_tick_bar_update_pb2")
        self._register_template(250, "TimeBar", MessageDirection.FROM_SERVER,
                               InfrastructureType.HISTORY_PLANT, "TimeBar", "time_bar_pb2",
                               TimeBar, TimeBarDAO, is_streaming=True)
        self._register_template(251, "TickBar", MessageDirection.FROM_SERVER,
                               InfrastructureType.HISTORY_PLANT, "TickBar", "tick_bar_pb2",
                               TickBar, TickBarDAO, is_streaming=True)
        
        # Order Management Templates (300-356)
        self._register_template(312, "NewOrderRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.ORDER_PLANT, "RequestNewOrder", "request_new_order_pb2")
        self._register_template(313, "NewOrderResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.ORDER_PLANT, "ResponseNewOrder", "response_new_order_pb2")
        self._register_template(351, "RithmicOrderNotification", MessageDirection.FROM_SERVER,
                               InfrastructureType.ORDER_PLANT, "RithmicOrderNotification", "rithmic_order_notification_pb2",
                               OrderNotification, OrderNotificationDAO, is_streaming=True)
        
        # Position and PnL Templates (400-451)
        self._register_template(400, "PnLPositionUpdatesRequest", MessageDirection.FROM_CLIENT,
                               InfrastructureType.PNL_PLANT, "RequestPnLPositionUpdates", "request_pnl_position_updates_pb2")
        self._register_template(401, "PnLPositionUpdatesResponse", MessageDirection.FROM_SERVER,
                               InfrastructureType.PNL_PLANT, "ResponsePnLPositionUpdates", "response_pnl_position_updates_pb2")
        self._register_template(450, "InstrumentPnLPositionUpdate", MessageDirection.FROM_SERVER,
                               InfrastructureType.PNL_PLANT, "InstrumentPnLPositionUpdate", "instrument_pnl_position_update_pb2",
                               Position, PositionDAO, is_streaming=True)
        
        logger.info(f"Initialized {len(self._templates)} template definitions")
    
    def _register_template(self, template_id: int, template_name: str, direction: MessageDirection,
                          infrastructure_type: InfrastructureType, protobuf_class_name: str,
                          protobuf_module_name: str, database_model_class: Optional[Type] = None,
                          database_dao_class: Optional[Type] = None, is_streaming: bool = False,
                          is_multi_response: bool = False, priority: int = 1):
        """Register a template definition."""
        template_def = TemplateDefinition(
            template_id=template_id,
            template_name=template_name,
            direction=direction,
            infrastructure_type=infrastructure_type,
            protobuf_class_name=protobuf_class_name,
            protobuf_module_name=protobuf_module_name,
            database_model_class=database_model_class,
            database_dao_class=database_dao_class,
            is_streaming=is_streaming,
            is_multi_response=is_multi_response,
            priority=priority
        )
        self._templates[template_id] = template_def
    
    def get_template(self, template_id: int) -> Optional[TemplateDefinition]:
        """Get template definition by ID."""
        return self._templates.get(template_id)
    
    def get_all_templates(self) -> Dict[int, TemplateDefinition]:
        """Get all template definitions."""
        return self._templates.copy()
    
    def get_templates_by_infrastructure(self, infrastructure_type: InfrastructureType) -> List[TemplateDefinition]:
        """Get templates for specific infrastructure type."""
        return [template for template in self._templates.values() 
                if template.infrastructure_type == infrastructure_type]
    
    def get_streaming_templates(self) -> List[TemplateDefinition]:
        """Get all streaming templates."""
        return [template for template in self._templates.values() if template.is_streaming]
    
    def register_dynamic_template(self, template_id: int, **kwargs):
        """Register a new template dynamically."""
        if template_id in self._templates:
            logger.warning(f"Template {template_id} already registered, overriding")
        self._register_template(template_id, **kwargs)


# =============================================================================
# MESSAGE FACTORY
# =============================================================================

class MessageFactory:
    """Factory for creating protobuf message instances dynamically."""
    
    def __init__(self):
        self._protobuf_cache: Dict[str, Type] = {}
        self._module_cache: Dict[str, Any] = {}
    
    def get_protobuf_class(self, template_def: TemplateDefinition) -> Optional[Type]:
        """Get protobuf class for template definition."""
        cache_key = f"{template_def.protobuf_module_name}.{template_def.protobuf_class_name}"
        
        if cache_key in self._protobuf_cache:
            return self._protobuf_cache[cache_key]
        
        try:
            # Load module if not cached
            if template_def.protobuf_module_name not in self._module_cache:
                module_path = f"proto_generated.{template_def.protobuf_module_name}"
                module = importlib.import_module(module_path)
                self._module_cache[template_def.protobuf_module_name] = module
            
            # Get class from module
            module = self._module_cache[template_def.protobuf_module_name]
            protobuf_class = getattr(module, template_def.protobuf_class_name)
            
            self._protobuf_cache[cache_key] = protobuf_class
            return protobuf_class
            
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to load protobuf class {cache_key}: {e}")
            return None
    
    def parse_message(self, template_def: TemplateDefinition, raw_data: bytes) -> Optional[Any]:
        """Parse raw message data using template definition."""
        protobuf_class = self.get_protobuf_class(template_def)
        if not protobuf_class:
            return None
        
        try:
            message = protobuf_class()
            message.ParseFromString(raw_data)
            return message
        except Exception as e:
            logger.error(f"Failed to parse message for template {template_def.template_id}: {e}")
            return None


# =============================================================================
# MESSAGE PROCESSOR
# =============================================================================

class MessageProcessor:
    """Comprehensive message processing pipeline for Rithmic API."""
    
    def __init__(self, enable_database_persistence: bool = True, enable_async_processing: bool = True):
        self.template_registry = TemplateRegistry()
        self.message_factory = MessageFactory()
        self.db_manager = get_database_manager() if enable_database_persistence else None
        self.enable_async = enable_async_processing
        
        # DAO instances for database persistence
        self._dao_cache: Dict[Type, Any] = {}
        self._initialize_dao_cache()
        
        # Performance tracking
        self._stats = defaultdict(int)
        self._processing_times = []
        
        logger.info("Message processor initialized with comprehensive template support")
    
    def _initialize_dao_cache(self):
        """Initialize DAO instance cache."""
        if not self.db_manager:
            return
            
        dao_classes = [
            SymbolDAO, BestBidOfferDAO, LastTradeDAO, UserSessionDAO,
            DepthByOrderSnapshotDAO, DepthByOrderUpdateDAO, OrderBookLevelDAO,
            TimeBarDAO, TickBarDAO, OrderNotificationDAO, OrderFillDAO,
            PositionDAO, AccountInfoDAO, SystemEventDAO, TradeStatisticsDAO,
            QuoteStatisticsDAO, HeartbeatDAO
        ]
        
        for dao_class in dao_classes:
            try:
                dao_instance = dao_class()
                self._dao_cache[dao_class] = dao_instance
            except Exception as e:
                logger.error(f"Failed to initialize DAO {dao_class.__name__}: {e}")
    
    async def process_message(self, template_id: int, raw_data: bytes, 
                            metadata: Optional[Dict[str, Any]] = None) -> ProcessedMessage:
        """
        Process a message with the given template ID and raw data.
        
        Args:
            template_id: Rithmic API template ID
            raw_data: Raw protobuf message bytes
            metadata: Optional metadata dictionary
            
        Returns:
            ProcessedMessage with parsing and persistence results
        """
        start_time = time.time()
        metadata = metadata or {}
        
        # Get template definition
        template_def = self.template_registry.get_template(template_id)
        if not template_def:
            self._stats['unknown_templates'] += 1
            return ProcessedMessage(
                template_id=template_id,
                template_name=f"Unknown_{template_id}",
                direction=MessageDirection.FROM_SERVER,
                infrastructure_type=InfrastructureType.TICKER_PLANT,
                raw_data=raw_data,
                parsed_message=None,
                metadata=metadata,
                error=f"Unknown template ID: {template_id}"
            )
        
        # Parse message
        parsed_message = self.message_factory.parse_message(template_def, raw_data)
        processing_time = time.time() - start_time
        
        # Create processed message
        processed_msg = ProcessedMessage(
            template_id=template_id,
            template_name=template_def.template_name,
            direction=template_def.direction,
            infrastructure_type=template_def.infrastructure_type,
            raw_data=raw_data,
            parsed_message=parsed_message,
            metadata=metadata,
            processing_time=processing_time
        )
        
        if parsed_message is None:
            processed_msg.error = "Failed to parse message"
            self._stats['parse_errors'] += 1
        else:
            self._stats['parsed_messages'] += 1
            
            # Persist to database if enabled and configured
            if (self.db_manager and template_def.database_dao_class and 
                template_def.database_model_class):
                try:
                    if self.enable_async:
                        asyncio.create_task(self._persist_message_async(template_def, parsed_message))
                    else:
                        await self._persist_message_sync(template_def, parsed_message)
                    processed_msg.database_persisted = True
                    self._stats['persisted_messages'] += 1
                except Exception as e:
                    logger.error(f"Database persistence failed for template {template_id}: {e}")
                    processed_msg.error = f"Persistence error: {e}"
                    self._stats['persistence_errors'] += 1
        
        # Update performance tracking
        self._processing_times.append(processing_time)
        if len(self._processing_times) > 1000:
            self._processing_times = self._processing_times[-500:]  # Keep last 500
        
        return processed_msg
    
    async def _persist_message_async(self, template_def: TemplateDefinition, parsed_message: Any):
        """Asynchronously persist message to database."""
        await asyncio.get_event_loop().run_in_executor(
            None, self._persist_message_sync, template_def, parsed_message
        )
    
    def _persist_message_sync(self, template_def: TemplateDefinition, parsed_message: Any):
        """Synchronously persist message to database."""
        dao_instance = self._dao_cache.get(template_def.database_dao_class)
        if not dao_instance:
            logger.error(f"DAO not found for {template_def.database_dao_class}")
            return
        
        try:
            # Convert protobuf message to database model
            model_instance = self._protobuf_to_model(template_def, parsed_message)
            if model_instance:
                # Use upsert if available, otherwise insert
                if hasattr(dao_instance, 'upsert'):
                    dao_instance.upsert(model_instance)
                else:
                    dao_instance.insert(model_instance)
        except Exception as e:
            logger.error(f"Model conversion/persistence failed: {e}")
            raise
    
    def _protobuf_to_model(self, template_def: TemplateDefinition, parsed_message: Any) -> Optional[Any]:
        """Convert protobuf message to database model instance."""
        if not template_def.database_model_class:
            return None
        
        try:
            # Extract common fields from protobuf message
            model_data = {}
            
            # Handle common patterns based on template type
            if template_def.template_id in [150]:  # LastTrade
                model_data = {
                    'symbol': getattr(parsed_message, 'symbol', ''),
                    'exchange': getattr(parsed_message, 'exchange', ''),
                    'trade_price': self._safe_decimal(getattr(parsed_message, 'trade_price', None)),
                    'trade_size': getattr(parsed_message, 'trade_size', None),
                    'volume': getattr(parsed_message, 'volume', None),
                    'ssboe': getattr(parsed_message, 'ssboe', None),
                    'usecs': getattr(parsed_message, 'usecs', None),
                    'trade_timestamp': self._ssboe_to_datetime(
                        getattr(parsed_message, 'ssboe', 0),
                        getattr(parsed_message, 'usecs', 0)
                    )
                }
            elif template_def.template_id in [151]:  # BestBidOffer
                model_data = {
                    'symbol': getattr(parsed_message, 'symbol', ''),
                    'exchange': getattr(parsed_message, 'exchange', ''),
                    'bid_price': self._safe_decimal(getattr(parsed_message, 'bid_price', None)),
                    'bid_size': getattr(parsed_message, 'bid_size', None),
                    'ask_price': self._safe_decimal(getattr(parsed_message, 'ask_price', None)),
                    'ask_size': getattr(parsed_message, 'ask_size', None),
                    'ssboe': getattr(parsed_message, 'ssboe', None),
                    'usecs': getattr(parsed_message, 'usecs', None),
                    'quote_timestamp': self._ssboe_to_datetime(
                        getattr(parsed_message, 'ssboe', 0),
                        getattr(parsed_message, 'usecs', 0)
                    )
                }
            elif template_def.template_id in [351]:  # OrderNotification
                model_data = {
                    'notify_type': getattr(parsed_message, 'notify_type', ''),
                    'symbol': getattr(parsed_message, 'symbol', ''),
                    'exchange': getattr(parsed_message, 'exchange', ''),
                    'account_id': getattr(parsed_message, 'account_id', ''),
                    'quantity': getattr(parsed_message, 'quantity', None),
                    'price': self._safe_decimal(getattr(parsed_message, 'price', None)),
                    'transaction_type': getattr(parsed_message, 'transaction_type', ''),
                    'ssboe': getattr(parsed_message, 'ssboe', None),
                    'usecs': getattr(parsed_message, 'usecs', None),
                    'order_timestamp': self._ssboe_to_datetime(
                        getattr(parsed_message, 'ssboe', 0),
                        getattr(parsed_message, 'usecs', 0)
                    )
                }
            # Add more template-specific conversions as needed
            
            # Create model instance with extracted data
            return template_def.database_model_class(**model_data)
            
        except Exception as e:
            logger.error(f"Protobuf to model conversion failed for template {template_def.template_id}: {e}")
            return None
    
    def _safe_decimal(self, value: Union[float, int, None]) -> Optional[Decimal]:
        """Safely convert numeric value to Decimal."""
        if value is None:
            return None
        try:
            return Decimal(str(value))
        except:
            return None
    
    def _ssboe_to_datetime(self, ssboe: int, usecs: int) -> datetime:
        """Convert SSBOE (seconds since epoch) and microseconds to datetime."""
        return datetime.fromtimestamp(ssboe + usecs / 1_000_000)
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get message processing statistics."""
        stats = dict(self._stats)
        
        if self._processing_times:
            avg_time = sum(self._processing_times) / len(self._processing_times)
            max_time = max(self._processing_times)
            min_time = min(self._processing_times)
            
            stats.update({
                'avg_processing_time_ms': round(avg_time * 1000, 3),
                'max_processing_time_ms': round(max_time * 1000, 3),
                'min_processing_time_ms': round(min_time * 1000, 3),
                'total_processed': len(self._processing_times)
            })
        
        stats['registered_templates'] = len(self.template_registry.get_all_templates())
        stats['dao_cache_size'] = len(self._dao_cache)
        
        return stats
    
    def get_supported_templates(self) -> List[Dict[str, Any]]:
        """Get list of all supported templates."""
        templates = []
        for template_def in self.template_registry.get_all_templates().values():
            templates.append({
                'template_id': template_def.template_id,
                'template_name': template_def.template_name,
                'direction': template_def.direction.value,
                'infrastructure_type': template_def.infrastructure_type.value,
                'is_streaming': template_def.is_streaming,
                'has_database_persistence': template_def.database_dao_class is not None
            })
        return sorted(templates, key=lambda x: x['template_id'])


# =============================================================================
# GLOBAL MESSAGE PROCESSOR INSTANCE
# =============================================================================

_message_processor = None

def get_message_processor() -> MessageProcessor:
    """Get the global message processor instance."""
    global _message_processor
    if _message_processor is None:
        _message_processor = MessageProcessor()
    return _message_processor

def initialize_message_processor(**kwargs):
    """Initialize the global message processor with custom configuration."""
    global _message_processor
    _message_processor = MessageProcessor(**kwargs)