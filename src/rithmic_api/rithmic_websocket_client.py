import asyncio
import websockets
import ssl
import logging
import json
import time
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List, Callable, Union, Awaitable
from pathlib import Path
from dataclasses import dataclass, field
from collections import defaultdict
from enum import Enum

# Import configuration and utilities
from .config import config
from ..utils.data_utils import (
    clear_data_directory, 
    save_text_data, 
    save_json_data, 
    generate_timestamp_filename,
    protobuf_message_to_text,
    append_tx_message,
    append_rx_message,
    initialize_message_logging
)

# Import database components
from ..database.database_manager import get_database_manager
from ..database.models import (
    Symbol, SymbolDAO, BestBidOffer, BestBidOfferDAO,
    LastTrade, LastTradeDAO, UserSession, UserSessionDAO,
    protobuf_to_symbol, protobuf_to_best_bid_offer,
    create_all_tables
)

# Import comprehensive message processor
from .message_processor import get_message_processor

# Import generated protobuf classes
import sys
import os
# Add proto_generated to path (relative to project root)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.join(project_root, 'proto_generated'))

from proto_generated import (
    base_pb2,
    request_rithmic_system_info_pb2,
    response_rithmic_system_info_pb2,
    request_login_pb2,
    response_login_pb2,
    request_heartbeat_pb2,
    response_heartbeat_pb2,
    request_front_month_contract_pb2,
    response_front_month_contract_pb2,
    request_search_symbols_pb2,
    response_search_symbols_pb2,
    request_market_data_update_pb2,
    response_market_data_update_pb2,
    request_depth_by_order_updates_pb2,
    response_depth_by_order_updates_pb2,
    request_depth_by_order_snapshot_pb2,
    response_depth_by_order_snapshot_pb2,
    last_trade_pb2,
    best_bid_offer_pb2,
    order_book_pb2,
    depth_by_order_pb2,
    depth_by_order_end_event_pb2
)

# ========================= MESSAGE ROUTING INFRASTRUCTURE =========================

class MessageType(Enum):
    """Types of messages for routing."""
    REQUEST_RESPONSE = "request_response"
    STREAMING_DATA = "streaming_data"
    HEARTBEAT = "heartbeat"
    ERROR = "error"

@dataclass
class PendingRequest:
    """Tracks a pending request waiting for response."""
    request_id: str
    template_id: int
    response_template_ids: List[int]
    future: asyncio.Future
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    timeout: float = 30.0
    
    # Multi-response support
    is_multi_response: bool = False
    max_responses: Optional[int] = None
    max_timeouts: int = 3
    collected_responses: List[tuple] = field(default_factory=list)
    timeout_count: int = 0
    
    def is_expired(self) -> bool:
        """Check if request has expired."""
        return time.time() - self.created_at > self.timeout
    
    def is_multi_response_complete(self) -> bool:
        """Check if multi-response request is complete."""
        if not self.is_multi_response:
            return len(self.collected_responses) > 0
        
        # Check max responses limit
        if self.max_responses and len(self.collected_responses) >= self.max_responses:
            return True
            
        # Check for end-of-data markers in the last response
        if self.collected_responses:
            _, last_message, _ = self.collected_responses[-1]
            if hasattr(last_message, 'rp_code') and hasattr(last_message, 'rq_handler_rp_code'):
                rp_codes = list(last_message.rp_code) if last_message.rp_code else []
                rq_handler_codes = list(last_message.rq_handler_rp_code) if last_message.rq_handler_rp_code else []
                
                # If rp_code equals rq_handler_rp_code, this indicates end of responses
                if rp_codes == rq_handler_codes and rp_codes:
                    return True
        
        # Check timeout count
        return self.timeout_count >= self.max_timeouts
    
    def add_response(self, template_id: int, message, raw_data: bytes):
        """Add a response to the collected responses."""
        self.collected_responses.append((template_id, message, raw_data))
        self.timeout_count = 0  # Reset timeout counter on successful response

@dataclass
class StreamingSubscription:
    """Tracks a streaming subscription."""
    subscription_id: str
    symbol: str
    exchange: str
    template_ids: List[int]
    callback: Optional[Callable] = None
    active: bool = True
    created_at: float = field(default_factory=time.time)

class MessageRouter:
    """Central message router that handles websocket message dispatching."""
    
    def __init__(self, websocket_client):
        self.client = websocket_client
        self.logger = logging.getLogger(f"{__name__}.MessageRouter")
        
        # Request tracking
        self.pending_requests: Dict[str, PendingRequest] = {}
        self.template_to_requests: Dict[int, List[str]] = defaultdict(list)
        
        # Subscription tracking
        self.streaming_subscriptions: Dict[str, StreamingSubscription] = {}
        self.template_to_subscriptions: Dict[int, List[str]] = defaultdict(list)
        
        # Router state
        self.router_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        # Message statistics
        self.stats = {
            'messages_routed': 0,
            'requests_handled': 0,
            'streaming_messages': 0,
            'routing_errors': 0
        }
    
    async def start(self):
        """Start the message router."""
        if self.is_running:
            return
            
        self.is_running = True
        self.router_task = asyncio.create_task(self._message_loop())
        self.logger.info("Message router started")
    
    async def stop(self):
        """Stop the message router."""
        self.is_running = False
        
        if self.router_task:
            self.router_task.cancel()
            try:
                await self.router_task
            except asyncio.CancelledError:
                pass
        
        # Cancel all pending requests
        for request in self.pending_requests.values():
            if not request.future.done():
                request.future.cancel()
        
        self.pending_requests.clear()
        self.template_to_requests.clear()
        self.streaming_subscriptions.clear()
        self.template_to_subscriptions.clear()
        
        self.logger.info("Message router stopped")
    
    async def _message_loop(self):
        """Main message routing loop."""
        while self.is_running and self.client.is_connected:
            try:
                # Check for expired requests
                await self._cleanup_expired_requests()
                
                # Receive message from websocket
                template_id, message, raw_data = await self.client._raw_receive_message()
                
                if template_id is None:
                    continue
                
                self.stats['messages_routed'] += 1
                
                # Route the message
                await self._route_message(template_id, message, raw_data)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in message routing loop: {e}")
                self.stats['routing_errors'] += 1
                await asyncio.sleep(0.1)  # Brief pause on error
    
    async def _route_message(self, template_id: int, message, raw_data: bytes):
        """Route message to appropriate handler."""
        try:
            # Check if this is a response to a pending request
            if await self._handle_request_response(template_id, message, raw_data):
                self.stats['requests_handled'] += 1
                return
            
            # Check if this is streaming data for subscriptions
            if await self._handle_streaming_data(template_id, message, raw_data):
                self.stats['streaming_messages'] += 1
                return
            
            # Handle heartbeats
            if template_id == self.client.TEMPLATE_IDS['RESPONSE_HEARTBEAT']:
                self.logger.debug("Heartbeat response received")
                return
            
            # Log unhandled messages
            self.logger.debug(f"Unhandled message: template_id={template_id}")
            
        except Exception as e:
            self.logger.error(f"Error routing message {template_id}: {e}")
            self.stats['routing_errors'] += 1
    
    async def _handle_request_response(self, template_id: int, message, raw_data: bytes) -> bool:
        """Handle request-response messages."""
        request_ids = self.template_to_requests.get(template_id, [])
        if not request_ids:
            return False
        
        # Find the matching request
        for request_id in request_ids[:]:
            pending_request = self.pending_requests.get(request_id)
            if not pending_request:
                continue
            
            if template_id in pending_request.response_template_ids:
                if pending_request.future.done():
                    continue  # Request already completed
                
                if pending_request.is_multi_response:
                    # Handle multi-response pattern
                    pending_request.add_response(template_id, message, raw_data)
                    
                    # Check if multi-response is complete
                    if pending_request.is_multi_response_complete():
                        pending_request.future.set_result(pending_request.collected_responses)
                        self._remove_pending_request(request_id)
                else:
                    # Handle single-response pattern (legacy behavior)
                    pending_request.future.set_result((template_id, message, raw_data))
                    self._remove_pending_request(request_id)
                
                return True
        
        return False
    
    async def _handle_streaming_data(self, template_id: int, message, raw_data: bytes) -> bool:
        """Handle streaming data messages."""
        subscription_ids = self.template_to_subscriptions.get(template_id, [])
        if not subscription_ids:
            return False
        
        # Deliver to all matching subscriptions
        for subscription_id in subscription_ids:
            subscription = self.streaming_subscriptions.get(subscription_id)
            if not subscription or not subscription.active:
                continue
            
            try:
                if subscription.callback:
                    # Call subscription callback asynchronously
                    if asyncio.iscoroutinefunction(subscription.callback):
                        asyncio.create_task(subscription.callback(template_id, message, raw_data))
                    else:
                        subscription.callback(template_id, message, raw_data)
                else:
                    # Default handling - just log
                    symbol = getattr(message, 'symbol', 'N/A')
                    self.logger.debug(f"Streaming data for {symbol}: template_id={template_id}")
                    
            except Exception as e:
                self.logger.error(f"Error in subscription callback for {subscription.symbol}: {e}")
        
        return True
    
    async def _cleanup_expired_requests(self):
        """Clean up expired requests and handle multi-response timeouts."""
        current_time = time.time()
        expired_requests = []
        timeout_increment_requests = []
        
        for request_id, request in self.pending_requests.items():
            if request.is_expired():
                expired_requests.append(request_id)
            elif request.is_multi_response and request.collected_responses:
                # For multi-response requests, check if we should increment timeout counter
                # This happens when we haven't received a response in a reasonable time
                time_since_creation = current_time - request.created_at
                if time_since_creation > 2.0:  # 2 second intervals for timeout counting
                    timeout_increment_requests.append(request_id)
        
        # Handle expired requests
        for request_id in expired_requests:
            request = self.pending_requests.get(request_id)
            if request and not request.future.done():
                if request.is_multi_response and request.collected_responses:
                    # Multi-response with some data - return what we have
                    request.future.set_result(request.collected_responses)
                else:
                    # No data or single response - treat as timeout
                    request.future.set_exception(TimeoutError(f"Request {request_id} timed out"))
            self._remove_pending_request(request_id)
            self.logger.warning(f"Request {request_id} expired and was cleaned up")
        
        # Handle multi-response timeout increments
        for request_id in timeout_increment_requests:
            request = self.pending_requests.get(request_id)
            if request and not request.future.done():
                request.timeout_count += 1
                request.created_at = current_time  # Reset timer for next interval
                
                # Check if we should complete due to max timeouts
                if request.is_multi_response_complete():
                    request.future.set_result(request.collected_responses)
                    self._remove_pending_request(request_id)
                    self.logger.debug(f"Multi-response {request_id} completed after {len(request.collected_responses)} responses")
    
    def add_pending_request(self, request: PendingRequest) -> str:
        """Add a pending request to track."""
        self.pending_requests[request.request_id] = request
        
        for template_id in request.response_template_ids:
            self.template_to_requests[template_id].append(request.request_id)
        
        return request.request_id
    
    def _remove_pending_request(self, request_id: str):
        """Remove a pending request."""
        request = self.pending_requests.pop(request_id, None)
        if not request:
            return
        
        # Remove from template mappings
        for template_id in request.response_template_ids:
            try:
                self.template_to_requests[template_id].remove(request_id)
            except ValueError:
                pass
    
    def add_streaming_subscription(self, subscription: StreamingSubscription) -> str:
        """Add a streaming subscription."""
        self.streaming_subscriptions[subscription.subscription_id] = subscription
        
        for template_id in subscription.template_ids:
            self.template_to_subscriptions[template_id].append(subscription.subscription_id)
        
        return subscription.subscription_id
    
    def remove_streaming_subscription(self, subscription_id: str):
        """Remove a streaming subscription."""
        subscription = self.streaming_subscriptions.pop(subscription_id, None)
        if not subscription:
            return
        
        # Remove from template mappings
        for template_id in subscription.template_ids:
            try:
                self.template_to_subscriptions[template_id].remove(subscription_id)
            except ValueError:
                pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get router statistics."""
        return {
            **self.stats,
            'pending_requests': len(self.pending_requests),
            'active_subscriptions': len([s for s in self.streaming_subscriptions.values() if s.active]),
            'total_subscriptions': len(self.streaming_subscriptions)
        }

class RithmicWebSocketClient:
    """
    Comprehensive WebSocket client for Rithmic API with authentication,
    market data subscription, and data collection capabilities.
    """
    
    # Template IDs from the API documentation
    TEMPLATE_IDS = {
        'REQUEST_RITHMIC_SYSTEM_INFO': 16,
        'RESPONSE_RITHMIC_SYSTEM_INFO': 17,
        'REQUEST_LOGIN': 10,
        'RESPONSE_LOGIN': 11,
        'REQUEST_HEARTBEAT': 18,
        'RESPONSE_HEARTBEAT': 19,
        'REQUEST_FRONT_MONTH_CONTRACT': 113,
        'RESPONSE_FRONT_MONTH_CONTRACT': 114,
        'REQUEST_SEARCH_SYMBOLS': 109,
        'RESPONSE_SEARCH_SYMBOLS': 110,
        'REQUEST_MARKET_DATA_UPDATE': 100,
        'RESPONSE_MARKET_DATA_UPDATE': 101,
        'REQUEST_DEPTH_BY_ORDER_UPDATES': 117,
        'RESPONSE_DEPTH_BY_ORDER_UPDATES': 118,
        'REQUEST_DEPTH_BY_ORDER_SNAPSHOT': 115,
        'RESPONSE_DEPTH_BY_ORDER_SNAPSHOT': 116,
        'LAST_TRADE': 150,
        'BEST_BID_OFFER': 151,
        'TRADE_STATISTICS': 152,
        'END_OF_DAY_PRICES': 155,
        'ORDER_BOOK': 156,
        'DEPTH_BY_ORDER': 160,
        'DEPTH_BY_ORDER_END_EVENT': 161,
        # Additional reference data endpoints
        'REQUEST_GET_INSTRUMENT_BY_UNDERLYING': 102,
        'RESPONSE_GET_INSTRUMENT_BY_UNDERLYING': 103,
        'REQUEST_GIVE_TICK_SIZE_TYPE_TABLE': 107,
        'RESPONSE_GIVE_TICK_SIZE_TYPE_TABLE': 108,
        'REQUEST_PRODUCT_CODES': 111,
        'RESPONSE_PRODUCT_CODES': 112,
        'REQUEST_GET_VOLUME_AT_PRICE': 119,
        'RESPONSE_GET_VOLUME_AT_PRICE': 120,
        'REQUEST_AUXILIARY_REFERENCE_DATA': 121,
        'RESPONSE_AUXILIARY_REFERENCE_DATA': 122,
        'REQUEST_RITHMIC_SYSTEM_GATEWAY_INFO': 20,
        'RESPONSE_RITHMIC_SYSTEM_GATEWAY_INFO': 21,
        'REQUEST_LIST_EXCHANGE_PERMISSIONS': 342,
        'RESPONSE_LIST_EXCHANGE_PERMISSIONS': 343,
    }
    
    # All supported instrument types
    INSTRUMENT_TYPES = {
        'FUTURE': 1,
        'FUTURE_OPTION': 2,
        'FUTURE_STRATEGY': 3,
        'EQUITY': 4,
        'EQUITY_OPTION': 5,
        'EQUITY_STRATEGY': 6,
        'INDEX': 7,
        'INDEX_OPTION': 8,
        'SPREAD': 9,
        'SYNTHETIC': 10
    }
    
    def __init__(self, 
                 uri: Optional[str] = None,
                 ssl_cert_path: Optional[str] = None,
                 clear_data: Optional[bool] = None):
        """
        Initialize the Rithmic WebSocket client.
        
        Args:
            uri: WebSocket URI for Rithmic API (defaults to config)
            ssl_cert_path: Path to SSL certificate file (defaults to config)
            clear_data: Whether to clear data directory on start (defaults to config)
        """
        self.uri = uri or config.uri
        self.ssl_cert_path = ssl_cert_path or config.ssl_cert_path
        self.websocket = None
        self.is_connected = False
        self.is_authenticated = False
        self.available_systems = []
        self.heartbeat_interval = 30.0  # Default heartbeat interval
        self.heartbeat_task = None
        self.message_handlers = {}
        self.data_dir = Path(config.data_directory)
        
        # Clear data directory if requested
        clear_data_flag = clear_data if clear_data is not None else config.clear_data_on_start
        if clear_data_flag:
            clear_data_directory(self.data_dir)
        else:
            self.data_dir.mkdir(exist_ok=True)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Message counter for unique identification
        self.message_counter = 0
        
        # Template version from config
        self.template_version = config.template_version
        
        # Initialize message router for concurrent operations
        self.message_router = MessageRouter(self)
        
        # Request timeout configuration
        self.default_timeout = 30.0
        
        # Connection state lock for thread safety
        self._connection_lock = asyncio.Lock()
        
        # Initialize database components
        self.database_enabled = True
        self.db_manager = None
        self.symbol_dao = None
        self.bbo_dao = None
        self.trade_dao = None
        self.session_dao = None
        self._init_database()
        
        # Initialize comprehensive message processor
        self.message_processor_enabled = True
        self.message_processor = None
        self._init_message_processor()
    
    def _init_database(self):
        """Initialize database connection and DAOs."""
        try:
            self.db_manager = get_database_manager()
            
            # Initialize Data Access Objects
            self.symbol_dao = SymbolDAO()
            self.bbo_dao = BestBidOfferDAO()
            self.trade_dao = LastTradeDAO()
            self.session_dao = UserSessionDAO()
            
            # Create tables if they don't exist
            create_all_tables()
            
            self.logger.info("Database integration initialized successfully")
            
        except Exception as e:
            self.logger.warning(f"Database initialization failed: {e}")
            self.database_enabled = False
    
    def _init_message_processor(self):
        """Initialize comprehensive message processor."""
        try:
            self.message_processor = get_message_processor()
            self.logger.info("Comprehensive message processor initialized successfully")
            
        except Exception as e:
            self.logger.warning(f"Message processor initialization failed: {e}")
            self.message_processor_enabled = False
    
    @property
    def pending_requests(self) -> Dict[str, PendingRequest]:
        """Access to pending requests from message router."""
        return self.message_router.pending_requests
    
    @property
    def streaming_subscriptions(self) -> Dict[str, StreamingSubscription]:
        """Access to streaming subscriptions from message router."""
        return self.message_router.streaming_subscriptions
    
    async def _persist_message(self, template_id: int, message, raw_data: bytes):
        """Persist message to database based on template ID."""
        if not self.database_enabled:
            return
            
        try:
            # Handle symbol/reference data
            if template_id == self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']:
                await self._persist_symbol_data(message)
            
            # Handle real-time market data
            elif template_id == self.TEMPLATE_IDS['BEST_BID_OFFER']:
                await self._persist_best_bid_offer(message)
            elif template_id == self.TEMPLATE_IDS['LAST_TRADE']:
                await self._persist_last_trade(message)
            elif template_id == self.TEMPLATE_IDS['TRADE_STATISTICS']:
                await self._persist_trade_statistics(message)
            elif template_id == self.TEMPLATE_IDS['END_OF_DAY_PRICES']:
                await self._persist_end_of_day_prices(message)
            
            # Handle session data
            elif template_id == self.TEMPLATE_IDS['RESPONSE_LOGIN']:
                await self._persist_login_session(message)
                
        except Exception as e:
            self.logger.error(f"Error persisting message (template_id={template_id}): {e}")
    
    async def _persist_symbol_data(self, message):
        """Persist symbol search response data."""
        try:
            symbol = protobuf_to_symbol(message)
            if symbol.symbol and symbol.exchange:
                self.symbol_dao.upsert(symbol)
                self.logger.debug(f"Persisted symbol: {symbol.symbol}@{symbol.exchange}")
        except Exception as e:
            self.logger.error(f"Error persisting symbol data: {e}")
    
    async def _persist_best_bid_offer(self, message):
        """Persist best bid/offer market data."""
        try:
            bbo = protobuf_to_best_bid_offer(message)
            if bbo.symbol and bbo.exchange:
                self.bbo_dao.insert(bbo)
                self.logger.debug(f"Persisted BBO: {bbo.symbol}@{bbo.exchange}")
        except Exception as e:
            self.logger.error(f"Error persisting BBO data: {e}")
    
    async def _persist_last_trade(self, message):
        """Persist last trade data."""
        try:
            # Create LastTrade from protobuf message
            trade = LastTrade(
                symbol=getattr(message, 'symbol', ''),
                exchange=getattr(message, 'exchange', ''),
                trade_price=getattr(message, 'trade_price', None),
                trade_size=getattr(message, 'trade_size', None),
                volume=getattr(message, 'volume', None),
                market_timestamp=datetime.now()
            )
            if trade.symbol and trade.exchange:
                self.trade_dao.insert(trade)
                self.logger.debug(f"Persisted trade: {trade.symbol}@{trade.exchange}")
        except Exception as e:
            self.logger.error(f"Error persisting trade data: {e}")
    
    async def _persist_login_session(self, message):
        """Persist login session data."""
        try:
            session = UserSession(
                unique_user_id=getattr(message, 'unique_user_id', ''),
                fcm_id=getattr(message, 'fcm_id', ''),
                heartbeat_interval=getattr(message, 'heartbeat_interval', None),
                template_version=getattr(message, 'template_version', ''),
                session_info={'login_response': str(message)}
            )
            if session.unique_user_id:
                self.session_dao.insert(session)
                self.logger.info(f"Persisted login session: {session.unique_user_id}")
        except Exception as e:
            self.logger.error(f"Error persisting session data: {e}")
    
    async def _persist_trade_statistics(self, message):
        """Persist trade statistics data."""
        try:
            self.logger.info(f"Received trade statistics for symbol: {getattr(message, 'symbol', 'N/A')}")
            # For now, just log the data - could add dedicated table later
            self.logger.debug(f"Trade statistics data: {message}")
        except Exception as e:
            self.logger.error(f"Error persisting trade statistics: {e}")
    
    async def _persist_end_of_day_prices(self, message):
        """Persist end of day prices data."""
        try:
            self.logger.info(f"Received end of day prices for symbol: {getattr(message, 'symbol', 'N/A')}")
            # For now, just log the data - could add dedicated table later
            self.logger.debug(f"End of day prices data: {message}")
        except Exception as e:
            self.logger.error(f"Error persisting end of day prices: {e}")
        
    def setup_ssl_context(self) -> ssl.SSLContext:
        """Setup SSL context with the required certificate."""
        ssl_context = ssl.create_default_context()
        
        # Load the certificate if it exists
        if Path(self.ssl_cert_path).exists():
            ssl_context.load_verify_locations(self.ssl_cert_path)
        else:
            self.logger.warning(f"SSL certificate not found at {self.ssl_cert_path}")
            # For development, allow unverified SSL
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
        return ssl_context
    
    async def connect(self) -> bool:
        """Establish WebSocket connection."""
        async with self._connection_lock:
            try:
                ssl_context = self.setup_ssl_context()
                self.websocket = await websockets.connect(
                    self.uri, 
                    ssl=ssl_context,
                    ping_interval=None,  # Disable built-in ping/pong
                    ping_timeout=None
                )
                self.is_connected = True
                self.logger.info(f"Connected to {self.uri}")
                
                # Start message router for optimized concurrent operations
                await self.message_router.start()
                self.logger.info("Message router started")
                
                return True
            except Exception as e:
                self.logger.error(f"Connection failed: {e}")
                return False
    
    async def disconnect(self):
        """Close WebSocket connection."""
        async with self._connection_lock:
            # Stop message router first
            await self.message_router.stop()
            
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
                
            self.is_connected = False
            self.is_authenticated = False
            self.logger.info("Disconnected from server")
    
    def initialize_data_collection(self):
        """Initialize message logging directories."""
        try:
            initialize_message_logging(self.data_dir)
            self.logger.info("Initialized message logging for Tx/Rx")
        except Exception as e:
            self.logger.error(f"Failed to initialize message logging: {e}")
    
    def pack_message(self, message) -> bytes:
        """
        Pack a protobuf message for transmission.
        The message is sent directly as serialized protobuf (no length prefix).
        """
        return message.SerializeToString()
    
    def unpack_message(self, data: bytes) -> tuple:
        """
        Unpack received binary data.
        Returns: (template_id, message_data)
        """
        try:
            # Parse the base message to get template ID
            base = base_pb2.Base()
            base.ParseFromString(data)
            return base.template_id, data
        except Exception as e:
            self.logger.error(f"Error parsing message: {e}")
            return 0, data
    
    async def send_message(self, message):
        """Send a protobuf message to the server."""
        if not self.is_connected or not self.websocket:
            raise ConnectionError("Not connected to server")
            
        packed_message = self.pack_message(message)
        await self.websocket.send(packed_message)
        
        # Log the message for debugging
        self.logger.debug(f"Sent message: {type(message).__name__}")
        
        # Save message to Tx directory by message type
        text_content = protobuf_message_to_text(message)
        message_type = type(message).__name__
        # Get template ID from the message if available
        template_id = getattr(message, 'template_id', 0)
        json_data = {
            'template_id': template_id,
            'timestamp': datetime.now().isoformat(),
            'message_type': message_type,
            'message_data': self.message_to_dict(message)
        }
        append_tx_message(self.data_dir, message_type, template_id, json_data, text_content)
    
    async def _raw_receive_message(self):
        """Raw receive method used by message router - single point of websocket.recv()"""
        if not self.is_connected or not self.websocket:
            raise ConnectionError("Not connected to server")
            
        try:
            raw_data = await self.websocket.recv()
            
            template_id, message_data = self.unpack_message(raw_data)
            
            self.logger.debug(f"Received message with template ID: {template_id}")
            
            # Try to parse the message based on template ID
            parsed_message = self.parse_message_by_template_id(template_id, message_data)
            
            if parsed_message:
                # Save parsed message to Rx directory by message type
                parsed_text_content = protobuf_message_to_text(parsed_message)
                message_type = type(parsed_message).__name__
                json_data = {
                    'template_id': template_id,
                    'timestamp': datetime.now().isoformat(),
                    'message_type': message_type,
                    'message_data': self.message_to_dict(parsed_message)
                }
                append_rx_message(self.data_dir, message_type, template_id, json_data, parsed_text_content)
                
                # Persist message to database
                await self._persist_message(template_id, parsed_message, raw_data)
                
                # Process message through comprehensive message processor
                if self.message_processor_enabled and self.message_processor:
                    await self.message_processor.process_message(template_id, raw_data, 
                                                               metadata={'source': 'websocket', 'parsed_message': parsed_message})
            
            return template_id, parsed_message, raw_data
            
        except websockets.exceptions.ConnectionClosed:
            self.logger.error("WebSocket connection closed")
            self.is_connected = False
            raise
        except Exception as e:
            self.logger.error(f"Error receiving message: {e}")
            raise

    async def receive_message(self):
        """LEGACY: Receive and parse a message from the server - Use send_request() instead for new code."""
        self.logger.warning("Using legacy receive_message() - consider upgrading to send_request() for better concurrency")
        return await self._raw_receive_message()
    
    async def send_request(self, 
                          request_message, 
                          expected_response_template_ids: List[int],
                          timeout: float = None) -> tuple:
        """Send a request and wait for response using optimized message routing.
        
        Args:
            request_message: Protobuf request message to send
            expected_response_template_ids: List of template IDs to wait for in response
            timeout: Request timeout in seconds (defaults to self.default_timeout)
            
        Returns:
            Tuple of (template_id, response_message, raw_data)
        """
        if not self.is_authenticated and not isinstance(request_message, (
            request_rithmic_system_info_pb2.RequestRithmicSystemInfo,
            request_login_pb2.RequestLogin
        )):
            raise Exception("Not authenticated. Please login first.")
        
        if not self.message_router.is_running:
            await self.message_router.start()
        
        timeout = timeout or self.default_timeout
        request_id = str(uuid.uuid4())
        
        # Create pending request
        pending_request = PendingRequest(
            request_id=request_id,
            template_id=getattr(request_message, 'template_id', 0),
            response_template_ids=expected_response_template_ids,
            future=asyncio.Future(),
            timeout=timeout
        )
        
        # Register the request
        self.message_router.add_pending_request(pending_request)
        
        try:
            # Send the request
            await self.send_message(request_message)
            
            # Wait for response
            template_id, response_message, raw_data = await asyncio.wait_for(
                pending_request.future, timeout=timeout
            )
            
            return template_id, response_message, raw_data
            
        except asyncio.TimeoutError:
            self.logger.error(f"Request {request_id} timed out after {timeout}s")
            raise
        except Exception as e:
            self.logger.error(f"Error in send_request {request_id}: {e}")
            raise
        finally:
            # Clean up the request if it's still pending
            self.message_router._remove_pending_request(request_id)
    
    async def send_request_multi_response(self,
                                        request_message, 
                                        expected_response_template_ids: List[int],
                                        timeout: float = None,
                                        max_responses: int = None,
                                        max_timeouts: int = 3) -> List[tuple]:
        """Send a request and collect multiple responses using MessageRouter.
        
        Args:
            request_message: Protobuf request message to send
            expected_response_template_ids: List of template IDs to wait for in response
            timeout: Total timeout in seconds (defaults to self.default_timeout)
            max_responses: Maximum number of responses to collect (None = unlimited)
            max_timeouts: Maximum number of consecutive timeouts before giving up
            
        Returns:
            List of tuples: [(template_id, response_message, raw_data), ...]
        """
        if not self.is_authenticated and not isinstance(request_message, (
            request_rithmic_system_info_pb2.RequestRithmicSystemInfo,
            request_login_pb2.RequestLogin
        )):
            raise Exception("Not authenticated. Please login first.")
        
        if not self.message_router.is_running:
            await self.message_router.start()
        
        timeout = timeout or self.default_timeout
        request_id = str(uuid.uuid4())
        
        self.logger.debug(f"Starting multi-response request {request_id}")
        
        # Create a multi-response pending request
        future = asyncio.Future()
        pending_request = PendingRequest(
            request_id=request_id,
            template_id=getattr(request_message, 'template_id', 0),
            response_template_ids=expected_response_template_ids,
            future=future,
            timeout=timeout,
            is_multi_response=True,
            max_responses=max_responses,
            max_timeouts=max_timeouts
        )
        
        # Register the request with the message router
        self.message_router.add_pending_request(pending_request)
        
        try:
            # Send the request
            await self.send_message(request_message)
            
            # Wait for the response collection to complete
            responses = await future
            
            self.logger.info(f"Multi-response request {request_id} completed with {len(responses)} responses")
            return responses
            
        except asyncio.TimeoutError:
            self.logger.warning(f"Multi-response request {request_id} timed out")
            return []
        except Exception as e:
            self.logger.error(f"Error in multi-response request {request_id}: {e}")
            return []
        finally:
            # Ensure cleanup
            if request_id in self.message_router.pending_requests:
                self.message_router._remove_pending_request(request_id)
    
    def parse_message_by_template_id(self, template_id: int, message_data: bytes):
        """Parse message data based on template ID."""
        try:
            if template_id == self.TEMPLATE_IDS['RESPONSE_RITHMIC_SYSTEM_INFO']:
                msg = response_rithmic_system_info_pb2.ResponseRithmicSystemInfo()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['RESPONSE_LOGIN']:
                msg = response_login_pb2.ResponseLogin()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['RESPONSE_HEARTBEAT']:
                msg = response_heartbeat_pb2.ResponseHeartbeat()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['RESPONSE_FRONT_MONTH_CONTRACT']:
                msg = response_front_month_contract_pb2.ResponseFrontMonthContract()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']:
                msg = response_search_symbols_pb2.ResponseSearchSymbols()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['RESPONSE_MARKET_DATA_UPDATE']:
                msg = response_market_data_update_pb2.ResponseMarketDataUpdate()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['RESPONSE_DEPTH_BY_ORDER_UPDATES']:
                msg = response_depth_by_order_updates_pb2.ResponseDepthByOrderUpdates()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['RESPONSE_DEPTH_BY_ORDER_SNAPSHOT']:
                msg = response_depth_by_order_snapshot_pb2.ResponseDepthByOrderSnapshot()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['LAST_TRADE']:
                msg = last_trade_pb2.LastTrade()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['BEST_BID_OFFER']:
                msg = best_bid_offer_pb2.BestBidOffer()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['ORDER_BOOK']:
                msg = order_book_pb2.OrderBook()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['DEPTH_BY_ORDER']:
                msg = depth_by_order_pb2.DepthByOrder()
                msg.ParseFromString(message_data)
                return msg
            elif template_id == self.TEMPLATE_IDS['DEPTH_BY_ORDER_END_EVENT']:
                msg = depth_by_order_end_event_pb2.DepthByOrderEndEvent()
                msg.ParseFromString(message_data)
                return msg
            else:
                self.logger.warning(f"Unknown template ID: {template_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error parsing message with template ID {template_id}: {e}")
            return None
    
    def message_to_dict(self, message) -> dict:
        """Convert protobuf message to dictionary for JSON serialization."""
        from google.protobuf.json_format import MessageToDict
        try:
            return MessageToDict(message)
        except:
            # Fallback to string representation
            return {'raw_message': str(message)}
    
    async def discover_systems(self) -> List[str]:
        """
        Step 1: Discover available Rithmic systems.
        Returns list of available system names.
        """
        self.logger.info("Starting system discovery...")
        
        if not await self.connect():
            raise ConnectionError("Failed to connect for system discovery")
        
        try:
            # Create system info request
            request = request_rithmic_system_info_pb2.RequestRithmicSystemInfo()
            request.template_id = self.TEMPLATE_IDS['REQUEST_RITHMIC_SYSTEM_INFO']
            request.user_msg.append("System Discovery Request")
            
            # Use optimized send_request method
            template_id, response, raw_data = await self.send_request(
                request,
                [self.TEMPLATE_IDS['RESPONSE_RITHMIC_SYSTEM_INFO']],
                timeout=10.0
            )
            
            if template_id == self.TEMPLATE_IDS['RESPONSE_RITHMIC_SYSTEM_INFO']:
                if response and len(response.rp_code) == 1 and response.rp_code[0] == "0":
                    # Success
                    self.available_systems = list(response.system_name)
                    self.logger.info(f"Available systems: {self.available_systems}")
                    return self.available_systems
                else:
                    error_msg = f"System discovery failed: {response.rp_code if response else 'Unknown error'}"
                    self.logger.error(error_msg)
                    raise Exception(error_msg)
            else:
                raise Exception(f"Unexpected response template ID: {template_id}")
                
        finally:
            await self.disconnect()
    
    async def login(self, 
                   user: Optional[str] = None,
                   password: Optional[str] = None, 
                   system_name: Optional[str] = None,
                   infra_type: str = "TICKER_PLANT",
                   app_name: Optional[str] = None,
                   app_version: Optional[str] = None) -> bool:
        """
        Step 2: Login to the Rithmic system.
        """
        # Use config defaults if not provided
        user = user or config.user
        password = password or config.password
        system_name = system_name or config.system_name
        app_name = app_name or config.app_name
        app_version = app_version or config.app_version
        
        self.logger.info(f"Logging in to {system_name} ({infra_type})...")
        
        if not await self.connect():
            raise ConnectionError("Failed to connect for login")
        
        # Create login request
        request = request_login_pb2.RequestLogin()
        request.template_id = self.TEMPLATE_IDS['REQUEST_LOGIN']
        request.template_version = self.template_version
        request.user_msg.append("Login Request")
        request.user = user
        request.password = password
        request.app_name = app_name
        request.app_version = app_version
        request.system_name = system_name
        
        # Set infrastructure type
        if infra_type == "TICKER_PLANT":
            request.infra_type = request_login_pb2.RequestLogin.TICKER_PLANT
        elif infra_type == "ORDER_PLANT":
            request.infra_type = request_login_pb2.RequestLogin.ORDER_PLANT
        elif infra_type == "HISTORY_PLANT":
            request.infra_type = request_login_pb2.RequestLogin.HISTORY_PLANT
        elif infra_type == "PNL_PLANT":
            request.infra_type = request_login_pb2.RequestLogin.PNL_PLANT
        elif infra_type == "REPOSITORY_PLANT":
            request.infra_type = request_login_pb2.RequestLogin.REPOSITORY_PLANT
        
        # Send login request using optimized method
        template_id, response, raw_data = await self.send_request(
            request,
            [self.TEMPLATE_IDS['RESPONSE_LOGIN']],
            timeout=15.0
        )
        
        if template_id == self.TEMPLATE_IDS['RESPONSE_LOGIN']:
            if response and len(response.rp_code) == 1 and response.rp_code[0] == "0":
                # Login successful
                self.is_authenticated = True
                if hasattr(response, 'heartbeat_interval') and response.heartbeat_interval > 0:
                    self.heartbeat_interval = response.heartbeat_interval
                self.logger.info(f"Login successful. Heartbeat interval: {self.heartbeat_interval}s")
                
                # Start heartbeat task
                self.heartbeat_task = asyncio.create_task(self.heartbeat_loop())
                
                return True
            else:
                error_msg = f"Login failed: {response.rp_code if response else 'Unknown error'}"
                self.logger.error(error_msg)
                await self.disconnect()
                return False
        else:
            error_msg = f"Unexpected login response template ID: {template_id}"
            self.logger.error(error_msg)
            await self.disconnect()
            return False
    
    async def heartbeat_loop(self):
        """Send periodic heartbeats to maintain connection."""
        while self.is_connected and self.is_authenticated:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.is_connected:
                    break
                    
                # Send heartbeat
                request = request_heartbeat_pb2.RequestHeartbeat()
                request.template_id = self.TEMPLATE_IDS['REQUEST_HEARTBEAT']
                
                await self.send_message(request)
                self.logger.debug("Heartbeat sent")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Heartbeat error: {e}")
                break
    
    async def find_front_month_contract(self, product_code: str = "ES", exchange: str = "CME") -> Optional[str]:
        """
        Find the front month contract for a given product (e.g., ES futures).
        Returns the contract symbol or None if not found.
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
        
        self.logger.info(f"Finding front month contract for {product_code} on {exchange}")
        
        try:
            # Method 1: Use RequestFrontMonthContract
            request = request_front_month_contract_pb2.RequestFrontMonthContract()
            request.template_id = self.TEMPLATE_IDS['REQUEST_FRONT_MONTH_CONTRACT']
            request.user_msg.append(f"Front month contract request for {product_code}")
            
            # Set the product code and exchange
            if hasattr(request, 'symbol'):
                request.symbol = product_code
            if hasattr(request, 'exchange'):
                request.exchange = exchange
            
            # Use optimized send_request method
            try:
                template_id, response, raw_data = await self.send_request(
                    request,
                    [self.TEMPLATE_IDS['RESPONSE_FRONT_MONTH_CONTRACT']],
                    timeout=10.0
                )
                
                if template_id == self.TEMPLATE_IDS['RESPONSE_FRONT_MONTH_CONTRACT']:
                    if response and len(response.rp_code) == 1 and response.rp_code[0] == "0":
                        # Success - extract contract symbol from trading_symbol field
                        if hasattr(response, 'trading_symbol') and response.trading_symbol:
                            self.logger.info(f"Found front month contract: {response.trading_symbol}")
                            return response.trading_symbol
                        elif hasattr(response, 'symbol') and response.symbol:
                            self.logger.info(f"Found front month contract (fallback): {response.symbol}")
                            return response.symbol
                    else:
                        self.logger.warning("Front month contract request failed, trying symbol search...")
                        
            except asyncio.TimeoutError:
                self.logger.warning("Front month contract request timed out, trying symbol search...")
            
            # Method 2: Fallback to symbol search
            return await self.search_front_month_contract(product_code, exchange)
            
        except Exception as e:
            self.logger.error(f"Error finding front month contract: {e}")
            return None
    
    async def search_front_month_contract(self, product_code: str, exchange: str = "CME") -> Optional[str]:
        """
        Fallback method: Search for contracts using symbol search.
        """
        self.logger.info(f"Searching for {product_code} contracts using symbol search on {exchange}")
        
        try:
            request = request_search_symbols_pb2.RequestSearchSymbols()
            request.template_id = self.TEMPLATE_IDS['REQUEST_SEARCH_SYMBOLS']
            request.user_msg.append(f"Symbol search for {product_code}")
            
            # Set search parameters
            if hasattr(request, 'symbol'):
                request.symbol = f"{product_code}*"  # Wildcard search
            elif hasattr(request, 'search_string'):
                request.search_string = f"{product_code}*"
                
            # Set exchange if field exists
            if hasattr(request, 'exchange'):
                request.exchange = exchange
            
            # Use optimized send_request method
            template_id, response, raw_data = await self.send_request(
                request,
                [self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']],
                timeout=10.0
            )
            
            if template_id == self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']:
                if response and len(response.rp_code) == 1 and response.rp_code[0] == "0":
                    # Parse search results to find the nearest expiration
                    contracts = []
                    if hasattr(response, 'symbol_name'):
                        contracts = list(response.symbol_name)
                    
                    if contracts:
                        # For ES futures, find the nearest month
                        # This is a simplified approach - in practice you'd parse expiration dates
                        front_month = min(contracts)  # Alphabetically first is often nearest
                        self.logger.info(f"Found front month contract via search: {front_month}")
                        return front_month
                        
        except Exception as e:
            self.logger.error(f"Error in symbol search: {e}")
            
        return None
    
    async def subscribe_to_market_data(self, symbol: str, exchange: str = "CME") -> bool:
        """
        Subscribe to Level 1 market data for a symbol.
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Subscribing to market data for {symbol} on {exchange}")
        
        try:
            request = request_market_data_update_pb2.RequestMarketDataUpdate()
            request.template_id = self.TEMPLATE_IDS['REQUEST_MARKET_DATA_UPDATE']
            request.user_msg.append(f"Market data subscription for {symbol}")
            request.symbol = symbol
            request.exchange = exchange
            
            # Set request type to SUBSCRIBE
            request.request = request_market_data_update_pb2.RequestMarketDataUpdate.SUBSCRIBE
            
            # Request specific update types (using proper enum values from proto)
            update_bits = (
                request_market_data_update_pb2.RequestMarketDataUpdate.LAST_TRADE |
                request_market_data_update_pb2.RequestMarketDataUpdate.BBO |
                request_market_data_update_pb2.RequestMarketDataUpdate.ORDER_BOOK |
                request_market_data_update_pb2.RequestMarketDataUpdate.OPEN |
                request_market_data_update_pb2.RequestMarketDataUpdate.HIGH_LOW |
                request_market_data_update_pb2.RequestMarketDataUpdate.CLOSE
            )
            request.update_bits = update_bits
            
            # Use optimized send_request method
            template_id, response, raw_data = await self.send_request(
                request,
                [self.TEMPLATE_IDS['RESPONSE_MARKET_DATA_UPDATE']],
                timeout=10.0
            )
            
            if template_id == self.TEMPLATE_IDS['RESPONSE_MARKET_DATA_UPDATE']:
                if response and len(response.rp_code) == 1 and response.rp_code[0] == "0":
                    self.logger.info(f"Successfully subscribed to market data for {symbol}")
                    
                    # Register streaming subscriptions with MessageRouter for market data
                    await self._register_market_data_streaming(symbol, exchange)
                    
                    return True
                else:
                    self.logger.error(f"Market data subscription failed: {response.rp_code if response else 'Unknown error'}")
                    return False
            elif template_id in [self.TEMPLATE_IDS['ORDER_BOOK'], self.TEMPLATE_IDS['LAST_TRADE'], self.TEMPLATE_IDS['BEST_BID_OFFER']]:
                # We received market data immediately - subscription was successful
                self.logger.info(f"Successfully subscribed to market data for {symbol} (received immediate data, template {template_id})")
                
                # Register streaming subscriptions with MessageRouter for market data
                await self._register_market_data_streaming(symbol, exchange)
                
                return True
            
        except Exception as e:
            self.logger.error(f"Error subscribing to market data: {e}")
            
        return False
    
    async def _register_market_data_streaming(self, symbol: str, exchange: str):
        """Register streaming subscriptions for market data with the MessageRouter."""
        def market_data_callback(template_id: int, message, raw_data: bytes):
            """Callback to process market data messages through the MessageRouter."""
            try:
                if message:
                    self.process_market_data_update(template_id, message)
            except Exception as e:
                self.logger.error(f"Error processing market data in callback: {e}")
        
        # Register subscriptions for market data template IDs
        market_data_templates = [
            self.TEMPLATE_IDS['LAST_TRADE'],
            self.TEMPLATE_IDS['BEST_BID_OFFER'], 
            self.TEMPLATE_IDS['ORDER_BOOK']
        ]
        
        # Create a single subscription for all market data templates
        subscription_id = f"{symbol}_{exchange}_market_data"
        
        subscription = StreamingSubscription(
            subscription_id=subscription_id,
            symbol=symbol,
            exchange=exchange,
            template_ids=market_data_templates,
            callback=market_data_callback,
            active=True
        )
            
        # Register with message router
        self.message_router.streaming_subscriptions[subscription_id] = subscription
        
        # Register for all market data template IDs
        for template_id in market_data_templates:
            self.message_router.template_to_subscriptions[template_id].append(subscription_id)
            self.logger.debug(f"Registered streaming subscription for {symbol} template {template_id}")
    
    async def subscribe_to_depth_by_order(self, symbol: str, exchange: str = "CME") -> bool:
        """
        Subscribe to Level 3 depth-by-order data for a symbol.
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Subscribing to depth-by-order data for {symbol} on {exchange}")
        
        try:
            request = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates()
            request.template_id = self.TEMPLATE_IDS['REQUEST_DEPTH_BY_ORDER_UPDATES']
            request.user_msg.append(f"Depth by order subscription for {symbol}")
            request.symbol = symbol
            request.exchange = exchange
            
            # Set request type to SUBSCRIBE
            if hasattr(request, 'request'):
                request.request = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates.SUBSCRIBE
            
            # Use optimized send_request method - eliminates recv conflicts
            template_id, response, raw_data = await self.send_request(
                request_message=request,
                expected_response_template_ids=[self.TEMPLATE_IDS['RESPONSE_DEPTH_BY_ORDER_UPDATES']],
                timeout=10.0
            )
            
            if template_id == self.TEMPLATE_IDS['RESPONSE_DEPTH_BY_ORDER_UPDATES']:
                if response and len(response.rp_code) == 1 and response.rp_code[0] == "0":
                    self.logger.info(f"Successfully subscribed to depth-by-order data for {symbol}")
                    return True
                else:
                    self.logger.error(f"Depth-by-order subscription failed: {response.rp_code if response else 'Unknown error'}")
                    return False
            
        except Exception as e:
            self.logger.error(f"Error subscribing to depth-by-order data: {e}")
            
        return False
    
    async def request_order_book_snapshot(self, symbol: str, exchange: str = "CME") -> bool:
        """
        Request a complete order book snapshot.
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Requesting order book snapshot for {symbol} on {exchange}")
        
        try:
            request = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
            request.template_id = self.TEMPLATE_IDS['REQUEST_DEPTH_BY_ORDER_SNAPSHOT']
            request.user_msg.append(f"Order book snapshot for {symbol}")
            request.symbol = symbol
            request.exchange = exchange
            
            # Use optimized multi-response with custom snapshot completion logic
            try:
                responses = await self.send_request_multi_response(
                    request_message=request,
                    expected_response_template_ids=[self.TEMPLATE_IDS['RESPONSE_DEPTH_BY_ORDER_SNAPSHOT']],
                    timeout=30.0,  # Longer timeout for large order books
                    max_responses=50,  # Allow up to 50 snapshot messages
                    max_timeouts=5
                )
                
                snapshot_data_count = 0
                
                # Process responses with order book snapshot logic
                for template_id, response, raw_data in responses:
                    if template_id == self.TEMPLATE_IDS['RESPONSE_DEPTH_BY_ORDER_SNAPSHOT']:
                        # ResponseDepthByOrderSnapshot can have either rq_handler_rp_code (data) or rp_code (end marker)
                        has_rq_handler = hasattr(response, 'rq_handler_rp_code') and len(response.rq_handler_rp_code) > 0
                        has_rp = hasattr(response, 'rp_code') and len(response.rp_code) > 0
                        
                        if has_rq_handler and response.rq_handler_rp_code[0] == "0":
                            snapshot_data_count += 1
                            self.logger.info(f"Received order book snapshot data #{snapshot_data_count} for {symbol}")
                            # Consider successful after receiving first data message
                            if snapshot_data_count == 1:
                                self.logger.info(f"Order book snapshot request successful - receiving data stream")
                                return True
                        elif has_rp and response.rp_code[0] == "0":
                            self.logger.info(f"Successfully completed order book snapshot for {symbol}")
                            return True
                        else:
                            error_codes = response.rq_handler_rp_code if has_rq_handler else (response.rp_code if has_rp else ['Unknown'])
                            self.logger.error(f"Order book snapshot request failed: {error_codes}")
                            return False
                
                # If we processed responses but didn't find success markers, consider successful if we got data
                if snapshot_data_count > 0:
                    self.logger.info(f"Order book snapshot completed with {snapshot_data_count} data messages")
                    return True
                else:
                    self.logger.warning("No valid snapshot data received")
                    return False
                    
            except Exception as e:
                self.logger.error(f"Error in optimized order book snapshot request: {e}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error requesting order book snapshot: {e}")
            
        return False
    
    async def listen_for_updates(self, duration: Optional[float] = None):
        """
        Listen for real-time market data updates.
        
        Args:
            duration: How long to listen in seconds. If None, listen indefinitely.
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Listening for market data updates{' for ' + str(duration) + ' seconds' if duration else ' indefinitely'}...")
        
        start_time = time.time()
        
        # Market data template IDs to listen for
        market_data_templates = [
            self.TEMPLATE_IDS['LAST_TRADE'],
            self.TEMPLATE_IDS['BEST_BID_OFFER'],
            self.TEMPLATE_IDS['ORDER_BOOK'],
            self.TEMPLATE_IDS['DEPTH_BY_ORDER'],
            self.TEMPLATE_IDS['DEPTH_BY_ORDER_END_EVENT']
        ]
        
        # Ensure message router is running for streaming data
        if not self.message_router.is_running:
            await self.message_router.start()
        
        consecutive_errors = 0
        max_consecutive_errors = 10  # Allow up to 10 consecutive errors before giving up
        
        # Create a message counter to track data flow
        message_count = 0
        last_message_time = time.time()
        
        try:
            # Use message router for all message handling to avoid concurrency issues
            while self.is_connected and self.is_authenticated:
                if duration and (time.time() - start_time) > duration:
                    self.logger.info(f"Data collection completed after {duration} seconds")
                    break
                
                # Check message router stats to see if we're receiving data
                current_time = time.time()
                current_message_count = self.message_router.stats.get('messages_routed', 0)
                
                if current_message_count > message_count:
                    # We've received new messages
                    message_count = current_message_count
                    last_message_time = current_time
                    consecutive_errors = 0
                    self.logger.debug(f"Total messages routed: {message_count}")
                
                # Check if we haven't received messages for too long
                time_since_last_message = current_time - last_message_time
                if time_since_last_message > 30:  # 30 seconds without messages
                    consecutive_errors += 1
                    self.logger.warning(f"No messages received for {time_since_last_message:.1f}s (#{consecutive_errors})")
                    
                    if consecutive_errors >= max_consecutive_errors:
                        self.logger.error(f"No data received for too long ({consecutive_errors} periods), stopping collection")
                        break
                    
                    last_message_time = current_time  # Reset to avoid constant warnings
                
                # Sleep briefly to avoid busy waiting
                await asyncio.sleep(1.0)
                    
        except KeyboardInterrupt:
            self.logger.info("Stopping market data listening...")
    
    def process_market_data_update(self, template_id: int, message):
        """Process different types of market data updates."""
        if template_id == self.TEMPLATE_IDS['LAST_TRADE']:
            self.logger.info(f"Last Trade: {message.symbol if hasattr(message, 'symbol') else 'N/A'} - "
                           f"Price: {message.trade_price if hasattr(message, 'trade_price') else 'N/A'} - "
                           f"Size: {message.trade_size if hasattr(message, 'trade_size') else 'N/A'}")
        elif template_id == self.TEMPLATE_IDS['BEST_BID_OFFER']:
            self.logger.info(f"Best Bid/Offer: {message.symbol if hasattr(message, 'symbol') else 'N/A'}")
        elif template_id == self.TEMPLATE_IDS['ORDER_BOOK']:
            self.logger.info(f"Order Book: {message.symbol if hasattr(message, 'symbol') else 'N/A'}")
        elif template_id == self.TEMPLATE_IDS['DEPTH_BY_ORDER']:
            self.logger.info(f"Depth By Order: {message.symbol if hasattr(message, 'symbol') else 'N/A'}")
        elif template_id == self.TEMPLATE_IDS['DEPTH_BY_ORDER_END_EVENT']:
            self.logger.info(f"Depth By Order End Event: {message.symbol if hasattr(message, 'symbol') else 'N/A'}")
    
    # ========================= ENHANCED SYMBOL SEARCH METHODS =========================
    
    async def search_symbols(self, 
                           search_text: str = None,
                           exchange: str = None,
                           product_code: str = None,
                           instrument_type: str = None,
                           pattern: str = "CONTAINS") -> List[Dict[str, Any]]:
        """
        Comprehensive symbol search with multiple filtering options.
        
        Args:
            search_text: Free text search string
            exchange: Filter by exchange (e.g., "CME", "CBOT")
            product_code: Filter by product code (e.g., "ES", "NQ")
            instrument_type: Filter by instrument type (e.g., "FUTURE", "EQUITY")
            pattern: Search pattern - "CONTAINS" or "EQUALS"
            
        Returns:
            List of symbol dictionaries with details
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Searching symbols: text='{search_text}', exchange='{exchange}', "
                        f"product='{product_code}', type='{instrument_type}', pattern='{pattern}'")
        
        try:
            request = request_search_symbols_pb2.RequestSearchSymbols()
            request.template_id = self.TEMPLATE_IDS['REQUEST_SEARCH_SYMBOLS']
            request.user_msg.append(f"Symbol search request")
            
            # Set search parameters
            if search_text:
                request.search_text = search_text
            if exchange:
                request.exchange = exchange
            if product_code:
                request.product_code = product_code
            if instrument_type:
                request.instrument_type = instrument_type
                
            # Set pattern (default to CONTAINS)
            if pattern == "EQUALS":
                request.pattern = request_search_symbols_pb2.RequestSearchSymbols.EQUALS
            else:
                request.pattern = request_search_symbols_pb2.RequestSearchSymbols.CONTAINS
            
            # Use the new multi-response method to collect all symbol responses
            responses = await self.send_request_multi_response(
                request_message=request,
                expected_response_template_ids=[self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']],
                timeout=15.0,
                max_timeouts=3
            )
            
            # Process all collected responses
            symbols = []
            for template_id, response, raw_data in responses:
                if template_id == self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']:
                    if response and hasattr(response, 'symbol'):
                            # Extract ALL available fields for comprehensive storage
                            symbol_info = {
                                'symbol': response.symbol,
                                'symbol_name': getattr(response, 'symbol_name', ''),
                                'exchange': getattr(response, 'exchange', ''),
                                'product_code': getattr(response, 'product_code', ''),
                                'instrument_type': getattr(response, 'instrument_type', ''),
                                'expiration_date': getattr(response, 'expiration_date', ''),
                                'strike_price': getattr(response, 'strike_price', ''),
                                'put_call_indicator': getattr(response, 'put_call_indicator', ''),
                                # Additional comprehensive fields
                                'underlying_symbol': getattr(response, 'underlying_symbol', ''),
                                'tick_size': getattr(response, 'tick_size', ''),
                                'display_factor': getattr(response, 'display_factor', ''),
                                'maturity_date': getattr(response, 'maturity_date', ''),
                                'currency': getattr(response, 'currency', ''),
                                'single_point_value': getattr(response, 'single_point_value', ''),
                                'description': getattr(response, 'description', ''),
                                'contract_size': getattr(response, 'contract_size', ''),
                                'tick_value': getattr(response, 'tick_value', ''),
                                'price_format': getattr(response, 'price_format', ''),
                                'trading_hours': getattr(response, 'trading_hours', ''),
                                'settlement_type': getattr(response, 'settlement_type', ''),
                                'margin_requirement': getattr(response, 'margin_requirement', ''),
                                'is_tradeable': getattr(response, 'is_tradeable', True),
                                'listing_market': getattr(response, 'listing_market', ''),
                                'product_group': getattr(response, 'product_group', ''),
                                'sector': getattr(response, 'sector', ''),
                                'subsector': getattr(response, 'subsector', ''),
                                'country': getattr(response, 'country', ''),
                                'time_zone': getattr(response, 'time_zone', ''),
                                'last_trading_day': getattr(response, 'last_trading_day', ''),
                                'first_notice_day': getattr(response, 'first_notice_day', ''),
                                'delivery_method': getattr(response, 'delivery_method', ''),
                                'minimum_price_increment': getattr(response, 'minimum_price_increment', ''),
                                'maximum_price_increment': getattr(response, 'maximum_price_increment', ''),
                                'daily_price_limit': getattr(response, 'daily_price_limit', ''),
                                'position_limit': getattr(response, 'position_limit', ''),
                                'reportable_position': getattr(response, 'reportable_position', ''),
                                'discovery_timestamp': datetime.now().isoformat(),
                                'template_id': template_id,
                                # Extract any additional fields dynamically
                                'raw_fields': {}
                            }
                            
                            # Dynamically extract any other fields present in the response
                            try:
                                for field in response.DESCRIPTOR.fields:
                                    field_name = field.name
                                    if field_name not in symbol_info and hasattr(response, field_name):
                                        field_value = getattr(response, field_name)
                                        # Only store non-empty values
                                        if field_value not in ['', 0, None, []]:
                                            symbol_info['raw_fields'][field_name] = field_value
                            except AttributeError:
                                # Response doesn't have DESCRIPTOR, skip dynamic extraction
                                pass
                            symbols.append(symbol_info)
            
            self.logger.info(f"Symbol search completed with {len(symbols)} results")
            return symbols
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive symbol search: {e}")
            return []
    
    async def search_symbols_alphanumeric(self, 
                                        exchange: str = None,
                                        instrument_type: str = "FUTURE",
                                        max_results: int = None) -> List[Dict[str, Any]]:
        """
        Comprehensive symbol search using alphanumeric iteration instead of wildcards.
        
        This method searches for symbols by iterating through all letters A-Z and digits 0-9
        using the CONTAINS pattern to discover all available symbols systematically.
        
        Args:
            exchange: Filter by exchange (e.g., "CME", "CBOT")
            instrument_type: Filter by instrument type (default: "FUTURE")
            max_results: Maximum number of results to return (None = unlimited)
            
        Returns:
            List of unique symbol dictionaries with details
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Starting alphanumeric symbol search for {exchange or 'all exchanges'}")
        
        # Characters to search with - all letters and digits
        search_chars = list('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')
        
        all_symbols = {}  # Use dict to avoid duplicates (keyed by symbol)
        total_found = 0
        
        try:
            for char in search_chars:
                if max_results and len(all_symbols) >= max_results:
                    self.logger.info(f"Reached maximum results limit ({max_results})")
                    break
                    
                self.logger.info(f"[{search_chars.index(char)+1}/{len(search_chars)}] Searching symbols containing '{char}' on {exchange or 'all exchanges'}...")
                
                # Search for symbols containing this character
                char_results = await self.search_symbols(
                    search_text=char,
                    exchange=exchange,
                    instrument_type=instrument_type,
                    pattern="CONTAINS"
                )
                
                # Add unique results
                new_count = 0
                for symbol_info in char_results:
                    symbol_key = symbol_info.get('symbol', '')
                    if symbol_key and symbol_key not in all_symbols:
                        all_symbols[symbol_key] = symbol_info
                        new_count += 1
                
                total_found += new_count
                
                # More verbose logging
                if new_count > 0:
                    self.logger.info(f"✅ Character '{char}': {len(char_results)} results ({new_count} new), total unique: {len(all_symbols)}")
                    
                    # Show sample of new symbols found
                    if new_count > 0:
                        new_symbols = [info.get('symbol', 'N/A') for info in char_results if info.get('symbol', '') not in all_symbols or info.get('symbol', '') in [s.get('symbol', '') for s in char_results[:3]]][:3]
                        if new_symbols:
                            self.logger.info(f"   New symbols: {', '.join(new_symbols)}")
                else:
                    self.logger.info(f"⚪ Character '{char}': {len(char_results)} results (0 new), total unique: {len(all_symbols)}")
                
                # Small delay to avoid overwhelming the server
                await asyncio.sleep(0.1)
            
            unique_symbols = list(all_symbols.values())
            self.logger.info(f"Alphanumeric search completed: {len(unique_symbols)} unique symbols found")
            
            return unique_symbols
            
        except Exception as e:
            self.logger.error(f"Error in alphanumeric symbol search: {e}")
            return list(all_symbols.values())  # Return what we have so far
    
    async def get_instrument_by_underlying(self, 
                                         underlying_symbol: str,
                                         exchange: str = None,
                                         expiration_date: str = None) -> List[Dict[str, Any]]:
        """
        Get all instruments (options, spreads, etc.) based on an underlying symbol.
        
        Args:
            underlying_symbol: The underlying symbol to search for
            exchange: Optional exchange filter
            expiration_date: Optional expiration date filter (YYYYMMDD format)
            
        Returns:
            List of instrument dictionaries related to the underlying
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Getting instruments by underlying: {underlying_symbol}")
        
        try:
            # Import the protobuf messages
            from proto_generated import request_get_instrument_by_underlying_pb2, response_get_instrument_by_underlying_pb2
            
            request = request_get_instrument_by_underlying_pb2.RequestGetInstrumentByUnderlying()
            request.template_id = self.TEMPLATE_IDS['REQUEST_GET_INSTRUMENT_BY_UNDERLYING']
            request.user_msg.append(f"Get instruments for underlying {underlying_symbol}")
            
            # Set required parameters
            request.underlying_symbol = underlying_symbol
            if exchange:
                request.exchange = exchange
            if expiration_date:
                request.expiration_date = expiration_date
            
            # Use the new multi-response method to collect all instrument responses
            responses = await self.send_request_multi_response(
                request_message=request,
                expected_response_template_ids=[self.TEMPLATE_IDS['RESPONSE_GET_INSTRUMENT_BY_UNDERLYING']],
                timeout=30.0,  # Longer timeout for comprehensive searches
                max_timeouts=3
            )
            
            # Process all collected responses
            instruments = []
            for template_id, response, raw_data in responses:
                if template_id == self.TEMPLATE_IDS['RESPONSE_GET_INSTRUMENT_BY_UNDERLYING']:
                    if response and hasattr(response, 'symbol'):
                        instrument_info = {
                            'symbol': response.symbol,
                            'underlying_symbol': getattr(response, 'underlying_symbol', ''),
                            'exchange': getattr(response, 'exchange', ''),
                            'instrument_type': getattr(response, 'instrument_type', ''),
                            'expiration_date': getattr(response, 'expiration_date', ''),
                            'strike_price': getattr(response, 'strike_price', ''),
                            'put_call_indicator': getattr(response, 'put_call_indicator', ''),
                            'product_code': getattr(response, 'product_code', ''),
                            'tick_size': getattr(response, 'tick_size', ''),
                            'display_factor': getattr(response, 'display_factor', ''),
                            'maturity_date': getattr(response, 'maturity_date', ''),
                            'currency': getattr(response, 'currency', ''),
                            'single_point_value': getattr(response, 'single_point_value', ''),
                            'description': getattr(response, 'description', '')
                        }
                        instruments.append(instrument_info)
            
            self.logger.info(f"Found {len(instruments)} instruments for underlying {underlying_symbol}")
            return instruments
            
        except ImportError:
            self.logger.warning("Get instrument by underlying protobuf not available")
            return []
        except Exception as e:
            self.logger.error(f"Error getting instruments by underlying: {e}")
            return []
    
    async def get_auxiliary_reference_data(self, 
                                         symbol: str,
                                         exchange: str = None) -> Dict[str, Any]:
        """
        Get auxiliary reference data for a specific symbol.
        
        Args:
            symbol: Symbol to get reference data for
            exchange: Optional exchange specification
            
        Returns:
            Dictionary containing auxiliary reference data
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Getting auxiliary reference data for: {symbol}")
        
        try:
            # Import the protobuf messages
            from proto_generated import request_auxilliary_reference_data_pb2, response_auxilliary_reference_data_pb2
            
            request = request_auxilliary_reference_data_pb2.RequestAuxilliaryReferenceData()
            request.template_id = self.TEMPLATE_IDS['REQUEST_AUXILIARY_REFERENCE_DATA']
            request.user_msg.append(f"Auxiliary reference data for {symbol}")
            
            # Set parameters
            request.symbol = symbol
            if exchange:
                request.exchange = exchange
            
            # Use optimized send_request method - eliminates recv conflicts
            template_id, response, raw_data = await self.send_request(
                request_message=request,
                expected_response_template_ids=[self.TEMPLATE_IDS['RESPONSE_AUXILIARY_REFERENCE_DATA']],
                timeout=15.0
            )
            
            if template_id == self.TEMPLATE_IDS['RESPONSE_AUXILIARY_REFERENCE_DATA']:
                if response:
                    # Extract all available fields
                    aux_data = {}
                    for field in response.DESCRIPTOR.fields:
                        field_name = field.name
                        if hasattr(response, field_name):
                            aux_data[field_name] = getattr(response, field_name)
                    
                    self.logger.info(f"Retrieved auxiliary reference data for {symbol}")
                    return aux_data
            
            return {}
            
        except ImportError:
            self.logger.warning("Auxiliary reference data protobuf not available")
            return {}
        except Exception as e:
            self.logger.error(f"Error getting auxiliary reference data for {symbol}: {e}")
            return {}
    
    async def get_all_product_codes(self, exchange: str = None) -> List[str]:
        """
        Get all available product codes using systematic discovery.
        
        This method discovers all unique product codes by searching through
        all available symbols and extracting their product codes.
        
        Args:
            exchange: Optional exchange filter (e.g., "CME")
            
        Returns:
            List of unique product codes
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Discovering all product codes for {exchange or 'all exchanges'}")
        
        try:
            # Get all contracts to extract product codes from
            all_contracts = await self.search_symbols_alphanumeric(
                exchange=exchange,
                instrument_type="FUTURE"
            )
            
            # Extract unique product codes
            product_codes = set()
            for contract in all_contracts:
                product_code = contract.get('product_code', '').strip()
                if product_code:
                    product_codes.add(product_code)
            
            # Convert to sorted list
            product_codes_list = sorted(list(product_codes))
            
            self.logger.info(f"Discovered {len(product_codes_list)} unique product codes")
            self.logger.debug(f"Product codes: {', '.join(product_codes_list)}")
            
            return product_codes_list
            
        except Exception as e:
            self.logger.error(f"Error discovering product codes: {e}")
            return []
    
    async def get_contracts_by_product_codes(self, 
                                           product_codes: List[str], 
                                           exchange: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get all contracts organized by product codes.
        
        Args:
            product_codes: List of product codes to search for
            exchange: Optional exchange filter
            
        Returns:
            Dictionary mapping product codes to their contract lists
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Getting contracts for {len(product_codes)} product codes")
        
        results = {}
        
        try:
            for product_code in product_codes:
                self.logger.info(f"Getting contracts for product code: {product_code}")
                
                # Search for all contracts with this product code
                contracts = await self.search_symbols(
                    product_code=product_code,
                    exchange=exchange,
                    instrument_type="FUTURE"
                )
                
                if contracts:
                    results[product_code] = contracts
                    self.logger.info(f"Found {len(contracts)} contracts for {product_code}")
                else:
                    self.logger.warning(f"No contracts found for {product_code}")
                
                # Small delay to avoid overwhelming the server
                await asyncio.sleep(0.1)
            
            total_contracts = sum(len(contracts) for contracts in results.values())
            self.logger.info(f"Retrieved {total_contracts} contracts across {len(results)} product codes")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error getting contracts by product codes: {e}")
            return results  # Return partial results
    
    async def get_comprehensive_reference_data(self, exchanges: List[str] = None) -> Dict[str, Any]:
        """
        Retrieve comprehensive reference data from the Rithmic API.
        
        This method systematically discovers and retrieves all available static reference data:
        - All exchanges and their symbols
        - All product codes and their contracts  
        - Symbol details and metadata
        - Exchange statistics and summaries
        
        Args:
            exchanges: Optional list of exchanges to focus on (e.g., ["CME", "CBOT"])
                      If None, discovers all available exchanges
            
        Returns:
            Dictionary containing comprehensive reference data organized by category
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info("Starting comprehensive reference data collection...")
        
        reference_data = {
            'collection_timestamp': datetime.now().isoformat(),
            'exchanges': {},
            'product_codes': {},
            'symbols': {},
            'statistics': {
                'total_exchanges': 0,
                'total_product_codes': 0,
                'total_symbols': 0,
                'total_contracts': 0
            },
            'errors': []
        }
        
        try:
            # Step 1: Discover exchanges if not specified
            if not exchanges:
                self.logger.info("Discovering all available exchanges...")
                
                # Get a sample of all symbols to discover exchanges
                sample_symbols = await self.search_symbols_alphanumeric(max_results=1000)
                
                discovered_exchanges = set()
                for symbol in sample_symbols:
                    exchange = symbol.get('exchange', '').strip()
                    if exchange:
                        discovered_exchanges.add(exchange)
                
                exchanges = sorted(list(discovered_exchanges))
                self.logger.info(f"Discovered exchanges: {exchanges}")
            
            reference_data['statistics']['total_exchanges'] = len(exchanges)
            
            # Step 2: Collect data for each exchange
            for exchange in exchanges:
                self.logger.info(f"Collecting reference data for exchange: {exchange}")
                
                exchange_data = {
                    'exchange_name': exchange,
                    'symbols': [],
                    'product_codes': [],
                    'contracts_by_product': {},
                    'statistics': {
                        'total_symbols': 0,
                        'total_product_codes': 0,
                        'total_contracts': 0
                    }
                }
                
                try:
                    # Get all symbols for this exchange using alphanumeric search
                    self.logger.info(f"Getting all symbols for {exchange}...")
                    exchange_symbols = await self.search_symbols_alphanumeric(
                        exchange=exchange,
                        instrument_type="FUTURE"
                    )
                    
                    exchange_data['symbols'] = exchange_symbols
                    exchange_data['statistics']['total_symbols'] = len(exchange_symbols)
                    
                    # Extract and organize product codes
                    product_codes = set()
                    for symbol in exchange_symbols:
                        product_code = symbol.get('product_code', '').strip()
                        if product_code:
                            product_codes.add(product_code)
                    
                    exchange_data['product_codes'] = sorted(list(product_codes))
                    exchange_data['statistics']['total_product_codes'] = len(product_codes)
                    
                    # Organize contracts by product code
                    for product_code in product_codes:
                        product_contracts = [
                            symbol for symbol in exchange_symbols 
                            if symbol.get('product_code', '') == product_code
                        ]
                        exchange_data['contracts_by_product'][product_code] = product_contracts
                    
                    total_contracts = sum(
                        len(contracts) for contracts in exchange_data['contracts_by_product'].values()
                    )
                    exchange_data['statistics']['total_contracts'] = total_contracts
                    
                    # Add to global statistics
                    reference_data['statistics']['total_symbols'] += len(exchange_symbols)
                    reference_data['statistics']['total_contracts'] += total_contracts
                    
                    # Store exchange data
                    reference_data['exchanges'][exchange] = exchange_data
                    
                    self.logger.info(f"Exchange {exchange}: {len(exchange_symbols)} symbols, "
                                   f"{len(product_codes)} product codes, {total_contracts} contracts")
                
                except Exception as e:
                    error_msg = f"Error collecting data for exchange {exchange}: {e}"
                    self.logger.error(error_msg)
                    reference_data['errors'].append(error_msg)
                    continue
            
            # Step 3: Create global product codes index
            all_product_codes = set()
            for exchange_data in reference_data['exchanges'].values():
                all_product_codes.update(exchange_data['product_codes'])
            
            reference_data['product_codes'] = {
                'all_codes': sorted(list(all_product_codes)),
                'by_exchange': {
                    exchange: data['product_codes'] 
                    for exchange, data in reference_data['exchanges'].items()
                }
            }
            reference_data['statistics']['total_product_codes'] = len(all_product_codes)
            
            # Step 4: Create global symbols index
            all_symbols = {}
            for exchange, exchange_data in reference_data['exchanges'].items():
                for symbol_data in exchange_data['symbols']:
                    symbol_key = symbol_data.get('symbol', '')
                    if symbol_key:
                        all_symbols[symbol_key] = symbol_data
            
            reference_data['symbols'] = all_symbols
            
            # Final statistics
            stats = reference_data['statistics']
            self.logger.info("=== COMPREHENSIVE REFERENCE DATA COLLECTION COMPLETED ===")
            self.logger.info(f"Total exchanges: {stats['total_exchanges']}")
            self.logger.info(f"Total product codes: {stats['total_product_codes']}")
            self.logger.info(f"Total unique symbols: {len(all_symbols)}")
            self.logger.info(f"Total contracts: {stats['total_contracts']}")
            
            if reference_data['errors']:
                self.logger.warning(f"Collection completed with {len(reference_data['errors'])} errors")
            
            return reference_data
            
        except Exception as e:
            error_msg = f"Error in comprehensive reference data collection: {e}"
            self.logger.error(error_msg)
            reference_data['errors'].append(error_msg)
            return reference_data
    
    async def search_all_instrument_types(self, 
                                        exchange: str = None,
                                        search_text: str = None,
                                        verbose: bool = True) -> Dict[str, List[Dict[str, Any]]]:
        """
        Exhaustive search across all 10 instrument types to discover every possible contract.
        
        Args:
            exchange: Optional exchange filter
            search_text: Optional search text (defaults to alphanumeric iteration)
            verbose: Enable detailed logging
            
        Returns:
            Dictionary mapping instrument type names to their contract lists
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        if verbose:
            self.logger.info(f"Starting exhaustive search across all instrument types for {exchange or 'all exchanges'}")
        
        results_by_type = {}
        total_contracts = 0
        
        try:
            for instrument_name, instrument_value in self.INSTRUMENT_TYPES.items():
                if verbose:
                    self.logger.info(f"=== SEARCHING INSTRUMENT TYPE: {instrument_name} (ID: {instrument_value}) ===")
                
                try:
                    if search_text:
                        # Use specific search text
                        contracts = await self.search_symbols(
                            search_text=search_text,
                            exchange=exchange,
                            instrument_type=instrument_name,
                            pattern="CONTAINS"
                        )
                    else:
                        # Use comprehensive alphanumeric search (no limits)
                        contracts = await self.search_symbols_alphanumeric(
                            exchange=exchange,
                            instrument_type=instrument_name,
                            max_results=None  # No limits
                        )
                    
                    if contracts:
                        results_by_type[instrument_name] = contracts
                        contract_count = len(contracts)
                        total_contracts += contract_count
                        
                        if verbose:
                            self.logger.info(f"✅ {instrument_name}: Found {contract_count} contracts")
                            
                            # Show sample contracts for each type
                            if contract_count > 0:
                                sample_symbols = [c.get('symbol', 'N/A') for c in contracts[:3]]
                                self.logger.info(f"   Sample symbols: {', '.join(sample_symbols)}")
                                
                                # Show exchange breakdown
                                exchanges_found = {}
                                for contract in contracts:
                                    ex = contract.get('exchange', 'Unknown')
                                    exchanges_found[ex] = exchanges_found.get(ex, 0) + 1
                                
                                if len(exchanges_found) > 1:
                                    breakdown = ', '.join([f"{ex}:{count}" for ex, count in exchanges_found.items()])
                                    self.logger.info(f"   Exchange breakdown: {breakdown}")
                    else:
                        if verbose:
                            self.logger.info(f"⚠️  {instrument_name}: No contracts found")
                    
                    # Small delay between instrument types
                    await asyncio.sleep(0.2)
                    
                except Exception as e:
                    error_msg = f"Error searching {instrument_name}: {e}"
                    self.logger.error(error_msg)
                    continue
            
            if verbose:
                self.logger.info("=== EXHAUSTIVE SEARCH SUMMARY ===")
                self.logger.info(f"Total instrument types searched: {len(self.INSTRUMENT_TYPES)}")
                self.logger.info(f"Instrument types with contracts: {len(results_by_type)}")
                self.logger.info(f"Total contracts found: {total_contracts}")
                
                for instrument_name, contracts in results_by_type.items():
                    self.logger.info(f"  {instrument_name}: {len(contracts)} contracts")
            
            return results_by_type
            
        except Exception as e:
            self.logger.error(f"Error in exhaustive instrument type search: {e}")
            return results_by_type  # Return partial results
    
    async def get_exchange_permissions(self) -> List[str]:
        """
        Get the list of exchanges this user has permission to access.
        
        Returns:
            List of permitted exchange names
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info("Requesting exchange permissions...")
        
        try:
            # Import the protobuf message
            from proto_generated import request_list_exchange_permissions_pb2, response_list_exchange_permissions_pb2
            
            request = request_list_exchange_permissions_pb2.RequestListExchangePermissions()
            request.template_id = self.TEMPLATE_IDS['REQUEST_LIST_EXCHANGE_PERMISSIONS']
            request.user_msg.append("Exchange permissions request")
            
            # Use the new multi-response method to collect all exchange permission responses
            responses = await self.send_request_multi_response(
                request_message=request,
                expected_response_template_ids=[self.TEMPLATE_IDS['RESPONSE_LIST_EXCHANGE_PERMISSIONS']],
                timeout=15.0,
                max_timeouts=3
            )
            
            # Process all collected responses
            exchanges = []
            for template_id, response, raw_data in responses:
                if template_id == self.TEMPLATE_IDS['RESPONSE_LIST_EXCHANGE_PERMISSIONS']:
                    if response and hasattr(response, 'exchange'):
                        exchange_name = response.exchange
                        if exchange_name and exchange_name not in exchanges:
                            exchanges.append(exchange_name)
                            self.logger.info(f"Permission granted for exchange: {exchange_name}")
            
            if exchanges:
                self.logger.info(f"User has permission for {len(exchanges)} exchanges: {', '.join(exchanges)}")
            else:
                self.logger.warning("No exchange permissions received or user has no exchange permissions")
            
            return exchanges
            
        except ImportError:
            # If protobuf file doesn't exist, return hardcoded list as fallback
            self.logger.warning("Exchange permissions protobuf not available, using fallback discovery")
            return await self._discover_exchanges_fallback()
        except Exception as e:
            self.logger.error(f"Error getting exchange permissions: {e}")
            # Fallback to discovery method
            return await self._discover_exchanges_fallback()
    
    async def _discover_exchanges_fallback(self) -> List[str]:
        """
        Fallback method to discover exchanges by sampling symbols.
        """
        self.logger.info("Discovering exchanges through symbol sampling...")
        
        try:
            # Get a sample of symbols to discover exchanges
            sample_symbols = await self.search_symbols_alphanumeric(max_results=1000)
            
            exchanges = set()
            for symbol in sample_symbols:
                exchange = symbol.get('exchange', '').strip()
                if exchange:
                    exchanges.add(exchange)
            
            exchange_list = sorted(list(exchanges))
            self.logger.info(f"Discovered {len(exchange_list)} exchanges through sampling: {', '.join(exchange_list)}")
            
            return exchange_list
            
        except Exception as e:
            self.logger.error(f"Error in exchange discovery fallback: {e}")
            # Ultimate fallback to known major exchanges
            fallback_exchanges = ["CME", "CBOT", "NYMEX", "COMEX", "ICE", "EUREX"]
            self.logger.warning(f"Using hardcoded fallback exchanges: {', '.join(fallback_exchanges)}")
            return fallback_exchanges
    
    async def get_contract_series(self, product_code: str, exchange: str = "CME") -> List[Dict[str, Any]]:
        """
        Get all available contracts for a specific product code.
        
        Args:
            product_code: Product symbol (e.g., "ES", "NQ")
            exchange: Exchange name (default: "CME")
            
        Returns:
            List of contract dictionaries sorted by expiration date
        """
        self.logger.info(f"Getting contract series for {product_code} on {exchange}")
        
        # Use wildcard search to find all contracts for this product
        contracts = await self.search_symbols(
            search_text=f"{product_code}*",
            exchange=exchange,
            product_code=product_code,
            instrument_type="FUTURE"
        )
        
        # Sort contracts by symbol (which typically includes expiration info)
        contracts_sorted = sorted(contracts, key=lambda x: x['symbol'])
        
        self.logger.info(f"Found {len(contracts_sorted)} contracts for {product_code}")
        return contracts_sorted
    
    async def search_contracts_by_pattern(self, pattern: str) -> List[Dict[str, Any]]:
        """
        Search for contracts using wildcard patterns.
        
        Supports patterns like:
        - "ES*" - All ES contracts
        - "*.CME" - All CME contracts  
        - "*" - All contracts
        
        Args:
            pattern: Search pattern with wildcards
            
        Returns:
            List of matching contract dictionaries
        """
        self.logger.info(f"Searching contracts by pattern: {pattern}")
        
        # Parse pattern to extract exchange and product info
        exchange = None
        search_text = pattern
        
        # Handle exchange-specific patterns like "*.CME"
        if "." in pattern:
            parts = pattern.split(".")
            if len(parts) == 2:
                search_text = parts[0] if parts[0] != "*" else ""
                exchange = parts[1] if parts[1] != "*" else None
        
        # Handle product-specific patterns like "ES*"
        product_code = None
        if search_text and search_text != "*":
            # Extract product code (everything before the first wildcard)
            if "*" in search_text:
                product_code = search_text.replace("*", "")
            else:
                product_code = search_text
        
        # Perform the search
        results = await self.search_symbols(
            search_text=search_text if search_text else None,
            exchange=exchange,
            product_code=product_code,
            instrument_type="FUTURE"
        )
        
        self.logger.info(f"Pattern '{pattern}' matched {len(results)} contracts")
        return results
    
    async def get_all_contracts(self, exchange: str = None) -> List[Dict[str, Any]]:
        """
        Get all available contracts, optionally filtered by exchange.
        
        Args:
            exchange: Optional exchange filter (e.g., "CME")
            
        Returns:
            List of all contract dictionaries
        """
        self.logger.info(f"Getting all contracts" + (f" on {exchange}" if exchange else ""))
        
        # Use alphanumeric iteration search instead of wildcard (more reliable)
        return await self.search_symbols_alphanumeric(
            exchange=exchange,
            instrument_type="FUTURE"
        )
    
    async def validate_symbol(self, symbol: str, exchange: str = "CME") -> bool:
        """
        Validate if a symbol exists and is tradeable.
        
        Args:
            symbol: Symbol to validate
            exchange: Exchange to check on
            
        Returns:
            True if symbol exists and is valid
        """
        self.logger.info(f"Validating symbol: {symbol} on {exchange}")
        
        # Search for exact symbol match
        results = await self.search_symbols(
            search_text=symbol,
            exchange=exchange,
            pattern="EQUALS"
        )
        
        is_valid = len(results) > 0
        self.logger.info(f"Symbol {symbol} is {'valid' if is_valid else 'invalid'}")
        return is_valid
    
    async def resolve_continuous_contract(self, product_code: str, exchange: str = "CME") -> str:
        """
        Resolve a continuous contract (e.g., "@ES") to the actual front month contract.
        
        Args:
            product_code: Product code (e.g., "ES")
            exchange: Exchange name
            
        Returns:
            Actual front month contract symbol or None
        """
        self.logger.info(f"Resolving continuous contract for {product_code}")
        
        # Use existing front month contract method
        front_month = await self.find_front_month_contract(product_code, exchange)
        
        if front_month:
            self.logger.info(f"Continuous contract @{product_code} resolved to {front_month}")
        else:
            self.logger.warning(f"Could not resolve continuous contract @{product_code}")
            
        return front_month
    
    async def search_symbols_optimized(self, 
                                  search_text: str = None,
                                  exchange: str = None,
                                  product_code: str = None,
                                  instrument_type: str = None,
                                  pattern: str = "CONTAINS") -> List[Dict[str, Any]]:
        """
        Optimized symbol search using send_request method for concurrent operation safety.
        
        Args:
            search_text: Free text search string
            exchange: Filter by exchange (e.g., "CME", "CBOT")
            product_code: Filter by product code (e.g., "ES", "NQ")
            instrument_type: Filter by instrument type (e.g., "FUTURE", "EQUITY")
            pattern: Search pattern - "CONTAINS" or "EQUALS"
            
        Returns:
            List of symbol dictionaries with details
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Searching symbols (optimized): text='{search_text}', exchange='{exchange}', "
                        f"product='{product_code}', type='{instrument_type}', pattern='{pattern}'")
        
        try:
            request = request_search_symbols_pb2.RequestSearchSymbols()
            request.template_id = self.TEMPLATE_IDS['REQUEST_SEARCH_SYMBOLS']
            request.user_msg.append(f"Symbol search request")
            
            # Set search parameters
            if search_text:
                request.search_text = search_text
            if exchange:
                request.exchange = exchange
            if product_code:
                request.product_code = product_code
            if instrument_type:
                request.instrument_type = instrument_type
                
            # Set pattern (default to CONTAINS)
            if pattern == "EQUALS":
                request.pattern = request_search_symbols_pb2.RequestSearchSymbols.EQUALS
            else:
                request.pattern = request_search_symbols_pb2.RequestSearchSymbols.CONTAINS
            
            # Use optimized send_request method - this eliminates recv conflicts
            template_id, response, raw_data = await self.send_request(
                request,
                [self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']],
                timeout=10.0
            )
            
            symbols = []
            
            if template_id == self.TEMPLATE_IDS['RESPONSE_SEARCH_SYMBOLS']:
                if response and hasattr(response, 'symbol'):
                    # Extract symbol information
                    symbol_info = {
                        'symbol': response.symbol,
                        'symbol_name': getattr(response, 'symbol_name', ''),
                        'exchange': getattr(response, 'exchange', ''),
                        'product_code': getattr(response, 'product_code', ''),
                        'instrument_type': getattr(response, 'instrument_type', ''),
                        'expiration_date': getattr(response, 'expiration_date', ''),
                        'strike_price': getattr(response, 'strike_price', ''),
                        'put_call_indicator': getattr(response, 'put_call_indicator', ''),
                        'underlying_symbol': getattr(response, 'underlying_symbol', ''),
                        'tick_size': getattr(response, 'tick_size', ''),
                        'display_factor': getattr(response, 'display_factor', ''),
                        'currency': getattr(response, 'currency', ''),
                        'description': getattr(response, 'description', ''),
                        'contract_size': getattr(response, 'contract_size', ''),
                        'is_tradeable': getattr(response, 'is_tradeable', True),
                        'discovery_timestamp': datetime.now().isoformat(),
                        'discovery_method': 'optimized_search'
                    }
                    symbols.append(symbol_info)
                    
                    self.logger.info(f"Found {len(symbols)} symbols via optimized search")
                    return symbols
                else:
                    self.logger.warning("No symbols found in search response")
                    return []
            else:
                self.logger.error(f"Unexpected response template ID: {template_id}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error in optimized symbol search: {e}")
            return []

    # ========================= SUBSCRIPTION MANAGEMENT METHODS =========================
    
    async def add_market_data_subscription(self, 
                                         symbol: str, 
                                         exchange: str = "CME",
                                         callback: Optional[Callable] = None) -> str:
        """Add a market data subscription with callback.
        
        Args:
            symbol: Symbol to subscribe to
            exchange: Exchange name
            callback: Optional callback for streaming data
            
        Returns:
            Subscription ID for management
        """
        subscription_id = str(uuid.uuid4())
        
        # Define template IDs for market data streaming
        streaming_template_ids = [
            self.TEMPLATE_IDS['LAST_TRADE'],
            self.TEMPLATE_IDS['BEST_BID_OFFER'],
            self.TEMPLATE_IDS['ORDER_BOOK'],
            self.TEMPLATE_IDS['DEPTH_BY_ORDER'],
            self.TEMPLATE_IDS['DEPTH_BY_ORDER_END_EVENT']
        ]
        
        # Create subscription
        subscription = StreamingSubscription(
            subscription_id=subscription_id,
            symbol=symbol,
            exchange=exchange,
            template_ids=streaming_template_ids,
            callback=callback
        )
        
        # Register with message router
        self.message_router.add_streaming_subscription(subscription)
        
        self.logger.info(f"Added market data subscription for {symbol} on {exchange} (ID: {subscription_id})")
        return subscription_id
    
    async def remove_market_data_subscription(self, subscription_id: str):
        """Remove a market data subscription.
        
        Args:
            subscription_id: Subscription ID to remove
        """
        self.message_router.remove_streaming_subscription(subscription_id)
        self.logger.info(f"Removed market data subscription (ID: {subscription_id})")
    
    async def subscribe_to_market_data_optimized(self, 
                                               symbol: str, 
                                               exchange: str = "CME",
                                               callback: Optional[Callable] = None) -> str:
        """Subscribe to market data with optimized message routing.
        
        Args:
            symbol: Symbol to subscribe to
            exchange: Exchange name
            callback: Optional callback for streaming data
            
        Returns:
            Subscription ID for management
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
            
        self.logger.info(f"Subscribing to market data for {symbol} on {exchange} (optimized)")
        
        try:
            # Add subscription first
            subscription_id = await self.add_market_data_subscription(symbol, exchange, callback)
            
            # Create subscription request
            request = request_market_data_update_pb2.RequestMarketDataUpdate()
            request.template_id = self.TEMPLATE_IDS['REQUEST_MARKET_DATA_UPDATE']
            request.user_msg.append(f"Market data subscription for {symbol}")
            request.symbol = symbol
            request.exchange = exchange
            
            # Set request type to SUBSCRIBE
            request.request = request_market_data_update_pb2.RequestMarketDataUpdate.SUBSCRIBE
            
            # Request specific update types
            update_bits = (
                request_market_data_update_pb2.RequestMarketDataUpdate.LAST_TRADE |
                request_market_data_update_pb2.RequestMarketDataUpdate.BBO |
                request_market_data_update_pb2.RequestMarketDataUpdate.ORDER_BOOK |
                request_market_data_update_pb2.RequestMarketDataUpdate.OPEN |
                request_market_data_update_pb2.RequestMarketDataUpdate.HIGH_LOW |
                request_market_data_update_pb2.RequestMarketDataUpdate.CLOSE
            )
            request.update_bits = update_bits
            
            # Send subscription request and wait for confirmation
            template_id, response, raw_data = await self.send_request(
                request,
                [self.TEMPLATE_IDS['RESPONSE_MARKET_DATA_UPDATE']],
                timeout=10.0
            )
            
            if template_id == self.TEMPLATE_IDS['RESPONSE_MARKET_DATA_UPDATE']:
                if response and len(response.rp_code) == 1 and response.rp_code[0] == "0":
                    self.logger.info(f"Successfully subscribed to market data for {symbol} (ID: {subscription_id})")
                    return subscription_id
                else:
                    # Remove subscription on failure
                    await self.remove_market_data_subscription(subscription_id)
                    error_msg = f"Market data subscription failed: {response.rp_code if response else 'Unknown error'}"
                    self.logger.error(error_msg)
                    raise Exception(error_msg)
            
        except Exception as e:
            self.logger.error(f"Error subscribing to market data: {e}")
            raise
        
        return subscription_id
    
    # ========================= BATCH OPERATIONS WITH OPTIMIZED ROUTING =========================
    
    async def batch_symbol_search(self, 
                                search_requests: List[Dict[str, Any]],
                                max_concurrent: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """Perform multiple symbol searches concurrently using optimized routing.
        
        Args:
            search_requests: List of search request dictionaries
            max_concurrent: Maximum concurrent requests
            
        Returns:
            Dictionary mapping request IDs to search results
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
        
        self.logger.info(f"Starting batch symbol search for {len(search_requests)} requests")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def search_single(search_params: Dict[str, Any]) -> tuple:
            async with semaphore:
                request_id = search_params.get('id', str(uuid.uuid4()))
                try:
                    results = await self.search_symbols_optimized(
                        search_text=search_params.get('search_text'),
                        exchange=search_params.get('exchange'),
                        product_code=search_params.get('product_code'),
                        instrument_type=search_params.get('instrument_type', 'FUTURE'),
                        pattern=search_params.get('pattern', 'CONTAINS')
                    )
                    return request_id, results
                except Exception as e:
                    self.logger.error(f"Error in batch search {request_id}: {e}")
                    return request_id, []
        
        # Execute all searches concurrently
        tasks = [search_single(params) for params in search_requests]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        results = {}
        for result in batch_results:
            if isinstance(result, Exception):
                self.logger.error(f"Batch search task failed: {result}")
                continue
            
            request_id, search_results = result
            results[request_id] = search_results
        
        total_results = sum(len(r) for r in results.values())
        self.logger.info(f"Batch symbol search completed: {len(results)} requests, {total_results} total results")
        
        return results
    
    async def batch_subscribe_market_data(self, 
                                        symbols: List[Dict[str, str]],
                                        max_concurrent: int = 10,
                                        callback: Optional[Callable] = None) -> Dict[str, str]:
        """Subscribe to market data for multiple symbols concurrently.
        
        Args:
            symbols: List of symbol dictionaries with 'symbol' and 'exchange' keys
            max_concurrent: Maximum concurrent subscriptions
            callback: Optional global callback for all subscriptions
            
        Returns:
            Dictionary mapping symbols to subscription IDs
        """
        if not self.is_authenticated:
            raise Exception("Not authenticated. Please login first.")
        
        self.logger.info(f"Starting batch market data subscription for {len(symbols)} symbols")
        
        # Create semaphore to limit concurrent subscriptions
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def subscribe_single(symbol_info: Dict[str, str]) -> tuple:
            async with semaphore:
                symbol = symbol_info['symbol']
                exchange = symbol_info.get('exchange', 'CME')
                
                try:
                    subscription_id = await self.subscribe_to_market_data_optimized(
                        symbol=symbol,
                        exchange=exchange,
                        callback=callback
                    )
                    return symbol, subscription_id
                except Exception as e:
                    self.logger.error(f"Error subscribing to {symbol}: {e}")
                    return symbol, None
        
        # Execute all subscriptions concurrently
        tasks = [subscribe_single(symbol_info) for symbol_info in symbols]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        subscriptions = {}
        successful = 0
        
        for result in batch_results:
            if isinstance(result, Exception):
                self.logger.error(f"Batch subscription task failed: {result}")
                continue
            
            symbol, subscription_id = result
            if subscription_id:
                subscriptions[symbol] = subscription_id
                successful += 1
        
        self.logger.info(f"Batch subscription completed: {successful}/{len(symbols)} successful")
        return subscriptions
    
    # ========================= CONNECTION AND PERFORMANCE MONITORING =========================
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get comprehensive connection and performance statistics."""
        router_stats = self.message_router.get_stats() if self.message_router else {}
        
        return {
            'connection': {
                'is_connected': self.is_connected,
                'is_authenticated': self.is_authenticated,
                'heartbeat_interval': self.heartbeat_interval,
                'uri': self.uri
            },
            'message_routing': router_stats,
            'memory_usage': {
                'pending_requests': router_stats.get('pending_requests', 0),
                'active_subscriptions': router_stats.get('active_subscriptions', 0),
                'total_subscriptions': router_stats.get('total_subscriptions', 0)
            },
            'performance': {
                'messages_routed': router_stats.get('messages_routed', 0),
                'requests_handled': router_stats.get('requests_handled', 0),
                'streaming_messages': router_stats.get('streaming_messages', 0),
                'routing_errors': router_stats.get('routing_errors', 0)
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a comprehensive health check of the connection."""
        health = {
            'status': 'healthy',
            'checks': {},
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # Check basic connection
            health['checks']['connection'] = {
                'status': 'pass' if self.is_connected else 'fail',
                'details': 'WebSocket connection active' if self.is_connected else 'No connection'
            }
            
            # Check authentication
            health['checks']['authentication'] = {
                'status': 'pass' if self.is_authenticated else 'fail',
                'details': 'Authenticated' if self.is_authenticated else 'Not authenticated'
            }
            
            # Check message router
            if self.message_router.is_running:
                router_stats = self.message_router.get_stats()
                health['checks']['message_router'] = {
                    'status': 'pass',
                    'details': f"Router active, {router_stats.get('messages_routed', 0)} messages routed"
                }
            else:
                health['checks']['message_router'] = {
                    'status': 'warn',
                    'details': 'Message router not running'
                }
            
            # Check for any failed checks
            failed_checks = [check for check in health['checks'].values() if check['status'] == 'fail']
            if failed_checks:
                health['status'] = 'unhealthy'
            elif any(check['status'] == 'warn' for check in health['checks'].values()):
                health['status'] = 'degraded'
            
        except Exception as e:
            health['status'] = 'error'
            health['error'] = str(e)
        
        return health
    
    def get_router_stats(self) -> Dict[str, Any]:
        """Get message router statistics for monitoring."""
        return self.message_router.get_stats()
    
    # ========================= UTILITY METHODS =========================
    
    async def cleanup_resources(self):
        """Clean up all resources and connections."""
        self.logger.info("Cleaning up websocket client resources...")
        
        try:
            # Stop message router
            if self.message_router:
                await self.message_router.stop()
            
            # Disconnect websocket
            await self.disconnect()
            
            self.logger.info("Resource cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during resource cleanup: {e}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup_resources()