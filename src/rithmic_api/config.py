"""
Configuration management for Rithmic API client.
Loads settings from .env file with fallback defaults.
"""

import os
from pathlib import Path
from typing import Optional, List

class RithmicConfig:
    """Configuration manager for Rithmic API settings."""
    
    def __init__(self, env_file: str = ".env"):
        """
        Initialize configuration from .env file.
        
        Args:
            env_file: Path to environment file (default: .env)
        """
        self.env_file = Path(env_file)
        self._load_env_file()
    
    def _load_env_file(self):
        """Load environment variables from .env file."""
        if self.env_file.exists():
            with open(self.env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
    
    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get configuration value with optional default."""
        return os.environ.get(key, default)
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean configuration value."""
        value = self.get(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    # Credentials
    @property
    def user(self) -> str:
        return self.get('RITHMIC_USER', 'PP-013155')
    
    @property
    def password(self) -> str:
        return self.get('RITHMIC_PASSWORD', 'b7neA8k6JA')
    
    @property
    def system_name(self) -> str:
        return self.get('RITHMIC_SYSTEM', 'Rithmic Paper Trading')
    
    @property
    def gateway(self) -> str:
        return self.get('RITHMIC_GATEWAY', 'Chicago Area')
    
    @property
    def app_name(self) -> str:
        return self.get('RITHMIC_APP_NAME', 'PythonRithmicClient')
    
    @property
    def app_version(self) -> str:
        return self.get('RITHMIC_APP_VERSION', '1.0.0')
    
    # Connection Settings
    @property
    def uri(self) -> str:
        return self.get('RITHMIC_URI', 'wss://rprotocol.rithmic.com:443')
    
    @property
    def ssl_cert_path(self) -> str:
        return self.get('RITHMIC_SSL_CERT_PATH', 'etc/rithmic_ssl_cert_auth_params')
    
    @property
    def template_version(self) -> str:
        return self.get('RITHMIC_TEMPLATE_VERSION', '3.9')
    
    # Data Settings
    @property
    def data_directory(self) -> str:
        return self.get('DATA_DIRECTORY', 'data')
    
    @property
    def clear_data_on_start(self) -> bool:
        return self.get_bool('CLEAR_DATA_ON_START', True)
    
    # ===== MULTI-CONTRACT SUBSCRIPTION SETTINGS =====
    
    @property
    def contracts_to_subscribe(self) -> List[str]:
        """Get list of contracts to subscribe to from configuration."""
        contracts_str = self.get('CONTRACTS_TO_SUBSCRIBE', '@ES')
        if not contracts_str.strip():
            return ['@ES']  # Default to ES front month
        return [contract.strip() for contract in contracts_str.split() if contract.strip()]
    
    @property
    def max_concurrent_contracts(self) -> Optional[int]:
        """Maximum number of contracts to subscribe to simultaneously."""
        max_str = self.get('MAX_CONCURRENT_CONTRACTS')
        if max_str and max_str.strip():
            try:
                return int(max_str)
            except ValueError:
                return None
        return None
    
    @property
    def cache_contracts(self) -> bool:
        """Whether to cache contract information."""
        return self.get_bool('CACHE_CONTRACTS', True)
    
    @property
    def cache_ttl_hours(self) -> int:
        """Cache time-to-live in hours."""
        ttl_str = self.get('CACHE_TTL_HOURS', '24')
        try:
            return int(ttl_str)
        except ValueError:
            return 24
    
    @property
    def auto_refresh_cache(self) -> bool:
        """Whether to automatically refresh expired cache."""
        return self.get_bool('AUTO_REFRESH_CACHE', True)
    
    @property
    def subscribe_on_startup(self) -> bool:
        """Whether to subscribe to contracts on startup."""
        return self.get_bool('SUBSCRIBE_ON_STARTUP', True)
    
    @property
    def validate_contracts_before_subscribe(self) -> bool:
        """Whether to validate contracts before subscribing."""
        return self.get_bool('VALIDATE_CONTRACTS_BEFORE_SUBSCRIBE', True)
    
    @property
    def continue_on_invalid_contract(self) -> bool:
        """Whether to continue with other contracts if one is invalid."""
        return self.get_bool('CONTINUE_ON_INVALID_CONTRACT', True)
    
    @property
    def batch_subscription_size(self) -> int:
        """Number of contracts to subscribe to in each batch."""
        batch_str = self.get('BATCH_SUBSCRIPTION_SIZE', '5')
        try:
            return int(batch_str)
        except ValueError:
            return 5
    
    @property
    def subscription_delay_ms(self) -> int:
        """Delay between subscription requests in milliseconds."""
        delay_str = self.get('SUBSCRIPTION_DELAY_MS', '100')
        try:
            return int(delay_str)
        except ValueError:
            return 100

# Global configuration instance
config = RithmicConfig()