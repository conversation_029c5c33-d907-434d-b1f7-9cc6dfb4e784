"""
Rithmic API SDK
===============

A comprehensive Python SDK for the Rithmic R | Protocol API providing:
- WebSocket client with authentication
- Market data subscription (Level 1 & Level 3)
- Contract discovery
- Real-time data collection

Main Components:
- RithmicWebSocketClient: Core client for API interactions
- Configuration management via config module
"""

from .rithmic_websocket_client import RithmicWebSocketClient
from .config import config

__version__ = "1.0.0"
__all__ = ["RithmicWebSocketClient", "config"]