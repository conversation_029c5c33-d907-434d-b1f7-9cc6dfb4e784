-- =============================================================================
-- Rithmic API Database Schema
-- =============================================================================
-- Comprehensive MySQL database schema for persisting all Rithmic API data
-- including static reference data, real-time market data, and system data.
-- 
-- Created: 2025-06-25
-- Version: 1.0.0
-- =============================================================================

-- Set SQL mode for strict data handling
SET SQL_MODE = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- Use UTF-8 character set for international symbol support
SET NAMES utf8mb4;

-- =============================================================================
-- 1. STATIC REFERENCE DATA TABLES
-- =============================================================================

-- Exchanges table - stores exchange information
CREATE TABLE IF NOT EXISTS exchanges (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    exchange_code VARCHAR(20) NOT NULL UNIQUE,
    exchange_name VARCHAR(100),
    timezone VARCHAR(50),
    country_code VARCHAR(5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_exchange_code (exchange_code),
    INDEX idx_country_code (country_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Product codes table - stores product information
CREATE TABLE IF NOT EXISTS product_codes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(20) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    symbol_name VARCHAR(255),
    timezone_time_of_interest VARCHAR(50),
    begin_time_of_interest_msm INT,
    end_time_of_interest_msm INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_product_exchange (product_code, exchange),
    INDEX idx_product_code (product_code),
    INDEX idx_exchange (exchange)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Symbols table - comprehensive contract/symbol information
CREATE TABLE IF NOT EXISTS symbols (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    symbol_name VARCHAR(255),
    product_code VARCHAR(20),
    instrument_type VARCHAR(50),
    expiration_date DATE,
    strike_price DECIMAL(15,8),
    put_call_indicator ENUM('PUT', 'CALL'),
    currency VARCHAR(10),
    lot_size INT,
    tick_size DECIMAL(15,8),
    tick_value DECIMAL(15,8),
    margin_rate DECIMAL(8,4),
    template_id INT DEFAULT 110,
    raw_response JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_symbol_exchange (symbol, exchange),
    INDEX idx_product_code (product_code),
    INDEX idx_instrument_type (instrument_type),
    INDEX idx_expiration_date (expiration_date),
    INDEX idx_exchange (exchange),
    INDEX idx_strike_price (strike_price)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- 2. USER SESSION AND AUTHENTICATION TABLES
-- =============================================================================

-- User sessions table - login session tracking
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    unique_user_id VARCHAR(100) NOT NULL,
    fcm_id VARCHAR(50),
    ib_id VARCHAR(50),
    country_code VARCHAR(5),
    state_code VARCHAR(5),
    template_version VARCHAR(20),
    heartbeat_interval DECIMAL(8,3),
    login_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat TIMESTAMP,
    logout_timestamp TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    session_info JSON,
    
    INDEX idx_unique_user_id (unique_user_id),
    INDEX idx_login_timestamp (login_timestamp),
    INDEX idx_is_active (is_active),
    INDEX idx_last_heartbeat (last_heartbeat)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System info table - Rithmic system information
CREATE TABLE IF NOT EXISTS system_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    system_name VARCHAR(100),
    gateway_name VARCHAR(100),
    server_version VARCHAR(50),
    infrastructure_type VARCHAR(50),
    system_timestamp TIMESTAMP,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    system_data JSON,
    
    INDEX idx_system_name (system_name),
    INDEX idx_received_at (received_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- 3. REAL-TIME MARKET DATA TABLES
-- =============================================================================

-- Best bid/offer table - Level 1 market data
CREATE TABLE IF NOT EXISTS best_bid_offer (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    bid_price DECIMAL(15,8),
    bid_size INT,
    bid_orders INT,
    bid_implicit_size INT,
    bid_time VARCHAR(50),
    ask_price DECIMAL(15,8),
    ask_size INT,
    ask_orders INT,
    ask_implicit_size INT,
    ask_time VARCHAR(50),
    lean_price DECIMAL(15,8),
    presence_bits INT UNSIGNED,
    clear_bits INT UNSIGNED,
    is_snapshot BOOLEAN DEFAULT FALSE,
    ssboe INT,
    usecs INT,
    market_timestamp TIMESTAMP(6),
    received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
    
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_market_timestamp (market_timestamp),
    INDEX idx_received_at (received_at),
    INDEX idx_symbol_time (symbol, market_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Last trades table - trade execution data
CREATE TABLE IF NOT EXISTS last_trades (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    trade_price DECIMAL(15,8),
    trade_size INT,
    aggressor ENUM('BUY', 'SELL'),
    exchange_order_id VARCHAR(100),
    aggressor_exchange_order_id VARCHAR(100),
    net_change DECIMAL(15,8),
    percent_change DECIMAL(8,4),
    volume BIGINT UNSIGNED,
    vwap DECIMAL(15,8),
    presence_bits INT UNSIGNED,
    clear_bits INT UNSIGNED,
    is_snapshot BOOLEAN DEFAULT FALSE,
    ssboe INT,
    usecs INT,
    source_ssboe INT,
    source_usecs INT,
    source_nsecs INT,
    jop_ssboe INT,
    jop_nsecs INT,
    trade_timestamp TIMESTAMP(6),
    received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
    
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_trade_timestamp (trade_timestamp),
    INDEX idx_received_at (received_at),
    INDEX idx_volume (volume),
    INDEX idx_trade_price (trade_price),
    INDEX idx_symbol_time (symbol, trade_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Market data update subscriptions - tracks active subscriptions
CREATE TABLE IF NOT EXISTS market_data_subscriptions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    subscription_type ENUM('LEVEL1', 'LEVEL2', 'TRADES', 'ALL') DEFAULT 'LEVEL1',
    is_active BOOLEAN DEFAULT TRUE,
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL,
    last_update TIMESTAMP NULL,
    update_count BIGINT DEFAULT 0,
    
    UNIQUE KEY unique_symbol_subscription (symbol, exchange, subscription_type),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_is_active (is_active),
    INDEX idx_last_update (last_update)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- 4. ORDER BOOK DEPTH TABLES (Level 2/3 Data)
-- =============================================================================

-- Depth by order snapshots - order book snapshot headers
CREATE TABLE IF NOT EXISTS depth_by_order_snapshots (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    sequence_number BIGINT UNSIGNED,
    presence_bits INT UNSIGNED,
    clear_bits INT UNSIGNED,
    is_snapshot BOOLEAN DEFAULT TRUE,
    ssboe INT,
    usecs INT,
    source_ssboe INT,
    source_usecs INT,
    source_nsecs INT,
    jop_ssboe INT,
    jop_nsecs INT,
    market_timestamp TIMESTAMP(6),
    received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
    level_count INT DEFAULT 0,
    
    UNIQUE KEY unique_symbol_sequence (symbol, exchange, sequence_number),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_market_timestamp (market_timestamp),
    INDEX idx_sequence_number (sequence_number),
    INDEX idx_symbol_time (symbol, market_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Depth by order levels - individual order book levels
CREATE TABLE IF NOT EXISTS depth_by_order_levels (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    snapshot_id BIGINT NOT NULL,
    level_index TINYINT NOT NULL,
    update_type ENUM('NEW', 'CHANGE', 'DELETE', 'OVERLAY'),
    transaction_type ENUM('BUY', 'SELL'),
    depth_price DECIMAL(15,8),
    prev_depth_price DECIMAL(15,8),
    prev_depth_price_flag BOOLEAN DEFAULT FALSE,
    depth_size INT,
    depth_order_priority BIGINT UNSIGNED,
    exchange_order_id VARCHAR(100),
    
    FOREIGN KEY (snapshot_id) REFERENCES depth_by_order_snapshots(id) ON DELETE CASCADE,
    INDEX idx_snapshot_level (snapshot_id, level_index),
    INDEX idx_price_side (depth_price, transaction_type),
    INDEX idx_exchange_order_id (exchange_order_id),
    INDEX idx_update_type (update_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- 5. HISTORICAL DATA TABLES
-- =============================================================================

-- Time bars table - OHLCV historical data
CREATE TABLE IF NOT EXISTS time_bars (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    bar_type ENUM('MINUTE', 'HOUR', 'DAILY', 'WEEKLY', 'MONTHLY') DEFAULT 'MINUTE',
    bar_interval INT DEFAULT 1,
    bar_timestamp TIMESTAMP NOT NULL,
    open_price DECIMAL(15,8),
    high_price DECIMAL(15,8),
    low_price DECIMAL(15,8),
    close_price DECIMAL(15,8),
    volume BIGINT UNSIGNED DEFAULT 0,
    num_trades INT DEFAULT 0,
    vwap DECIMAL(15,8),
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_symbol_bar (symbol, exchange, bar_type, bar_interval, bar_timestamp),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_bar_timestamp (bar_timestamp),
    INDEX idx_bar_type (bar_type),
    INDEX idx_volume (volume)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tick bars table - tick-based historical data
CREATE TABLE IF NOT EXISTS tick_bars (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    tick_count INT NOT NULL,
    bar_timestamp TIMESTAMP NOT NULL,
    open_price DECIMAL(15,8),
    high_price DECIMAL(15,8),
    low_price DECIMAL(15,8),
    close_price DECIMAL(15,8),
    volume BIGINT UNSIGNED DEFAULT 0,
    num_trades INT DEFAULT 0,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_symbol_tick_bar (symbol, exchange, tick_count, bar_timestamp),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_bar_timestamp (bar_timestamp),
    INDEX idx_tick_count (tick_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- 6. ACCOUNT AND ORDER MANAGEMENT TABLES
-- =============================================================================

-- Account list table - user account information
CREATE TABLE IF NOT EXISTS accounts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_id VARCHAR(50) NOT NULL UNIQUE,
    fcm_id VARCHAR(50),
    ib_id VARCHAR(50),
    user_id VARCHAR(100),
    account_name VARCHAR(100),
    account_type VARCHAR(50),
    currency VARCHAR(10),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_account_id (account_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Orders table - order information and status
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(100) NOT NULL,
    account_id VARCHAR(50) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    order_type ENUM('MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT') NOT NULL,
    side ENUM('BUY', 'SELL') NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(15,8),
    stop_price DECIMAL(15,8),
    filled_quantity INT DEFAULT 0,
    remaining_quantity INT,
    avg_fill_price DECIMAL(15,8),
    order_status ENUM('NEW', 'PARTIAL', 'FILLED', 'CANCELLED', 'REJECTED') DEFAULT 'NEW',
    time_in_force ENUM('DAY', 'GTC', 'IOC', 'FOK') DEFAULT 'DAY',
    order_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    order_data JSON,
    
    UNIQUE KEY unique_order_id (order_id),
    INDEX idx_account_id (account_id),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_order_status (order_status),
    INDEX idx_order_timestamp (order_timestamp),
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Positions table - account position tracking
CREATE TABLE IF NOT EXISTS positions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_id VARCHAR(50) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    position_type ENUM('LONG', 'SHORT', 'FLAT') DEFAULT 'FLAT',
    quantity INT DEFAULT 0,
    avg_price DECIMAL(15,8),
    market_value DECIMAL(15,2),
    unrealized_pnl DECIMAL(15,2),
    realized_pnl DECIMAL(15,2),
    position_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_account_symbol (account_id, symbol, exchange),
    INDEX idx_account_id (account_id),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_position_type (position_type),
    INDEX idx_last_update (last_update),
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- 7. SYSTEM MONITORING AND LOGGING TABLES
-- =============================================================================

-- Message log table - comprehensive message tracking
CREATE TABLE IF NOT EXISTS message_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    message_type ENUM('REQUEST', 'RESPONSE', 'UPDATE', 'NOTIFICATION') NOT NULL,
    direction ENUM('SENT', 'RECEIVED') NOT NULL,
    symbol VARCHAR(50),
    exchange VARCHAR(20),
    session_id VARCHAR(100),
    message_size INT,
    processing_time_ms DECIMAL(8,3),
    message_timestamp TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
    raw_message LONGBLOB,
    parsed_data JSON,
    
    INDEX idx_template_id (template_id),
    INDEX idx_message_type (message_type),
    INDEX idx_direction (direction),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_message_timestamp (message_timestamp),
    INDEX idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Error log table - system error tracking
CREATE TABLE IF NOT EXISTS error_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    error_code VARCHAR(20),
    error_message TEXT,
    template_id INT,
    symbol VARCHAR(50),
    exchange VARCHAR(20),
    session_id VARCHAR(100),
    error_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    context_data JSON,
    
    INDEX idx_error_code (error_code),
    INDEX idx_template_id (template_id),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_error_timestamp (error_timestamp),
    INDEX idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Performance metrics table - system performance tracking
CREATE TABLE IF NOT EXISTS performance_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4),
    metric_unit VARCHAR(20),
    symbol VARCHAR(50),
    exchange VARCHAR(20),
    measurement_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    additional_data JSON,
    
    INDEX idx_metric_name (metric_name),
    INDEX idx_symbol_exchange (symbol, exchange),
    INDEX idx_measurement_timestamp (measurement_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- 8. DATA PARTITIONING (Optional - for large datasets)
-- =============================================================================

-- Example partitioning for high-volume tables (uncomment if needed)
/*
-- Partition best_bid_offer by date (monthly partitions)
ALTER TABLE best_bid_offer 
PARTITION BY RANGE (YEAR(received_at) * 100 + MONTH(received_at)) (
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- Partition last_trades by date (monthly partitions)
ALTER TABLE last_trades 
PARTITION BY RANGE (YEAR(received_at) * 100 + MONTH(received_at)) (
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- =============================================================================
-- 9. SCHEMA VERSION TRACKING
-- =============================================================================

-- Schema version table - track database schema versions
CREATE TABLE IF NOT EXISTS schema_version (
    id INT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_version (version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial schema version
INSERT INTO schema_version (version, description) 
VALUES ('1.0.0', 'Initial Rithmic API database schema')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- =============================================================================
-- 10. STORED PROCEDURES AND FUNCTIONS (Optional)
-- =============================================================================

DELIMITER //

-- Function to calculate time since last heartbeat
CREATE FUNCTION IF NOT EXISTS time_since_last_heartbeat(user_id VARCHAR(100))
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE last_hb TIMESTAMP;
    DECLARE time_diff INT;
    
    SELECT last_heartbeat INTO last_hb 
    FROM user_sessions 
    WHERE unique_user_id = user_id AND is_active = TRUE 
    ORDER BY login_timestamp DESC 
    LIMIT 1;
    
    IF last_hb IS NULL THEN
        RETURN -1;
    END IF;
    
    SET time_diff = TIMESTAMPDIFF(SECOND, last_hb, NOW());
    RETURN time_diff;
END//

-- Procedure to clean old market data (older than specified days)
CREATE PROCEDURE IF NOT EXISTS clean_old_market_data(IN days_to_keep INT)
BEGIN
    DECLARE cutoff_date TIMESTAMP;
    SET cutoff_date = DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    DELETE FROM best_bid_offer WHERE received_at < cutoff_date;
    DELETE FROM last_trades WHERE received_at < cutoff_date;
    DELETE FROM depth_by_order_snapshots WHERE received_at < cutoff_date;
    DELETE FROM message_log WHERE message_timestamp < cutoff_date;
    
    SELECT ROW_COUNT() as rows_deleted;
END//

DELIMITER ;

-- =============================================================================
-- END OF SCHEMA
-- =============================================================================

-- Display completion message
SELECT 'Rithmic API Database Schema Created Successfully' AS status,
       COUNT(*) AS total_tables
FROM information_schema.tables 
WHERE table_schema = DATABASE();