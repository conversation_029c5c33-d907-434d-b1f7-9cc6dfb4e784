#!/usr/bin/env python3
"""
Database Models for Rithmic API

Provides data access objects (DAOs) and model classes for all database tables.
Each model provides CRUD operations and specialized methods for data persistence.
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, asdict
import json
from decimal import Decimal
import time
from threading import RLock

from .database_manager import get_database_manager

logger = logging.getLogger(__name__)


# =============================================================================
# BASE MODEL CLASS
# =============================================================================

class BaseModel:
    """Base class for all database models."""
    
    def __init__(self):
        self.db = get_database_manager()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary."""
        return {}
    
    def from_dict(self, data: Dict[str, Any]):
        """Populate model from dictionary."""
        pass


# =============================================================================
# STATIC REFERENCE DATA MODELS
# =============================================================================

@dataclass
class Symbol:
    """Symbol/Contract model for static reference data."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    symbol_name: str = ""
    product_code: str = ""
    instrument_type: str = ""
    expiration_date: Optional[date] = None
    strike_price: Optional[Decimal] = None
    put_call_indicator: Optional[str] = None
    currency: str = ""
    lot_size: Optional[int] = None
    tick_size: Optional[Decimal] = None
    tick_value: Optional[Decimal] = None
    margin_rate: Optional[Decimal] = None
    template_id: int = 110
    raw_response: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


# ============================================================================= 
# CACHING INFRASTRUCTURE
# =============================================================================

class SymbolCache:
    """Thread-safe LRU cache for symbol data."""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        """
        Initialize symbol cache.
        
        Args:
            max_size: Maximum number of symbols to cache
            ttl_seconds: Time-to-live for cached entries in seconds
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
        self._lock = RLock()
        
    def _make_key(self, symbol: str, exchange: str) -> str:
        """Create cache key from symbol and exchange."""
        return f"{symbol}:{exchange}"
    
    def _is_expired(self, timestamp: float) -> bool:
        """Check if timestamp is expired."""
        return time.time() - timestamp > self.ttl_seconds
    
    def _evict_if_needed(self):
        """Evict expired and least recently used entries."""
        current_time = time.time()
        
        # Remove expired entries
        expired_keys = []
        for key, access_time in self._access_times.items():
            if self._is_expired(access_time):
                expired_keys.append(key)
        
        for key in expired_keys:
            self._cache.pop(key, None)
            self._access_times.pop(key, None)
        
        # Remove LRU entries if over max size
        while len(self._cache) >= self.max_size:
            # Find least recently used
            lru_key = min(self._access_times.items(), key=lambda x: x[1])[0]
            self._cache.pop(lru_key, None)
            self._access_times.pop(lru_key, None)
    
    def get(self, symbol: str, exchange: str) -> Optional[Symbol]:
        """Get symbol from cache."""
        with self._lock:
            key = self._make_key(symbol, exchange)
            
            if key not in self._cache:
                return None
            
            if self._is_expired(self._access_times[key]):
                self._cache.pop(key, None)
                self._access_times.pop(key, None)
                return None
            
            # Update access time
            self._access_times[key] = time.time()
            
            # Return symbol object
            cached_data = self._cache[key]
            return Symbol(**cached_data)
    
    def put(self, symbol: Symbol):
        """Store symbol in cache."""
        with self._lock:
            if not symbol.symbol or not symbol.exchange:
                return
            
            key = self._make_key(symbol.symbol, symbol.exchange)
            current_time = time.time()
            
            # Evict if needed before adding
            self._evict_if_needed()
            
            # Store symbol data
            self._cache[key] = symbol.to_dict()
            self._access_times[key] = current_time
    
    def invalidate(self, symbol: str, exchange: str):
        """Remove specific symbol from cache."""
        with self._lock:
            key = self._make_key(symbol, exchange)
            self._cache.pop(key, None)
            self._access_times.pop(key, None)
    
    def clear(self):
        """Clear all cached entries."""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            current_time = time.time()
            active_entries = sum(1 for t in self._access_times.values() 
                               if not self._is_expired(t))
            
            return {
                'total_entries': len(self._cache),
                'active_entries': active_entries,
                'max_size': self.max_size,
                'ttl_seconds': self.ttl_seconds,
                'cache_hit_ratio': getattr(self, '_hit_ratio', 0.0)
            }


class SymbolDAO(BaseModel):
    """Data access object for symbols table with caching support."""
    
    def __init__(self):
        super().__init__()
        self.cache = SymbolCache()
        self._cache_hits = 0
        self._cache_misses = 0
    
    def create_table(self) -> bool:
        """Create the symbols table if it doesn't exist."""
        sql = """
        CREATE TABLE IF NOT EXISTS symbols (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            symbol_name VARCHAR(255),
            product_code VARCHAR(20),
            instrument_type VARCHAR(50),
            expiration_date DATE,
            strike_price DECIMAL(15,8),
            put_call_indicator VARCHAR(10) DEFAULT NULL,
            currency VARCHAR(10),
            lot_size INT,
            tick_size DECIMAL(15,8),
            tick_value DECIMAL(15,8),
            margin_rate DECIMAL(8,4),
            template_id INT DEFAULT 110,
            raw_response JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            UNIQUE KEY unique_symbol_exchange (symbol, exchange),
            INDEX idx_product_code (product_code),
            INDEX idx_instrument_type (instrument_type),
            INDEX idx_expiration_date (expiration_date),
            INDEX idx_exchange (exchange),
            INDEX idx_strike_price (strike_price),
            INDEX idx_product_exchange (product_code, exchange),
            INDEX idx_instrument_exchange (instrument_type, exchange),
            INDEX idx_expiration_product (expiration_date, product_code),
            INDEX idx_symbol_search (symbol_name),
            INDEX idx_updated_at (updated_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, symbol: Symbol) -> int:
        """Insert a new symbol record."""
        sql = """
        INSERT INTO symbols (
            symbol, exchange, symbol_name, product_code, instrument_type,
            expiration_date, strike_price, put_call_indicator, currency,
            lot_size, tick_size, tick_value, margin_rate, template_id, raw_response
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            symbol.symbol, symbol.exchange, symbol.symbol_name, symbol.product_code,
            symbol.instrument_type, symbol.expiration_date, symbol.strike_price,
            symbol.put_call_indicator, symbol.currency, symbol.lot_size,
            symbol.tick_size, symbol.tick_value, symbol.margin_rate,
            symbol.template_id, json.dumps(symbol.raw_response) if symbol.raw_response else None
        )
        return self.db.execute_insert(sql, params)
    
    def upsert(self, symbol: Symbol) -> int:
        """Insert or update symbol record with cache invalidation."""
        # Invalidate cache entry since we're updating
        self.cache.invalidate(symbol.symbol, symbol.exchange)
        
        sql = """
        INSERT INTO symbols (
            symbol, exchange, symbol_name, product_code, instrument_type,
            expiration_date, strike_price, put_call_indicator, currency,
            lot_size, tick_size, tick_value, margin_rate, template_id, raw_response
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            symbol_name = VALUES(symbol_name),
            product_code = VALUES(product_code),
            instrument_type = VALUES(instrument_type),
            expiration_date = VALUES(expiration_date),
            strike_price = VALUES(strike_price),
            put_call_indicator = VALUES(put_call_indicator),
            currency = VALUES(currency),
            lot_size = VALUES(lot_size),
            tick_size = VALUES(tick_size),
            tick_value = VALUES(tick_value),
            margin_rate = VALUES(margin_rate),
            raw_response = VALUES(raw_response),
            updated_at = CURRENT_TIMESTAMP
        """
        params = (
            symbol.symbol, symbol.exchange, symbol.symbol_name, symbol.product_code,
            symbol.instrument_type, symbol.expiration_date, symbol.strike_price,
            symbol.put_call_indicator, symbol.currency, symbol.lot_size,
            symbol.tick_size, symbol.tick_value, symbol.margin_rate,
            symbol.template_id, json.dumps(symbol.raw_response) if symbol.raw_response else None
        )
        result = self.db.execute_insert(sql, params)
        
        # Update cache with new data
        self.cache.put(symbol)
        return result
    
    def bulk_insert(self, symbols: List[Symbol]) -> int:
        """Bulk insert multiple symbols."""
        if not symbols:
            return 0
            
        sql = """
        INSERT IGNORE INTO symbols (
            symbol, exchange, symbol_name, product_code, instrument_type,
            expiration_date, strike_price, put_call_indicator, currency,
            lot_size, tick_size, tick_value, margin_rate, template_id, raw_response
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params_list = []
        for symbol in symbols:
            params = (
                symbol.symbol, symbol.exchange, symbol.symbol_name, symbol.product_code,
                symbol.instrument_type, symbol.expiration_date, symbol.strike_price,
                symbol.put_call_indicator, symbol.currency, symbol.lot_size,
                symbol.tick_size, symbol.tick_value, symbol.margin_rate,
                symbol.template_id, json.dumps(symbol.raw_response) if symbol.raw_response else None
            )
            params_list.append(params)
        
        return self.db.execute_many(sql, params_list)
    
    def find_by_symbol_exchange(self, symbol: str, exchange: str) -> Optional[Symbol]:
        """Find symbol by symbol name and exchange with caching."""
        # Check cache first
        cached_symbol = self.cache.get(symbol, exchange)
        if cached_symbol:
            self._cache_hits += 1
            return cached_symbol
        
        # Cache miss - query database
        self._cache_misses += 1
        sql = "SELECT * FROM symbols WHERE symbol = %s AND exchange = %s"
        results = self.db.execute_query(sql, (symbol, exchange), fetch_all=False)
        
        if results:
            symbol_obj = Symbol(**results[0])
            # Store in cache for future use
            self.cache.put(symbol_obj)
            return symbol_obj
        
        return None
    
    def find_by_product_code(self, product_code: str) -> List[Symbol]:
        """Find symbols by product code."""
        sql = "SELECT * FROM symbols WHERE product_code = %s ORDER BY expiration_date"
        results = self.db.execute_query(sql, (product_code,))
        return [Symbol(**row) for row in results]
    
    def find_by_exchange(self, exchange: str) -> List[Symbol]:
        """Find all symbols for an exchange."""
        sql = "SELECT * FROM symbols WHERE exchange = %s ORDER BY symbol"
        results = self.db.execute_query(sql, (exchange,))
        return [Symbol(**row) for row in results]
    
    def get_all_exchanges(self) -> List[str]:
        """Get list of all exchanges."""
        sql = "SELECT DISTINCT exchange FROM symbols ORDER BY exchange"
        results = self.db.execute_query(sql)
        return [row['exchange'] for row in results]
    
    def get_count(self) -> int:
        """Get total symbol count."""
        return self.db.get_table_row_count('symbols')
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = self._cache_hits + self._cache_misses
        hit_ratio = (self._cache_hits / total_requests * 100) if total_requests > 0 else 0.0
        
        cache_stats = self.cache.stats()
        cache_stats.update({
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'hit_ratio_percent': hit_ratio
        })
        
        return cache_stats
    
    def clear_cache(self):
        """Clear the symbol cache."""
        self.cache.clear()
        self._cache_hits = 0
        self._cache_misses = 0
    
    def warm_cache(self, limit: int = 100):
        """Pre-populate cache with frequently used symbols."""
        sql = """
        SELECT * FROM symbols 
        ORDER BY updated_at DESC 
        LIMIT %s
        """
        results = self.db.execute_query(sql, (limit,))
        
        for row in results:
            symbol = Symbol(**row)
            self.cache.put(symbol)
        
        logger.info(f"Warmed cache with {len(results)} symbols")


# =============================================================================
# MARKET DATA MODELS
# =============================================================================

@dataclass
class BestBidOffer:
    """Best bid/offer market data model."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    bid_price: Optional[Decimal] = None
    bid_size: Optional[int] = None
    bid_orders: Optional[int] = None
    bid_implicit_size: Optional[int] = None
    bid_time: str = ""
    ask_price: Optional[Decimal] = None
    ask_size: Optional[int] = None
    ask_orders: Optional[int] = None
    ask_implicit_size: Optional[int] = None
    ask_time: str = ""
    lean_price: Optional[Decimal] = None
    presence_bits: Optional[int] = None
    clear_bits: Optional[int] = None
    is_snapshot: bool = False
    ssboe: Optional[int] = None
    usecs: Optional[int] = None
    quote_timestamp: Optional[datetime] = None
    market_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class BestBidOfferDAO(BaseModel):
    """Data access object for best_bid_offer table."""
    
    def create_table(self) -> bool:
        """Create the best_bid_offer table."""
        sql = """
        CREATE TABLE IF NOT EXISTS best_bid_offer (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            bid_price DECIMAL(15,8),
            bid_size INT,
            bid_orders INT,
            bid_implicit_size INT,
            bid_time VARCHAR(50),
            ask_price DECIMAL(15,8),
            ask_size INT,
            ask_orders INT,
            ask_implicit_size INT,
            ask_time VARCHAR(50),
            lean_price DECIMAL(15,8),
            presence_bits INT UNSIGNED,
            clear_bits INT UNSIGNED,
            is_snapshot BOOLEAN DEFAULT FALSE,
            ssboe INT,
            usecs INT,
            quote_timestamp TIMESTAMP(6),
            market_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_market_timestamp (market_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_symbol_time (symbol, market_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: BestBidOffer) -> int:
        """Insert best bid offer data."""
        sql = """
        INSERT INTO best_bid_offer (
            symbol, exchange, bid_price, bid_size, bid_orders, bid_implicit_size, bid_time,
            ask_price, ask_size, ask_orders, ask_implicit_size, ask_time, lean_price,
            presence_bits, clear_bits, is_snapshot, ssboe, usecs, quote_timestamp, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.bid_price, data.bid_size, data.bid_orders,
            data.bid_implicit_size, data.bid_time, data.ask_price, data.ask_size,
            data.ask_orders, data.ask_implicit_size, data.ask_time, data.lean_price,
            data.presence_bits, data.clear_bits, data.is_snapshot, data.ssboe,
            data.usecs, data.quote_timestamp, data.market_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def bulk_insert(self, data_list: List[BestBidOffer]) -> int:
        """Bulk insert best bid offer data."""
        if not data_list:
            return 0
            
        sql = """
        INSERT IGNORE INTO best_bid_offer (
            symbol, exchange, bid_price, bid_size, bid_orders, bid_implicit_size, bid_time,
            ask_price, ask_size, ask_orders, ask_implicit_size, ask_time, lean_price,
            presence_bits, clear_bits, is_snapshot, ssboe, usecs, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params_list = []
        for data in data_list:
            params = (
                data.symbol, data.exchange, data.bid_price, data.bid_size, data.bid_orders,
                data.bid_implicit_size, data.bid_time, data.ask_price, data.ask_size,
                data.ask_orders, data.ask_implicit_size, data.ask_time, data.lean_price,
                data.presence_bits, data.clear_bits, data.is_snapshot, data.ssboe,
                data.usecs, data.market_timestamp
            )
            params_list.append(params)
        
        return self.db.execute_many(sql, params_list)
    
    def find_by_symbol(self, symbol: str) -> List[BestBidOffer]:
        """Find all quotes for a symbol."""
        sql = """
        SELECT * FROM best_bid_offer 
        WHERE symbol = %s 
        ORDER BY received_at DESC
        """
        results = self.db.execute_query(sql, (symbol,))
        return [BestBidOffer(**row) for row in results]
    
    def get_latest_quote(self, symbol: str, exchange: str) -> Optional[BestBidOffer]:
        """Get latest quote for symbol."""
        sql = """
        SELECT * FROM best_bid_offer 
        WHERE symbol = %s AND exchange = %s 
        ORDER BY received_at DESC LIMIT 1
        """
        results = self.db.execute_query(sql, (symbol, exchange), fetch_all=False)
        return BestBidOffer(**results[0]) if results else None


@dataclass 
class LastTrade:
    """Last trade market data model."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    trade_price: Optional[Decimal] = None
    trade_size: Optional[int] = None
    aggressor: Optional[str] = None
    exchange_order_id: str = ""
    aggressor_exchange_order_id: str = ""
    net_change: Optional[Decimal] = None
    percent_change: Optional[Decimal] = None
    volume: Optional[int] = None
    vwap: Optional[Decimal] = None
    presence_bits: Optional[int] = None
    clear_bits: Optional[int] = None
    is_snapshot: bool = False
    ssboe: Optional[int] = None
    usecs: Optional[int] = None
    source_ssboe: Optional[int] = None
    source_usecs: Optional[int] = None
    source_nsecs: Optional[int] = None
    jop_ssboe: Optional[int] = None
    jop_nsecs: Optional[int] = None
    trade_timestamp: Optional[datetime] = None
    market_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class LastTradeDAO(BaseModel):
    """Data access object for last_trades table."""
    
    def create_table(self) -> bool:
        """Create the last_trades table."""
        sql = """
        CREATE TABLE IF NOT EXISTS last_trades (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            trade_price DECIMAL(15,8),
            trade_size INT,
            aggressor VARCHAR(10) DEFAULT NULL,
            exchange_order_id VARCHAR(100),
            aggressor_exchange_order_id VARCHAR(100),
            net_change DECIMAL(15,8),
            percent_change DECIMAL(8,4),
            volume BIGINT UNSIGNED,
            vwap DECIMAL(15,8),
            presence_bits INT UNSIGNED,
            clear_bits INT UNSIGNED,
            is_snapshot BOOLEAN DEFAULT FALSE,
            ssboe INT,
            usecs INT,
            source_ssboe INT,
            source_usecs INT,
            source_nsecs INT,
            jop_ssboe INT,
            jop_nsecs INT,
            trade_timestamp TIMESTAMP(6),
            market_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_trade_timestamp (trade_timestamp),
            INDEX idx_market_timestamp (market_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_volume (volume),
            INDEX idx_trade_price (trade_price),
            INDEX idx_symbol_time (symbol, trade_timestamp),
            INDEX idx_symbol_market_time (symbol, market_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: LastTrade) -> int:
        """Insert last trade data."""
        sql = """
        INSERT INTO last_trades (
            symbol, exchange, trade_price, trade_size, aggressor, exchange_order_id,
            aggressor_exchange_order_id, net_change, percent_change, volume, vwap,
            presence_bits, clear_bits, is_snapshot, ssboe, usecs, source_ssboe,
            source_usecs, source_nsecs, jop_ssboe, jop_nsecs, trade_timestamp, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.trade_price, data.trade_size, data.aggressor,
            data.exchange_order_id, data.aggressor_exchange_order_id, data.net_change,
            data.percent_change, data.volume, data.vwap, data.presence_bits, data.clear_bits,
            data.is_snapshot, data.ssboe, data.usecs, data.source_ssboe, data.source_usecs,
            data.source_nsecs, data.jop_ssboe, data.jop_nsecs, data.trade_timestamp, data.market_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def bulk_insert(self, data_list: List[LastTrade]) -> int:
        """Bulk insert last trade data."""
        if not data_list:
            return 0
            
        sql = """
        INSERT IGNORE INTO last_trades (
            symbol, exchange, trade_price, trade_size, aggressor, exchange_order_id,
            aggressor_exchange_order_id, net_change, percent_change, volume, vwap,
            presence_bits, clear_bits, is_snapshot, ssboe, usecs, source_ssboe,
            source_usecs, source_nsecs, jop_ssboe, jop_nsecs, trade_timestamp, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params_list = []
        for data in data_list:
            params = (
                data.symbol, data.exchange, data.trade_price, data.trade_size, data.aggressor,
                data.exchange_order_id, data.aggressor_exchange_order_id, data.net_change,
                data.percent_change, data.volume, data.vwap, data.presence_bits, data.clear_bits,
                data.is_snapshot, data.ssboe, data.usecs, data.source_ssboe, data.source_usecs,
                data.source_nsecs, data.jop_ssboe, data.jop_nsecs, data.trade_timestamp, data.market_timestamp
            )
            params_list.append(params)
        
        return self.db.execute_many(sql, params_list)
    
    def find_by_symbol(self, symbol: str) -> List[LastTrade]:
        """Find all trades for a symbol."""
        sql = """
        SELECT * FROM last_trades 
        WHERE symbol = %s 
        ORDER BY received_at DESC
        """
        results = self.db.execute_query(sql, (symbol,))
        return [LastTrade(**row) for row in results]
    
    def get_latest_trade(self, symbol: str, exchange: str) -> Optional[LastTrade]:
        """Get latest trade for symbol."""
        sql = """
        SELECT * FROM last_trades 
        WHERE symbol = %s AND exchange = %s 
        ORDER BY received_at DESC LIMIT 1
        """
        results = self.db.execute_query(sql, (symbol, exchange), fetch_all=False)
        return LastTrade(**results[0]) if results else None


# =============================================================================
# USER SESSION MODELS  
# =============================================================================

@dataclass
class UserSession:
    """User session model."""
    id: Optional[int] = None
    unique_user_id: str = ""
    fcm_id: str = ""
    ib_id: str = ""
    country_code: str = ""
    state_code: str = ""
    template_version: str = ""
    heartbeat_interval: Optional[Decimal] = None
    login_timestamp: Optional[datetime] = None
    last_heartbeat: Optional[datetime] = None
    logout_timestamp: Optional[datetime] = None
    is_active: bool = True
    session_info: Optional[Dict[str, Any]] = None


class UserSessionDAO(BaseModel):
    """Data access object for user_sessions table."""
    
    def create_table(self) -> bool:
        """Create the user_sessions table."""
        sql = """
        CREATE TABLE IF NOT EXISTS user_sessions (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            unique_user_id VARCHAR(100) NOT NULL,
            fcm_id VARCHAR(50),
            ib_id VARCHAR(50),
            country_code VARCHAR(5),
            state_code VARCHAR(5),
            template_version VARCHAR(20),
            heartbeat_interval DECIMAL(8,3),
            login_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_heartbeat TIMESTAMP,
            logout_timestamp TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE,
            session_info JSON,
            
            INDEX idx_unique_user_id (unique_user_id),
            INDEX idx_login_timestamp (login_timestamp),
            INDEX idx_is_active (is_active),
            INDEX idx_last_heartbeat (last_heartbeat)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, session: UserSession) -> int:
        """Insert new session."""
        sql = """
        INSERT INTO user_sessions (
            unique_user_id, fcm_id, ib_id, country_code, state_code,
            template_version, heartbeat_interval, session_info
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            session.unique_user_id, session.fcm_id, session.ib_id, session.country_code,
            session.state_code, session.template_version, session.heartbeat_interval,
            json.dumps(session.session_info) if session.session_info else None
        )
        return self.db.execute_insert(sql, params)
    
    def update_heartbeat(self, user_id: str) -> int:
        """Update last heartbeat timestamp."""
        sql = """
        UPDATE user_sessions 
        SET last_heartbeat = CURRENT_TIMESTAMP 
        WHERE unique_user_id = %s AND is_active = TRUE
        """
        return self.db.execute_update(sql, (user_id,))


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def create_all_tables() -> bool:
    """Create all database tables."""
    try:
        # Initialize all DAOs
        symbol_dao = SymbolDAO()
        bbo_dao = BestBidOfferDAO()
        trade_dao = LastTradeDAO()
        session_dao = UserSessionDAO()
        
        # Level 3 Depth-by-Order DAOs
        depth_snapshot_dao = DepthByOrderSnapshotDAO()
        depth_update_dao = DepthByOrderUpdateDAO()
        order_book_dao = OrderBookLevelDAO()
        
        # Historical Data DAOs
        time_bar_dao = TimeBarDAO()
        tick_bar_dao = TickBarDAO()
        
        # Order Management DAOs
        order_notification_dao = OrderNotificationDAO()
        order_fill_dao = OrderFillDAO()
        
        # Position & Account DAOs
        position_dao = PositionDAO()
        account_info_dao = AccountInfoDAO()
        
        # System Events & Statistics DAOs
        system_event_dao = SystemEventDAO()
        trade_stats_dao = TradeStatisticsDAO()
        quote_stats_dao = QuoteStatisticsDAO()
        heartbeat_dao = HeartbeatDAO()
        
        # Create tables
        tables_created = []
        
        # Core tables
        if symbol_dao.create_table():
            tables_created.append('symbols')
        if bbo_dao.create_table():
            tables_created.append('best_bid_offer')
        if trade_dao.create_table():
            tables_created.append('last_trades')
        if session_dao.create_table():
            tables_created.append('user_sessions')
        
        # Level 3 Depth-by-Order tables
        if depth_snapshot_dao.create_table():
            tables_created.append('depth_by_order_snapshot')
        if depth_update_dao.create_table():
            tables_created.append('depth_by_order_updates')
        if order_book_dao.create_table():
            tables_created.append('order_book_levels')
        
        # Historical data tables
        if time_bar_dao.create_table():
            tables_created.append('time_bars')
        if tick_bar_dao.create_table():
            tables_created.append('tick_bars')
        
        # Order management tables
        if order_notification_dao.create_table():
            tables_created.append('order_notifications')
        if order_fill_dao.create_table():
            tables_created.append('order_fills')
        
        # Position & account tables
        if position_dao.create_table():
            tables_created.append('positions')
        if account_info_dao.create_table():
            tables_created.append('account_info')
        
        # System events & statistics tables
        if system_event_dao.create_table():
            tables_created.append('system_events')
        if trade_stats_dao.create_table():
            tables_created.append('trade_statistics')
        if quote_stats_dao.create_table():
            tables_created.append('quote_statistics')
        if heartbeat_dao.create_table():
            tables_created.append('heartbeats')
        
        logger.info(f"Created tables: {', '.join(tables_created)}")
        return len(tables_created) > 0
        
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        return False


def get_database_stats() -> Dict[str, Any]:
    """Get database statistics."""
    try:
        db = get_database_manager()
        
        stats = {
            'connection_info': db.get_connection_info(),
            'table_counts': {},
            'total_records': 0
        }
        
        # Get row counts for main tables
        main_tables = [
            'symbols', 'best_bid_offer', 'last_trades', 'user_sessions',
            'depth_by_order_snapshot', 'depth_by_order_updates', 'order_book_levels',
            'time_bars', 'tick_bars', 'order_notifications', 'order_fills',
            'positions', 'account_info', 'system_events', 'trade_statistics',
            'quote_statistics', 'heartbeats'
        ]
        for table in main_tables:
            if db.table_exists(table):
                count = db.get_table_row_count(table)
                stats['table_counts'][table] = count
                stats['total_records'] += count
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting database stats: {e}")
        return {}


# =============================================================================
# PROTOCOL BUFFER TO MODEL CONVERTERS
# =============================================================================

def protobuf_to_symbol(pb_response: Any, exchange: str = "") -> Symbol:
    """Convert protobuf response to Symbol model."""
    try:
        symbol = Symbol()
        
        # Extract fields from protobuf response
        if hasattr(pb_response, 'symbol'):
            symbol.symbol = pb_response.symbol or ""
        if hasattr(pb_response, 'exchange'):
            symbol.exchange = pb_response.exchange or exchange
        if hasattr(pb_response, 'symbol_name'):
            symbol.symbol_name = pb_response.symbol_name or ""
        if hasattr(pb_response, 'product_code'):
            symbol.product_code = pb_response.product_code or ""
        if hasattr(pb_response, 'instrument_type'):
            symbol.instrument_type = pb_response.instrument_type or ""
        
        # Handle optional fields
        if hasattr(pb_response, 'expiration_date') and pb_response.expiration_date:
            try:
                # Convert string date to date object if needed
                if isinstance(pb_response.expiration_date, str):
                    symbol.expiration_date = datetime.strptime(pb_response.expiration_date, '%Y%m%d').date()
                else:
                    symbol.expiration_date = pb_response.expiration_date
            except:
                pass
        
        # Store raw response for debugging
        symbol.raw_response = {
            'template_id': getattr(pb_response, 'template_id', None),
            'original_data': str(pb_response)
        }
        
        return symbol
        
    except Exception as e:
        logger.error(f"Error converting protobuf to symbol: {e}")
        return Symbol()


def protobuf_to_best_bid_offer(pb_response: Any) -> BestBidOffer:
    """Convert protobuf response to BestBidOffer model."""
    try:
        bbo = BestBidOffer()
        
        # Extract basic fields
        if hasattr(pb_response, 'symbol'):
            bbo.symbol = pb_response.symbol or ""
        if hasattr(pb_response, 'exchange'):
            bbo.exchange = pb_response.exchange or ""
            
        # Extract bid data
        if hasattr(pb_response, 'bid_price'):
            bbo.bid_price = Decimal(str(pb_response.bid_price)) if pb_response.bid_price else None
        if hasattr(pb_response, 'bid_size'):
            bbo.bid_size = pb_response.bid_size
        if hasattr(pb_response, 'bid_orders'):
            bbo.bid_orders = pb_response.bid_orders
        
        # Extract ask data
        if hasattr(pb_response, 'ask_price'):
            bbo.ask_price = Decimal(str(pb_response.ask_price)) if pb_response.ask_price else None
        if hasattr(pb_response, 'ask_size'):
            bbo.ask_size = pb_response.ask_size
        if hasattr(pb_response, 'ask_orders'):
            bbo.ask_orders = pb_response.ask_orders
        
        # Extract timing data
        if hasattr(pb_response, 'ssboe'):
            bbo.ssboe = pb_response.ssboe
        if hasattr(pb_response, 'usecs'):
            bbo.usecs = pb_response.usecs
            
        # Calculate market timestamp if possible
        if bbo.ssboe and bbo.usecs:
            try:
                timestamp_seconds = bbo.ssboe + (bbo.usecs / 1_000_000)
                bbo.market_timestamp = datetime.fromtimestamp(timestamp_seconds)
            except:
                pass
        
        return bbo
        
    except Exception as e:
        logger.error(f"Error converting protobuf to BestBidOffer: {e}")
        return BestBidOffer()


# =============================================================================
# LEVEL 3 DEPTH-BY-ORDER MODELS
# =============================================================================

@dataclass
class DepthByOrderSnapshot:
    """Level 3 depth-by-order snapshot data model (Templates 115/116)."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    sequence_number: Optional[int] = None
    depth_side: str = ""  # BUY, SELL
    depth_price: Optional[Decimal] = None
    depth_size: Optional[int] = None
    depth_order_priority: Optional[int] = None
    exchange_order_id: str = ""
    template_id: int = 116
    is_snapshot: bool = True
    ssboe: Optional[int] = None
    usecs: Optional[int] = None
    market_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class DepthByOrderSnapshotDAO(BaseModel):
    """Data access object for depth_by_order_snapshot table."""
    
    def create_table(self) -> bool:
        """Create the depth_by_order_snapshot table."""
        sql = """
        CREATE TABLE IF NOT EXISTS depth_by_order_snapshot (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            sequence_number BIGINT UNSIGNED,
            depth_side VARCHAR(10) DEFAULT NULL,
            depth_price DECIMAL(15,8),
            depth_size INT,
            depth_order_priority BIGINT UNSIGNED,
            exchange_order_id VARCHAR(100),
            template_id INT DEFAULT 116,
            is_snapshot BOOLEAN DEFAULT TRUE,
            ssboe INT,
            usecs INT,
            market_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_sequence_number (sequence_number),
            INDEX idx_market_timestamp (market_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_symbol_side_price (symbol, depth_side, depth_price),
            INDEX idx_exchange_order_id (exchange_order_id),
            INDEX idx_order_priority (depth_order_priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: DepthByOrderSnapshot) -> int:
        """Insert depth-by-order snapshot data."""
        sql = """
        INSERT INTO depth_by_order_snapshot (
            symbol, exchange, sequence_number, depth_side, depth_price, depth_size,
            depth_order_priority, exchange_order_id, template_id, is_snapshot,
            ssboe, usecs, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.sequence_number, data.depth_side,
            data.depth_price, data.depth_size, data.depth_order_priority,
            data.exchange_order_id, data.template_id, data.is_snapshot,
            data.ssboe, data.usecs, data.market_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def bulk_insert(self, data_list: List[DepthByOrderSnapshot]) -> int:
        """Bulk insert depth-by-order snapshot data."""
        if not data_list:
            return 0
            
        sql = """
        INSERT IGNORE INTO depth_by_order_snapshot (
            symbol, exchange, sequence_number, depth_side, depth_price, depth_size,
            depth_order_priority, exchange_order_id, template_id, is_snapshot,
            ssboe, usecs, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params_list = []
        for data in data_list:
            params = (
                data.symbol, data.exchange, data.sequence_number, data.depth_side,
                data.depth_price, data.depth_size, data.depth_order_priority,
                data.exchange_order_id, data.template_id, data.is_snapshot,
                data.ssboe, data.usecs, data.market_timestamp
            )
            params_list.append(params)
        
        return self.db.execute_many(sql, params_list)
    
    def find_by_symbol_and_side(self, symbol: str, exchange: str, side: str) -> List[DepthByOrderSnapshot]:
        """Find depth data by symbol and side."""
        sql = """
        SELECT * FROM depth_by_order_snapshot 
        WHERE symbol = %s AND exchange = %s AND depth_side = %s
        ORDER BY depth_price DESC, depth_order_priority ASC
        """
        results = self.db.execute_query(sql, (symbol, exchange, side))
        return [DepthByOrderSnapshot(**row) for row in results]
    
    def get_latest_snapshot(self, symbol: str, exchange: str) -> List[DepthByOrderSnapshot]:
        """Get latest complete snapshot for symbol."""
        sql = """
        SELECT * FROM depth_by_order_snapshot 
        WHERE symbol = %s AND exchange = %s
        AND received_at = (
            SELECT MAX(received_at) FROM depth_by_order_snapshot 
            WHERE symbol = %s AND exchange = %s AND is_snapshot = TRUE
        )
        ORDER BY depth_side, depth_price DESC, depth_order_priority ASC
        """
        results = self.db.execute_query(sql, (symbol, exchange, symbol, exchange))
        return [DepthByOrderSnapshot(**row) for row in results]


@dataclass
class DepthByOrderUpdate:
    """Level 3 depth-by-order update data model (Templates 117/118)."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    sequence_number: Optional[int] = None
    depth_side: str = ""  # BUY, SELL
    depth_price: Optional[Decimal] = None
    depth_size: Optional[int] = None
    depth_order_priority: Optional[int] = None
    exchange_order_id: str = ""
    update_type: str = ""  # ADD, MODIFY, DELETE
    template_id: int = 118
    is_snapshot: bool = False
    ssboe: Optional[int] = None
    usecs: Optional[int] = None
    market_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class DepthByOrderUpdateDAO(BaseModel):
    """Data access object for depth_by_order_updates table."""
    
    def create_table(self) -> bool:
        """Create the depth_by_order_updates table."""
        sql = """
        CREATE TABLE IF NOT EXISTS depth_by_order_updates (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            sequence_number BIGINT UNSIGNED,
            depth_side VARCHAR(10) DEFAULT NULL,
            depth_price DECIMAL(15,8),
            depth_size INT,
            depth_order_priority BIGINT UNSIGNED,
            exchange_order_id VARCHAR(100),
            update_type VARCHAR(20) DEFAULT NULL,
            template_id INT DEFAULT 118,
            is_snapshot BOOLEAN DEFAULT FALSE,
            ssboe INT,
            usecs INT,
            market_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_sequence_number (sequence_number),
            INDEX idx_market_timestamp (market_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_symbol_side_price (symbol, depth_side, depth_price),
            INDEX idx_exchange_order_id (exchange_order_id),
            INDEX idx_update_type (update_type),
            INDEX idx_order_priority (depth_order_priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: DepthByOrderUpdate) -> int:
        """Insert depth-by-order update data."""
        sql = """
        INSERT INTO depth_by_order_updates (
            symbol, exchange, sequence_number, depth_side, depth_price, depth_size,
            depth_order_priority, exchange_order_id, update_type, template_id,
            is_snapshot, ssboe, usecs, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.sequence_number, data.depth_side,
            data.depth_price, data.depth_size, data.depth_order_priority,
            data.exchange_order_id, data.update_type, data.template_id,
            data.is_snapshot, data.ssboe, data.usecs, data.market_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def bulk_insert(self, data_list: List[DepthByOrderUpdate]) -> int:
        """Bulk insert depth-by-order update data."""
        if not data_list:
            return 0
            
        sql = """
        INSERT IGNORE INTO depth_by_order_updates (
            symbol, exchange, sequence_number, depth_side, depth_price, depth_size,
            depth_order_priority, exchange_order_id, update_type, template_id,
            is_snapshot, ssboe, usecs, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params_list = []
        for data in data_list:
            params = (
                data.symbol, data.exchange, data.sequence_number, data.depth_side,
                data.depth_price, data.depth_size, data.depth_order_priority,
                data.exchange_order_id, data.update_type, data.template_id,
                data.is_snapshot, data.ssboe, data.usecs, data.market_timestamp
            )
            params_list.append(params)
        
        return self.db.execute_many(sql, params_list)
    
    def find_by_symbol_timerange(self, symbol: str, exchange: str, 
                                start_time: datetime, end_time: datetime) -> List[DepthByOrderUpdate]:
        """Find updates within time range."""
        sql = """
        SELECT * FROM depth_by_order_updates 
        WHERE symbol = %s AND exchange = %s 
        AND market_timestamp BETWEEN %s AND %s
        ORDER BY sequence_number ASC, market_timestamp ASC
        """
        results = self.db.execute_query(sql, (symbol, exchange, start_time, end_time))
        return [DepthByOrderUpdate(**row) for row in results]


@dataclass
class OrderBookLevel:
    """Aggregated order book level data (Level 2)."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    side: str = ""  # BUY, SELL
    price_level: Optional[Decimal] = None
    total_size: Optional[int] = None
    order_count: Optional[int] = None
    level_position: Optional[int] = None  # 0=best, 1=second best, etc.
    ssboe: Optional[int] = None
    usecs: Optional[int] = None
    market_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class OrderBookLevelDAO(BaseModel):
    """Data access object for order_book_levels table."""
    
    def create_table(self) -> bool:
        """Create the order_book_levels table."""
        sql = """
        CREATE TABLE IF NOT EXISTS order_book_levels (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            side VARCHAR(10) DEFAULT NULL,
            price_level DECIMAL(15,8) NOT NULL,
            total_size INT DEFAULT 0,
            order_count INT DEFAULT 0,
            level_position TINYINT DEFAULT 0,
            ssboe INT,
            usecs INT,
            market_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            UNIQUE KEY unique_symbol_side_price (symbol, exchange, side, price_level, market_timestamp),
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_market_timestamp (market_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_symbol_side_level (symbol, side, level_position),
            INDEX idx_price_level (price_level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: OrderBookLevel) -> int:
        """Insert order book level."""
        sql = """
        INSERT INTO order_book_levels (
            symbol, exchange, side, price_level, total_size, order_count,
            level_position, ssboe, usecs, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.side, data.price_level,
            data.total_size, data.order_count, data.level_position,
            data.ssboe, data.usecs, data.market_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def upsert_level(self, data: OrderBookLevel) -> int:
        """Insert or update order book level."""
        sql = """
        INSERT INTO order_book_levels (
            symbol, exchange, side, price_level, total_size, order_count,
            level_position, ssboe, usecs, market_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            total_size = VALUES(total_size),
            order_count = VALUES(order_count),
            level_position = VALUES(level_position),
            received_at = CURRENT_TIMESTAMP(6)
        """
        params = (
            data.symbol, data.exchange, data.side, data.price_level,
            data.total_size, data.order_count, data.level_position,
            data.ssboe, data.usecs, data.market_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def get_current_book(self, symbol: str, exchange: str, levels: int = 10) -> Dict[str, List[OrderBookLevel]]:
        """Get current order book with specified depth."""
        # Get buy side (descending price)
        buy_sql = """
        SELECT * FROM order_book_levels 
        WHERE symbol = %s AND exchange = %s AND side = 'BUY'
        ORDER BY price_level DESC 
        LIMIT %s
        """
        buy_results = self.db.execute_query(buy_sql, (symbol, exchange, levels))
        
        # Get sell side (ascending price)
        sell_sql = """
        SELECT * FROM order_book_levels 
        WHERE symbol = %s AND exchange = %s AND side = 'SELL'
        ORDER BY price_level ASC 
        LIMIT %s
        """
        sell_results = self.db.execute_query(sell_sql, (symbol, exchange, levels))
        
        return {
            'bids': [OrderBookLevel(**row) for row in buy_results],
            'asks': [OrderBookLevel(**row) for row in sell_results]
        }


# =============================================================================
# HISTORICAL DATA MODELS
# =============================================================================

@dataclass
class TimeBar:
    """Time-based OHLCV bar data model."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    bar_type: str = ""  # SECOND_BAR, MINUTE_BAR, DAILY_BAR, WEEKLY_BAR
    period: str = ""  # "1", "5", "15", etc.
    marker: Optional[int] = None
    num_trades: Optional[int] = None
    volume: Optional[int] = None
    bid_volume: Optional[int] = None
    ask_volume: Optional[int] = None
    open_price: Optional[Decimal] = None
    close_price: Optional[Decimal] = None
    high_price: Optional[Decimal] = None
    low_price: Optional[Decimal] = None
    settlement_price: Optional[Decimal] = None
    has_settlement_price: bool = False
    must_clear_settlement_price: bool = False
    template_id: int = 200
    bar_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class TimeBarDAO(BaseModel):
    """Data access object for time_bars table."""
    
    def create_table(self) -> bool:
        """Create the time_bars table."""
        sql = """
        CREATE TABLE IF NOT EXISTS time_bars (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            bar_type VARCHAR(20) DEFAULT NULL,
            period VARCHAR(10) NOT NULL,
            marker INT,
            num_trades BIGINT UNSIGNED,
            volume BIGINT UNSIGNED,
            bid_volume BIGINT UNSIGNED,
            ask_volume BIGINT UNSIGNED,
            open_price DECIMAL(15,8),
            close_price DECIMAL(15,8),
            high_price DECIMAL(15,8),
            low_price DECIMAL(15,8),
            settlement_price DECIMAL(15,8),
            has_settlement_price BOOLEAN DEFAULT FALSE,
            must_clear_settlement_price BOOLEAN DEFAULT FALSE,
            template_id INT DEFAULT 200,
            bar_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            UNIQUE KEY unique_symbol_bar (symbol, exchange, bar_type, period, bar_timestamp),
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_bar_timestamp (bar_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_bar_type_period (bar_type, period),
            INDEX idx_volume (volume),
            INDEX idx_symbol_time_range (symbol, bar_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: TimeBar) -> int:
        """Insert time bar data."""
        sql = """
        INSERT INTO time_bars (
            symbol, exchange, bar_type, period, marker, num_trades, volume,
            bid_volume, ask_volume, open_price, close_price, high_price, low_price,
            settlement_price, has_settlement_price, must_clear_settlement_price,
            template_id, bar_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.bar_type, data.period, data.marker,
            data.num_trades, data.volume, data.bid_volume, data.ask_volume,
            data.open_price, data.close_price, data.high_price, data.low_price,
            data.settlement_price, data.has_settlement_price, data.must_clear_settlement_price,
            data.template_id, data.bar_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def upsert(self, data: TimeBar) -> int:
        """Insert or update time bar data."""
        sql = """
        INSERT INTO time_bars (
            symbol, exchange, bar_type, period, marker, num_trades, volume,
            bid_volume, ask_volume, open_price, close_price, high_price, low_price,
            settlement_price, has_settlement_price, must_clear_settlement_price,
            template_id, bar_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            num_trades = VALUES(num_trades),
            volume = VALUES(volume),
            bid_volume = VALUES(bid_volume),
            ask_volume = VALUES(ask_volume),
            close_price = VALUES(close_price),
            high_price = GREATEST(high_price, VALUES(high_price)),
            low_price = LEAST(low_price, VALUES(low_price)),
            settlement_price = VALUES(settlement_price),
            has_settlement_price = VALUES(has_settlement_price),
            received_at = CURRENT_TIMESTAMP(6)
        """
        params = (
            data.symbol, data.exchange, data.bar_type, data.period, data.marker,
            data.num_trades, data.volume, data.bid_volume, data.ask_volume,
            data.open_price, data.close_price, data.high_price, data.low_price,
            data.settlement_price, data.has_settlement_price, data.must_clear_settlement_price,
            data.template_id, data.bar_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def find_bars_in_range(self, symbol: str, exchange: str, bar_type: str, period: str,
                          start_time: datetime, end_time: datetime) -> List[TimeBar]:
        """Find time bars within date range."""
        sql = """
        SELECT * FROM time_bars 
        WHERE symbol = %s AND exchange = %s AND bar_type = %s AND period = %s
        AND bar_timestamp BETWEEN %s AND %s
        ORDER BY bar_timestamp ASC
        """
        results = self.db.execute_query(sql, (symbol, exchange, bar_type, period, start_time, end_time))
        return [TimeBar(**row) for row in results]


@dataclass
class TickBar:
    """Tick-based OHLCV bar data model."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    bar_type: str = ""  # TICK_BAR, RANGE_BAR, VOLUME_BAR
    bar_sub_type: str = ""  # REGULAR, CUSTOM
    type_specifier: str = ""
    num_trades: Optional[int] = None
    volume: Optional[int] = None
    bid_volume: Optional[int] = None
    ask_volume: Optional[int] = None
    open_price: Optional[Decimal] = None
    close_price: Optional[Decimal] = None
    high_price: Optional[Decimal] = None
    low_price: Optional[Decimal] = None
    custom_session_open_ssm: Optional[int] = None
    data_bar_ssboe: Optional[int] = None
    data_bar_usecs: Optional[int] = None
    template_id: int = 204
    bar_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class TickBarDAO(BaseModel):
    """Data access object for tick_bars table."""
    
    def create_table(self) -> bool:
        """Create the tick_bars table."""
        sql = """
        CREATE TABLE IF NOT EXISTS tick_bars (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            bar_type VARCHAR(20) DEFAULT NULL,
            bar_sub_type VARCHAR(20) DEFAULT NULL,
            type_specifier VARCHAR(100),
            num_trades BIGINT UNSIGNED,
            volume BIGINT UNSIGNED,
            bid_volume BIGINT UNSIGNED,
            ask_volume BIGINT UNSIGNED,
            open_price DECIMAL(15,8),
            close_price DECIMAL(15,8),
            high_price DECIMAL(15,8),
            low_price DECIMAL(15,8),
            custom_session_open_ssm INT,
            data_bar_ssboe INT,
            data_bar_usecs INT,
            template_id INT DEFAULT 204,
            bar_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_bar_timestamp (bar_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_bar_type (bar_type),
            INDEX idx_volume (volume),
            INDEX idx_symbol_time_range (symbol, bar_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: TickBar) -> int:
        """Insert tick bar data."""
        sql = """
        INSERT INTO tick_bars (
            symbol, exchange, bar_type, bar_sub_type, type_specifier, num_trades,
            volume, bid_volume, ask_volume, open_price, close_price, high_price,
            low_price, custom_session_open_ssm, data_bar_ssboe, data_bar_usecs,
            template_id, bar_timestamp
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.bar_type, data.bar_sub_type,
            data.type_specifier, data.num_trades, data.volume, data.bid_volume,
            data.ask_volume, data.open_price, data.close_price, data.high_price,
            data.low_price, data.custom_session_open_ssm, data.data_bar_ssboe,
            data.data_bar_usecs, data.template_id, data.bar_timestamp
        )
        return self.db.execute_insert(sql, params)


# =============================================================================
# ORDER MANAGEMENT MODELS
# =============================================================================

@dataclass
class OrderNotification:
    """Order notification and lifecycle tracking model."""
    id: Optional[int] = None
    user_tag: str = ""
    notify_type: str = ""  # ORDER_RCVD_FROM_CLNT, OPEN, COMPLETE, etc.
    is_snapshot: bool = False
    status: str = ""
    basket_id: str = ""
    original_basket_id: str = ""
    linked_basket_ids: str = ""
    
    # Account information
    fcm_id: str = ""
    ib_id: str = ""
    user_id: str = ""
    account_id: str = ""
    
    # Instrument details
    symbol: str = ""
    exchange: str = ""
    trade_exchange: str = ""
    trade_route: str = ""
    exchange_order_id: str = ""
    instrument_type: str = ""
    completion_reason: str = ""
    
    # Order details
    quantity: Optional[int] = None
    quan_release_pending: Optional[int] = None
    price: Optional[Decimal] = None
    trigger_price: Optional[Decimal] = None
    transaction_type: str = ""  # BUY, SELL, SS
    duration: str = ""  # DAY, GTC, IOC, FOK
    price_type: str = ""  # LIMIT, MARKET, STOP_LIMIT, STOP_MARKET
    orig_price_type: str = ""
    manual_or_auto: str = ""  # MANUAL, AUTO
    bracket_type: str = ""
    
    # Fill information
    avg_fill_price: Optional[Decimal] = None
    total_fill_size: Optional[int] = None
    total_unfilled_size: Optional[int] = None
    
    # Trailing stops
    trail_by_ticks: Optional[int] = None
    trail_by_price_id: Optional[int] = None
    
    # Sequence numbers
    sequence_number: str = ""
    orig_sequence_number: str = ""
    cor_sequence_number: str = ""
    
    # Location and metadata
    currency: str = ""
    country_code: str = ""
    text: str = ""
    report_text: str = ""
    remarks: str = ""
    window_name: str = ""
    originator_window_name: str = ""
    
    # Timing
    cancel_at_ssboe: Optional[int] = None
    cancel_at_usecs: Optional[int] = None
    cancel_after_secs: Optional[int] = None
    ssboe: Optional[int] = None
    usecs: Optional[int] = None
    
    # System fields
    template_id: int = 0
    order_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None


class OrderNotificationDAO(BaseModel):
    """Data access object for order_notifications table."""
    
    def create_table(self) -> bool:
        """Create the order_notifications table."""
        sql = """
        CREATE TABLE IF NOT EXISTS order_notifications (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            user_tag VARCHAR(100),
            notify_type VARCHAR(100) DEFAULT NULL,
            is_snapshot BOOLEAN DEFAULT FALSE,
            status VARCHAR(50),
            basket_id VARCHAR(100),
            original_basket_id VARCHAR(100),
            linked_basket_ids TEXT,
            
            fcm_id VARCHAR(50),
            ib_id VARCHAR(50),
            user_id VARCHAR(100),
            account_id VARCHAR(100),
            
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            trade_exchange VARCHAR(20),
            trade_route VARCHAR(50),
            exchange_order_id VARCHAR(100),
            instrument_type VARCHAR(50),
            completion_reason VARCHAR(200),
            
            quantity INT,
            quan_release_pending INT,
            price DECIMAL(15,8),
            trigger_price DECIMAL(15,8),
            transaction_type VARCHAR(20) DEFAULT NULL,
            duration VARCHAR(20) DEFAULT NULL,
            price_type VARCHAR(30) DEFAULT NULL,
            orig_price_type VARCHAR(30) DEFAULT NULL,
            manual_or_auto VARCHAR(20) DEFAULT NULL,
            bracket_type VARCHAR(50) DEFAULT NULL,
            
            avg_fill_price DECIMAL(15,8),
            total_fill_size INT,
            total_unfilled_size INT,
            
            trail_by_ticks INT,
            trail_by_price_id INT,
            
            sequence_number VARCHAR(100),
            orig_sequence_number VARCHAR(100),
            cor_sequence_number VARCHAR(100),
            
            currency VARCHAR(10),
            country_code VARCHAR(5),
            text TEXT,
            report_text TEXT,
            remarks TEXT,
            window_name VARCHAR(100),
            originator_window_name VARCHAR(100),
            
            cancel_at_ssboe INT,
            cancel_at_usecs INT,
            cancel_after_secs INT,
            ssboe INT,
            usecs INT,
            
            template_id INT,
            order_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            INDEX idx_basket_id (basket_id),
            INDEX idx_exchange_order_id (exchange_order_id),
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_account_id (account_id),
            INDEX idx_user_id (user_id),
            INDEX idx_notify_type (notify_type),
            INDEX idx_status (status),
            INDEX idx_order_timestamp (order_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_sequence_number (sequence_number),
            INDEX idx_completion_reason (completion_reason)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def _convert_enum_field(self, value: str) -> Optional[str]:
        """Convert empty string enum fields to None for proper NULL insertion."""
        return None if value == "" else value
    
    def insert(self, data: OrderNotification) -> int:
        """Insert order notification."""
        sql = """
        INSERT INTO order_notifications (
            user_tag, notify_type, is_snapshot, status, basket_id, original_basket_id,
            linked_basket_ids, fcm_id, ib_id, user_id, account_id, symbol, exchange,
            trade_exchange, trade_route, exchange_order_id, instrument_type, completion_reason,
            quantity, quan_release_pending, price, trigger_price, transaction_type, duration,
            price_type, orig_price_type, manual_or_auto, bracket_type, avg_fill_price,
            total_fill_size, total_unfilled_size, trail_by_ticks, trail_by_price_id,
            sequence_number, orig_sequence_number, cor_sequence_number, currency, country_code,
            text, report_text, remarks, window_name, originator_window_name, cancel_at_ssboe,
            cancel_at_usecs, cancel_after_secs, ssboe, usecs, template_id, order_timestamp
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """
        params = (
            data.user_tag, data.notify_type, data.is_snapshot, data.status, data.basket_id,
            data.original_basket_id, data.linked_basket_ids, data.fcm_id, data.ib_id,
            data.user_id, data.account_id, data.symbol, data.exchange, data.trade_exchange,
            data.trade_route, data.exchange_order_id, data.instrument_type, data.completion_reason,
            data.quantity, data.quan_release_pending, data.price, data.trigger_price,
            self._convert_enum_field(data.transaction_type), self._convert_enum_field(data.duration), 
            self._convert_enum_field(data.price_type), self._convert_enum_field(data.orig_price_type),
            self._convert_enum_field(data.manual_or_auto), self._convert_enum_field(data.bracket_type), 
            data.avg_fill_price, data.total_fill_size, data.total_unfilled_size, data.trail_by_ticks, 
            data.trail_by_price_id, data.sequence_number, data.orig_sequence_number, data.cor_sequence_number,
            data.currency, data.country_code, data.text, data.report_text, data.remarks,
            data.window_name, data.originator_window_name, data.cancel_at_ssboe,
            data.cancel_at_usecs, data.cancel_after_secs, data.ssboe, data.usecs,
            data.template_id, data.order_timestamp
        )
        return self.db.execute_insert(sql, params)
    
    def find_by_basket_id(self, basket_id: str) -> List[OrderNotification]:
        """Find all notifications for a basket ID."""
        sql = """
        SELECT * FROM order_notifications 
        WHERE basket_id = %s 
        ORDER BY received_at ASC
        """
        results = self.db.execute_query(sql, (basket_id,))
        return [OrderNotification(**row) for row in results]
    
    def find_by_account(self, account_id: str, limit: int = 100) -> List[OrderNotification]:
        """Find recent notifications for an account."""
        sql = """
        SELECT * FROM order_notifications 
        WHERE account_id = %s 
        ORDER BY received_at DESC 
        LIMIT %s
        """
        results = self.db.execute_query(sql, (account_id, limit))
        return [OrderNotification(**row) for row in results]
    
    def get_order_lifecycle(self, exchange_order_id: str) -> List[OrderNotification]:
        """Get complete lifecycle for an order."""
        sql = """
        SELECT * FROM order_notifications 
        WHERE exchange_order_id = %s 
        ORDER BY received_at ASC
        """
        results = self.db.execute_query(sql, (exchange_order_id,))
        return [OrderNotification(**row) for row in results]


@dataclass
class OrderFill:
    """Order fill execution data model."""
    id: Optional[int] = None
    basket_id: str = ""
    exchange_order_id: str = ""
    fill_id: str = ""
    symbol: str = ""
    exchange: str = ""
    account_id: str = ""
    
    # Fill details
    fill_price: Optional[Decimal] = None
    fill_quantity: Optional[int] = None
    fill_side: str = ""  # BUY, SELL
    is_buy: bool = True
    
    # Execution details
    execution_id: str = ""
    contra_broker: str = ""
    settlement_currency: str = ""
    settlement_date: Optional[date] = None
    commission: Optional[Decimal] = None
    
    # Timing
    execution_timestamp: Optional[datetime] = None
    trade_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None
    
    # Market data at fill
    bid_price: Optional[Decimal] = None
    ask_price: Optional[Decimal] = None
    last_price: Optional[Decimal] = None
    
    template_id: int = 0


class OrderFillDAO(BaseModel):
    """Data access object for order_fills table."""
    
    def create_table(self) -> bool:
        """Create the order_fills table."""
        sql = """
        CREATE TABLE IF NOT EXISTS order_fills (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            basket_id VARCHAR(100) NOT NULL,
            exchange_order_id VARCHAR(100),
            fill_id VARCHAR(100) NOT NULL,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            account_id VARCHAR(100),
            
            fill_price DECIMAL(15,8) NOT NULL,
            fill_quantity INT NOT NULL,
            fill_side VARCHAR(10) DEFAULT NULL,
            is_buy BOOLEAN DEFAULT TRUE,
            
            execution_id VARCHAR(100),
            contra_broker VARCHAR(100),
            settlement_currency VARCHAR(10),
            settlement_date DATE,
            commission DECIMAL(15,8),
            
            execution_timestamp TIMESTAMP(6),
            trade_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            bid_price DECIMAL(15,8),
            ask_price DECIMAL(15,8),
            last_price DECIMAL(15,8),
            
            template_id INT,
            
            UNIQUE KEY unique_fill (fill_id, exchange_order_id),
            INDEX idx_basket_id (basket_id),
            INDEX idx_exchange_order_id (exchange_order_id),
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_account_id (account_id),
            INDEX idx_execution_timestamp (execution_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_fill_side (fill_side),
            INDEX idx_settlement_date (settlement_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: OrderFill) -> int:
        """Insert order fill."""
        sql = """
        INSERT INTO order_fills (
            basket_id, exchange_order_id, fill_id, symbol, exchange, account_id,
            fill_price, fill_quantity, fill_side, is_buy, execution_id, contra_broker,
            settlement_currency, settlement_date, commission, execution_timestamp,
            trade_timestamp, bid_price, ask_price, last_price, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.basket_id, data.exchange_order_id, data.fill_id, data.symbol, data.exchange,
            data.account_id, data.fill_price, data.fill_quantity, data.fill_side, data.is_buy,
            data.execution_id, data.contra_broker, data.settlement_currency, data.settlement_date,
            data.commission, data.execution_timestamp, data.trade_timestamp, data.bid_price,
            data.ask_price, data.last_price, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def find_fills_by_order(self, exchange_order_id: str) -> List[OrderFill]:
        """Find all fills for an order."""
        sql = """
        SELECT * FROM order_fills 
        WHERE exchange_order_id = %s 
        ORDER BY execution_timestamp ASC
        """
        results = self.db.execute_query(sql, (exchange_order_id,))
        return [OrderFill(**row) for row in results]
    
    def get_account_fills(self, account_id: str, start_date: date, end_date: date) -> List[OrderFill]:
        """Get fills for account within date range."""
        sql = """
        SELECT * FROM order_fills 
        WHERE account_id = %s 
        AND DATE(execution_timestamp) BETWEEN %s AND %s 
        ORDER BY execution_timestamp DESC
        """
        results = self.db.execute_query(sql, (account_id, start_date, end_date))
        return [OrderFill(**row) for row in results]


# =============================================================================
# POSITION & ACCOUNT MODELS
# =============================================================================

@dataclass
class Position:
    """Position tracking model."""
    id: Optional[int] = None
    account_id: str = ""
    symbol: str = ""
    exchange: str = ""
    
    # Position details
    net_position: Optional[int] = None
    buy_quantity: Optional[int] = None
    sell_quantity: Optional[int] = None
    
    # Average prices
    avg_buy_price: Optional[Decimal] = None
    avg_sell_price: Optional[Decimal] = None
    avg_open_price: Optional[Decimal] = None
    
    # P&L information
    realized_pnl: Optional[Decimal] = None
    unrealized_pnl: Optional[Decimal] = None
    total_pnl: Optional[Decimal] = None
    
    # Market data
    mark_price: Optional[Decimal] = None
    last_trade_price: Optional[Decimal] = None
    
    # Risk metrics
    margin_requirement: Optional[Decimal] = None
    buying_power_effect: Optional[Decimal] = None
    
    # Timestamps
    position_timestamp: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    received_at: Optional[datetime] = None
    
    template_id: int = 0


class PositionDAO(BaseModel):
    """Data access object for positions table."""
    
    def create_table(self) -> bool:
        """Create the positions table."""
        sql = """
        CREATE TABLE IF NOT EXISTS positions (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            account_id VARCHAR(100) NOT NULL,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            
            net_position INT DEFAULT 0,
            buy_quantity INT DEFAULT 0,
            sell_quantity INT DEFAULT 0,
            
            avg_buy_price DECIMAL(15,8),
            avg_sell_price DECIMAL(15,8),
            avg_open_price DECIMAL(15,8),
            
            realized_pnl DECIMAL(15,2),
            unrealized_pnl DECIMAL(15,2),
            total_pnl DECIMAL(15,2),
            
            mark_price DECIMAL(15,8),
            last_trade_price DECIMAL(15,8),
            
            margin_requirement DECIMAL(15,2),
            buying_power_effect DECIMAL(15,2),
            
            position_timestamp TIMESTAMP(6),
            last_updated TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            template_id INT,
            
            UNIQUE KEY unique_account_symbol (account_id, symbol, exchange),
            INDEX idx_account_id (account_id),
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_net_position (net_position),
            INDEX idx_position_timestamp (position_timestamp),
            INDEX idx_last_updated (last_updated),
            INDEX idx_total_pnl (total_pnl)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: Position) -> int:
        """Insert position data."""
        sql = """
        INSERT INTO positions (
            account_id, symbol, exchange, net_position, buy_quantity, sell_quantity,
            avg_buy_price, avg_sell_price, avg_open_price, realized_pnl, unrealized_pnl,
            total_pnl, mark_price, last_trade_price, margin_requirement, buying_power_effect,
            position_timestamp, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.account_id, data.symbol, data.exchange, data.net_position, data.buy_quantity,
            data.sell_quantity, data.avg_buy_price, data.avg_sell_price, data.avg_open_price,
            data.realized_pnl, data.unrealized_pnl, data.total_pnl, data.mark_price,
            data.last_trade_price, data.margin_requirement, data.buying_power_effect,
            data.position_timestamp, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def upsert(self, data: Position) -> int:
        """Insert or update position."""
        sql = """
        INSERT INTO positions (
            account_id, symbol, exchange, net_position, buy_quantity, sell_quantity,
            avg_buy_price, avg_sell_price, avg_open_price, realized_pnl, unrealized_pnl,
            total_pnl, mark_price, last_trade_price, margin_requirement, buying_power_effect,
            position_timestamp, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            net_position = VALUES(net_position),
            buy_quantity = VALUES(buy_quantity),
            sell_quantity = VALUES(sell_quantity),
            avg_buy_price = VALUES(avg_buy_price),
            avg_sell_price = VALUES(avg_sell_price),
            avg_open_price = VALUES(avg_open_price),
            realized_pnl = VALUES(realized_pnl),
            unrealized_pnl = VALUES(unrealized_pnl),
            total_pnl = VALUES(total_pnl),
            mark_price = VALUES(mark_price),
            last_trade_price = VALUES(last_trade_price),
            margin_requirement = VALUES(margin_requirement),
            buying_power_effect = VALUES(buying_power_effect),
            position_timestamp = VALUES(position_timestamp),
            last_updated = CURRENT_TIMESTAMP(6)
        """
        params = (
            data.account_id, data.symbol, data.exchange, data.net_position, data.buy_quantity,
            data.sell_quantity, data.avg_buy_price, data.avg_sell_price, data.avg_open_price,
            data.realized_pnl, data.unrealized_pnl, data.total_pnl, data.mark_price,
            data.last_trade_price, data.margin_requirement, data.buying_power_effect,
            data.position_timestamp, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def find_by_account(self, account_id: str) -> List[Position]:
        """Find all positions for an account."""
        sql = """
        SELECT * FROM positions 
        WHERE account_id = %s 
        AND net_position != 0 
        ORDER BY symbol ASC
        """
        results = self.db.execute_query(sql, (account_id,))
        return [Position(**row) for row in results]
    
    def get_position(self, account_id: str, symbol: str, exchange: str) -> Optional[Position]:
        """Get specific position."""
        sql = """
        SELECT * FROM positions 
        WHERE account_id = %s AND symbol = %s AND exchange = %s
        """
        results = self.db.execute_query(sql, (account_id, symbol, exchange), fetch_all=False)
        return Position(**results[0]) if results else None


@dataclass
class AccountInfo:
    """Account information model."""
    id: Optional[int] = None
    account_id: str = ""
    fcm_id: str = ""
    ib_id: str = ""
    user_id: str = ""
    
    # Account details
    account_name: str = ""
    account_type: str = ""
    currency: str = ""
    country_code: str = ""
    
    # Financial information
    net_liquidation_value: Optional[Decimal] = None
    total_cash_balance: Optional[Decimal] = None
    available_funds: Optional[Decimal] = None
    buying_power: Optional[Decimal] = None
    margin_requirement: Optional[Decimal] = None
    
    # Risk limits
    day_trading_buying_power: Optional[Decimal] = None
    overnight_buying_power: Optional[Decimal] = None
    max_position_value: Optional[Decimal] = None
    
    # Status
    is_active: bool = True
    risk_status: str = ""
    
    # Timestamps
    last_updated: Optional[datetime] = None
    received_at: Optional[datetime] = None
    
    template_id: int = 0


class AccountInfoDAO(BaseModel):
    """Data access object for account_info table."""
    
    def create_table(self) -> bool:
        """Create the account_info table."""
        sql = """
        CREATE TABLE IF NOT EXISTS account_info (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            account_id VARCHAR(100) NOT NULL UNIQUE,
            fcm_id VARCHAR(50),
            ib_id VARCHAR(50),
            user_id VARCHAR(100),
            
            account_name VARCHAR(200),
            account_type VARCHAR(50),
            currency VARCHAR(10),
            country_code VARCHAR(5),
            
            net_liquidation_value DECIMAL(15,2),
            total_cash_balance DECIMAL(15,2),
            available_funds DECIMAL(15,2),
            buying_power DECIMAL(15,2),
            margin_requirement DECIMAL(15,2),
            
            day_trading_buying_power DECIMAL(15,2),
            overnight_buying_power DECIMAL(15,2),
            max_position_value DECIMAL(15,2),
            
            is_active BOOLEAN DEFAULT TRUE,
            risk_status VARCHAR(50),
            
            last_updated TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            template_id INT,
            
            INDEX idx_fcm_id (fcm_id),
            INDEX idx_ib_id (ib_id),
            INDEX idx_user_id (user_id),
            INDEX idx_account_type (account_type),
            INDEX idx_is_active (is_active),
            INDEX idx_last_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: AccountInfo) -> int:
        """Insert account info."""
        sql = """
        INSERT INTO account_info (
            account_id, fcm_id, ib_id, user_id, account_name, account_type, currency,
            country_code, net_liquidation_value, total_cash_balance, available_funds,
            buying_power, margin_requirement, day_trading_buying_power, overnight_buying_power,
            max_position_value, is_active, risk_status, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.account_id, data.fcm_id, data.ib_id, data.user_id, data.account_name,
            data.account_type, data.currency, data.country_code, data.net_liquidation_value,
            data.total_cash_balance, data.available_funds, data.buying_power,
            data.margin_requirement, data.day_trading_buying_power, data.overnight_buying_power,
            data.max_position_value, data.is_active, data.risk_status, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def upsert(self, data: AccountInfo) -> int:
        """Insert or update account info."""
        sql = """
        INSERT INTO account_info (
            account_id, fcm_id, ib_id, user_id, account_name, account_type, currency,
            country_code, net_liquidation_value, total_cash_balance, available_funds,
            buying_power, margin_requirement, day_trading_buying_power, overnight_buying_power,
            max_position_value, is_active, risk_status, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            fcm_id = VALUES(fcm_id),
            ib_id = VALUES(ib_id),
            user_id = VALUES(user_id),
            account_name = VALUES(account_name),
            account_type = VALUES(account_type),
            currency = VALUES(currency),
            country_code = VALUES(country_code),
            net_liquidation_value = VALUES(net_liquidation_value),
            total_cash_balance = VALUES(total_cash_balance),
            available_funds = VALUES(available_funds),
            buying_power = VALUES(buying_power),
            margin_requirement = VALUES(margin_requirement),
            day_trading_buying_power = VALUES(day_trading_buying_power),
            overnight_buying_power = VALUES(overnight_buying_power),
            max_position_value = VALUES(max_position_value),
            is_active = VALUES(is_active),
            risk_status = VALUES(risk_status),
            last_updated = CURRENT_TIMESTAMP(6)
        """
        params = (
            data.account_id, data.fcm_id, data.ib_id, data.user_id, data.account_name,
            data.account_type, data.currency, data.country_code, data.net_liquidation_value,
            data.total_cash_balance, data.available_funds, data.buying_power,
            data.margin_requirement, data.day_trading_buying_power, data.overnight_buying_power,
            data.max_position_value, data.is_active, data.risk_status, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def find_by_user(self, user_id: str) -> List[AccountInfo]:
        """Find accounts for a user."""
        sql = """
        SELECT * FROM account_info 
        WHERE user_id = %s AND is_active = TRUE 
        ORDER BY account_id ASC
        """
        results = self.db.execute_query(sql, (user_id,))
        return [AccountInfo(**row) for row in results]


# =============================================================================
# SYSTEM EVENTS & STATISTICS MODELS
# =============================================================================

@dataclass
class SystemEvent:
    """System events and connection tracking model."""
    id: Optional[int] = None
    event_type: str = ""  # LOGIN, LOGOUT, HEARTBEAT, CONNECTION_LOST, etc.
    user_id: str = ""
    fcm_id: str = ""
    ib_id: str = ""
    system_name: str = ""
    gateway_name: str = ""
    infra_type: str = ""  # TICKER_PLANT, ORDER_PLANT, etc.
    
    # Event details
    event_status: str = ""  # SUCCESS, FAILED, TIMEOUT
    event_message: str = ""
    error_code: str = ""
    client_ip: str = ""
    session_id: str = ""
    
    # Timing
    event_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None
    
    template_id: int = 0


class SystemEventDAO(BaseModel):
    """Data access object for system_events table."""
    
    def create_table(self) -> bool:
        """Create the system_events table."""
        sql = """
        CREATE TABLE IF NOT EXISTS system_events (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            event_type VARCHAR(50) DEFAULT NULL,
            user_id VARCHAR(100),
            fcm_id VARCHAR(50),
            ib_id VARCHAR(50),
            system_name VARCHAR(100),
            gateway_name VARCHAR(100),
            infra_type VARCHAR(100) DEFAULT NULL,
            
            event_status VARCHAR(20) DEFAULT NULL,
            event_message TEXT,
            error_code VARCHAR(50),
            client_ip VARCHAR(45),
            session_id VARCHAR(100),
            
            event_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            template_id INT,
            
            INDEX idx_event_type (event_type),
            INDEX idx_user_id (user_id),
            INDEX idx_system_name (system_name),
            INDEX idx_infra_type (infra_type),
            INDEX idx_event_status (event_status),
            INDEX idx_event_timestamp (event_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_session_id (session_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: SystemEvent) -> int:
        """Insert system event."""
        sql = """
        INSERT INTO system_events (
            event_type, user_id, fcm_id, ib_id, system_name, gateway_name, infra_type,
            event_status, event_message, error_code, client_ip, session_id,
            event_timestamp, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.event_type, data.user_id, data.fcm_id, data.ib_id, data.system_name,
            data.gateway_name, data.infra_type, data.event_status, data.event_message,
            data.error_code, data.client_ip, data.session_id, data.event_timestamp,
            data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def find_by_user_and_type(self, user_id: str, event_type: str, limit: int = 50) -> List[SystemEvent]:
        """Find events by user and type."""
        sql = """
        SELECT * FROM system_events 
        WHERE user_id = %s AND event_type = %s 
        ORDER BY event_timestamp DESC 
        LIMIT %s
        """
        results = self.db.execute_query(sql, (user_id, event_type, limit))
        return [SystemEvent(**row) for row in results]
    
    def get_recent_events(self, hours: int = 24, limit: int = 100) -> List[SystemEvent]:
        """Get recent system events."""
        sql = """
        SELECT * FROM system_events 
        WHERE event_timestamp >= DATE_SUB(NOW(), INTERVAL %s HOUR) 
        ORDER BY event_timestamp DESC 
        LIMIT %s
        """
        results = self.db.execute_query(sql, (hours, limit))
        return [SystemEvent(**row) for row in results]


@dataclass
class TradeStatistics:
    """Trade statistics model (Template 152)."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    
    # Trade counts
    trade_count: Optional[int] = None
    buy_trade_count: Optional[int] = None
    sell_trade_count: Optional[int] = None
    
    # Volume statistics
    total_volume: Optional[int] = None
    buy_volume: Optional[int] = None
    sell_volume: Optional[int] = None
    
    # Price statistics
    high_trade_price: Optional[Decimal] = None
    low_trade_price: Optional[Decimal] = None
    first_trade_price: Optional[Decimal] = None
    last_trade_price: Optional[Decimal] = None
    vwap: Optional[Decimal] = None
    
    # Price changes
    net_change: Optional[Decimal] = None
    percent_change: Optional[Decimal] = None
    
    # Settlement and previous close
    settlement_price: Optional[Decimal] = None
    previous_close_price: Optional[Decimal] = None
    open_interest: Optional[int] = None
    
    # Timing
    stats_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None
    
    template_id: int = 152


class TradeStatisticsDAO(BaseModel):
    """Data access object for trade_statistics table."""
    
    def create_table(self) -> bool:
        """Create the trade_statistics table."""
        sql = """
        CREATE TABLE IF NOT EXISTS trade_statistics (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            
            trade_count BIGINT UNSIGNED,
            buy_trade_count BIGINT UNSIGNED,
            sell_trade_count BIGINT UNSIGNED,
            
            total_volume BIGINT UNSIGNED,
            buy_volume BIGINT UNSIGNED,
            sell_volume BIGINT UNSIGNED,
            
            high_trade_price DECIMAL(15,8),
            low_trade_price DECIMAL(15,8),
            first_trade_price DECIMAL(15,8),
            last_trade_price DECIMAL(15,8),
            vwap DECIMAL(15,8),
            
            net_change DECIMAL(15,8),
            percent_change DECIMAL(8,4),
            
            settlement_price DECIMAL(15,8),
            previous_close_price DECIMAL(15,8),
            open_interest BIGINT UNSIGNED,
            
            stats_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            template_id INT DEFAULT 152,
            
            UNIQUE KEY unique_symbol_exchange_time (symbol, exchange, stats_timestamp),
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_stats_timestamp (stats_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_total_volume (total_volume),
            INDEX idx_last_trade_price (last_trade_price)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: TradeStatistics) -> int:
        """Insert trade statistics."""
        sql = """
        INSERT INTO trade_statistics (
            symbol, exchange, trade_count, buy_trade_count, sell_trade_count,
            total_volume, buy_volume, sell_volume, high_trade_price, low_trade_price,
            first_trade_price, last_trade_price, vwap, net_change, percent_change,
            settlement_price, previous_close_price, open_interest, stats_timestamp, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.trade_count, data.buy_trade_count,
            data.sell_trade_count, data.total_volume, data.buy_volume, data.sell_volume,
            data.high_trade_price, data.low_trade_price, data.first_trade_price,
            data.last_trade_price, data.vwap, data.net_change, data.percent_change,
            data.settlement_price, data.previous_close_price, data.open_interest,
            data.stats_timestamp, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def upsert(self, data: TradeStatistics) -> int:
        """Insert or update trade statistics."""
        sql = """
        INSERT INTO trade_statistics (
            symbol, exchange, trade_count, buy_trade_count, sell_trade_count,
            total_volume, buy_volume, sell_volume, high_trade_price, low_trade_price,
            first_trade_price, last_trade_price, vwap, net_change, percent_change,
            settlement_price, previous_close_price, open_interest, stats_timestamp, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            trade_count = VALUES(trade_count),
            buy_trade_count = VALUES(buy_trade_count),
            sell_trade_count = VALUES(sell_trade_count),
            total_volume = VALUES(total_volume),
            buy_volume = VALUES(buy_volume),
            sell_volume = VALUES(sell_volume),
            high_trade_price = GREATEST(high_trade_price, VALUES(high_trade_price)),
            low_trade_price = LEAST(low_trade_price, VALUES(low_trade_price)),
            last_trade_price = VALUES(last_trade_price),
            vwap = VALUES(vwap),
            net_change = VALUES(net_change),
            percent_change = VALUES(percent_change),
            settlement_price = VALUES(settlement_price),
            open_interest = VALUES(open_interest),
            received_at = CURRENT_TIMESTAMP(6)
        """
        params = (
            data.symbol, data.exchange, data.trade_count, data.buy_trade_count,
            data.sell_trade_count, data.total_volume, data.buy_volume, data.sell_volume,
            data.high_trade_price, data.low_trade_price, data.first_trade_price,
            data.last_trade_price, data.vwap, data.net_change, data.percent_change,
            data.settlement_price, data.previous_close_price, data.open_interest,
            data.stats_timestamp, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def get_latest_stats(self, symbol: str, exchange: str) -> Optional[TradeStatistics]:
        """Get latest trade statistics for symbol."""
        sql = """
        SELECT * FROM trade_statistics 
        WHERE symbol = %s AND exchange = %s 
        ORDER BY stats_timestamp DESC 
        LIMIT 1
        """
        results = self.db.execute_query(sql, (symbol, exchange), fetch_all=False)
        return TradeStatistics(**results[0]) if results else None


@dataclass
class QuoteStatistics:
    """Quote statistics model (Template 153)."""
    id: Optional[int] = None
    symbol: str = ""
    exchange: str = ""
    
    # Quote counts
    quote_count: Optional[int] = None
    bid_quote_count: Optional[int] = None
    ask_quote_count: Optional[int] = None
    
    # Price statistics
    high_bid_price: Optional[Decimal] = None
    low_bid_price: Optional[Decimal] = None
    high_ask_price: Optional[Decimal] = None
    low_ask_price: Optional[Decimal] = None
    
    # Size statistics
    total_bid_size: Optional[int] = None
    total_ask_size: Optional[int] = None
    avg_bid_size: Optional[Decimal] = None
    avg_ask_size: Optional[Decimal] = None
    
    # Spread statistics
    avg_spread: Optional[Decimal] = None
    min_spread: Optional[Decimal] = None
    max_spread: Optional[Decimal] = None
    
    # Current market
    current_bid_price: Optional[Decimal] = None
    current_ask_price: Optional[Decimal] = None
    current_bid_size: Optional[int] = None
    current_ask_size: Optional[int] = None
    
    # Timing
    stats_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None
    
    template_id: int = 153


class QuoteStatisticsDAO(BaseModel):
    """Data access object for quote_statistics table."""
    
    def create_table(self) -> bool:
        """Create the quote_statistics table."""
        sql = """
        CREATE TABLE IF NOT EXISTS quote_statistics (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(50) NOT NULL,
            exchange VARCHAR(20) NOT NULL,
            
            quote_count BIGINT UNSIGNED,
            bid_quote_count BIGINT UNSIGNED,
            ask_quote_count BIGINT UNSIGNED,
            
            high_bid_price DECIMAL(15,8),
            low_bid_price DECIMAL(15,8),
            high_ask_price DECIMAL(15,8),
            low_ask_price DECIMAL(15,8),
            
            total_bid_size BIGINT UNSIGNED,
            total_ask_size BIGINT UNSIGNED,
            avg_bid_size DECIMAL(15,2),
            avg_ask_size DECIMAL(15,2),
            
            avg_spread DECIMAL(15,8),
            min_spread DECIMAL(15,8),
            max_spread DECIMAL(15,8),
            
            current_bid_price DECIMAL(15,8),
            current_ask_price DECIMAL(15,8),
            current_bid_size INT,
            current_ask_size INT,
            
            stats_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            
            template_id INT DEFAULT 153,
            
            UNIQUE KEY unique_symbol_exchange_time (symbol, exchange, stats_timestamp),
            INDEX idx_symbol_exchange (symbol, exchange),
            INDEX idx_stats_timestamp (stats_timestamp),
            INDEX idx_received_at (received_at),
            INDEX idx_current_bid_price (current_bid_price),
            INDEX idx_current_ask_price (current_ask_price),
            INDEX idx_avg_spread (avg_spread)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: QuoteStatistics) -> int:
        """Insert quote statistics."""
        sql = """
        INSERT INTO quote_statistics (
            symbol, exchange, quote_count, bid_quote_count, ask_quote_count,
            high_bid_price, low_bid_price, high_ask_price, low_ask_price,
            total_bid_size, total_ask_size, avg_bid_size, avg_ask_size,
            avg_spread, min_spread, max_spread, current_bid_price, current_ask_price,
            current_bid_size, current_ask_size, stats_timestamp, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.symbol, data.exchange, data.quote_count, data.bid_quote_count,
            data.ask_quote_count, data.high_bid_price, data.low_bid_price,
            data.high_ask_price, data.low_ask_price, data.total_bid_size,
            data.total_ask_size, data.avg_bid_size, data.avg_ask_size,
            data.avg_spread, data.min_spread, data.max_spread,
            data.current_bid_price, data.current_ask_price, data.current_bid_size,
            data.current_ask_size, data.stats_timestamp, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def upsert(self, data: QuoteStatistics) -> int:
        """Insert or update quote statistics."""
        sql = """
        INSERT INTO quote_statistics (
            symbol, exchange, quote_count, bid_quote_count, ask_quote_count,
            high_bid_price, low_bid_price, high_ask_price, low_ask_price,
            total_bid_size, total_ask_size, avg_bid_size, avg_ask_size,
            avg_spread, min_spread, max_spread, current_bid_price, current_ask_price,
            current_bid_size, current_ask_size, stats_timestamp, template_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            quote_count = VALUES(quote_count),
            bid_quote_count = VALUES(bid_quote_count),
            ask_quote_count = VALUES(ask_quote_count),
            high_bid_price = GREATEST(high_bid_price, VALUES(high_bid_price)),
            low_bid_price = LEAST(low_bid_price, VALUES(low_bid_price)),
            high_ask_price = GREATEST(high_ask_price, VALUES(high_ask_price)),
            low_ask_price = LEAST(low_ask_price, VALUES(low_ask_price)),
            total_bid_size = VALUES(total_bid_size),
            total_ask_size = VALUES(total_ask_size),
            avg_bid_size = VALUES(avg_bid_size),
            avg_ask_size = VALUES(avg_ask_size),
            avg_spread = VALUES(avg_spread),
            min_spread = LEAST(min_spread, VALUES(min_spread)),
            max_spread = GREATEST(max_spread, VALUES(max_spread)),
            current_bid_price = VALUES(current_bid_price),
            current_ask_price = VALUES(current_ask_price),
            current_bid_size = VALUES(current_bid_size),
            current_ask_size = VALUES(current_ask_size),
            received_at = CURRENT_TIMESTAMP(6)
        """
        params = (
            data.symbol, data.exchange, data.quote_count, data.bid_quote_count,
            data.ask_quote_count, data.high_bid_price, data.low_bid_price,
            data.high_ask_price, data.low_ask_price, data.total_bid_size,
            data.total_ask_size, data.avg_bid_size, data.avg_ask_size,
            data.avg_spread, data.min_spread, data.max_spread,
            data.current_bid_price, data.current_ask_price, data.current_bid_size,
            data.current_ask_size, data.stats_timestamp, data.template_id
        )
        return self.db.execute_insert(sql, params)
    
    def get_latest_stats(self, symbol: str, exchange: str) -> Optional[QuoteStatistics]:
        """Get latest quote statistics for symbol."""
        sql = """
        SELECT * FROM quote_statistics 
        WHERE symbol = %s AND exchange = %s 
        ORDER BY stats_timestamp DESC 
        LIMIT 1
        """
        results = self.db.execute_query(sql, (symbol, exchange), fetch_all=False)
        return QuoteStatistics(**results[0]) if results else None


@dataclass
class Heartbeat:
    """Heartbeat tracking model (Templates 18/19)."""
    id: Optional[int] = None
    user_id: str = ""
    session_id: str = ""
    infra_type: str = ""
    heartbeat_interval: Optional[Decimal] = None
    client_heartbeat_timestamp: Optional[datetime] = None
    server_heartbeat_timestamp: Optional[datetime] = None
    received_at: Optional[datetime] = None
    template_id: int = 19
    rtt_ms: Optional[Decimal] = None  # Round trip time in milliseconds


class HeartbeatDAO(BaseModel):
    """Data access object for heartbeats table."""
    
    def create_table(self) -> bool:
        """Create the heartbeats table."""
        sql = """
        CREATE TABLE IF NOT EXISTS heartbeats (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(100) NOT NULL,
            session_id VARCHAR(100),
            infra_type VARCHAR(100) DEFAULT NULL,
            heartbeat_interval DECIMAL(8,3),
            client_heartbeat_timestamp TIMESTAMP(6),
            server_heartbeat_timestamp TIMESTAMP(6),
            received_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
            template_id INT DEFAULT 19,
            rtt_ms DECIMAL(8,3),
            
            INDEX idx_user_id (user_id),
            INDEX idx_session_id (session_id),
            INDEX idx_infra_type (infra_type),
            INDEX idx_client_heartbeat_timestamp (client_heartbeat_timestamp),
            INDEX idx_server_heartbeat_timestamp (server_heartbeat_timestamp),
            INDEX idx_received_at (received_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        return self.db.create_table_from_sql(sql)
    
    def insert(self, data: Heartbeat) -> int:
        """Insert heartbeat record."""
        sql = """
        INSERT INTO heartbeats (
            user_id, session_id, infra_type, heartbeat_interval,
            client_heartbeat_timestamp, server_heartbeat_timestamp,
            template_id, rtt_ms
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            data.user_id, data.session_id, data.infra_type, data.heartbeat_interval,
            data.client_heartbeat_timestamp, data.server_heartbeat_timestamp,
            data.template_id, data.rtt_ms
        )
        return self.db.execute_insert(sql, params)
    
    def get_latest_heartbeats(self, user_id: str, minutes: int = 30) -> List[Heartbeat]:
        """Get recent heartbeats for a user."""
        sql = """
        SELECT * FROM heartbeats 
        WHERE user_id = %s 
        AND received_at >= DATE_SUB(NOW(), INTERVAL %s MINUTE) 
        ORDER BY received_at DESC
        """
        results = self.db.execute_query(sql, (user_id, minutes))
        return [Heartbeat(**row) for row in results]


# Export main classes
__all__ = [
    'Symbol', 'SymbolDAO',
    'BestBidOffer', 'BestBidOfferDAO', 
    'LastTrade', 'LastTradeDAO',
    'UserSession', 'UserSessionDAO',
    # Level 3 Depth-by-Order
    'DepthByOrderSnapshot', 'DepthByOrderSnapshotDAO',
    'DepthByOrderUpdate', 'DepthByOrderUpdateDAO',
    'OrderBookLevel', 'OrderBookLevelDAO',
    # Historical Data
    'TimeBar', 'TimeBarDAO',
    'TickBar', 'TickBarDAO',
    # Order Management
    'OrderNotification', 'OrderNotificationDAO',
    'OrderFill', 'OrderFillDAO',
    # Position & Account
    'Position', 'PositionDAO',
    'AccountInfo', 'AccountInfoDAO',
    # System Events & Statistics
    'SystemEvent', 'SystemEventDAO',
    'TradeStatistics', 'TradeStatisticsDAO',
    'QuoteStatistics', 'QuoteStatisticsDAO',
    'Heartbeat', 'HeartbeatDAO',
    'create_all_tables', 'get_database_stats',
    'protobuf_to_symbol', 'protobuf_to_best_bid_offer'
]