# Rithmic API Simple Demos Usage Guide

## Quick Start

### Prerequisites
1. Valid Rithmic account credentials (paper trading or production)
2. Python 3.7+ with required packages: `websockets`, `protobuf`, `python-dotenv`
3. Configuration in `.env.simple-demos` or command line arguments

### Authentication Test
Start by verifying your connection and credentials:
```bash
python3 discover_systems.py
```

```
# Output:
Discovering available Rithmic systems...
Using URI: wss://rprotocol.rithmic.com:443
Using User: PP-013155
Step A: Getting system info from wss://rprotocol.rithmic.com:443
Connected for system info
Sending system info request...
Available systems:
  - Rithmic 01
  - Rithmic 04 Colo
  - Rithmic Paper Trading
  - TopstepTrader
  - TradeFundrr
  - Apex
  - MES Capital
  ...

Discovered 20 available systems:
  1. Rithmic 01
  2. Rithmic 04 Colo
  3. Rithmic Paper Trading
  ...
```

This will display available Rithmic systems and confirm your authentication is working.

## Configuration Options

### Environment Variables (.env.simple-demos)
All scripts use `.env.simple-demos` for default configuration. Key variables:
```bash
RITHMIC_SYSTEM=Rithmic Paper Trading
RITHMIC_GATEWAY=Chicago Area
RITHMIC_USER=PP-013155
RITHMIC_PASSWORD=b7neA8k6JA
SYMBOL=ESZ5
EXCHANGE=CME
```

### Command Line Arguments
Every script now supports command line arguments that override environment defaults:
```bash
# Connection settings (available in all scripts)
--system "Rithmic Paper Trading"  # RITHMIC_SYSTEM
--gateway "Chicago Area"          # RITHMIC_GATEWAY
--user "PP-013155"                # RITHMIC_USER
--password "password"             # RITHMIC_PASSWORD

# Market data settings (realtime scripts)
--symbol "ESZ5"                   # SYMBOL
--exchange "CME"                  # EXCHANGE

# Historical data settings (historical scripts)
--symbol "ESZ5"                   # HIST_SYMBOL
--exchange "CME"                  # HIST_EXCHANGE
--bar-specifier 5                 # BAR_TYPE_SPECIFIER (used by historical_time_bars.py and historical_tick_bars.py for time/tick bar aggregation)
--volume-specifier 500            # VOLUME_BAR_SPECIFIER (used by historical_volume_bars.py for volume-based bar aggregation)
--range-specifier 4               # RANGE_BAR_SPECIFIER (used by historical_range_bars.py for price range-based bar aggregation)
--start-index 1719532800          # HIST_START_INDEX (used by historical_volume_bars.py and historical_range_bars.py for date range start)
--finish-index 1719619199         # HIST_FINISH_INDEX (used by historical_volume_bars.py and historical_range_bars.py for date range end)
```

## Symbol Discovery Workflow

### Step 1: Find Available Contracts
Search for all ES (S&P 500 E-mini) contract months:
```bash
# Using environment defaults
python3 search/search_contracts.py

# Custom search patterns
python3 search/search_contracts.py --pattern "ES*" --exchange "CME"

```
# Output:
Searching for symbols with pattern: 'ES*'
Exchange: CME
Product code: ES
Instrument type: 1
Pattern type: 2 (CONTAINS)
...
Authentication successful!
Sending search request...

Search response:
Response code: ['0']
Template ID: 110

No symbols found in response
# Note: Paper trading accounts may have limited search capabilities
```

python3 search/search_contracts.py --pattern "ES*5" --exchange "CME"  # All 2025 ES contracts
python3 search/search_contracts.py --pattern "ESZ*" --exchange "CME"  # All December ES contracts
```

### Step 2: Use Results for Market Data
Take symbols from search results and subscribe to market data:
```bash
# Level 1 market data (Best Bid/Offer + Last Trade)
python3 realtime/level1_bbo_trades.py --symbol "ESZ5" --exchange "CME"

```
# Output:
Subscribing to 1 symbol(s)...
Subscribing to ESZ5 on CME
Sending Level 1 subscription request...
Listening for Level 1 market data updates... (Ctrl+C to exit)
# Market data updates will stream here:
# [ESZ5] Best Bid/Offer: bid_price: 4565.25, bid_size: 10, ask_price: 4565.50, ask_size: 8
# [ESZ5] Last Trade: trade_price: 4565.25, trade_size: 5, aggressor: SELL
```

# Level 3 market data (Full order book depth)
python3 realtime/level3_depth_by_order.py --symbol "ESZ5" --exchange "CME"

```
# Output:
Subscribing to 1 symbol(s)...
Subscribing to depth updates for ESZ5 on CME
Requesting depth snapshot for ESZ5 on CME
Listening for Level 3 depth by order data... (Ctrl+C to exit)
Note: Updates received before snapshot will be buffered and applied after snapshot processing
# Depth data will stream here
```
```

## Market Data Subscription Patterns

**Level 1 (level1_bbo_trades.py)**:
- Provides: Best Bid/Offer (Template 151) + Last Trade (Template 150)
- Use case: Complete market picture with quotes and trades
- Bandwidth: Moderate (both bid/offer updates and trades)

**Level 3 (level3_depth_by_order.py)**:
- Provides: Full order book depth with individual order tracking
- Use case: Advanced market analysis and order flow monitoring
- Bandwidth: Higher (all order book changes)

### Market Data Script Options

```bash
# Level 1: Best Bid/Offer + Last Trade
python3 realtime/level1_bbo_trades.py --symbol "ESZ5"

# Level 3: Depth by Order (individual order tracking)
python3 realtime/level3_depth_by_order.py --symbol "ESZ5"
```

## Historical Data Analysis Workflow

### Recommended Analysis Sequence
1. **Time Bars**: Start with time-based analysis
2. **Tick Bars**: Analyze trade frequency patterns
3. **Volume Bars**: Study volume-based price movements
4. **Range Bars**: Examine price volatility patterns

### Step 1: Time Bar Analysis
```bash
# 1-minute bars (default)
python3 historical/historical_time_bars.py --symbol "ESZ5"

# 5-minute bars
python3 historical/historical_time_bars.py --symbol "ESZ5" --bar-specifier 5

```
# Output:
Historical Time Bars Configuration:
  System: Rithmic Paper Trading
  Gateway: Chicago Area
  User: PP-013155
  Symbol: ESZ5
  Exchange: CME
  Bar Period: 5 minute(s)

...
Authentication successful!
Sending historical time bars request...
Waiting for historical time bar data...
# Time bar data will stream here
```

# Daily bars
python3 historical/historical_time_bars.py --symbol "ESZ5" --bar-specifier 1440
```

### Step 2: Tick Bar Analysis
```bash
# Every trade (1-tick bars)
python3 historical/historical_tick_bars.py --symbol "ESZ5"

# Every 100 trades
python3 historical/historical_tick_bars.py --symbol "ESZ5" --bar-specifier 100

# High-frequency analysis (every 500 trades)
python3 historical/historical_tick_bars.py --symbol "ESZ5" --bar-specifier 500
```

### Step 3: Volume Bar Analysis
```bash
# Default 500 contracts per bar
python3 historical/historical_volume_bars.py --symbol "ESZ5"

```
# Output:
Historical Volume Bars Configuration:
  System: Rithmic Paper Trading
  Gateway: Chicago Area
  User: PP-013155
  Symbol: ESZ5
  Exchange: CME
  Volume Specifier: 500 contracts per bar
  Start Index: 1719532800
  Finish Index: 1719619199

...
🔍 Historical Volume Bars Request
📊 Configuration: ESZ5 on CME
📈 Volume threshold: 500 contracts per bar

🎯 Strategy 1/5: Primary date range (from command line/env)
=== Attempting volume bar request: Primary date range ===
Parameters: ESZ5 on CME, volume threshold: 500, dates: 1719532800-1719619199
Sending request...
✅ Data found! Volume Bar Response: ...
✅ Request complete! Total volume bars received: 1
🎉 SUCCESS! Volume bars retrieved using: Primary date range
```

# Higher volume threshold (1000 contracts)
python3 historical/historical_volume_bars.py --symbol "ESZ5" --volume-specifier 1000

# Custom date range
python3 historical/historical_volume_bars.py --symbol "ESZ5" --volume-specifier 500 \
    --start-index 1719532800 --finish-index 1719619199
```

### Step 4: Range Bar Analysis
```bash
# Default 4-tick range (1 point for ES)
python3 historical/historical_range_bars.py --symbol "ESZ5"

# Wider range (8 ticks = 2 points)
python3 historical/historical_range_bars.py --symbol "ESZ5" --range-specifier 8

# Custom date range with tight range
python3 historical/historical_range_bars.py --symbol "ESZ5" --range-specifier 2 \
    --start-index 1719532800 --finish-index 1719619199
```

## Common Workflow Patterns

### Market Analysis Workflow
```bash
# 1. Discover available contracts
python3 search/search_contracts.py --pattern "ES*"

# 2. Monitor real-time Level 1 data
python3 realtime/level1_bbo_trades.py --symbol "ESZ5" &

# 3. Analyze historical patterns (parallel)
python3 historical/historical_time_bars.py --symbol "ESZ5" --bar-specifier 5 &
python3 historical/historical_volume_bars.py --symbol "ESZ5" --volume-specifier 500 &
python3 historical/historical_range_bars.py --symbol "ESZ5" --range-specifier 4 &
```

### Multi-Symbol Monitoring
```bash
# Subscribe to ALL ES contracts with a single command
python3 realtime/level1_bbo_trades.py --underlying "ES" --exchange "CME"

```
# Output:
Searching for all ES contracts...
Searching for all ES contracts on CME...
# Note: If no contracts found via search, specify symbols manually
No contracts found for underlying ES
Subscribing to 1 symbol(s)...
Subscribing to ESZ5 on CME
# Market data for all found contracts will stream here
```

# Subscribe to all ES contracts with Level 3 depth
python3 realtime/level3_depth_by_order.py --underlying "ES" --exchange "CME"

# Legacy method: Monitor specific contracts individually
python3 realtime/level1_bbo_trades.py --symbol "ESZ5" &  # December 2025
python3 realtime/level1_bbo_trades.py --symbol "ESH6" &  # March 2026
python3 realtime/level1_bbo_trades.py --symbol "ESM6" &  # June 2026
```

### Historical Comparison Analysis
```bash
# Compare different time periods for same symbol
python3 historical/historical_time_bars.py --symbol "ESZ5" \
    --start-index 1715731200 --finish-index 1715817599 > may_data.txt

python3 historical/historical_time_bars.py --symbol "ESZ5" \
    --start-index 1719532800 --finish-index 1719619199 > june_data.txt
```

## Parameter Optimization Guidelines

### Bar Type Specifier Optimization

**Time Bars**:
- Range: 1-10080 minutes
- Common: 1, 5, 15, 60, 1440 (1min, 5min, 15min, 1hour, daily)
- Purpose: Time-based OHLCV aggregation

**Tick Bars**:
- Range: 1-1000 trades
- Common: 1, 25, 100, 500 (every trade, 25 trades, 100 trades, high activity)
- Purpose: Trade-count based aggregation

**Volume Bars**:
- Range: 100-25000 contracts
- Common: 500, 1000, 2500 (moderate, active, very active)
- Purpose: Volume-based aggregation
- Note: ES futures typically trade 500-2000 contracts per significant move

**Range Bars**:
- Range: 1-32 ticks
- Common: 4, 8, 16 (1 point, 2 points, 4 points for ES)
- Purpose: Price-movement based aggregation
- Note: ES futures move in 0.25 point increments (1 tick = 0.25 points)

### Date Range Selection
```bash
# Recent active trading day (recommended)
--start-index 1719532800 --finish-index 1719619199  # June 28, 2024

# Alternative date ranges (fallbacks)
--start-index 1715731200 --finish-index 1715817599  # May 15, 2024
--start-index 1712707200 --finish-index 1712793599  # April 10, 2024
```

## Troubleshooting Common Issues

### "No Data" Responses
1. **Check date range**: Avoid weekends and holidays
2. **Increase bar specifiers**: Use larger volume/range thresholds
3. **Try CME E-mini futures**: ES, NQ, RTY are most reliable
4. **Test basic connectivity**: Start with tick bars using same date range

### Authentication Issues
```bash
# Test system discovery first
python3 discover_systems.py

# Verify credentials in .env.simple-demos
# Check RITHMIC_SYSTEM, RITHMIC_USER, RITHMIC_PASSWORD
```

### Permission Denied Errors
- Paper trading accounts typically only have access to CME E-mini futures
- Other exchanges (NYMEX, CBOT, COMEX) may require additional permissions
- Equity symbols (AAPL, MSFT) are not supported in paper trading

### Search and Symbol Issues
**Problem**: "No contracts found for underlying ES"
```bash
# Common causes and solutions:
# 1. Paper trading accounts have limited symbol access
# 2. Try verified symbols: ES, NQ, RTY (E-mini futures)
# 3. Verify exchange: CME for most E-minis
# 4. Use manual fallback:
python3 realtime/level1_bbo_trades.py --underlying "ES" --symbol "ESZ5"
```

**Problem**: "Error parsing message" or protobuf errors
```bash
# Usually indicates network issues or malformed responses
# Script will continue running - this is expected behavior
# If persistent, check network connection and server status
```

**Problem**: "Subscription confirmed but no market data"
```bash
# For depth-by-order scripts, ensure proper sequence:
# 1. Updates subscription confirmed first
# 2. Snapshot request sent after confirmation  
# 3. Check if market is open and symbol is actively trading
```

### Multi-Contract Subscription Issues
**Problem**: Script hangs during multi-contract search
```bash
# Fallback to single symbol if search fails:
python3 realtime/level1_bbo_trades.py --symbol "ESZ5" --exchange "CME"

# Or specify manual symbol with underlying search:
python3 realtime/level3_depth_by_order.py --underlying "ES" --symbol "ESZ5"
```

### Performance Optimization
```bash
# For high-frequency data collection, use larger thresholds
python3 historical/historical_volume_bars.py --volume-specifier 1000
python3 historical/historical_range_bars.py --range-specifier 8

# For detailed analysis, use smaller thresholds
python3 historical/historical_volume_bars.py --volume-specifier 250
python3 historical/historical_range_bars.py --range-specifier 2
```

## Help and Documentation

### Get Help for Any Script
```bash
python3 discover_systems.py --help
python3 search/search_contracts.py --help
python3 realtime/level1_bbo_trades.py --help
python3 historical/historical_volume_bars.py --help
```

### Environment Variable Reference
See `.env.simple-demos` for complete list of available configuration options and their descriptions.

### Script Categories
- **Connection**: `discover_systems.py`
- **Search**: `search/search_contracts.py`
- **Real-time**: `realtime/level1_bbo_trades.py`, `level3_depth_by_order.py`
- **Historical**: `historical/historical_time_bars.py`, `historical_tick_bars.py`, `historical_volume_bars.py`, `historical_range_bars.py`

This guide provides comprehensive coverage of all simple-demos functionality with practical examples for efficient market data analysis and monitoring.