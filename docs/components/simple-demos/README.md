# Rithmic API Simple Demos

Ultra-minimal demonstration scripts for Rithmic R | Protocol API functions, organized by functionality.

## Prerequisites

```bash
pip install websockets protobuf python-dotenv
```

## Configuration

**IMPORTANT**: All environment variables in `.env.simple-demos` are REQUIRED. No default fallback values are provided.

Edit `.env.simple-demos` to configure symbols, patterns, and connection parameters. The configuration uses the same credentials as the main project. See the comprehensive BAR_TYPE_SPECIFIER documentation in the file for different aggregation options (time, tick, volume, range bars).

## Folder Structure

```
simple-demos/
├── shared.py                    # Common authentication/connection functions
├── .env.simple-demos           # Configuration file
├── README.md                   # This file
├── discover_systems.py         # Utility to discover available systems
├── search/
│   └── search_contracts.py     # Symbol search functionality
├── realtime/
│   ├── level1_bbo_trades.py    # Level 1: Best Bid/Offer + Last Trade
│   └── level3_depth_by_order.py # Level 3: Depth by Order (individual orders)
└── historical/
    ├── historical_time_bars.py   # OHLCV time bars
    ├── historical_tick_bars.py   # Tick bars
    ├── historical_volume_bars.py # Volume bars
    └── historical_range_bars.py  # Range bars
```

## Scripts

### Symbol Search
- **search/search_contracts.py** - Search for contracts using configurable pattern

### Real-time Market Data
- **realtime/level1_bbo_trades.py** - Level 1: Best Bid/Offer + Last Trade
- **realtime/level3_depth_by_order.py** - Level 3: Depth by Order (individual orders)

### Historical Data
- **historical/historical_time_bars.py** - OHLCV time bars
- **historical/historical_tick_bars.py** - Tick bars
- **historical/historical_volume_bars.py** - Volume bars
- **historical/historical_range_bars.py** - Range bars

## Usage

Run scripts from their respective directories:

```bash
# System discovery utility
python3 discover_systems.py

# Symbol search
cd search
python3 search_contracts.py

# Real-time market data
cd realtime
python3 level1_bbo_trades.py
python3 level3_depth_by_order.py

# Historical data
cd historical
python3 historical_time_bars.py
python3 historical_tick_bars.py
python3 historical_volume_bars.py
python3 historical_range_bars.py
```

## Notes

- **ALL environment variables are REQUIRED** - scripts will fail if any .env values are missing
- Scripts print raw protobuf responses with minimal processing
- Real-time scripts run until Ctrl+C
- Historical scripts exit after data retrieval
- Each script shows all available request fields for reference
- Uses correct Rithmic Paper Trading credentials from main .env
- All import paths updated for subfolder structure
- BAR_TYPE_SPECIFIER documentation in .env file explains aggregation options for different bar types