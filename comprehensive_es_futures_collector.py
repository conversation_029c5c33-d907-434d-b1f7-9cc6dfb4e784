#!/usr/bin/env python3

"""
COMPREHENSIVE ES FUTURES DATA COLLECTION SYSTEM
===============================================

Ultra-comprehensive market data collection system for ALL ES futures contracts
with BOTH Level 1 and Level 3 data simultaneously.

Features:
- Discovers ALL available ES futures contracts (ESU5, ESZ5, ESH6, etc.)
- Collects Level 1 data: Best Bid/Offer, Last Trade
- Collects Level 3 data: Full order book depth-by-order updates
- Concurrent multi-contract processing
- Robust error handling and automatic recovery
- Real-time database persistence
- Continuous 24/7 operation
- Performance monitoring and statistics

Architecture:
- Multi-contract discovery using wildcard patterns
- Concurrent WebSocket streams for each contract
- Message routing and processing pipeline
- Database integration with optimized schemas
- Heartbeat monitoring and connection management
"""

import asyncio
import logging
import signal
import sys
import os
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Set, Optional
import time

# Add project imports
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.rithmic_api.rithmic_websocket_client import RithmicWebSocketClient
from src.rithmic_api.config import config
from src.utils.contract_resolver import contract_resolver
from src.utils import MultiContractManager, contract_cache
from src.database.database_manager import get_database_manager

class ComprehensiveESFuturesCollector:
    """
    Ultra-comprehensive ES futures data collection system.
    
    Manages simultaneous Level 1 and Level 3 data collection
    for ALL available ES futures contracts.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self.multi_contract_manager = None
        self.db_manager = None
        
        # Collection state
        self.active_contracts = set()
        self.collection_stats = {
            'session_start': None,
            'contracts_discovered': 0,
            'level1_subscriptions': 0,
            'level3_subscriptions': 0,
            'total_messages_processed': 0,
            'last_trade_updates': 0,
            'best_bid_offer_updates': 0,
            'depth_by_order_updates': 0,
            'errors_encountered': 0,
            'last_update_time': None
        }
        
        # Configuration
        self.collection_config = {
            'target_product': 'ES',
            'include_level1': True,
            'include_level3': True,
            'max_contracts': 50,  # Safety limit
            'heartbeat_interval': 30,
            'stats_log_interval': 60,
            'auto_restart_on_error': True,
            'max_restart_attempts': 10
        }
        
        # Shutdown handling
        self.shutdown_requested = False
        self.restart_count = 0
        
    async def initialize(self):
        """Initialize the comprehensive collection system."""
        self.logger.info("🚀 INITIALIZING COMPREHENSIVE ES FUTURES COLLECTOR")
        self.logger.info("=" * 70)
        
        # Initialize client
        self.client = RithmicWebSocketClient()
        await self.client.initialize_data_collection()
        
        # Initialize multi-contract manager
        self.multi_contract_manager = MultiContractManager(self.client)
        
        # Initialize database manager
        self.db_manager = get_database_manager()
        
        # Set up contract resolver with client
        contract_resolver.client = self.client
        
        self.logger.info("✅ Comprehensive collector initialized successfully")
        
    async def discover_all_es_contracts(self) -> List[str]:
        """
        Discover ALL available ES futures contracts.
        
        Returns:
            List of discovered ES contract symbols
        """
        self.logger.info("🔍 DISCOVERING ALL ES FUTURES CONTRACTS")
        self.logger.info("-" * 50)
        
        try:
            # Use wildcard pattern to discover all ES contracts
            contract_specs = ['ES*']  # This will find all ES contracts
            
            # Resolve using the sophisticated contract resolver
            resolved_contracts = await contract_resolver.resolve_contract_list(
                contract_specs=contract_specs,
                max_contracts=self.collection_config['max_contracts']
            )
            
            # Extract contract symbols
            es_contracts = []
            for contract in resolved_contracts:
                if contract.product_code.upper() == 'ES':
                    es_contracts.append(contract.resolved_symbol)
            
            # Sort contracts for consistent ordering
            es_contracts.sort()
            
            self.collection_stats['contracts_discovered'] = len(es_contracts)
            
            self.logger.info(f"📊 DISCOVERED {len(es_contracts)} ES FUTURES CONTRACTS:")
            for i, contract in enumerate(es_contracts, 1):
                self.logger.info(f"   {i:2d}. {contract}")
            
            return es_contracts
            
        except Exception as e:
            self.logger.error(f"❌ Contract discovery failed: {e}")
            return []
    
    async def setup_system_connection(self) -> bool:
        """Set up system discovery and login."""
        self.logger.info("🔗 SETTING UP SYSTEM CONNECTION")
        self.logger.info("-" * 40)
        
        try:
            # System discovery
            self.logger.info("🌐 Discovering available systems...")
            systems = await self.client.discover_systems()
            
            if not systems:
                self.logger.error("❌ No systems discovered")
                return False
                
            self.logger.info(f"✅ Found {len(systems)} systems")
            
            # Login to ticker plant
            target_system = "Rithmic Paper Trading" if "Rithmic Paper Trading" in systems else systems[0]
            self.logger.info(f"🔑 Logging into {target_system} (TICKER_PLANT)...")
            
            login_success = await self.client.login(
                user="PP-013155",
                password="b7neA8k6JA",
                system_name=target_system,
                infra_type="TICKER_PLANT",
                app_name="ComprehensiveESCollector",
                app_version="1.0.0"
            )
            
            if login_success:
                self.logger.info("✅ System connection established successfully")
                return True
            else:
                self.logger.error("❌ Login failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ System connection error: {e}")
            return False
    
    async def subscribe_to_comprehensive_data(self, contracts: List[str]) -> bool:
        """
        Subscribe to both Level 1 and Level 3 data for all contracts.
        
        Args:
            contracts: List of contract symbols to subscribe to
            
        Returns:
            Success status
        """
        self.logger.info("📡 SUBSCRIBING TO COMPREHENSIVE DATA")
        self.logger.info("-" * 45)
        
        success_count = 0
        total_subscriptions = 0
        
        for contract in contracts:
            try:
                contract_success = True
                
                # Level 1 subscription (Best Bid/Offer, Last Trade)
                if self.collection_config['include_level1']:
                    self.logger.info(f"📊 Subscribing to Level 1 data for {contract}...")
                    level1_success = await self.client.subscribe_to_market_data(
                        symbol=contract,
                        exchange="CME"
                    )
                    if level1_success:
                        self.collection_stats['level1_subscriptions'] += 1
                        self.logger.info(f"✅ Level 1 subscription successful for {contract}")
                    else:
                        self.logger.warning(f"⚠️ Level 1 subscription failed for {contract}")
                        contract_success = False
                    total_subscriptions += 1
                
                # Level 3 subscription (Depth-by-Order)
                if self.collection_config['include_level3']:
                    self.logger.info(f"📋 Subscribing to Level 3 data for {contract}...")
                    level3_success = await self.client.subscribe_to_depth_by_order(
                        symbol=contract,
                        exchange="CME"
                    )
                    if level3_success:
                        self.collection_stats['level3_subscriptions'] += 1
                        self.logger.info(f"✅ Level 3 subscription successful for {contract}")
                    else:
                        self.logger.warning(f"⚠️ Level 3 subscription failed for {contract}")
                        contract_success = False
                    total_subscriptions += 1
                
                if contract_success:
                    self.active_contracts.add(contract)
                    success_count += 1
                    
            except Exception as e:
                self.logger.error(f"❌ Subscription error for {contract}: {e}")
                self.collection_stats['errors_encountered'] += 1
        
        self.logger.info("📊 SUBSCRIPTION SUMMARY")
        self.logger.info(f"   Total contracts processed: {len(contracts)}")
        self.logger.info(f"   Successful contract subscriptions: {success_count}")
        self.logger.info(f"   Level 1 subscriptions: {self.collection_stats['level1_subscriptions']}")
        self.logger.info(f"   Level 3 subscriptions: {self.collection_stats['level3_subscriptions']}")
        self.logger.info(f"   Active contracts: {len(self.active_contracts)}")
        
        return success_count > 0
    
    def setup_enhanced_message_processing(self):
        """Set up enhanced message processing for comprehensive data collection."""
        self.logger.info("⚙️  Setting up enhanced message processing...")
        
        # Store original processor
        original_processor = self.client.process_market_data_update
        
        def comprehensive_message_processor(template_id: int, message):
            """Comprehensive message processor for all data types."""
            try:
                self.collection_stats['total_messages_processed'] += 1
                self.collection_stats['last_update_time'] = datetime.now()
                
                # Identify message type and update statistics
                if template_id == 150:  # Last Trade
                    self.collection_stats['last_trade_updates'] += 1
                    symbol = getattr(message, 'symbol', 'Unknown')
                    price = getattr(message, 'trade_price', 'N/A')
                    size = getattr(message, 'trade_size', 'N/A')
                    self.logger.debug(f"📈 Trade: {symbol} @ {price} x {size}")
                    
                elif template_id == 151:  # Best Bid/Offer
                    self.collection_stats['best_bid_offer_updates'] += 1
                    symbol = getattr(message, 'symbol', 'Unknown')
                    bid = getattr(message, 'bid_price', 'N/A')
                    ask = getattr(message, 'ask_price', 'N/A')
                    self.logger.debug(f"📊 BBO: {symbol} {bid}/{ask}")
                    
                elif template_id == 160:  # Depth by Order
                    self.collection_stats['depth_by_order_updates'] += 1
                    symbol = getattr(message, 'symbol', 'Unknown')
                    self.logger.debug(f"📋 Depth: {symbol}")
                    
                elif template_id == 161:  # Depth by Order End Event
                    self.collection_stats['depth_by_order_updates'] += 1
                    symbol = getattr(message, 'symbol', 'Unknown')
                    self.logger.debug(f"🏁 Depth End: {symbol}")
                    
                elif template_id == 156:  # Order Book
                    self.collection_stats['depth_by_order_updates'] += 1
                    symbol = getattr(message, 'symbol', 'Unknown')
                    self.logger.debug(f"📖 Order Book: {symbol}")
                    
                elif template_id == 152:  # Trade Statistics
                    self.collection_stats['last_trade_updates'] += 1
                    symbol = getattr(message, 'symbol', 'Unknown')
                    self.logger.debug(f"📊 Trade Stats: {symbol}")
                    
                elif template_id == 155:  # End of Day Prices
                    self.collection_stats['best_bid_offer_updates'] += 1
                    symbol = getattr(message, 'symbol', 'Unknown')
                    self.logger.debug(f"🌅 EOD: {symbol}")
                
                # Call original processor for data persistence
                original_processor(template_id, message)
                
            except Exception as e:
                self.logger.error(f"❌ Message processing error: {e}")
                self.collection_stats['errors_encountered'] += 1
        
        # Replace the message processor
        self.client.process_market_data_update = comprehensive_message_processor
        self.logger.info("✅ Enhanced message processing configured")
    
    async def monitor_collection_performance(self):
        """Monitor and log collection performance statistics."""
        last_stats_log = time.time()
        last_heartbeat = time.time()
        
        while not self.shutdown_requested:
            try:
                current_time = time.time()
                
                # Heartbeat check
                if current_time - last_heartbeat >= self.collection_config['heartbeat_interval']:
                    self.logger.debug("💓 Heartbeat - Collection system active")
                    last_heartbeat = current_time
                
                # Statistics logging
                if current_time - last_stats_log >= self.collection_config['stats_log_interval']:
                    await self.log_performance_statistics()
                    last_stats_log = current_time
                
                # Database health check
                await self.check_database_health()
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"❌ Performance monitoring error: {e}")
                await asyncio.sleep(10)
    
    async def log_performance_statistics(self):
        """Log comprehensive performance statistics."""
        uptime = datetime.now() - self.collection_stats['session_start']
        
        self.logger.info("📊 COMPREHENSIVE COLLECTION STATISTICS")
        self.logger.info("-" * 50)
        self.logger.info(f"   Session Uptime: {uptime}")
        self.logger.info(f"   Active Contracts: {len(self.active_contracts)}")
        self.logger.info(f"   Total Messages: {self.collection_stats['total_messages_processed']:,}")
        self.logger.info(f"   Last Trade Updates: {self.collection_stats['last_trade_updates']:,}")
        self.logger.info(f"   BBO Updates: {self.collection_stats['best_bid_offer_updates']:,}")
        self.logger.info(f"   Depth Updates: {self.collection_stats['depth_by_order_updates']:,}")
        self.logger.info(f"   Errors: {self.collection_stats['errors_encountered']}")
        
        if self.collection_stats['last_update_time']:
            time_since_last = datetime.now() - self.collection_stats['last_update_time']
            self.logger.info(f"   Last Update: {time_since_last.total_seconds():.1f}s ago")
    
    async def check_database_health(self):
        """Check database connectivity and recent data."""
        try:
            # Simple connectivity check
            if self.db_manager:
                # Check if we can execute a simple query
                test_query = "SELECT 1"
                result = self.db_manager.execute_query(test_query)
                if result is None and self.collection_stats['total_messages_processed'] > 100:
                    self.logger.warning("⚠️ Database connectivity issues detected")
        except Exception as e:
            self.logger.error(f"❌ Database health check failed: {e}")
    
    async def run_comprehensive_collection(self, duration_seconds: Optional[int] = None):
        """
        Run the comprehensive ES futures data collection.
        
        Args:
            duration_seconds: Collection duration (None for indefinite)
        """
        self.collection_stats['session_start'] = datetime.now()
        
        self.logger.info("🎯 STARTING COMPREHENSIVE ES FUTURES DATA COLLECTION")
        self.logger.info("=" * 70)
        self.logger.info(f"📅 Start Time: {self.collection_stats['session_start']}")
        self.logger.info(f"🎯 Target: ALL ES Futures Contracts")
        self.logger.info(f"📊 Data Types: Level 1 + Level 3")
        self.logger.info(f"⏱️ Duration: {'Indefinite' if not duration_seconds else f'{duration_seconds}s'}")
        self.logger.info("=" * 70)
        
        try:
            # Step 1: System setup
            if not await self.setup_system_connection():
                return False
            
            # Step 2: Contract discovery
            es_contracts = await self.discover_all_es_contracts()
            if not es_contracts:
                self.logger.error("❌ No ES contracts discovered")
                return False
            
            # Step 3: Comprehensive subscriptions
            if not await self.subscribe_to_comprehensive_data(es_contracts):
                self.logger.error("❌ Failed to establish any subscriptions")
                return False
            
            # Step 4: Enhanced message processing
            self.setup_enhanced_message_processing()
            
            # Step 5: Start monitoring
            monitor_task = asyncio.create_task(self.monitor_collection_performance())
            
            # Step 6: Data collection
            self.logger.info("🚀 COMPREHENSIVE DATA COLLECTION ACTIVE")
            self.logger.info(f"📡 Collecting from {len(self.active_contracts)} ES contracts")
            self.logger.info("💾 Data persisted to database and files")
            self.logger.info("📊 Real-time statistics every 60 seconds")
            
            try:
                # Listen for updates
                await self.client.listen_for_updates(duration=duration_seconds)
            except KeyboardInterrupt:
                self.logger.info("🛑 Data collection interrupted by user")
            finally:
                monitor_task.cancel()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Comprehensive collection error: {e}")
            return False
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """Clean up resources."""
        self.logger.info("🧹 Cleaning up resources...")
        
        if self.multi_contract_manager:
            await self.multi_contract_manager.unsubscribe_all()
        
        if self.client:
            await self.client.disconnect()
        
        # Database manager cleanup is handled automatically
        
        # Final statistics
        await self.log_performance_statistics()
        self.logger.info("✅ Cleanup completed")


async def main():
    """Main function to run comprehensive ES futures data collection."""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('comprehensive_es_collection.log'),
            logging.StreamHandler()
        ]
    )
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print("\n🛑 Shutdown requested...")
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Parse command line arguments
    duration = None
    if len(sys.argv) > 1:
        try:
            duration = int(sys.argv[1])
            print(f"⏱️ Collection duration: {duration} seconds")
        except ValueError:
            print("❌ Invalid duration. Using indefinite collection.")
    
    # Create and run collector
    collector = ComprehensiveESFuturesCollector()
    
    try:
        await collector.initialize()
        success = await collector.run_comprehensive_collection(duration_seconds=duration)
        
        if success:
            print("✅ Comprehensive ES futures data collection completed!")
            return 0
        else:
            print("❌ Comprehensive ES futures data collection failed!")
            return 1
            
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Collection interrupted")
        sys.exit(0)