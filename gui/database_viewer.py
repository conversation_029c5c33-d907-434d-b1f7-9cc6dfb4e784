#!/usr/bin/env python3
"""
Database Viewer - Reimplement from scratch
Professional futures trading database viewer with robust architecture
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import sys
import os
from datetime import datetime
import csv
import logging
from typing import Optional, Dict, Any, List

# Add src to path for imports
sys.path.append('src')
sys.path.append(os.getcwd())

# Import database logging infrastructure
try:
    from src.utils.database_logger import get_database_logger, LogCategory
    DATABASE_LOGGING_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Database logging not available: {e}")
    DATABASE_LOGGING_AVAILABLE = False

# Setup standard logging (fallback)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Initialize database logger for database viewer operations
db_logger = None
if DATABASE_LOGGING_AVAILABLE:
    try:
        db_logger = get_database_logger('database_viewer')
        db_logger.info(
            "Database Viewer initialized with database logging",
            category=LogCategory.SYSTEM,
            component="database_viewer"
        )
    except Exception as e:
        logger.warning(f"Failed to initialize database logging: {e}")
        db_logger = None

class DatabaseViewer:
    """
    Self-contained database viewer with clean architecture.
    
    Key Design Principles:
    - Single UI creation (no widget destruction/recreation)
    - Clear state management with status indicators
    - Separation of database logic from UI logic
    - Comprehensive error handling
    - No timing-dependent operations
    """
    
    # Connection states
    DISCONNECTED = "DISCONNECTED"
    CONNECTING = "CONNECTING" 
    CONNECTED = "CONNECTED"
    ERROR = "ERROR"
    
    def __init__(self, parent_frame, gui_controller):
        """Initialize the database viewer."""
        self.parent_frame = parent_frame
        self.gui_controller = gui_controller
        
        # Database connection
        self.db_manager = None
        self.connection_status = self.DISCONNECTED
        
        # Data management
        self.current_table = None
        self.current_page = 0
        self.total_pages = 0
        self.page_size = 100
        self.current_filter = ""
        
        # UI Components (will be created once)
        self.status_frame = None
        self.status_label = None
        self.connect_btn = None
        self.disconnect_btn = None
        self.tables_tree = None
        self.data_tree = None
        self.data_tree_frame = None
        self.page_info_label = None
        self.page_size_var = None
        
        # Create the complete UI immediately
        self.create_ui()
        
    def create_ui(self):
        """Create the complete database viewer UI once. Never destroy these widgets."""
        # Clear any existing content
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
            
        # Status bar at top
        self.create_status_bar()
        
        # Main content paned window
        main_paned = ttk.PanedWindow(self.parent_frame, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Tables
        self.create_tables_panel(main_paned)
        
        # Right panel - Data viewer
        self.create_data_panel(main_paned)
        
        # Initialize to disconnected state
        self.update_connection_status(self.DISCONNECTED)
        
    def create_status_bar(self):
        """Create status bar with connection controls."""
        self.status_frame = ttk.Frame(self.parent_frame)
        self.status_frame.pack(fill='x', padx=10, pady=(10, 5))
        
        # Status label
        self.status_label = ttk.Label(self.status_frame, text="🔴 Disconnected", 
                                     font=('Arial', 10, 'bold'))
        self.status_label.pack(side='left')
        
        # Connection buttons
        button_frame = ttk.Frame(self.status_frame)
        button_frame.pack(side='right')
        
        self.connect_btn = ttk.Button(button_frame, text="🔌 Connect", 
                                     command=self.connect_to_database,
                                     style='Green.TButton')
        self.connect_btn.pack(side='left', padx=(0, 5))
        
        self.disconnect_btn = ttk.Button(button_frame, text="🔌 Disconnect", 
                                        command=self.disconnect_from_database,
                                        style='Red.TButton')
        self.disconnect_btn.pack(side='left')
        
    def create_tables_panel(self, parent_paned):
        """Create the tables list panel."""
        tables_frame = ttk.LabelFrame(parent_paned, text="📋 Database Tables", padding=10)
        parent_paned.add(tables_frame, weight=1)
        
        # Table controls
        table_controls = ttk.Frame(tables_frame)
        table_controls.pack(fill='x', pady=(0, 10))
        
        ttk.Button(table_controls, text="🔄 Refresh", 
                  command=self.refresh_tables).pack(side='left', padx=(0, 5))
        ttk.Button(table_controls, text="📊 Schema", 
                  command=self.show_table_schema).pack(side='left', padx=(0, 5))
        ttk.Button(table_controls, text="📄 Code", 
                  command=self.show_query_code).pack(side='left', padx=(0, 5))
        
        # Tables treeview
        self.tables_tree = ttk.Treeview(tables_frame, columns=('rows',), height=15)
        self.tables_tree.heading('#0', text='Table Name', anchor='w')
        self.tables_tree.heading('rows', text='Row Count', anchor='center')
        self.tables_tree.column('#0', width=200)
        self.tables_tree.column('rows', width=100)
        
        # Tables scrollbar
        tables_scrollbar = ttk.Scrollbar(tables_frame, orient="vertical", 
                                        command=self.tables_tree.yview)
        self.tables_tree.configure(yscrollcommand=tables_scrollbar.set)
        
        self.tables_tree.pack(side='left', fill='both', expand=True)
        tables_scrollbar.pack(side='right', fill='y')
        
        # Bind table selection
        self.tables_tree.bind('<<TreeviewSelect>>', self.on_table_select)
        
    def create_data_panel(self, parent_paned):
        """Create the data viewer panel."""
        data_frame = ttk.LabelFrame(parent_paned, text="📊 Table Data", padding=10)
        parent_paned.add(data_frame, weight=3)
        
        # Data controls
        data_controls = ttk.Frame(data_frame)
        data_controls.pack(fill='x', pady=(0, 10))
        
        # Pagination controls
        ttk.Label(data_controls, text="Rows per page:").pack(side='left', padx=(0, 5))
        self.page_size_var = tk.StringVar(value="100")
        page_size_combo = ttk.Combobox(data_controls, textvariable=self.page_size_var,
                                      values=['50', '100', '500', '1000'], width=8, state='readonly')
        page_size_combo.pack(side='left', padx=(0, 10))
        page_size_combo.bind('<<ComboboxSelected>>', self.on_page_size_change)
        
        ttk.Button(data_controls, text="⬅️ Previous", 
                  command=self.previous_page).pack(side='left', padx=(0, 5))
        ttk.Button(data_controls, text="➡️ Next", 
                  command=self.next_page).pack(side='left', padx=(0, 5))
        
        self.page_info_label = ttk.Label(data_controls, text="Page 0 of 0")
        self.page_info_label.pack(side='left', padx=(10, 10))
        
        # Data management controls
        ttk.Button(data_controls, text="📤 Export CSV", 
                  command=self.export_table_data).pack(side='right', padx=(5, 0))
        ttk.Button(data_controls, text="🔍 Filter", 
                  command=self.show_data_filter).pack(side='right', padx=(5, 0))
        
        # Data treeview container
        self.data_tree_frame = ttk.Frame(data_frame)
        self.data_tree_frame.pack(fill='both', expand=True)
        
        # Create placeholder data tree (will be recreated when table is selected)
        self.create_data_tree([])
        
    def create_data_tree(self, columns):
        """Create or recreate the data treeview with specific columns."""
        # Clear existing data tree
        for widget in self.data_tree_frame.winfo_children():
            widget.destroy()
            
        if not columns:
            # No table selected - show placeholder
            placeholder_label = ttk.Label(self.data_tree_frame, text="Select a table to view data",
                                         foreground='gray', font=('Arial', 12))
            placeholder_label.pack(expand=True)
            self.data_tree = None
            return
            
        # Create new data treeview with specified columns
        self.data_tree = ttk.Treeview(self.data_tree_frame, columns=columns, show='headings')
        
        # Configure columns
        for col in columns:
            self.data_tree.heading(col, text=col, anchor='w')
            self.data_tree.column(col, width=150, minwidth=50)
            
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(self.data_tree_frame, orient="vertical", 
                                   command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(self.data_tree_frame, orient="horizontal", 
                                   command=self.data_tree.xview)
        
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.data_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Configure grid weights
        self.data_tree_frame.grid_rowconfigure(0, weight=1)
        self.data_tree_frame.grid_columnconfigure(0, weight=1)
        
    def update_connection_status(self, status, message=""):
        """Update connection status and enable/disable widgets accordingly."""
        self.connection_status = status
        
        if status == self.DISCONNECTED:
            self.status_label.config(text="🔴 Disconnected", foreground='red')
            self.connect_btn.config(state='normal')
            self.disconnect_btn.config(state='disabled')
            self.enable_data_widgets(False)
            
        elif status == self.CONNECTING:
            self.status_label.config(text="🟡 Connecting...", foreground='orange')
            self.connect_btn.config(state='disabled')
            self.disconnect_btn.config(state='disabled')
            self.enable_data_widgets(False)
            
        elif status == self.CONNECTED:
            self.status_label.config(text=f"🟢 Connected: {message}", foreground='green')
            self.connect_btn.config(state='disabled')
            self.disconnect_btn.config(state='normal')
            self.enable_data_widgets(True)
            # Automatically refresh tables when connected
            self.refresh_tables()
            
        elif status == self.ERROR:
            self.status_label.config(text=f"❌ Error: {message}", foreground='red')
            self.connect_btn.config(state='normal')
            self.disconnect_btn.config(state='disabled')
            self.enable_data_widgets(False)
            
    def enable_data_widgets(self, enabled):
        """Enable or disable data-related widgets."""
        state = 'normal' if enabled else 'disabled'
        
        # Find all buttons in tables_tree parent and data panel
        if hasattr(self, 'tables_tree') and self.tables_tree:
            for widget in self.tables_tree.master.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Button):
                            child.config(state=state)
                            
        # Data panel controls
        if hasattr(self, 'data_tree_frame') and self.data_tree_frame:
            for widget in self.data_tree_frame.master.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, (ttk.Button, ttk.Combobox)):
                            child.config(state=state)
                            
    def get_database_config(self):
        """Get database configuration from GUI settings."""
        if not hasattr(self.gui_controller, 'env_vars') or not self.gui_controller.env_vars:
            raise Exception("Database configuration not available. Please configure settings in Configuration tab.")
            
        try:
            config = {
                'host': self.gui_controller.env_vars['MYSQL_HOST']['entry'].get().strip(),
                'port': int(self.gui_controller.env_vars['MYSQL_PORT']['entry'].get().strip()),
                'user': self.gui_controller.env_vars['MYSQL_USER']['entry'].get().strip(),
                'password': self.gui_controller.env_vars['MYSQL_PASSWORD']['entry'].get().strip(),
                'database': self.gui_controller.env_vars['MYSQL_DATABASE']['entry'].get().strip(),
                'pool_size': 5,
                'max_overflow': 10,
                'pool_timeout': 30,
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
            }
            
            # Validate required fields
            required_fields = ['host', 'user', 'password', 'database']
            for field in required_fields:
                if not config[field]:
                    raise Exception(f"Missing required field: {field}")
                    
            return config
            
        except Exception as e:
            raise Exception(f"Invalid database configuration: {str(e)}")
            
    def connect_to_database(self):
        """Connect to the database."""
        try:
            self.update_connection_status(self.CONNECTING)
            self.log_message("🔌 Connecting to database...")
            
            # Get database configuration
            config = self.get_database_config()
            
            # Create database manager
            from src.database.database_manager import DatabaseManager
            self.db_manager = DatabaseManager(config)
            
            # Test connection
            if self.db_manager.test_connection():
                db_info = f"{config['database']}@{config['host']}"
                self.update_connection_status(self.CONNECTED, db_info)
                
                # Log successful database connection
                if db_logger and db_logger.is_enabled():
                    db_logger.info(
                        f"Database viewer connected to database",
                        category=LogCategory.DATABASE,
                        component="database_viewer",
                        operation="database_connect",
                        host=config['host'],
                        database=config['database'],
                        user=config['user']
                    )
                
                self.log_message(f"✅ Connected to database: {db_info}")
                messagebox.showinfo("Connected", f"Successfully connected to database:\\n{db_info}")
            else:
                raise Exception("Connection test failed")
                
        except Exception as e:
            error_msg = str(e)
            
            # Log database connection failure
            if db_logger and db_logger.is_enabled():
                db_logger.error(
                    f"Database viewer connection failed: {error_msg}",
                    category=LogCategory.ERROR,
                    component="database_viewer",
                    operation="database_connect",
                    error=error_msg
                )
            
            self.update_connection_status(self.ERROR, error_msg)
            self.log_message(f"❌ Database connection failed: {error_msg}")
            messagebox.showerror("Connection Failed", f"Failed to connect to database:\\n{error_msg}")
            
    def disconnect_from_database(self):
        """Disconnect from the database."""
        try:
            if self.db_manager:
                self.db_manager.close()
                self.db_manager = None
                
            self.current_table = None
            self.current_page = 0
            self.total_pages = 0
            
            # Clear tables list
            if self.tables_tree:
                for item in self.tables_tree.get_children():
                    self.tables_tree.delete(item)
                    
            # Clear data view
            self.create_data_tree([])
            
            self.update_connection_status(self.DISCONNECTED)
            self.log_message("🔌 Disconnected from database")
            
        except Exception as e:
            self.log_message(f"⚠️ Error during disconnect: {str(e)}")
            
    def refresh_tables(self):
        """Refresh the list of database tables."""
        if not self.db_manager or self.connection_status != self.CONNECTED:
            self.log_message("⚠️ Not connected to database")
            return
            
        try:
            self.log_message("🔄 Refreshing database tables...")
            
            # Clear existing items
            for item in self.tables_tree.get_children():
                self.tables_tree.delete(item)
                
            # Get list of tables
            query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                ORDER BY table_name
            """
            tables = self.db_manager.execute_query(query)
            self.log_message(f"📊 Found {len(tables)} tables in database")
            
            # Get row counts for each table
            for table_info in tables:
                # Handle both uppercase and lowercase column names
                table_name = table_info.get('table_name') or table_info.get('TABLE_NAME')
                if not table_name:
                    continue
                    
                try:
                    row_count = self.db_manager.get_table_row_count(table_name)
                    self.tables_tree.insert('', 'end', text=table_name, values=(f"{row_count:,}",))
                    self.log_message(f"📋 Added table: {table_name} ({row_count:,} rows)")
                except Exception as e:
                    self.tables_tree.insert('', 'end', text=table_name, values=("Error",))
                    self.log_message(f"⚠️ Error getting row count for {table_name}: {str(e)}")
                    
            self.log_message(f"✅ Successfully refreshed {len(tables)} database tables")
            
        except Exception as e:
            error_msg = f"Error refreshing tables: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Refresh Failed", error_msg)
            
    def on_table_select(self, event):
        """Handle table selection in the treeview."""
        if not self.tables_tree or not self.tables_tree.selection():
            return
            
        try:
            selection = self.tables_tree.selection()
            if selection:
                item = self.tables_tree.item(selection[0])
                table_name = item['text']
                if table_name and table_name != self.current_table:
                    self.current_table = table_name
                    self.current_page = 0
                    self.log_message(f"📋 Selected table: {table_name}")
                    self.load_table_data()
                    
        except Exception as e:
            self.log_message(f"⚠️ Error selecting table: {str(e)}")
            
    def load_table_data(self):
        """Load data for the selected table with pagination."""
        if not self.current_table or not self.db_manager:
            return
            
        try:
            self.log_message(f"📊 Loading data for table: {self.current_table}")
            
            page_size = int(self.page_size_var.get())
            offset = self.current_page * page_size
            
            # Get total row count for pagination
            total_rows = self.db_manager.get_table_row_count(self.current_table)
            self.total_pages = max(1, (total_rows + page_size - 1) // page_size)
            
            # Get table columns
            columns = self.db_manager.get_table_columns(self.current_table)
            column_names = []
            for col in columns:
                # Handle both uppercase and lowercase column names
                col_name = col.get('column_name') or col.get('COLUMN_NAME')
                if col_name:
                    column_names.append(col_name)
                    
            # Create data treeview with these columns
            self.create_data_tree(column_names)
            
            # Build query with filter if needed
            base_query = f"SELECT * FROM `{self.current_table}`"
            if self.current_filter:
                query = f"{base_query} WHERE {self.current_filter} LIMIT {page_size} OFFSET {offset}"
            else:
                query = f"{base_query} LIMIT {page_size} OFFSET {offset}"
                
            # Execute query
            data = self.db_manager.execute_query(query)
            
            # Insert data into treeview
            if self.data_tree:
                for row in data:
                    # Convert row data to list of strings
                    row_values = []
                    for col_name in column_names:
                        value = row.get(col_name)
                        if value is None:
                            row_values.append("")
                        else:
                            row_values.append(str(value)[:100])  # Truncate long values
                            
                    self.data_tree.insert('', 'end', values=row_values)
                    
            # Update pagination info
            self.page_info_label.config(text=f"Page {self.current_page + 1} of {self.total_pages}")
            
            self.log_message(f"✅ Loaded {len(data)} rows for {self.current_table} (page {self.current_page + 1}/{self.total_pages})")
            
        except Exception as e:
            error_msg = f"Error loading table data: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Load Failed", error_msg)
            
    def on_page_size_change(self, event):
        """Handle page size change."""
        if self.current_table:
            self.current_page = 0  # Reset to first page
            self.load_table_data()
            
    def previous_page(self):
        """Navigate to previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_table_data()
            
    def next_page(self):
        """Navigate to next page."""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.load_table_data()
            
    def export_table_data(self):
        """Export current table data to CSV."""
        if not self.current_table or not self.db_manager:
            messagebox.showwarning("No Data", "Please select a table first.")
            return
            
        try:
            # Get filename from user
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"{self.current_table}_{timestamp}.csv"
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialvalue=default_filename
            )
            
            if not filename:
                return
                
            self.log_message(f"📤 Exporting {self.current_table} to {filename}...")
            
            # Get all data for the table (not just current page)
            base_query = f"SELECT * FROM `{self.current_table}`"
            if self.current_filter:
                query = f"{base_query} WHERE {self.current_filter}"
            else:
                query = base_query
                
            data = self.db_manager.execute_query(query)
            
            if not data:
                messagebox.showinfo("No Data", "No data to export.")
                return
                
            # Write to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                # Get column names from first row
                column_names = list(data[0].keys())
                writer = csv.DictWriter(csvfile, fieldnames=column_names)
                
                writer.writeheader()
                writer.writerows(data)
                
            # Log successful export to database
            if db_logger and db_logger.is_enabled():
                db_logger.info(
                    f"Database table exported to CSV",
                    category=LogCategory.EXPORT,
                    component="database_viewer",
                    operation="table_export",
                    table_name=self.current_table,
                    file_path=filename,
                    record_count=len(data),
                    export_format="CSV",
                    filter_applied=bool(self.current_filter)
                )
            
            self.log_message(f"✅ Exported {len(data)} rows to {filename}")
            messagebox.showinfo("Export Complete", f"Exported {len(data)} rows to:\\n{filename}")
            
        except Exception as e:
            error_msg = f"Error exporting data: {str(e)}"
            
            # Log export error to database
            if db_logger and db_logger.is_enabled():
                db_logger.error(
                    f"Database table export failed: {error_msg}",
                    category=LogCategory.ERROR,
                    component="database_viewer",
                    operation="table_export",
                    table_name=self.current_table,
                    error=str(e)
                )
            
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Export Failed", error_msg)
            
    def show_table_schema(self):
        """Show schema information for selected table."""
        if not self.current_table:
            messagebox.showwarning("No Table", "Please select a table first.")
            return
            
        try:
            # Get column information
            query = """
                SELECT column_name, data_type, is_nullable, column_default, column_key
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() AND table_name = %s
                ORDER BY ordinal_position
            """
            columns = self.db_manager.execute_query(query, (self.current_table,))
            
            # Format schema information
            schema_text = f"Schema for table: {self.current_table}\\n\\n"
            schema_text += f"{'Column':<20} {'Type':<15} {'Null':<8} {'Key':<8} {'Default':<15}\\n"
            schema_text += "-" * 80 + "\\n"
            
            for col in columns:
                col_name = col.get('column_name') or col.get('COLUMN_NAME')
                data_type = col.get('data_type') or col.get('DATA_TYPE')
                is_nullable = col.get('is_nullable') or col.get('IS_NULLABLE')
                column_key = col.get('column_key') or col.get('COLUMN_KEY')
                column_default = col.get('column_default') or col.get('COLUMN_DEFAULT') or ""
                
                schema_text += f"{col_name:<20} {data_type:<15} {is_nullable:<8} {column_key:<8} {str(column_default):<15}\\n"
                
            # Show in dialog
            self.show_text_dialog("Table Schema", schema_text)
            
        except Exception as e:
            error_msg = f"Error getting schema: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Schema Error", error_msg)
            
    def show_data_filter(self):
        """Show data filter dialog."""
        if not self.current_table:
            messagebox.showwarning("No Table", "Please select a table first.")
            return
            
        # Simple filter dialog
        filter_text = tk.simpledialog.askstring(
            "Data Filter",
            f"Enter WHERE clause for {self.current_table}:\\n(e.g., column_name > 100)",
            initialvalue=self.current_filter
        )
        
        if filter_text is not None:  # User didn't cancel
            self.current_filter = filter_text.strip()
            self.current_page = 0  # Reset to first page
            self.log_message(f"🔍 Applied filter: {self.current_filter or '(none)'}")
            self.load_table_data()
            
    def show_query_code(self):
        """Show enhanced multi-threaded query code for current table."""
        if not self.current_table:
            messagebox.showwarning("No Table", "Please select a table first.")
            return
            
        # Create enhanced query parameters dialog
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("Multi-Threaded Query Script Generator")
        dialog.geometry("500x400")
        
        # Main frame
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text=f"Generate Script for Table: {self.current_table}", 
                               font=('Arial', 12, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Threading strategy
        ttk.Label(main_frame, text="Threading Strategy:", font=('Arial', 10, 'bold')).pack(anchor='w')
        threading_var = tk.StringVar(value="ThreadPoolExecutor")
        threading_frame = ttk.Frame(main_frame)
        threading_frame.pack(fill='x', pady=(5, 15))
        
        ttk.Radiobutton(threading_frame, text="ThreadPoolExecutor (Recommended)", 
                       variable=threading_var, value="ThreadPoolExecutor").pack(anchor='w')
        ttk.Radiobutton(threading_frame, text="ProcessPoolExecutor (CPU Intensive)", 
                       variable=threading_var, value="ProcessPoolExecutor").pack(anchor='w')
        ttk.Radiobutton(threading_frame, text="Asyncio (I/O Bound)", 
                       variable=threading_var, value="asyncio").pack(anchor='w')
        
        # Parameters frame
        params_frame = ttk.LabelFrame(main_frame, text="Query Parameters", padding=10)
        params_frame.pack(fill='x', pady=(0, 15))
        
        # Chunk size
        ttk.Label(params_frame, text="Chunk Size (rows per thread):").grid(row=0, column=0, sticky='w', pady=2)
        chunk_size_var = tk.StringVar(value="10000")
        ttk.Entry(params_frame, textvariable=chunk_size_var, width=15).grid(row=0, column=1, sticky='w', padx=(10, 0))
        
        # Max workers
        ttk.Label(params_frame, text="Max Workers:").grid(row=1, column=0, sticky='w', pady=2)
        max_workers_var = tk.StringVar(value="os.cpu_count()")
        ttk.Entry(params_frame, textvariable=max_workers_var, width=15).grid(row=1, column=1, sticky='w', padx=(10, 0))
        
        # Where clause
        ttk.Label(params_frame, text="WHERE Clause (optional):").grid(row=2, column=0, sticky='w', pady=2)
        where_var = tk.StringVar(value=self.current_filter)
        ttk.Entry(params_frame, textvariable=where_var, width=30).grid(row=2, column=1, sticky='w', padx=(10, 0))
        
        # Export CSV option
        export_csv_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_frame, text="Export results to CSV", 
                       variable=export_csv_var).grid(row=3, column=0, columnspan=2, sticky='w', pady=5)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        def generate_script():
            """Generate the multi-threaded script."""
            try:
                # Validate chunk size
                try:
                    chunk_size = int(chunk_size_var.get())
                    if chunk_size <= 0:
                        raise ValueError("Chunk size must be positive")
                except ValueError:
                    messagebox.showerror("Invalid Input", "Chunk size must be a positive integer")
                    return
                
                # Prepare query parameters
                query_params = {
                    'chunk_size': chunk_size,
                    'where_clause': where_var.get().strip(),
                    'export_csv': export_csv_var.get()
                }
                
                # Get code generator from GUI controller
                if hasattr(self.gui_controller, 'code_generator'):
                    code_generator = self.gui_controller.code_generator
                else:
                    # Create temporary code generator
                    from es_futures_gui_controller import CodeGenerator
                    code_generator = CodeGenerator(self.gui_controller)
                
                # Generate enhanced multi-threaded script
                code = code_generator.generate_multi_threaded_query_script(
                    table_name=self.current_table,
                    query_params=query_params,
                    threading_strategy=threading_var.get(),
                    max_workers=max_workers_var.get()
                )
                
                dialog.destroy()
                
                # Show the generated code
                self.show_text_dialog(
                    title=f"Multi-Threaded Query Script - {self.current_table}",
                    text=code,
                    save_filename=f"multi_threaded_query_{self.current_table}.py"
                )
                
                self.log_message(f"✅ Generated multi-threaded query script for {self.current_table}")
                
            except Exception as e:
                error_msg = f"Error generating script: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("Generation Error", error_msg)
        
        def generate_simple_script():
            """Generate a simple single-threaded script for comparison."""
            try:
                # Simple script for comparison
                code = f'''#!/usr/bin/env python3
"""
Simple Single-Threaded Database Query Script for {self.current_table}
Generated for comparison with multi-threaded version
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import mysql.connector
import csv

# Add src directory to path
script_dir = Path(__file__).parent
src_dir = script_dir / 'src'
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

try:
    from database.database_manager import DatabaseManager
except ImportError:
    print("Warning: DatabaseManager not available")

def get_database_config():
    """Get database configuration."""
    return {{
        'host': os.getenv('MYSQL_HOST', 'your_host'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', 'your_user'),
        'password': os.getenv('MYSQL_PASSWORD', 'your_password'),
        'database': os.getenv('MYSQL_DATABASE', 'your_database')
    }}

def query_{self.current_table}():
    """Query data from {self.current_table} table (single-threaded)."""
    
    print(f"🔍 Starting simple query for table: {self.current_table}")
    print("=" * 50)
    
    # Database configuration
    config = get_database_config()
    
    try:
        # Create database manager
        db_manager = DatabaseManager(config)
        
        # Test connection
        if not db_manager.test_connection():
            print("❌ Failed to connect to database")
            return False
            
        print("✅ Database connection successful")
        
        # Build query
        query = "SELECT * FROM `{self.current_table}`"
        {f'query += " WHERE {where_var.get().strip()}"' if where_var.get().strip() else '# No filter applied'}
        
        print(f"📊 Executing query: {{query[:100]}}{'...' if len(query) > 100 else ''}")
        
        # Execute query
        start_time = datetime.now()
        data = db_manager.execute_query(query)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"✅ Retrieved {{len(data)}} rows in {{execution_time:.2f}} seconds")
        
        # Show sample data
        if data:
            print("\\nSample data (first 3 rows):")
            for i, row in enumerate(data[:3]):
                print(f"Row {{i+1}}: {{dict(list(row.items())[:5])}}...")  # Show first 5 columns
                
        # Export to CSV if requested
        if {str(export_csv_var.get()).lower()} and data:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"{self.current_table}_simple_{{timestamp}}.csv"
            
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                if data:
                    fieldnames = list(data[0].keys())
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
                    
            print(f"📄 Exported to {{csv_filename}}")
            
        print("\\n" + "=" * 50)
        print("📊 SUMMARY")
        print(f"Rows retrieved: {{len(data):,}}")
        print(f"Execution time: {{execution_time:.2f}} seconds")
        print(f"Rows per second: {{len(data)/max(1,execution_time):,.0f}}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during query: {{e}}")
        return False

if __name__ == "__main__":
    success = query_{self.current_table}()
    print("\\n✅ Query completed successfully" if success else "\\n❌ Query failed")
'''
                
                dialog.destroy()
                
                # Show the simple code
                self.show_text_dialog(
                    title=f"Simple Query Script - {self.current_table}",
                    text=code,
                    save_filename=f"simple_query_{self.current_table}.py"
                )
                
                self.log_message(f"✅ Generated simple query script for {self.current_table}")
                
            except Exception as e:
                error_msg = f"Error generating simple script: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("Generation Error", error_msg)
        
        # Buttons
        ttk.Button(buttons_frame, text="🚀 Generate Multi-Threaded Script", 
                  command=generate_script, style='Green.TButton').pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="📝 Generate Simple Script", 
                  command=generate_simple_script).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="Cancel", command=dialog.destroy).pack(side='right')
        
    def show_text_dialog(self, title, text, save_filename=None):
        """Show text in a dialog window with copy and save functionality."""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title(title)
        dialog.geometry("900x700")
        
        # Main frame
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Text widget with scrollbar
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill='both', expand=True)
        
        text_widget = tk.Text(text_frame, wrap=tk.NONE, font=('Consolas', 10), 
                             bg='#f8f9fa', fg='#2c3e50')
        v_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient="horizontal", command=text_widget.xview)
        text_widget.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout for scrollbars
        text_widget.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        
        text_widget.insert('1.0', text)
        text_widget.config(state='disabled')
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        def copy_to_clipboard():
            """Copy text to clipboard."""
            try:
                dialog.clipboard_clear()
                dialog.clipboard_append(text)
                self.log_message("✅ Code copied to clipboard")
                messagebox.showinfo("Copied", "Code copied to clipboard!")
            except Exception as e:
                self.log_message(f"❌ Error copying to clipboard: {str(e)}")
                messagebox.showerror("Copy Error", f"Failed to copy to clipboard: {str(e)}")
        
        def save_to_file():
            """Save text to file."""
            try:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".py",
                    filetypes=[("Python files", "*.py"), ("All files", "*.*")],
                    initialvalue=save_filename or "generated_script.py"
                )
                
                if filename:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(text)
                    self.log_message(f"✅ Code saved to {filename}")
                    messagebox.showinfo("Saved", f"Code saved to:\n{filename}")
                    
            except Exception as e:
                error_msg = f"Failed to save file: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("Save Error", error_msg)
        
        # Buttons
        ttk.Button(buttons_frame, text="📋 Copy to Clipboard", 
                  command=copy_to_clipboard).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="💾 Save to File", 
                  command=save_to_file).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="Close", 
                  command=dialog.destroy).pack(side='right')
        
    def log_message(self, message):
        """Log message to GUI controller."""
        if hasattr(self.gui_controller, 'log_message'):
            self.gui_controller.log_message(message)
        else:
            print(message)