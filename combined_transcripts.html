<!DOCTYPE html>
<html lang='en'>

<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Claude Transcripts - RProtocolAPI.******** (from yesterday to today)</title>
    
    <style>
/* Global styles shared across all templates */
body {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Droid Sans Mono', 'Source Code Pro', 'Ubuntu Mono', 'Cascadia Code', 'Menlo', 'Consolas', monospace;
    line-height: 1.5;
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px;
    background: linear-gradient(90deg, #f3d6d2, #f1dcce, #f0e4ca, #eeecc7, #e3ecc3, #d5eac0, #c6e8bd, #b9e6bc, #b6e3c5, #b3e1cf);
    color: #333;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.8em;
}

/* Common typography */
code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Droid Sans Mono', 'Source Code Pro', 'Ubuntu Mono', 'Cascadia Code', 'Menlo', 'Consolas', monospace;
    line-height: 1.5;
}

pre {
    background-color: #12121212;
    padding: 10px;
    border-radius: 5px;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-word;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Droid Sans Mono', 'Source Code Pro', 'Ubuntu Mono', 'Cascadia Code', 'Menlo', 'Consolas', monospace;
    line-height: 1.5;
}

/* Common card styling */
.card-base {
    background-color: #ffffff66;
    border-radius: 8px;
    padding: 16px;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-left: #ffffff66 1px solid;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

.card-base:hover {
    box-shadow: -10px -10px 15px #eeeeee66, 10px 10px 15px #00000022;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* Common header styling */
.header {
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

/* Timestamps */
.timestamp {
    font-size: 0.85em;
    color: #666;
    font-weight: normal;
}

/* Floating action buttons */
.floating-btn {
    position: fixed;
    right: 20px;
    background-color: #e8f4fd66;
    color: #666;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2em;
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s, transform 0.2s;
    z-index: 1000;
    text-decoration: none;
}

.floating-btn:hover {
    background-color: #e8f4fdcc;
    transform: translateY(-2px);
}

.floating-btn:visited {
    color: #666;
}

/* Floating buttons positioning */
.scroll-top.floating-btn {
    bottom: 20px;
}

.toggle-details.floating-btn {
    bottom: 80px;
}

.filter-messages.floating-btn {
    bottom: 140px;
}

.timeline-toggle.floating-btn {
    bottom: 200px;
}
/* Message and content styles */
.message {
    margin-bottom: 1em;
    padding: 1em;
    border-radius: 8px;
    border-left: #ffffff66 1px solid;
    background-color: #e3f2fd55;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

.session-divider {
    margin: 70px 0;
    border-top: 2px solid #fff;
}

/* Message type styling */
.user {
    border-left-color: #2196f3;
}

.assistant {
    border-left-color: #9c27b0;
}

.system {
    border-left-color: #ff9800;
}

.tool_use {
    border-left-color: #e91e63;
}

.tool_result {
    border-left-color: #4caf50;
}

.thinking {
    border-left-color: #9e9e9e;
}

.image {
    border-left-color: #ff5722;
}

/* Session header styling */
.session-header {
    background-color: #e8f4fd66;
    border-radius: 8px;
    padding: 16px;
    margin: 30px 0 20px 0;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-left: #ffffff66 1px solid;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

.session-header .header {
    margin-bottom: 8px;
    font-size: 1.2em;
}

/* Content styling */
.content {
    word-wrap: break-word;
}

.content > pre {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

.header:has(+ .content > details) {
    margin-left: 1em;
}

/* Tool content styling */
.tool-content {
    background-color: #f8f9fa66;
    border-radius: 4px;
    padding: 8px;
    margin: 8px 0;
    overflow-x: auto;
    box-shadow: -4px -4px 10px #eeeeee33, 4px 4px 10px #00000007;
    border-left: #ffffff66 1px solid;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

.tool-result {
    background-color: #e8f5e866;
    border-left: #4caf5088 1px solid;
}

.tool-use {
    background-color: #e3f2fd66;
    border-left: #2196f388 1px solid;
}

.thinking-content {
    background-color: #f0f0f066;
    border-left: #66666688 1px solid;
}

.thinking-text {
    font-style: italic;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #555;
}

.tool-input {
    background-color: #fff3cd66;
    border-radius: 4px;
    padding: 6px;
    margin: 4px 0;
    font-size: 0.9em;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-left: #ffffff66 1px solid;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

/* Session summary styling */
.session-summary {
    background-color: #ffffff66;
    border-left: #4caf5088 4px solid;
    padding: 12px;
    margin: 8px 0;
    border-radius: 0 4px 4px 0;
    font-style: italic;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

/* Collapsible details styling */
details summary {
    cursor: pointer;
    color: #666;
}

.collapsible-details {
    margin-top: -2em;
}

.collapsible-details summary {
    position: relative;
    cursor: pointer;
}

/* Preview content styling - shown when closed */
.collapsible-details:not([open]) .preview-content {
    margin-top: 4px;
}

/* Hide preview content when details is open */
.collapsible-details[open] .preview-content {
    display: none;
}

/* Style the full details content */
.details-content {
    margin-top: 4px;
}

/* Hide details content when closed */
.collapsible-details:not([open]) .details-content {
    display: none;
}

/* Style pre and other elements within details content */
.content pre {
    background-color: transparent;
    padding: 0;
    margin: 0;
    font-size: 0.9em;
    color: #555;
    line-height: 1.3;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Message filtering */
.message.filtered-hidden {
    display: none;
}
/* Session navigation styles */
.navigation {
    background-color: #f8f9fa66;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-left: #ffffff66 1px solid;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

.navigation h2 {
    margin: 0 0 12px 0;
    font-size: 1.2em;
    color: #495057;
}

.session-nav {
    margin-top: 1em;
    display: grid;
    gap: 8px;
}

.session-link {
    padding: 8px 12px;
    background-color: #ffffff66;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-decoration: none;
    color: #495057;
    transition: background-color 0.2s;
}

.session-link:hover {
    background-color: #ffffff99;
}

.session-link-title {
    font-weight: 600;
    font-size: 0.9em;
}

.session-link-meta {
    font-size: 0.8em;
    color: #6c757d;
    margin-top: 2px;
}

/* Project-specific session navigation */
.project-sessions {
    margin-top: 15px;
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.project-sessions h4 {
    margin: 0 0 10px 0;
    font-size: 0.9em;
    color: #495057;
    font-weight: 600;
}

.project-sessions .session-link {
    padding: 6px 8px;
    font-size: 0.8em;
    margin-bottom: 4px;
}

.project-sessions .session-link-title {
    font-size: 0.85em;
}

.project-sessions .session-link-meta {
    font-size: 0.75em;
}
/* Filter toolbar and controls */
.filter-toolbar {
    background-color: #f8f9fa66;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-left: #ffffff66 1px solid;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
    display: none;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(8px);
}

.filter-toolbar.visible {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 16px;
}

.filter-label {
    white-space: nowrap;
}

.filter-toggles {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.filter-toolbar h3 {
    margin: 0;
    font-size: 1em;
    color: #495057;
    font-weight: 600;
}

.filter-toggle {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    background-color: transparent;
    color: #495057;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.filter-toggle:hover {
    background-color: #ffffff99;
    transform: translateY(-1px);
}

.filter-toggle.active {
    background-color: #ffffffaa;
}

.filter-toggle.active:hover {
    background-color: #ffffff66;
}

.filter-actions {
    display: flex;
    gap: 6px;
    white-space: nowrap;
}

.filter-action-btn {
    padding: 4px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #ffffff66;
    color: #6c757d;
    font-size: 0.75em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.filter-action-btn:hover {
    background-color: #ffffff99;
}

.filter-toggle .count {
    opacity: 0.7;
    font-size: 0.9em;
    margin-left: 2px;
}

.filter-toggle.active .count {
    opacity: 1;
}

.filter-messages.active {
    background-color: #fff3cd;
}
/* TodoWrite tool styling */
.todo-write {
    background-color: #f0f8ff66;
    border-left: #4169e188 3px solid;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.tool-header {
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 1.1em;
}

.todo-list {
    background-color: #ffffff66;
    border-radius: 6px;
    padding: 8px;
    box-shadow: -7px -7px 10px #eeeeee44, 7px 7px 10px #00000011;
    border-left: #ffffff66 1px solid;
    border-top: #ffffff66 1px solid;
    border-bottom: #00000017 1px solid;
    border-right: #00000017 1px solid;
}

.todo-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 4px;
    border-bottom: 1px solid #f0f3f6;
    transition: background-color 0.2s ease;
}

.todo-item:last-child {
    border-bottom: none;
}

.todo-item:hover {
    background-color: #f8f9fa;
}

.todo-item.completed {
    opacity: 0.7;
}

.todo-item.completed .todo-content {
    text-decoration: line-through;
    color: #6c757d;
}

.todo-item input[type="checkbox"] {
    margin: 0;
    cursor: default;
}

.todo-status {
    font-size: 1.1em;
    line-height: 1;
}

.todo-content {
    flex: 1;
    color: #333;
    font-weight: 500;
}

.todo-id {
    font-size: 0.8em;
    color: #6c757d;
    font-weight: normal;
}

/* Priority-based left border colors */
.todo-item.high {
    border-left: 3px solid #dc3545;
}

.todo-item.medium {
    border-left: 3px solid #ffc107;
}

.todo-item.low {
    border-left: 3px solid #28a745;
}

/* Status-based background tints */
.todo-item.in_progress {
    background-color: #fff3cd;
}

.todo-item.completed {
    background-color: #d4edda;
}
/* Timeline-specific styles for vis-timeline */

/* Timeline toggle button styling */
.timeline-toggle.active {
    background-color: #fff3cd;
}

/* Timeline container positioning and styling */
#timeline-container {
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: top 0.3s ease;
    position: relative;
}

/* Timeline resize handle styling */
#timeline-resize-handle {
    transition: background 0.2s ease;
}

#timeline-resize-handle:hover {
    background: linear-gradient(to bottom, transparent, #bbb) !important;
}

#timeline-resize-handle:hover>div {
    background: #777 !important;
}

#timeline-resize-handle:active {
    background: linear-gradient(to bottom, transparent, #999) !important;
}

#timeline-resize-handle:active>div {
    background: #555 !important;
}

/* vis-timeline customizations */
.vis-timeline {
    border: none !important;
}

.vis-labelset .vis-label {
    font-size: 12px !important;
    font-weight: 500 !important;
    color: #495057 !important;
}

/* Timeline items styling */
.vis-item {
    border-radius: 4px !important;
    border: 1px solid #ddd !important;
    font-size: 11px !important;
    /* Stuck item workaround, see: https://github.com/visjs/vis-timeline/issues/494#issuecomment-1638974075 */
    transform: scale(0);
}

.vis-item .vis-item-content {
    padding: 2px 4px !important;
}

.vis-item.vis-selected {
    border-color: #007bff !important;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25) !important;
}

/* Message type specific styling */
.vis-item.timeline-item-user {
    background-color: #e3f2fd !important;
    border-color: #2196f3 !important;
}

.vis-item.timeline-item-assistant {
    background-color: #f3e5f5 !important;
    border-color: #9c27b0 !important;
}

.vis-item.timeline-item-tool_use {
    background-color: #fff3e0 !important;
    border-color: #ff9800 !important;
}

.vis-item.timeline-item-tool_result {
    background-color: #e8f5e8 !important;
    border-color: #4caf50 !important;
}

.vis-item.timeline-item-thinking {
    background-color: #fce4ec !important;
    border-color: #e91e63 !important;
}

.vis-item.timeline-item-system {
    background-color: #f5f5f5 !important;
    border-color: #9e9e9e !important;
}

.vis-item.timeline-item-image {
    background-color: #e1f5fe !important;
    border-color: #00bcd4 !important;
}

/* Timeline axis styling */
.vis-time-axis {
    border-top: 1px solid #ddd !important;
}

.vis-time-axis .vis-text {
    font-size: 11px !important;
    color: #666 !important;
}

/* Timeline navigation controls */
.vis-navigation {
    font-size: 12px !important;
}

/* Hide vis-timeline watermark if present */
.vis-timeline .vis-custom-time {
    display: none !important;
}

.vis-tooltip {
    max-width: 700px;
    padding: 0 !important;
}

.vis-tooltip pre {
    margin: 0;
}

.vis-tooltip img {
    max-width: 700px;
}
    </style>
</head>

<body>
    <h1 id="title">Claude Transcripts - RProtocolAPI.******** (from yesterday to today)</h1>

    <!-- Timeline Component -->
    <!-- Timeline Component Template -->
<!-- vis-timeline integration for transcript visualization -->

<div id="timeline-container"
    style="display: none; position: sticky; top: 0; z-index: 100; background: white; border-bottom: 1px solid #ddd; width: 100vw; margin-left: calc(-50vw + 50%); overflow: hidden; min-height: 150px; max-height: 80vh;">
    <div id="timeline-visualization" style="height: calc(100% - 8px); width: 100%;"></div>
    <div id="timeline-resize-handle"
        style="position: absolute; bottom: 0; left: 0; right: 0; height: 8px; background: linear-gradient(to bottom, transparent, #ddd); cursor: ns-resize; display: flex; align-items: center; justify-content: center;">
        <div style="width: 40px; height: 3px; background: #999; border-radius: 2px;"></div>
    </div>
</div>

<script id="timeline-script">
    // Timeline functionality - inline for self-contained HTML
    (function () {
        let timeline = null;
        let items = null;
        let groups = null;
        let isTimelineLoaded = false;
        let timelineIdToElement = new Map(); // Map timeline IDs to DOM elements
        let isResizing = false;

        // Message type to group mapping
        const messageTypeGroups = {
            'user': { id: 'user', content: '🤷 User', style: 'background-color: #e3f2fd;' },
            'assistant': { id: 'assistant', content: '🤖 Assistant', style: 'background-color: #f3e5f5;' },
            'tool_use': { id: 'tool_use', content: '🛠️ Tool Use', style: 'background-color: #fff3e0;' },
            'tool_result': { id: 'tool_result', content: '🧰 Tool Result', style: 'background-color: #e8f5e8;' },
            'thinking': { id: 'thinking', content: '💭 Thinking', style: 'background-color: #fce4ec;' },
            'system': { id: 'system', content: '⚙️ System', style: 'background-color: #f5f5f5;' },
            'image': { id: 'image', content: '🖼️ Image', style: 'background-color: #e1f5fe;' }
        };

        // Build timeline data from messages
        function buildTimelineData() {
            const timelineItems = [];
            const timelineGroups = [];
            const usedGroups = new Set();

            // Clear existing mapping
            timelineIdToElement.clear();

            // Get all messages from the page
            const messages = document.querySelectorAll('.message:not(.session-header)');

            messages.forEach((messageEl, index) => {
                // Extract message data
                const messageType = Array.from(messageEl.classList).find(cls =>
                    Object.keys(messageTypeGroups).includes(cls)
                ) || 'system';

                const timestampEl = messageEl.querySelector('.timestamp');
                if (!timestampEl) return; // Skip if no timestamp
                const timestamp = timestampEl.textContent.trim();

                // Get message content preview
                const contentEl = messageEl.querySelector('.content');
                let content = '';
                if (contentEl) {
                    const textContent = contentEl.textContent || contentEl.innerText || '';
                    content = textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;
                }

                // For tool_use messages, try to extract the tool name
                if (messageType === 'tool_use') {
                    // Try to extract tool name from JSON content
                    const nameMatch = content.match(/"name":\s*"([^"]+)"/);
                    if (nameMatch) {
                        const toolName = nameMatch[1];
                        content = toolName + ': ' + content.replace(/"name":\s*"[^"]+",?\s*/, '');
                    } else {
                        // Fallback: try to extract from header if available
                        const headerEl = messageEl.querySelector('.header span');
                        if (headerEl) {
                            const headerText = headerEl.textContent || '';
                            const toolMatch = headerText.match(/🛠️\s*(.+) \(Id:.*/);
                            if (toolMatch) {
                                content = toolMatch[1].replace("Tool Use: ", "") + (content ? ': ' + content : '');
                            }
                        }
                    }
                }

                // Add group if not already added
                if (!usedGroups.has(messageType)) {
                    timelineGroups.push(messageTypeGroups[messageType]);
                    usedGroups.add(messageType);
                }

                // Store mapping for click handling
                timelineIdToElement.set(index, messageEl);

                // Strip content to make tooltip formatting more uniform
                // TODO: this is very basic and TodoWrites have weird spacing so will need some more work
                let title = contentEl.innerHTML.includes("<pre") ? contentEl.innerHTML : `<pre>${contentEl.innerHTML}</pre>`
                if (title.includes("<details")) {
                    title = title.replace(/(<summary>.*<\/summary>)/gs, '').replace(/<details class="collapsible-details">(.*?)<\/details>/gs, (m, p) => p)
                }
                title = title.replace(/<pre>[\s\r\n]+(.*?)[\s\r\n]+<\/pre>/gs, (m, p) => `<pre>${p}</pre>`)

                // Create timeline item
                timelineItems.push({
                    id: index,
                    content: content || messageTypeGroups[messageType].content,
                    start: timestamp,
                    group: messageType,
                    tooltip: {
                        // FIXME: This followMouse doesn't work for some reason and the tooltip box gets cut off for the bottom timeline boxes
                        followMouse: true,
                        overflowMethod: 'cap'
                    },
                    title,
                    className: `timeline-item-${messageType}`
                });
            });

            return { items: timelineItems, groups: timelineGroups };
        }

        // Filter timeline items based on current message filters
        function applyFilters() {
            if (!timeline || !items) return;

            // Get active filter types
            const filterToggles = document.querySelectorAll('.filter-toggle');
            const activeTypes = [];
            let hasActiveFilters = false;

            filterToggles.forEach(toggle => {
                if (toggle.classList.contains('active')) {
                    activeTypes.push(toggle.dataset.type);
                    hasActiveFilters = true;
                }
            });

            // If no filters are active, show all items
            if (!hasActiveFilters) {
                timeline.setItems(items);
                return;
            }

            // Filter items based on active types
            const filteredItems = items.get().filter(item => {
                return activeTypes.includes(item.group);
            });

            timeline.setItems(new vis.DataSet(filteredItems));
        }

        // Handle timeline item click - scroll to corresponding message
        function onTimelineSelect(event) {
            const selection = timeline.getSelection();
            if (selection.length > 0) {
                const itemId = selection[0];
                const messageEl = timelineIdToElement.get(itemId);
                if (messageEl) {
                    // Calculate timeline height for proper scroll positioning
                    const timelineContainer = document.getElementById('timeline-container');
                    const timelineHeight = timelineContainer ? timelineContainer.offsetHeight : 0;

                    // Scroll so message top aligns with timeline bottom
                    const elementTop = messageEl.offsetTop;
                    const scrollPosition = elementTop - timelineHeight - 10; // 10px padding

                    window.scrollTo({
                        top: Math.max(0, scrollPosition),
                        behavior: 'smooth'
                    });

                    // Highlight the message briefly
                    messageEl.style.backgroundColor = '#fff3cd';
                    setTimeout(() => {
                        messageEl.style.backgroundColor = '';
                    }, 2000);
                }
            }
        }

        // Initialize timeline
        function initTimeline() {
            if (timeline) return; // Already initialized

            console.log('Initializing vis-timeline...');

            const container = document.getElementById('timeline-visualization');
            if (!container) {
                console.error('Timeline container not found');
                return;
            }

            // Build timeline data
            const timelineData = buildTimelineData();
            if (timelineData.items.length === 0) {
                console.warn('No timeline items found');
                return;
            }

            // Create DataSets
            items = new vis.DataSet(timelineData.items);
            groups = new vis.DataSet(timelineData.groups);

            // Timeline options
            const options = {
                height: '100%',
                stack: true,
                showCurrentTime: true,
                zoomMin: 1000 * 1, // 1 second
                zoomMax: 1000 * 60 * 60 * 24 * 30, // 30 days
                orientation: 'top',
                align: 'left',
                margin: {
                    item: 2,
                    axis: 2
                },
                groupOrder: (a, b) => {
                    const order = ['user', 'assistant', 'tool_use', 'tool_result', 'thinking', 'system', 'image'];
                    return order.indexOf(a.id) - order.indexOf(b.id);
                }
            };

            // Create timeline
            timeline = new vis.Timeline(container, items, groups, options);

            // Add event listeners
            timeline.on('select', onTimelineSelect);

            // Apply current filters
            applyFilters();

            console.log('Timeline initialized with', timelineData.items.length, 'items and', timelineData.groups.length, 'groups');
        }

        // Load vis-timeline library dynamically
        function loadVisTimeline() {
            return new Promise((resolve, reject) => {
                if (window.vis && window.vis.Timeline) {
                    resolve();
                    return;
                }

                console.log('Loading vis-timeline from CDN...');

                // Load CSS first
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://unpkg.com/vis-timeline/styles/vis-timeline-graph2d.min.css';
                document.head.appendChild(link);

                // Load JavaScript
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/vis-timeline/standalone/umd/vis-timeline-graph2d.min.js';
                script.onload = () => {
                    console.log('vis-timeline loaded successfully');
                    isTimelineLoaded = true;
                    resolve();
                };
                script.onerror = () => {
                    console.error('Failed to load vis-timeline');
                    reject(new Error('Failed to load vis-timeline'));
                };
                document.head.appendChild(script);
            });
        }

        // Toggle timeline visibility
        function toggleTimeline() {
            const container = document.getElementById('timeline-container');
            const button = document.getElementById('toggleTimeline');

            if (container.style.display === 'none') {
                // Show timeline
                button.classList.add('active');
                button.title = 'Hide timeline';
                button.textContent = '🗓️';

                // Load vis-timeline if needed and show timeline
                loadVisTimeline().then(() => {
                    container.style.display = 'block';
                    // Set default height if not already set
                    if (!container.style.height) {
                        container.style.height = '30vh';
                    }
                    // Wait for container to be visible, then initialize
                    setTimeout(() => {
                        initTimeline();
                        initTimelineResize();
                    }, 100);
                }).catch(error => {
                    console.error('Error loading timeline:', error);
                    alert('Failed to load timeline. Please check your internet connection.');
                    container.style.display = 'none';
                    button.classList.remove('active');
                    button.title = 'Show timeline';
                    button.textContent = '📆';
                });
            } else {
                // Hide timeline
                container.style.display = 'none';
                button.classList.remove('active');
                button.title = 'Show timeline';
                button.textContent = '📆';
            }
        }

        // Update timeline position when filter bar is toggled
        function updateTimelinePosition() {
            const container = document.getElementById('timeline-container');
            const filterToolbar = document.querySelector('.filter-toolbar');

            if (container && filterToolbar) {
                const filterHeight = filterToolbar.offsetHeight;
                const computedStyle = getComputedStyle(filterToolbar);
                const isFilterVisible = computedStyle.display !== 'none' &&
                    computedStyle.visibility !== 'hidden' &&
                    filterHeight > 0;

                container.style.top = isFilterVisible ? `${filterHeight}px` : '0px';
            }
        }

        // Initialize timeline resizing functionality
        function initTimelineResize() {
            const container = document.getElementById('timeline-container');
            const resizeHandle = document.getElementById('timeline-resize-handle');

            if (!container || !resizeHandle) return;

            let startY = 0;
            let startHeight = 0;

            function handleMouseDown(e) {
                isResizing = true;
                startY = e.clientY;
                startHeight = container.offsetHeight;

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);

                // Prevent text selection during resize
                document.body.style.userSelect = 'none';
                e.preventDefault();
            }

            function handleMouseMove(e) {
                if (!isResizing) return;

                const deltaY = e.clientY - startY;
                const newHeight = Math.max(150, Math.min(window.innerHeight * 0.8, startHeight + deltaY));

                container.style.height = newHeight + 'px';

                // Trigger timeline redraw if needed
                if (timeline) {
                    timeline.redraw();
                }
            }

            function handleMouseUp() {
                isResizing = false;
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
                document.body.style.userSelect = '';
            }

            // Add mouse event listeners
            resizeHandle.addEventListener('mousedown', handleMouseDown);

            // Also allow resizing by dragging the container bottom edge
            container.addEventListener('mousedown', function (e) {
                const rect = container.getBoundingClientRect();
                if (e.clientY >= rect.bottom - 8) {
                    handleMouseDown(e);
                }
            });
        }

        // Export functions to global scope
        window.toggleTimeline = toggleTimeline;
        window.applyTimelineFilters = applyFilters;
        window.updateTimelinePosition = updateTimelinePosition;

        // Hook into existing systems
        document.addEventListener('DOMContentLoaded', function () {
            // Listen for filter changes
            const filterToggles = document.querySelectorAll('.filter-toggle');
            filterToggles.forEach(toggle => {
                toggle.addEventListener('click', function () {
                    setTimeout(applyFilters, 50);
                });
            });

            // Listen for select all/none buttons
            const selectAllButton = document.getElementById('selectAll');
            const selectNoneButton = document.getElementById('selectNone');
            if (selectAllButton) {
                selectAllButton.addEventListener('click', function () {
                    setTimeout(applyFilters, 50);
                });
            }
            if (selectNoneButton) {
                selectNoneButton.addEventListener('click', function () {
                    setTimeout(applyFilters, 50);
                });
            }

            // Listen for filter toolbar visibility changes
            const filterButton = document.getElementById('filterMessages');
            const closeFiltersButton = document.getElementById('closeFilters');

            if (filterButton) {
                filterButton.addEventListener('click', function () {
                    setTimeout(updateTimelinePosition, 50);
                });
            }

            if (closeFiltersButton) {
                closeFiltersButton.addEventListener('click', function () {
                    setTimeout(updateTimelinePosition, 50);
                });
            }

            // Update timeline position on window resize
            window.addEventListener('resize', updateTimelinePosition);
        });
    })();
</script>

    <!-- Filter Toolbar -->
    <div class="filter-toolbar">
        <div class="filter-label">
            <h3>Filter:</h3>
        </div>
        <div class="filter-toggles">
            <button class="filter-toggle active" data-type="user">🤷 User <span class="count">(0)</span></button>
            <button class="filter-toggle active" data-type="assistant">🤖 Assistant <span
                    class="count">(0)</span></button>
            <button class="filter-toggle active" data-type="system">⚙️ System <span class="count">(0)</span></button>
            <button class="filter-toggle active" data-type="tool_use">🛠️ Tool Use <span
                    class="count">(0)</span></button>
            <button class="filter-toggle active" data-type="tool_result">🧰 Tool Results <span
                    class="count">(0)</span></button>
            <button class="filter-toggle active" data-type="thinking">💭 Thinking <span
                    class="count">(0)</span></button>
            <button class="filter-toggle active" data-type="image">🖼️ Images <span class="count">(0)</span></button>
        </div>
        <div class="filter-actions">
            <button class="filter-action-btn" id="selectAll">All</button>
            <button class="filter-action-btn" id="selectNone">None</button>
            <button class="filter-action-btn" id="closeFilters" title="Close filters">✕</button>
        </div>
    </div>

    

    

    <button class="timeline-toggle floating-btn" id="toggleTimeline" title="Show timeline">📆</button>
    <button class="filter-messages floating-btn" id="filterMessages" title="Toggle filters">🔍</button>
    <button class="toggle-details floating-btn" id="toggleDetails" title="Toggle all details">📋</button>
    <a class="scroll-top floating-btn" title="Scroll to top" href="#title">🔝</a>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const toggleButton = document.getElementById('toggleDetails');
            const timelineButton = document.getElementById('toggleTimeline');
            const filterButton = document.getElementById('filterMessages');
            const filterToolbar = document.querySelector('.filter-toolbar');
            const selectAllButton = document.getElementById('selectAll');
            const selectNoneButton = document.getElementById('selectNone');
            const closeFiltersButton = document.getElementById('closeFilters');
            const filterToggles = document.querySelectorAll('.filter-toggle');

            // Timeline toggle functionality
            if (timelineButton) {
                timelineButton.addEventListener('click', function () {
                    if (window.toggleTimeline) {
                        window.toggleTimeline();
                    }
                });
            }

            // Toggle details functionality
            function updateToggleButton() {
                const allDetails = document.querySelectorAll('details.collapsible-details');
                const openCount = document.querySelectorAll('details[open].collapsible-details').length;
                const totalCount = allDetails.length;

                if (totalCount === 0) {
                    toggleButton.style.display = 'none';
                    return;
                }

                // If more than half are open, show "close all" state, otherwise show "open all"
                const mostlyOpen = openCount > totalCount / 2;
                toggleButton.textContent = mostlyOpen ? '📦' : '🗃️';
                toggleButton.title = mostlyOpen ? 'Close all details' : 'Open all details';
            }

            function toggleAllDetails() {
                const allDetails = document.querySelectorAll('details.collapsible-details');
                const openCount = document.querySelectorAll('details[open].collapsible-details').length;
                const shouldOpen = openCount <= allDetails.length / 2;

                allDetails.forEach(details => {
                    if (shouldOpen) {
                        details.setAttribute('open', '');
                    } else {
                        details.removeAttribute('open');
                    }
                });

                updateToggleButton();
            }

            toggleButton.addEventListener('click', toggleAllDetails);

            // Filter toolbar toggle functionality
            function toggleFilterToolbar() {
                const isVisible = filterToolbar.classList.contains('visible');
                if (isVisible) {
                    filterToolbar.classList.remove('visible');
                    filterButton.classList.remove('active');
                    filterButton.title = 'Show filters';
                } else {
                    filterToolbar.classList.add('visible');
                    filterButton.classList.add('active');
                    filterButton.title = 'Hide filters';
                }
            }

            filterButton.addEventListener('click', toggleFilterToolbar);
            closeFiltersButton.addEventListener('click', toggleFilterToolbar);

            // Count messages by type and update button labels
            function updateMessageCounts() {
                const messageTypes = ['user', 'assistant', 'system', 'tool_use', 'tool_result', 'thinking', 'image'];

                messageTypes.forEach(type => {
                    const messages = document.querySelectorAll(`.message.${type}:not(.session-header)`);
                    const count = messages.length;
                    const toggle = document.querySelector(`[data-type="${type}"]`);
                    const countSpan = toggle.querySelector('.count');

                    if (countSpan) {
                        countSpan.textContent = `(${count})`;

                        // Hide toggles for message types with 0 count
                        if (count === 0) {
                            toggle.style.display = 'none';
                        } else {
                            toggle.style.display = 'flex';
                        }
                    }
                });
            }

            // Filter functionality
            function applyFilter() {
                const activeTypes = Array.from(filterToggles)
                    .filter(toggle => toggle.classList.contains('active'))
                    .map(toggle => toggle.dataset.type);

                // Show/hide messages based on active toggle buttons
                const allMessages = document.querySelectorAll('.message:not(.session-header)');
                allMessages.forEach(message => {
                    const shouldShow = activeTypes.some(type => message.classList.contains(type));
                    if (shouldShow) {
                        message.classList.remove('filtered-hidden');
                    } else {
                        message.classList.add('filtered-hidden');
                    }
                });

                // Update visible counts in real-time
                updateVisibleCounts();

                // Update filter button appearance based on whether all types are selected
                const allTypesSelected = activeTypes.length === filterToggles.length;
                if (!allTypesSelected && filterToolbar.classList.contains('visible')) {
                    filterButton.classList.add('active');
                } else if (allTypesSelected && filterToolbar.classList.contains('visible')) {
                    filterButton.classList.add('active');
                }
            }

            function updateVisibleCounts() {
                const messageTypes = ['user', 'assistant', 'system', 'tool_use', 'tool_result', 'thinking', 'image'];

                messageTypes.forEach(type => {
                    const visibleMessages = document.querySelectorAll(`.message.${type}:not(.session-header):not(.filtered-hidden)`);
                    const totalMessages = document.querySelectorAll(`.message.${type}:not(.session-header)`);
                    const visibleCount = visibleMessages.length;
                    const totalCount = totalMessages.length;

                    const toggle = document.querySelector(`[data-type="${type}"]`);
                    const countSpan = toggle.querySelector('.count');

                    if (countSpan && totalCount > 0) {
                        // Show "visible/total" format when filtering is active
                        const activeTypes = Array.from(filterToggles)
                            .filter(toggle => toggle.classList.contains('active'))
                            .map(toggle => toggle.dataset.type);

                        const isFiltering = activeTypes.length < filterToggles.length;

                        if (isFiltering && visibleCount !== totalCount) {
                            countSpan.textContent = `(${visibleCount}/${totalCount})`;
                        } else {
                            countSpan.textContent = `(${totalCount})`;
                        }
                    }
                });
            }

            function toggleFilter(button) {
                button.classList.toggle('active');
                applyFilter();
            }

            function selectAllTypes() {
                filterToggles.forEach(toggle => {
                    toggle.classList.add('active');
                });
                applyFilter();
            }

            function selectNoTypes() {
                filterToggles.forEach(toggle => {
                    toggle.classList.remove('active');
                });
                applyFilter();
            }

            // Event listeners for filter toggles
            filterToggles.forEach(toggle => {
                toggle.addEventListener('click', () => toggleFilter(toggle));
            });

            selectAllButton.addEventListener('click', selectAllTypes);
            selectNoneButton.addEventListener('click', selectNoTypes);

            // Initialize button state and message counts
            updateToggleButton();
            updateMessageCounts();
        });
    </script>
</body>

</html>