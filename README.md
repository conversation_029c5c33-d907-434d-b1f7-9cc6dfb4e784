# Rithmic R | Protocol API SDK

A comprehensive Python implementation for financial trading with real-time market data, order management, and historical data access.

**Status: PRODUCTION READY** ✅

## Quick Start

```bash
# Install dependencies
pip install websockets protobuf mysql-connector-python

# Configure environment
cp .env.example .env
# Edit .env with your credentials (paper trading included)

# Launch web dashboard
cd web_dashboard/
pip install -r requirements.txt
python main.py
# Open http://localhost:8000

# Or run simple demos
cd simple-demos/
python3 discover_systems.py
python3 search/search_contracts.py
```

## Documentation

Our documentation is organized into logical sections:

### User Documentation
- **[User Guide](docs/user-guide/README.md)** - Complete project overview and setup
- **[Node.js Setup](docs/user-guide/nodejs-setup.md)** - JavaScript/Node.js installation guide

### Developer Documentation  
- **[API Reference](docs/developer-guide/api-reference.md)** - Complete Rithmic API reference
- **[Multi-Contract Guide](docs/developer-guide/multi-contract.md)** - Advanced multi-contract features

### Component Documentation
- **[Web Dashboard](web_dashboard/README.md)** - Modern web-based trading interface
- **[Simple Demos](docs/components/simple-demos/README.md)** - Demo scripts and examples
- **[Usage Guide](docs/components/simple-demos/usage-guide.md)** - Detailed usage instructions

### Technical Documentation
- **[Development Notes](docs/technical/development-notes.md)** - Technical history and architecture
- **[Subscription Analysis](docs/technical/subscription-analysis.md)** - Market data subscription details

### Archive
- **[Implementation Plan](docs/archive/implementation-plan.md)** - Original development roadmap

## Key Features

- **Web Dashboard**: Modern browser-based interface with real-time updates
- **Real-time Market Data**: Level 1 & Level 3 depth-by-order data
- **Order Management**: Full order lifecycle with account tracking  
- **Historical Data**: Time bars and tick bars with persistence
- **Symbol Search**: Advanced pattern matching and symbol discovery
- **Multi-Contract Support**: Concurrent subscriptions with caching
- **Database Integration**: MySQL persistence with optimized tables
- **Code Generation**: Automated script generation from web interface
- **Performance Optimized**: Connection pooling and bulk operations
- **Simple Demos**: Ready-to-run example scripts with centralized argument parsing

## Project Structure

```
├── docs/                           # 📚 All documentation (organized by audience)
├── web_dashboard/                  # 🌐 Modern web-based trading interface
├── simple-demos/                   # 🚀 Ready-to-run demo scripts  
├── src/                           # 🔧 Core API implementation
├── samples/                       # 📝 Original vendor samples
├── proto/                         # 📡 Protocol Buffer definitions
└── tests/                         # ✅ Test suites
```

## License & Credits

This project includes the Rithmic R | Protocol API SDK (v0.84.0.0) with comprehensive Python and JavaScript implementations for financial trading applications.