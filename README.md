# Rithmic R | Protocol API SDK

## Overview

This is the **Rithmic R | Protocol API SDK** (v0.84.0.0) - a comprehensive Python implementation for financial trading with real-time market data, order management, and historical data access. The API uses WebSocket connections with Google Protocol Buffers for binary message serialization.

**Status: PRODUCTION READY** ✅

## Key Features

- **Real-time Market Data**: Level 1 & Level 3 depth-by-order data
- **Order Management**: Full order lifecycle with account tracking  
- **Historical Data**: Time bars and tick bars with persistence
- **Multi-Contract Support**: Concurrent subscriptions with caching
- **Database Integration**: MySQL persistence with 17 optimized tables
- **Performance Optimized**: Connection pooling and bulk operations

## Quick Start

### Installation & Setup
```bash
# Install dependencies
pip install websockets protobuf mysql-connector-python

# Configure environment
cp .env.example .env
# Edit .env with your credentials (paper trading included)

# Run comprehensive test
python3 test_unique_comprehensive.py
```

### Development Commands

**Python Development:**
```bash
# Navigate to samples
cd samples/samples.py/

# Run sample applications  
python3 SampleMD.py       # Market data subscription
python3 SampleBar.py      # Historical data
python3 SampleOrder.py    # Order management
```

**Database Setup:**
```bash
# Create schema (MySQL 9.3.0+ supported)
mysql -u root -p < src/database/schema.sql
```

## Project Structure

```
src/
├── rithmic_api/             # Core API components
├── database/                # MySQL integration & models
├── scripts/                 # Application scripts  
└── utils/                   # Utilities & caching
tests/                       # Test suite
examples/                    # Usage examples
proto/                       # Protocol definitions
samples/                     # JavaScript & Python samples
```

## Core API Components

### Infrastructure Plants
- **Ticker Plant**: Real-time market data (Level 1 & Level 3)
- **Order Plant**: Order management and account tracking  
- **History Plant**: Historical time bars and tick bars
- **PnL Plant**: Profit/loss position updates
- **Repository Plant**: User agreements and permissions

### Authentication Flow
1. **System Discovery**: Get available systems via Template 16
2. **Login**: Authenticate with credentials via Template 10
3. **Heartbeat**: Maintain connection with periodic heartbeats

## Implementation Credentials (Paper Trading)
- **User**: `PP-013155`
- **Password**: `b7neA8k6JA`  
- **System**: `Rithmic Paper Trading`
- **Gateway**: `Chicago Area`

## Documentation

- **`CLAUDE.md`**: Project instructions for AI development
- **`MULTI_CONTRACT_GUIDE.md`**: Multi-contract implementation guide
- **`doc/Reference_Guide.md`**: Complete API reference
- **`docs/archive/`**: Development history and archived documentation

## License & Credits

This SDK is built for the Rithmic R | Protocol API (v0.84.0.0). Rithmic is a trademark of Rithmic LLC.

