#!/usr/bin/env python3
"""
Cron Setup and Management for ES Futures Data Collection

Automated cron job configuration that handles:
- CME Globex market hours scheduling with DST awareness
- Automatic start/stop based on market sessions
- Health monitoring and process management
- Daylight saving time transitions
- Environment-specific configurations
- Cron job installation, removal, and validation

Market Schedule:
- Sunday 5:59 PM ET: Start collection (1 min before market open)
- Monday-Thursday 5:00 PM ET: Stop for maintenance
- Monday-Thursday 5:59 PM ET: Restart after maintenance
- Friday 5:00 PM ET: Stop for weekend

Usage:
    python cron_setup.py --install      # Install cron jobs
    python cron_setup.py --remove       # Remove cron jobs
    python cron_setup.py --status       # Show cron job status
    python cron_setup.py --generate     # Generate cron schedule
    python cron_setup.py --validate     # Validate current setup
"""

import os
import sys
import subprocess
import tempfile
import logging
from datetime import datetime, timezone
from typing import List, Dict, Optional, Tuple
import argparse
from pathlib import Path
import pwd
import shutil

# Import our modules
from market_hours import CMEGlobexHours
from config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CronScheduleGenerator:
    """Generate cron schedules for market hours."""
    
    def __init__(self):
        """Initialize cron schedule generator."""
        self.market_hours = CMEGlobexHours()
        self.config = get_config()
    
    def get_utc_times_for_et(self, et_hour: int, et_minute: int = 0) -> Tuple[int, int]:
        """
        Convert ET time to current UTC time considering DST.
        
        Args:
            et_hour: Hour in Eastern time (0-23)
            et_minute: Minute in Eastern time (0-59)
            
        Returns:
            Tuple[int, int]: (utc_hour, utc_minute)
        """
        # Create a sample ET datetime
        et_tz = self.market_hours.et_tz
        sample_date = datetime.now().replace(hour=et_hour, minute=et_minute, second=0, microsecond=0)
        et_time = et_tz.localize(sample_date)
        utc_time = et_time.astimezone(self.market_hours.utc_tz)
        
        return utc_time.hour, utc_time.minute
    
    def generate_market_schedule(self) -> Dict[str, str]:
        """
        Generate cron schedules for market hours.
        
        Returns:
            Dict[str, str]: Cron schedule entries
        """
        # Market times in ET
        market_open_et = (18, 0)  # 6:00 PM ET
        market_close_et = (17, 0)  # 5:00 PM ET
        
        # Start 1 minute early
        start_early_minutes = self.config.scheduling.start_early_minutes
        start_et = (market_open_et[0], market_open_et[1] - start_early_minutes)
        if start_et[1] < 0:
            start_et = (start_et[0] - 1, start_et[1] + 60)
        
        # Convert to UTC
        start_utc_hour, start_utc_minute = self.get_utc_times_for_et(start_et[0], start_et[1])
        stop_utc_hour, stop_utc_minute = self.get_utc_times_for_et(market_close_et[0], market_close_et[1])
        
        # Generate cron expressions
        schedules = {
            # Sunday: Start collection at 5:59 PM ET
            'start_sunday': f"{start_utc_minute} {start_utc_hour} * * 0",
            
            # Monday-Thursday: Stop at 5:00 PM ET for maintenance
            'stop_maintenance': f"{stop_utc_minute} {stop_utc_hour} * * 1-4",
            
            # Monday-Thursday: Restart at 5:59 PM ET after maintenance
            'start_after_maintenance': f"{start_utc_minute} {start_utc_hour} * * 1-4",
            
            # Friday: Stop at 5:00 PM ET for weekend
            'stop_friday': f"{stop_utc_minute} {stop_utc_hour} * * 5",
            
            # Health check every 5 minutes
            'health_check': "*/5 * * * *",
            
            # Daily log rotation at 2:00 AM UTC
            'log_rotation': "0 2 * * *"
        }
        
        return schedules
    
    def generate_dst_schedules(self) -> Dict[str, Dict[str, str]]:
        """
        Generate separate schedules for standard time and daylight time.
        
        Returns:
            Dict[str, Dict[str, str]]: Schedules for each time period
        """
        schedules = {}
        
        # Standard Time (EST) - typically November to March
        # 6:00 PM EST = 23:00 UTC, 5:00 PM EST = 22:00 UTC
        schedules['standard_time'] = {
            'start_sunday': "59 22 * * 0",           # 5:59 PM EST = 22:59 UTC
            'stop_maintenance': "0 22 * * 1-4",      # 5:00 PM EST = 22:00 UTC
            'start_after_maintenance': "59 22 * * 1-4", # 5:59 PM EST = 22:59 UTC
            'stop_friday': "0 22 * * 5",             # 5:00 PM EST = 22:00 UTC
            'health_check': "*/5 * * * *",
            'log_rotation': "0 2 * * *"
        }
        
        # Daylight Time (EDT) - typically March to November  
        # 6:00 PM EDT = 22:00 UTC, 5:00 PM EDT = 21:00 UTC
        schedules['daylight_time'] = {
            'start_sunday': "59 21 * * 0",           # 5:59 PM EDT = 21:59 UTC
            'stop_maintenance': "0 21 * * 1-4",      # 5:00 PM EDT = 21:00 UTC
            'start_after_maintenance': "59 21 * * 1-4", # 5:59 PM EDT = 21:59 UTC
            'stop_friday': "0 21 * * 5",             # 5:00 PM EDT = 21:00 UTC
            'health_check': "*/5 * * * *",
            'log_rotation': "0 2 * * *"
        }
        
        return schedules
    
    def get_current_schedule(self) -> Dict[str, str]:
        """
        Get the appropriate schedule for current time (DST vs standard).
        
        Returns:
            Dict[str, str]: Current appropriate schedule
        """
        dst_schedules = self.generate_dst_schedules()
        
        if self.market_hours.is_dst_active():
            logger.info("Using daylight time schedule (EDT)")
            return dst_schedules['daylight_time']
        else:
            logger.info("Using standard time schedule (EST)")
            return dst_schedules['standard_time']


class CronManager:
    """Manage cron jobs for the collection system."""
    
    def __init__(self):
        """Initialize cron manager."""
        self.config = get_config()
        self.schedule_generator = CronScheduleGenerator()
        self.work_dir = Path(self.config.work_dir).absolute()
        self.scripts_dir = self.work_dir / "scripts"
        self.user = self.config.scheduling.cron_user
        
        # Ensure scripts directory exists
        self.scripts_dir.mkdir(exist_ok=True)
        
        # Cron job identifiers
        self.cron_markers = {
            'start': '# ES Collection Start',
            'stop': '# ES Collection Stop', 
            'health': '# ES Collection Health Check',
            'logs': '# ES Collection Log Rotation'
        }
    
    def create_shell_scripts(self):
        """Create shell scripts for cron jobs."""
        scripts = {
            'start_collection.sh': self._generate_start_script(),
            'stop_collection.sh': self._generate_stop_script(),
            'health_check.sh': self._generate_health_check_script(),
            'rotate_logs.sh': self._generate_log_rotation_script()
        }
        
        for script_name, script_content in scripts.items():
            script_path = self.scripts_dir / script_name
            
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            # Make executable
            os.chmod(script_path, 0o755)
            logger.info(f"Created script: {script_path}")
    
    def _generate_start_script(self) -> str:
        """Generate start collection script."""
        python_path = sys.executable
        process_manager_path = self.work_dir / "process_manager.py"
        
        script = f"""#!/bin/bash
# ES Futures Collection Start Script
# Generated automatically by cron_setup.py

set -e

# Change to work directory
cd "{self.work_dir}"

# Set environment
export PYTHONPATH="{self.work_dir}:{self.work_dir}/simple-demos:$PYTHONPATH"
export ENVIRONMENT="{self.config.environment}"

# Logging
LOG_FILE="{self.work_dir}/logs/cron_start.log"
mkdir -p "$(dirname "$LOG_FILE")"

echo "$(date): Starting ES futures collection..." >> "$LOG_FILE"

# Start the collection process using process manager
"{python_path}" "{process_manager_path}" start >> "$LOG_FILE" 2>&1

if [ $? -eq 0 ]; then
    echo "$(date): Collection started successfully" >> "$LOG_FILE"
else
    echo "$(date): Failed to start collection" >> "$LOG_FILE"
    exit 1
fi
"""
        return script
    
    def _generate_stop_script(self) -> str:
        """Generate stop collection script."""
        python_path = sys.executable
        process_manager_path = self.work_dir / "process_manager.py"
        
        script = f"""#!/bin/bash
# ES Futures Collection Stop Script
# Generated automatically by cron_setup.py

set -e

# Change to work directory
cd "{self.work_dir}"

# Set environment
export PYTHONPATH="{self.work_dir}:{self.work_dir}/simple-demos:$PYTHONPATH"
export ENVIRONMENT="{self.config.environment}"

# Logging
LOG_FILE="{self.work_dir}/logs/cron_stop.log"
mkdir -p "$(dirname "$LOG_FILE")"

echo "$(date): Stopping ES futures collection..." >> "$LOG_FILE"

# Stop the collection process using process manager
"{python_path}" "{process_manager_path}" stop >> "$LOG_FILE" 2>&1

if [ $? -eq 0 ]; then
    echo "$(date): Collection stopped successfully" >> "$LOG_FILE"
else
    echo "$(date): Failed to stop collection (may not be running)" >> "$LOG_FILE"
fi
"""
        return script
    
    def _generate_health_check_script(self) -> str:
        """Generate health check script."""
        python_path = sys.executable
        monitoring_path = self.work_dir / "monitoring.py"
        
        script = f"""#!/bin/bash
# ES Futures Collection Health Check Script
# Generated automatically by cron_setup.py

# Change to work directory
cd "{self.work_dir}"

# Set environment
export PYTHONPATH="{self.work_dir}:{self.work_dir}/simple-demos:$PYTHONPATH"
export ENVIRONMENT="{self.config.environment}"

# Logging
LOG_FILE="{self.work_dir}/logs/health_check.log"
mkdir -p "$(dirname "$LOG_FILE")"

# Run health check (alerts mode)
"{python_path}" "{monitoring_path}" --alerts >> "$LOG_FILE" 2>&1

# Exit code 1 means alerts were found, but don't fail cron job
exit 0
"""
        return script
    
    def _generate_log_rotation_script(self) -> str:
        """Generate log rotation script."""
        script = f"""#!/bin/bash
# ES Futures Collection Log Rotation Script
# Generated automatically by cron_setup.py

# Change to work directory
cd "{self.work_dir}"

LOG_DIR="{self.work_dir}/logs"
RETENTION_DAYS=30

echo "$(date): Starting log rotation..." >> "$LOG_DIR/log_rotation.log"

# Rotate log files older than 1 day
find "$LOG_DIR" -name "*.log" -type f -mtime +1 -exec gzip {{}} \\;

# Remove compressed logs older than retention period
find "$LOG_DIR" -name "*.log.gz" -type f -mtime +$RETENTION_DAYS -delete

# Cleanup old process state files
find "$LOG_DIR" -name "process_state.json.*" -type f -mtime +7 -delete

echo "$(date): Log rotation completed" >> "$LOG_DIR/log_rotation.log"
"""
        return script
    
    def get_current_crontab(self) -> str:
        """
        Get current crontab content.
        
        Returns:
            str: Current crontab content
        """
        try:
            result = subprocess.run(
                ['crontab', '-l'],
                capture_output=True,
                text=True,
                check=False
            )
            return result.stdout if result.returncode == 0 else ""
        except Exception as e:
            logger.error(f"Error reading crontab: {e}")
            return ""
    
    def remove_existing_jobs(self, crontab_content: str) -> str:
        """
        Remove existing ES collection cron jobs.
        
        Args:
            crontab_content: Current crontab content
            
        Returns:
            str: Cleaned crontab content
        """
        lines = crontab_content.split('\n')
        cleaned_lines = []
        
        skip_next = False
        for line in lines:
            # Skip lines that are our markers or the jobs following them
            if any(marker in line for marker in self.cron_markers.values()):
                skip_next = True
                continue
            elif skip_next and line.strip() and not line.startswith('#'):
                skip_next = False
                continue
            elif skip_next and not line.strip():
                continue
            else:
                skip_next = False
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines).strip()
    
    def generate_cron_jobs(self) -> List[str]:
        """
        Generate cron job entries.
        
        Returns:
            List[str]: Cron job entries
        """
        schedule = self.schedule_generator.get_current_schedule()
        jobs = []
        
        # Start jobs
        jobs.append(self.cron_markers['start'])
        jobs.append(f"{schedule['start_sunday']} {self.scripts_dir}/start_collection.sh")
        jobs.append(f"{schedule['start_after_maintenance']} {self.scripts_dir}/start_collection.sh")
        jobs.append("")
        
        # Stop jobs
        jobs.append(self.cron_markers['stop'])
        jobs.append(f"{schedule['stop_maintenance']} {self.scripts_dir}/stop_collection.sh")
        jobs.append(f"{schedule['stop_friday']} {self.scripts_dir}/stop_collection.sh")
        jobs.append("")
        
        # Health check
        jobs.append(self.cron_markers['health'])
        jobs.append(f"{schedule['health_check']} {self.scripts_dir}/health_check.sh")
        jobs.append("")
        
        # Log rotation
        jobs.append(self.cron_markers['logs'])
        jobs.append(f"{schedule['log_rotation']} {self.scripts_dir}/rotate_logs.sh")
        jobs.append("")
        
        return jobs
    
    def install_cron_jobs(self) -> bool:
        """
        Install cron jobs for ES collection.
        
        Returns:
            bool: True if installation successful
        """
        try:
            # Create shell scripts first
            self.create_shell_scripts()
            
            # Get current crontab
            current_crontab = self.get_current_crontab()
            
            # Remove existing ES collection jobs
            cleaned_crontab = self.remove_existing_jobs(current_crontab)
            
            # Generate new jobs
            new_jobs = self.generate_cron_jobs()
            
            # Combine existing and new content
            if cleaned_crontab:
                new_crontab = cleaned_crontab + '\n\n' + '\n'.join(new_jobs)
            else:
                new_crontab = '\n'.join(new_jobs)
            
            # Install new crontab
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
                f.write(new_crontab)
                temp_file = f.name
            
            try:
                result = subprocess.run(
                    ['crontab', temp_file],
                    capture_output=True,
                    text=True,
                    check=True
                )
                
                logger.info("Cron jobs installed successfully")
                return True
                
            finally:
                os.unlink(temp_file)
        
        except Exception as e:
            logger.error(f"Error installing cron jobs: {e}")
            return False
    
    def remove_cron_jobs(self) -> bool:
        """
        Remove ES collection cron jobs.
        
        Returns:
            bool: True if removal successful
        """
        try:
            # Get current crontab
            current_crontab = self.get_current_crontab()
            
            if not current_crontab:
                logger.info("No crontab found")
                return True
            
            # Remove existing ES collection jobs
            cleaned_crontab = self.remove_existing_jobs(current_crontab)
            
            # Install cleaned crontab
            if cleaned_crontab.strip():
                with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
                    f.write(cleaned_crontab)
                    temp_file = f.name
                
                try:
                    subprocess.run(
                        ['crontab', temp_file],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                finally:
                    os.unlink(temp_file)
            else:
                # Remove entire crontab if empty
                subprocess.run(
                    ['crontab', '-r'],
                    capture_output=True,
                    text=True,
                    check=False
                )
            
            logger.info("Cron jobs removed successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error removing cron jobs: {e}")
            return False
    
    def show_status(self):
        """Show current cron job status."""
        current_crontab = self.get_current_crontab()
        
        if not current_crontab:
            print("No crontab found")
            return
        
        # Check for our markers
        es_jobs_found = any(marker in current_crontab for marker in self.cron_markers.values())
        
        if es_jobs_found:
            print("ES Collection cron jobs are installed:")
            print()
            
            lines = current_crontab.split('\n')
            in_es_section = False
            
            for line in lines:
                if any(marker in line for marker in self.cron_markers.values()):
                    in_es_section = True
                    print(line)
                elif in_es_section and line.strip():
                    if line.startswith('#'):
                        in_es_section = False
                        continue
                    else:
                        print(line)
                elif in_es_section and not line.strip():
                    in_es_section = False
        else:
            print("No ES Collection cron jobs found")
        
        # Show current schedule info
        print(f"\nCurrent time: {datetime.now()}")
        market_hours = CMEGlobexHours()
        print(f"DST active: {market_hours.is_dst_active()}")
        print(f"Market state: {market_hours.get_market_state().value}")
        
        schedule = self.schedule_generator.get_current_schedule()
        print(f"\nCurrent schedule (UTC):")
        for name, cron_expr in schedule.items():
            print(f"  {name}: {cron_expr}")
    
    def validate_setup(self) -> bool:
        """
        Validate the current cron setup.
        
        Returns:
            bool: True if setup is valid
        """
        issues = []
        
        # Check if cron is available
        if not shutil.which('crontab'):
            issues.append("crontab command not found")
        
        # Check scripts directory
        if not self.scripts_dir.exists():
            issues.append(f"Scripts directory not found: {self.scripts_dir}")
        
        # Check required scripts
        required_scripts = [
            'start_collection.sh',
            'stop_collection.sh', 
            'health_check.sh',
            'rotate_logs.sh'
        ]
        
        for script in required_scripts:
            script_path = self.scripts_dir / script
            if not script_path.exists():
                issues.append(f"Missing script: {script_path}")
            elif not os.access(script_path, os.X_OK):
                issues.append(f"Script not executable: {script_path}")
        
        # Check current crontab
        current_crontab = self.get_current_crontab()
        if current_crontab:
            es_jobs_found = any(marker in current_crontab for marker in self.cron_markers.values())
            if not es_jobs_found:
                issues.append("No ES collection cron jobs found in crontab")
        else:
            issues.append("No crontab found")
        
        # Check logs directory
        logs_dir = self.work_dir / "logs"
        if not logs_dir.exists():
            issues.append(f"Logs directory not found: {logs_dir}")
        
        if issues:
            print("Validation issues found:")
            for issue in issues:
                print(f"  ❌ {issue}")
            return False
        else:
            print("✅ Cron setup validation passed")
            return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='ES Futures Collection Cron Setup',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--install',
        action='store_true',
        help='Install cron jobs'
    )
    
    parser.add_argument(
        '--remove',
        action='store_true',
        help='Remove cron jobs'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show cron job status'
    )
    
    parser.add_argument(
        '--generate',
        action='store_true',
        help='Generate and display cron schedule'
    )
    
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Validate current setup'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='Force operation without confirmation'
    )
    
    args = parser.parse_args()
    
    # Create cron manager
    cron_manager = CronManager()
    
    if args.install:
        if not args.force:
            confirm = input("Install ES collection cron jobs? [y/N]: ")
            if confirm.lower() != 'y':
                print("Installation cancelled")
                return
        
        if cron_manager.install_cron_jobs():
            print("✅ Cron jobs installed successfully")
        else:
            print("❌ Failed to install cron jobs")
            sys.exit(1)
    
    elif args.remove:
        if not args.force:
            confirm = input("Remove ES collection cron jobs? [y/N]: ")
            if confirm.lower() != 'y':
                print("Removal cancelled")
                return
        
        if cron_manager.remove_cron_jobs():
            print("✅ Cron jobs removed successfully")
        else:
            print("❌ Failed to remove cron jobs")
            sys.exit(1)
    
    elif args.status:
        cron_manager.show_status()
    
    elif args.generate:
        generator = CronScheduleGenerator()
        schedule = generator.get_current_schedule()
        
        print("Generated cron schedule:")
        for name, cron_expr in schedule.items():
            print(f"  {name}: {cron_expr}")
        
        print("\nCron job entries:")
        jobs = cron_manager.generate_cron_jobs()
        for job in jobs:
            print(job)
    
    elif args.validate:
        if not cron_manager.validate_setup():
            sys.exit(1)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()