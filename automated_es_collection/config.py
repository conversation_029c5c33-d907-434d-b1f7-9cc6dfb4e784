#!/usr/bin/env python3
"""
Configuration Management for ES Futures Data Collection

Centralized configuration management system that handles:
- Environment variable loading and validation
- Database connection settings
- Rithmic API credentials and settings
- Logging configuration
- Process management settings
- Market hours and scheduling configuration
- Alert thresholds and monitoring settings

Supports multiple environments (development, production, testing)
with environment-specific overrides and validation.

Usage:
    from config import get_config
    
    config = get_config()
    print(config.database.host)
    print(config.rithmic.user)
"""

import os
import sys
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path
import json
from dotenv import load_dotenv
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Database connection configuration."""
    host: str = "**************"
    port: int = 3306
    user: str = "root"
    password: str = "debian"
    database: str = "rithmic_api"
    charset: str = "utf8mb4"
    pool_size: int = 10
    pool_recycle: int = 3600
    connect_timeout: int = 30
    read_timeout: int = 30
    write_timeout: int = 30
    
    @property
    def connection_string(self) -> str:
        """Get database connection string."""
        return f"mysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    def validate(self) -> List[str]:
        """Validate database configuration."""
        errors = []
        
        if not self.host:
            errors.append("Database host is required")
        if not self.user:
            errors.append("Database user is required")
        if not self.password:
            errors.append("Database password is required")
        if not self.database:
            errors.append("Database name is required")
        if not (1 <= self.port <= 65535):
            errors.append("Database port must be between 1 and 65535")
        if self.pool_size < 1:
            errors.append("Database pool size must be positive")
        
        return errors


@dataclass
class RithmicConfig:
    """Rithmic API configuration."""
    user: str = "PP-013155"
    password: str = "b7neA8k6JA"
    system: str = "Rithmic Paper Trading"
    gateway: str = "Chicago Area"
    
    # WebSocket settings
    heartbeat_interval: int = 30
    connection_timeout: int = 30
    reconnect_attempts: int = 5
    reconnect_delay: int = 5
    
    # SSL settings
    ssl_cert_path: str = "etc/rithmic_ssl_cert_auth_params"
    
    def validate(self) -> List[str]:
        """Validate Rithmic configuration."""
        errors = []
        
        if not self.user:
            errors.append("Rithmic user is required")
        if not self.password:
            errors.append("Rithmic password is required")
        if not self.system:
            errors.append("Rithmic system is required")
        if not self.gateway:
            errors.append("Rithmic gateway is required")
        if self.heartbeat_interval < 5:
            errors.append("Heartbeat interval must be at least 5 seconds")
        if self.connection_timeout < 5:
            errors.append("Connection timeout must be at least 5 seconds")
        
        return errors


@dataclass
class CollectionConfig:
    """Data collection configuration."""
    # Contract settings
    max_contracts: Optional[int] = None
    symbol_pattern: str = "ES"
    exchange: str = "CME"
    
    # Subscription settings
    enable_level1: bool = True
    enable_level3: bool = True
    batch_size: int = 10
    subscription_delay: float = 0.5
    
    # Error handling
    max_error_count: int = 100
    error_threshold_minutes: int = 60
    auto_restart: bool = True
    
    # Performance settings
    max_memory_mb: int = 1000
    max_cpu_percent: float = 80.0
    message_buffer_size: int = 1000
    
    def validate(self) -> List[str]:
        """Validate collection configuration."""
        errors = []
        
        if self.max_contracts is not None and self.max_contracts < 1:
            errors.append("Max contracts must be positive")
        if not self.symbol_pattern:
            errors.append("Symbol pattern is required")
        if not self.exchange:
            errors.append("Exchange is required")
        if self.batch_size < 1:
            errors.append("Batch size must be positive")
        if self.subscription_delay < 0:
            errors.append("Subscription delay must be non-negative")
        if self.max_memory_mb < 100:
            errors.append("Max memory must be at least 100MB")
        if not (0 < self.max_cpu_percent <= 100):
            errors.append("Max CPU percent must be between 0 and 100")
        
        return errors


@dataclass
class MonitoringConfig:
    """Monitoring and alerting configuration."""
    # Health check intervals
    health_check_interval: int = 60
    metrics_collection_interval: int = 300
    dashboard_refresh_interval: int = 5
    
    # Alert thresholds
    cpu_warning_threshold: float = 80.0
    cpu_critical_threshold: float = 95.0
    memory_warning_threshold: float = 80.0
    memory_critical_threshold: float = 95.0
    disk_warning_threshold: float = 85.0
    disk_critical_threshold: float = 95.0
    
    # Database thresholds
    db_connection_timeout_ms: float = 5000.0
    db_query_timeout_ms: float = 1000.0
    
    # Application thresholds
    message_rate_threshold: float = 10.0  # messages per minute
    process_restart_threshold: int = 3
    
    def validate(self) -> List[str]:
        """Validate monitoring configuration."""
        errors = []
        
        if self.health_check_interval < 10:
            errors.append("Health check interval must be at least 10 seconds")
        if not (0 < self.cpu_warning_threshold <= 100):
            errors.append("CPU warning threshold must be between 0 and 100")
        if not (0 < self.cpu_critical_threshold <= 100):
            errors.append("CPU critical threshold must be between 0 and 100")
        if self.cpu_critical_threshold <= self.cpu_warning_threshold:
            errors.append("CPU critical threshold must be higher than warning threshold")
        
        return errors


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # File logging
    file_enabled: bool = True
    file_path: str = "logs/es_collection.log"
    file_max_bytes: int = 10 * 1024 * 1024  # 10MB
    file_backup_count: int = 5
    
    # Console logging
    console_enabled: bool = True
    console_level: str = "INFO"
    
    # Database logging
    database_enabled: bool = True
    database_level: str = "WARNING"
    
    def validate(self) -> List[str]:
        """Validate logging configuration."""
        errors = []
        
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.level not in valid_levels:
            errors.append(f"Log level must be one of: {valid_levels}")
        if self.console_level not in valid_levels:
            errors.append(f"Console log level must be one of: {valid_levels}")
        if self.database_level not in valid_levels:
            errors.append(f"Database log level must be one of: {valid_levels}")
        if self.file_max_bytes < 1024:
            errors.append("File max bytes must be at least 1024")
        if self.file_backup_count < 1:
            errors.append("File backup count must be positive")
        
        return errors


@dataclass
class SchedulingConfig:
    """Market hours and scheduling configuration."""
    timezone: str = "America/New_York"
    
    # Market hours (in ET)
    market_open_hour: int = 18  # 6:00 PM
    market_close_hour: int = 17  # 5:00 PM
    maintenance_duration: int = 1  # 1 hour
    
    # Collection timing
    start_early_minutes: int = 1  # Start 1 minute before market open
    stop_delay_minutes: int = 0   # Stop immediately at market close
    
    # Cron settings
    enable_cron: bool = True
    cron_user: str = "ubuntu"
    
    def validate(self) -> List[str]:
        """Validate scheduling configuration."""
        errors = []
        
        if not (0 <= self.market_open_hour <= 23):
            errors.append("Market open hour must be between 0 and 23")
        if not (0 <= self.market_close_hour <= 23):
            errors.append("Market close hour must be between 0 and 23")
        if not (0 < self.maintenance_duration <= 23):
            errors.append("Maintenance duration must be between 1 and 23 hours")
        if not (0 <= self.start_early_minutes <= 60):
            errors.append("Start early minutes must be between 0 and 60")
        
        return errors


@dataclass
class Config:
    """Main configuration class."""
    environment: str = "development"
    debug: bool = False
    
    # Sub-configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    rithmic: RithmicConfig = field(default_factory=RithmicConfig)
    collection: CollectionConfig = field(default_factory=CollectionConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    scheduling: SchedulingConfig = field(default_factory=SchedulingConfig)
    
    # Runtime settings
    work_dir: str = field(default_factory=lambda: os.getcwd())
    config_file: Optional[str] = None
    
    def validate(self) -> List[str]:
        """Validate all configuration sections."""
        all_errors = []
        
        all_errors.extend(self.database.validate())
        all_errors.extend(self.rithmic.validate())
        all_errors.extend(self.collection.validate())
        all_errors.extend(self.monitoring.validate())
        all_errors.extend(self.logging.validate())
        all_errors.extend(self.scheduling.validate())
        
        # Environment validation
        valid_environments = ["development", "production", "testing"]
        if self.environment not in valid_environments:
            all_errors.append(f"Environment must be one of: {valid_environments}")
        
        return all_errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'environment': self.environment,
            'debug': self.debug,
            'database': self.database.__dict__,
            'rithmic': self.rithmic.__dict__,
            'collection': self.collection.__dict__,
            'monitoring': self.monitoring.__dict__,
            'logging': self.logging.__dict__,
            'scheduling': self.scheduling.__dict__,
            'work_dir': self.work_dir,
            'config_file': self.config_file
        }


class ConfigLoader:
    """Configuration loader with multiple sources."""
    
    def __init__(self):
        """Initialize configuration loader."""
        self.config = Config()
        self._loaded_sources = []
    
    def load_from_env_file(self, env_file: str) -> bool:
        """
        Load configuration from .env file.
        
        Args:
            env_file: Path to .env file
            
        Returns:
            bool: True if file was loaded successfully
        """
        if os.path.exists(env_file):
            load_dotenv(env_file)
            self._loaded_sources.append(f"env_file: {env_file}")
            logger.info(f"Loaded configuration from {env_file}")
            return True
        return False
    
    def load_from_environment(self):
        """Load configuration from environment variables."""
        # Database configuration
        if os.getenv('DB_HOST'):
            self.config.database.host = os.getenv('DB_HOST')
        if os.getenv('DB_PORT'):
            self.config.database.port = int(os.getenv('DB_PORT'))
        if os.getenv('DB_USER'):
            self.config.database.user = os.getenv('DB_USER')
        if os.getenv('DB_PASSWORD'):
            self.config.database.password = os.getenv('DB_PASSWORD')
        if os.getenv('DB_DATABASE'):
            self.config.database.database = os.getenv('DB_DATABASE')
        
        # Rithmic configuration
        if os.getenv('RITHMIC_USER'):
            self.config.rithmic.user = os.getenv('RITHMIC_USER')
        if os.getenv('RITHMIC_PASSWORD'):
            self.config.rithmic.password = os.getenv('RITHMIC_PASSWORD')
        if os.getenv('RITHMIC_SYSTEM'):
            self.config.rithmic.system = os.getenv('RITHMIC_SYSTEM')
        if os.getenv('RITHMIC_GATEWAY'):
            self.config.rithmic.gateway = os.getenv('RITHMIC_GATEWAY')
        
        # Environment settings
        if os.getenv('ENVIRONMENT'):
            self.config.environment = os.getenv('ENVIRONMENT')
        if os.getenv('DEBUG'):
            self.config.debug = os.getenv('DEBUG').lower() in ('true', '1', 'yes')
        
        # Collection settings
        if os.getenv('MAX_CONTRACTS'):
            self.config.collection.max_contracts = int(os.getenv('MAX_CONTRACTS'))
        if os.getenv('ENABLE_LEVEL1'):
            self.config.collection.enable_level1 = os.getenv('ENABLE_LEVEL1').lower() in ('true', '1', 'yes')
        if os.getenv('ENABLE_LEVEL3'):
            self.config.collection.enable_level3 = os.getenv('ENABLE_LEVEL3').lower() in ('true', '1', 'yes')
        
        # Logging settings
        if os.getenv('LOG_LEVEL'):
            self.config.logging.level = os.getenv('LOG_LEVEL')
        if os.getenv('LOG_FILE_PATH'):
            self.config.logging.file_path = os.getenv('LOG_FILE_PATH')
        
        self._loaded_sources.append("environment_variables")
    
    def load_from_json_file(self, json_file: str) -> bool:
        """
        Load configuration from JSON file.
        
        Args:
            json_file: Path to JSON configuration file
            
        Returns:
            bool: True if file was loaded successfully
        """
        if not os.path.exists(json_file):
            return False
        
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            # Update configuration from JSON data
            self._update_config_from_dict(data)
            self.config.config_file = json_file
            self._loaded_sources.append(f"json_file: {json_file}")
            logger.info(f"Loaded configuration from {json_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading configuration from {json_file}: {e}")
            return False
    
    def _update_config_from_dict(self, data: Dict[str, Any]):
        """Update configuration from dictionary data."""
        # Update database config
        if 'database' in data:
            db_data = data['database']
            for key, value in db_data.items():
                if hasattr(self.config.database, key):
                    setattr(self.config.database, key, value)
        
        # Update Rithmic config
        if 'rithmic' in data:
            rithmic_data = data['rithmic']
            for key, value in rithmic_data.items():
                if hasattr(self.config.rithmic, key):
                    setattr(self.config.rithmic, key, value)
        
        # Update collection config
        if 'collection' in data:
            collection_data = data['collection']
            for key, value in collection_data.items():
                if hasattr(self.config.collection, key):
                    setattr(self.config.collection, key, value)
        
        # Update monitoring config
        if 'monitoring' in data:
            monitoring_data = data['monitoring']
            for key, value in monitoring_data.items():
                if hasattr(self.config.monitoring, key):
                    setattr(self.config.monitoring, key, value)
        
        # Update logging config
        if 'logging' in data:
            logging_data = data['logging']
            for key, value in logging_data.items():
                if hasattr(self.config.logging, key):
                    setattr(self.config.logging, key, value)
        
        # Update scheduling config
        if 'scheduling' in data:
            scheduling_data = data['scheduling']
            for key, value in scheduling_data.items():
                if hasattr(self.config.scheduling, key):
                    setattr(self.config.scheduling, key, value)
        
        # Update main config
        for key in ['environment', 'debug']:
            if key in data:
                setattr(self.config, key, data[key])
    
    def apply_environment_overrides(self):
        """Apply environment-specific configuration overrides."""
        env = self.config.environment.lower()
        
        if env == 'production':
            # Production overrides
            self.config.debug = False
            self.config.logging.level = "INFO"
            self.config.logging.console_level = "WARNING"
            self.config.collection.auto_restart = True
            self.config.monitoring.health_check_interval = 30
            
        elif env == 'development':
            # Development overrides
            self.config.debug = True
            self.config.logging.level = "DEBUG"
            self.config.logging.console_level = "DEBUG"
            self.config.collection.max_contracts = 5  # Limit for testing
            self.config.monitoring.health_check_interval = 60
            
        elif env == 'testing':
            # Testing overrides
            self.config.debug = True
            self.config.logging.level = "DEBUG"
            self.config.collection.max_contracts = 2
            self.config.collection.enable_level3 = False  # Level 1 only for testing
            self.config.database.database = "rithmic_api_test"
    
    def load_all(self, config_dir: str = None) -> Config:
        """
        Load configuration from all available sources.
        
        Args:
            config_dir: Directory to search for configuration files
            
        Returns:
            Config: Loaded and validated configuration
        """
        if config_dir is None:
            config_dir = os.path.dirname(__file__)
        
        # Load from various sources in order of precedence
        config_files = [
            os.path.join(config_dir, '.env'),
            os.path.join(config_dir, '.env.simple-demos'),
            os.path.join(config_dir, '..', '.env'),
            os.path.join(config_dir, '..', 'simple-demos', '.env.simple-demos')
        ]
        
        for env_file in config_files:
            self.load_from_env_file(env_file)
        
        # Load from environment variables (highest precedence)
        self.load_from_environment()
        
        # Load from JSON config files
        json_files = [
            os.path.join(config_dir, 'config.json'),
            os.path.join(config_dir, f'config.{self.config.environment}.json')
        ]
        
        for json_file in json_files:
            self.load_from_json_file(json_file)
        
        # Apply environment-specific overrides
        self.apply_environment_overrides()
        
        # Set work directory
        self.config.work_dir = config_dir
        
        logger.info(f"Configuration loaded from sources: {', '.join(self._loaded_sources)}")
        logger.info(f"Environment: {self.config.environment}")
        
        return self.config


# Global configuration instance
_config = None


def get_config(reload: bool = False, config_dir: str = None) -> Config:
    """
    Get the global configuration instance.
    
    Args:
        reload: Force reload configuration
        config_dir: Directory to search for configuration files
        
    Returns:
        Config: Global configuration instance
    """
    global _config
    
    if _config is None or reload:
        loader = ConfigLoader()
        _config = loader.load_all(config_dir)
        
        # Validate configuration
        errors = _config.validate()
        if errors:
            logger.error("Configuration validation errors:")
            for error in errors:
                logger.error(f"  - {error}")
            raise ValueError(f"Configuration validation failed: {errors}")
        
        logger.info("Configuration loaded and validated successfully")
    
    return _config


def save_config_template(output_path: str):
    """
    Save a configuration template file.
    
    Args:
        output_path: Path to save the template file
    """
    template_config = Config()
    
    with open(output_path, 'w') as f:
        json.dump(template_config.to_dict(), f, indent=2)
    
    logger.info(f"Configuration template saved to {output_path}")


def main():
    """Main entry point for configuration management."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='ES Futures Collection Configuration Management',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Validate current configuration'
    )
    
    parser.add_argument(
        '--show',
        action='store_true',
        help='Show current configuration'
    )
    
    parser.add_argument(
        '--template',
        type=str,
        help='Save configuration template to file'
    )
    
    parser.add_argument(
        '--environment',
        choices=['development', 'production', 'testing'],
        help='Set environment for configuration'
    )
    
    args = parser.parse_args()
    
    # Set environment if specified
    if args.environment:
        os.environ['ENVIRONMENT'] = args.environment
    
    if args.template:
        save_config_template(args.template)
        return
    
    # Load configuration
    try:
        config = get_config()
        
        if args.validate:
            print("✅ Configuration is valid")
        
        if args.show:
            print("Current Configuration:")
            print(json.dumps(config.to_dict(), indent=2))
    
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()