#!/bin/bash
# Setup script for ES Futures Collector V2

set -e  # Exit on any error

echo "=== ES Futures Collector V2 Setup ==="
echo ""

# Check if we're in the right directory
if [ ! -f "es_futures_collector_v2.py" ]; then
    echo "❌ Error: Please run this script from the automated_es_collection/ directory"
    exit 1
fi

# Create logs directory
echo "1. Creating logs directory..."
mkdir -p logs
echo "✅ Logs directory created"

# Setup environment file
echo ""
echo "2. Setting up environment configuration..."
if [ ! -f ".env" ]; then
    echo "   Copying .env template..."
    cp .env.template .env
    echo "✅ Environment template copied to .env"
    echo ""
    echo "⚠️  IMPORTANT: You must edit .env with your credentials:"
    echo "   - Set RITHMIC_USER to your username"
    echo "   - Set RITHMIC_PASSWORD to your password"
    echo "   - Verify RITHMIC_SYSTEM matches your account type"
    echo ""
    read -p "Press Enter to continue after editing .env..."
else
    echo "✅ .env file already exists"
fi

# Test environment configuration
echo ""
echo "3. Testing environment configuration..."
cd ..
python -c "
try:
    from shared.config import get_config
    config = get_config()
    print('✅ Configuration loaded successfully')
    print(f'   System: {config.connection.system}')
    print(f'   User: {config.connection.user}')
    print(f'   Database enabled: {config.database.enabled}')
except Exception as e:
    print(f'❌ Configuration error: {e}')
    exit(1)
"
cd automated_es_collection

# Test shared components
echo ""
echo "4. Testing shared components integration..."
echo "   This will test connection, authentication, and contract discovery..."
echo "   (This may take 30-60 seconds)"

if python test_shared_components.py; then
    echo ""
    echo "✅ Shared components test PASSED"
    echo ""
    echo "=== Setup Complete! ==="
    echo ""
    echo "✅ Key improvements verified:"
    echo "   - Contract discovery finds actual monthly contracts (not generic 'ES')"
    echo "   - Environment-based configuration working"
    echo "   - WebSocket connection and authentication successful"
    echo "   - Database integration ready"
    echo ""
    echo "🚀 Ready to run the collector:"
    echo ""
    echo "   # Test mode (console output only):"
    echo "   python es_futures_collector_v2.py --dry-run --max-contracts 3"
    echo ""
    echo "   # Production mode (database writes):"
    echo "   python es_futures_collector_v2.py"
    echo ""
    echo "   # Batched mode for high performance:"
    echo "   python es_futures_collector_v2.py --batch-mode"
    echo ""
    echo "📖 See MIGRATION_GUIDE.md for complete usage instructions"
else
    echo ""
    echo "❌ Shared components test FAILED"
    echo ""
    echo "🔧 Troubleshooting steps:"
    echo "   1. Check your .env file configuration"
    echo "   2. Verify your Rithmic credentials are correct"
    echo "   3. Ensure you have access to CME E-mini futures"
    echo "   4. Check the error messages above"
    echo ""
    echo "📖 See MIGRATION_GUIDE.md for detailed troubleshooting"
    exit 1
fi