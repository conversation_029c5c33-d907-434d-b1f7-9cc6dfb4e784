# Migration Guide: ES Futures Collector V1 → V2

This guide helps you migrate from the original ES futures collector to the rebuilt version using shared components.

## Key Improvements in V2

### 🎯 **Contract Discovery Fix**
- **Problem Fixed**: V1 was collecting from generic "ES" contract instead of actual monthly contracts (ESU24, ESZ24, etc.)
- **Solution**: V2 uses comprehensive contract discovery to find ALL available ES monthly contracts
- **Result**: Proper two-sided BBO data during liquid market hours

### 🔧 **Unified Architecture**
- **Problem Fixed**: Duplicated code across multiple scripts
- **Solution**: V2 uses shared components for connection, authentication, market data, and database operations
- **Result**: Maintainable, reusable codebase

### ⚙️ **Environment-Based Configuration**
- **Problem Fixed**: Hardcoded credentials and settings
- **Solution**: V2 loads all configuration from environment files
- **Result**: Secure, flexible configuration management

### 💓 **Server-Provided Heartbeats**
- **Problem Fixed**: Hardcoded heartbeat intervals
- **Solution**: V2 extracts heartbeat interval from server login response
- **Result**: Optimal connection stability

## Migration Steps

### 1. Setup Environment Configuration

Copy the environment template and configure your settings:

```bash
cd automated_es_collection/
cp .env.template .env
```

Edit `.env` with your credentials:
```bash
# Required changes:
RITHMIC_USER=your_username_here
RITHMIC_PASSWORD=your_password_here

# Verify these match your account:
RITHMIC_SYSTEM=Rithmic Paper Trading
RITHMIC_GATEWAY=Chicago Area
```

### 2. Test the New Components

Before running the full collector, test that shared components work:

```bash
# Test shared components integration
python test_shared_components.py
```

This should show:
- ✅ Configuration loaded from environment
- ✅ Database connection established  
- ✅ WebSocket connection and authentication successful
- ✅ Contract discovery finds actual monthly contracts (ESU24, ESZ24, etc.)
- ✅ Market data subscription working
- ✅ Message reception test passes

### 3. Run the New Collector

#### Test Mode (Console Output Only)
```bash
# Dry run with limited contracts for testing
python es_futures_collector_v2.py --dry-run --max-contracts 3
```

#### Production Mode (Database Writes)
```bash
# Full collection with database persistence
python es_futures_collector_v2.py

# Batched mode for high-frequency data
python es_futures_collector_v2.py --batch-mode --batch-size 100

# Level 1 only (BBO and trades)
python es_futures_collector_v2.py --level1-only

# Level 3 only (depth by order)  
python es_futures_collector_v2.py --level3-only
```

### 4. Verify Data Quality

Check the database to confirm you're now collecting from actual contracts:

```sql
-- Check recent BBO data
SELECT symbol, COUNT(*) as message_count, 
       COUNT(CASE WHEN bid_price IS NOT NULL AND ask_price IS NOT NULL THEN 1 END) as two_sided_count
FROM best_bid_offer 
WHERE received_at > NOW() - INTERVAL 1 HOUR
GROUP BY symbol
ORDER BY message_count DESC;

-- Verify we're seeing monthly contracts like ESU24, ESZ24
SELECT DISTINCT symbol 
FROM best_bid_offer 
WHERE received_at > NOW() - INTERVAL 1 HOUR
ORDER BY symbol;
```

You should see:
- ✅ Actual monthly contract symbols (ESU24, ESZ24, ESH25, etc.)
- ✅ Two-sided BBO data during liquid market hours
- ✅ Higher message volumes for front-month contracts

## Command Line Options

### Collection Control
- `--dry-run`: Console output only, no database writes
- `--max-contracts N`: Limit to first N contracts (for testing)
- `--level1-only`: BBO and trades only
- `--level3-only`: Depth by order only
- `--force-run`: Run even when market is closed

### Performance Options
- `--batch-mode`: Use batched database writes for better performance
- `--batch-size N`: Records per batch (default: 100)
- `--batch-timeout S`: Max seconds to wait for batch (default: 5.0)

## Troubleshooting

### Contract Discovery Issues
If no contracts are discovered:
1. Check your RITHMIC_SYSTEM and credentials
2. Verify you have permission for CME E-mini futures
3. Check logs: `logs/es_futures_collector_v2.log`

### Connection Problems
If authentication fails:
1. Verify credentials in `.env` file
2. Check RITHMIC_SYSTEM matches your account type
3. Ensure RITHMIC_GATEWAY is correct for your location

### Database Issues
If database writes fail:
1. Test with `--dry-run` first
2. Verify MYSQL_* settings in `.env`
3. Check database connectivity and permissions
4. Use console mode: `ENABLE_DATABASE=false`

### One-Sided BBO Data
If you still see one-sided BBO:
1. Check you're collecting from monthly contracts (ESU24, etc.) not generic "ES"
2. Verify it's during liquid market hours
3. Back-month contracts naturally have less liquidity (this is expected)

## Monitoring and Logs

### Log Files
- `logs/es_futures_collector_v2.log`: Main application log
- Database message_log table: System events and statistics

### Statistics
The collector logs statistics every 60 seconds showing:
- Contracts discovered and subscribed
- Message counts by type (BBO, trades, depth)
- Database write statistics
- Error counts

### Health Monitoring
Use existing monitoring scripts with the new collector:
```bash
# Check if running
ps aux | grep es_futures_collector_v2

# Check recent logs
tail -f logs/es_futures_collector_v2.log

# Check database activity
SELECT COUNT(*) FROM best_bid_offer WHERE received_at > NOW() - INTERVAL 5 MINUTE;
```

## Rollback Plan

If you need to rollback to V1:
1. Stop the V2 collector: `Ctrl+C` or kill process
2. Restart V1 collector: `python es_futures_collector.py`
3. V1 and V2 can coexist (they write to the same database tables)

## Support

For issues with the migration:
1. Check logs: `logs/es_futures_collector_v2.log`
2. Test individual components: `python test_shared_components.py`
3. Use dry-run mode: `--dry-run` flag
4. Verify environment configuration: check `.env` file

The V2 collector fixes the critical contract discovery issue and provides a much more maintainable architecture for ongoing development.