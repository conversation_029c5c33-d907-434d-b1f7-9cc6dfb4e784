#!/usr/bin/env python3
"""
Market Hours Calculator for CME Globex ES Futures

Handles CME Globex trading hours with automatic daylight saving time detection:
- Trading Hours: Sunday 6:00 PM ET - Friday 5:00 PM ET
- Daily Maintenance: 5:00 PM - 6:00 PM ET (Monday-Friday)
- Weekend Break: Friday 5:00 PM ET - Sunday 6:00 PM ET

Provides UTC conversion for cron scheduling and market state detection.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Tuple, Optional, Dict, List
import pytz
from enum import Enum

logger = logging.getLogger(__name__)


class MarketState(Enum):
    """Market trading state enumeration."""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    MAINTENANCE = "MAINTENANCE"
    WEEKEND = "WEEKEND"


class CMEGlobexHours:
    """
    CME Globex market hours calculator with DST awareness.
    
    Handles the complex CME Globex schedule:
    - Sunday 6:00 PM ET - Monday 5:00 PM ET (trading)
    - Monday 6:00 PM ET - Tuesday 5:00 PM ET (trading)
    - Tuesday 6:00 PM ET - Wednesday 5:00 PM ET (trading)  
    - Wednesday 6:00 PM ET - Thursday 5:00 PM ET (trading)
    - Thursday 6:00 PM ET - Friday 5:00 PM ET (trading)
    - Daily maintenance: 5:00 PM - 6:00 PM ET
    - Weekend break: Friday 5:00 PM - Sunday 6:00 PM ET
    """
    
    def __init__(self):
        """Initialize market hours calculator."""
        self.et_tz = pytz.timezone('America/New_York')
        self.utc_tz = pytz.UTC
        
        # Market timing constants (in ET)
        self.market_open_hour = 18  # 6:00 PM ET
        self.market_close_hour = 17  # 5:00 PM ET
        self.maintenance_duration = 1  # 1 hour maintenance window
        
        logger.info("CME Globex market hours calculator initialized")
    
    def get_current_et_time(self) -> datetime:
        """Get current time in Eastern timezone."""
        return datetime.now(self.et_tz)
    
    def get_current_utc_time(self) -> datetime:
        """Get current time in UTC."""
        return datetime.now(self.utc_tz)
    
    def et_to_utc(self, et_time: datetime) -> datetime:
        """Convert Eastern time to UTC."""
        if et_time.tzinfo is None:
            et_time = self.et_tz.localize(et_time)
        return et_time.astimezone(self.utc_tz)
    
    def utc_to_et(self, utc_time: datetime) -> datetime:
        """Convert UTC time to Eastern time."""
        if utc_time.tzinfo is None:
            utc_time = self.utc_tz.localize(utc_time)
        return utc_time.astimezone(self.et_tz)
    
    def is_dst_active(self, dt: datetime = None) -> bool:
        """Check if daylight saving time is active."""
        if dt is None:
            dt = self.get_current_et_time()
        elif dt.tzinfo is None:
            dt = self.et_tz.localize(dt)
        
        return bool(dt.dst())
    
    def get_market_state(self, check_time: datetime = None) -> MarketState:
        """
        Determine current market state.
        
        Args:
            check_time: Time to check (defaults to current time in ET)
            
        Returns:
            MarketState: Current market state
        """
        if check_time is None:
            check_time = self.get_current_et_time()
        elif check_time.tzinfo is None:
            check_time = self.et_tz.localize(check_time)
        else:
            check_time = check_time.astimezone(self.et_tz)
        
        weekday = check_time.weekday()  # 0=Monday, 6=Sunday
        hour = check_time.hour
        
        # Saturday (5) is always weekend
        if weekday == 5:
            return MarketState.WEEKEND
        
        # Sunday (6) - market opens at 6:00 PM ET
        if weekday == 6:
            if hour < self.market_open_hour:
                return MarketState.WEEKEND
            else:
                return MarketState.OPEN
        
        # Monday-Thursday (0-3) - full trading days with maintenance
        if weekday in [0, 1, 2, 3]:
            if hour == self.market_close_hour:  # 5:00 PM ET maintenance hour
                return MarketState.MAINTENANCE
            elif hour < self.market_open_hour and hour > self.market_close_hour:
                return MarketState.MAINTENANCE
            else:
                return MarketState.OPEN
        
        # Friday (4) - market closes at 5:00 PM ET for weekend
        if weekday == 4:
            if hour < self.market_close_hour:
                return MarketState.OPEN
            else:
                return MarketState.WEEKEND
        
        return MarketState.CLOSED
    
    def get_next_market_open(self, from_time: datetime = None) -> datetime:
        """
        Get the next market open time.
        
        Args:
            from_time: Starting time (defaults to current time)
            
        Returns:
            datetime: Next market open time in ET
        """
        if from_time is None:
            from_time = self.get_current_et_time()
        elif from_time.tzinfo is None:
            from_time = self.et_tz.localize(from_time)
        else:
            from_time = from_time.astimezone(self.et_tz)
        
        current_state = self.get_market_state(from_time)
        
        # If market is currently open, next open is after next maintenance
        if current_state == MarketState.OPEN:
            # Find next maintenance period
            next_close = self.get_next_market_close(from_time)
            return next_close + timedelta(hours=self.maintenance_duration)
        
        # If in maintenance, market opens at end of maintenance
        if current_state == MarketState.MAINTENANCE:
            # Calculate end of current maintenance period
            maintenance_end = from_time.replace(
                hour=self.market_open_hour, 
                minute=0, 
                second=0, 
                microsecond=0
            )
            if from_time.hour >= self.market_open_hour:
                maintenance_end += timedelta(days=1)
            return maintenance_end
        
        # If weekend or closed, find next Sunday 6:00 PM ET
        if current_state in [MarketState.WEEKEND, MarketState.CLOSED]:
            # Calculate next Sunday 6:00 PM ET
            days_until_sunday = (6 - from_time.weekday()) % 7
            if days_until_sunday == 0 and from_time.hour >= self.market_open_hour:
                days_until_sunday = 7
            
            next_open = from_time + timedelta(days=days_until_sunday)
            next_open = next_open.replace(
                hour=self.market_open_hour,
                minute=0,
                second=0,
                microsecond=0
            )
            return next_open
        
        return from_time
    
    def get_next_market_close(self, from_time: datetime = None) -> datetime:
        """
        Get the next market close time.
        
        Args:
            from_time: Starting time (defaults to current time)
            
        Returns:
            datetime: Next market close time in ET
        """
        if from_time is None:
            from_time = self.get_current_et_time()
        elif from_time.tzinfo is None:
            from_time = self.et_tz.localize(from_time)
        else:
            from_time = from_time.astimezone(self.et_tz)
        
        current_state = self.get_market_state(from_time)
        
        # If market is open, find next close time
        if current_state == MarketState.OPEN:
            # Next close is at 5:00 PM ET
            next_close = from_time.replace(
                hour=self.market_close_hour,
                minute=0,
                second=0,
                microsecond=0
            )
            
            # If we're past 5:00 PM today, close is tomorrow
            if from_time.hour >= self.market_close_hour:
                next_close += timedelta(days=1)
            
            # Special case: Friday close is for the weekend
            if next_close.weekday() == 4:  # Friday
                return next_close
            
            return next_close
        
        # If not open, next close is when market reopens then closes
        next_open = self.get_next_market_open(from_time)
        return self.get_next_market_close(next_open + timedelta(minutes=1))
    
    def get_collection_schedule(self, days_ahead: int = 7) -> List[Dict]:
        """
        Generate collection schedule for the next N days.
        
        Args:
            days_ahead: Number of days to schedule ahead
            
        Returns:
            List[Dict]: Schedule entries with start/stop times
        """
        schedule = []
        current_time = self.get_current_et_time()
        end_time = current_time + timedelta(days=days_ahead)
        
        check_time = current_time
        while check_time < end_time:
            state = self.get_market_state(check_time)
            
            if state == MarketState.WEEKEND:
                # Find next market open (Sunday 6:00 PM ET)
                next_open = self.get_next_market_open(check_time)
                if next_open < end_time:
                    next_close = self.get_next_market_close(next_open)
                    
                    schedule.append({
                        'start_time_et': next_open,
                        'start_time_utc': self.et_to_utc(next_open),
                        'stop_time_et': next_close,
                        'stop_time_utc': self.et_to_utc(next_close),
                        'session_type': 'trading',
                        'day_of_week': next_open.strftime('%A')
                    })
                    
                    check_time = next_close + timedelta(hours=1)
                else:
                    break
            else:
                check_time += timedelta(hours=1)
        
        return schedule
    
    def get_cron_schedule_utc(self) -> Dict[str, str]:
        """
        Generate cron schedule entries in UTC.
        
        Returns:
            Dict[str, str]: Cron schedule entries
        """
        # Calculate standard start/stop times in UTC
        sample_et_time = self.et_tz.localize(datetime.now().replace(hour=18, minute=0, second=0))
        start_utc = self.et_to_utc(sample_et_time)
        
        sample_et_close = self.et_tz.localize(datetime.now().replace(hour=17, minute=0, second=0))
        stop_utc = self.et_to_utc(sample_et_close)
        
        # Start collection 1 minute before market open
        start_minute = (start_utc.minute - 1) % 60
        start_hour = start_utc.hour
        if start_minute == 59:
            start_hour = (start_hour - 1) % 24
        
        cron_entries = {
            'start_sunday': f"{start_minute} {start_hour} * * 0",  # Sunday start
            'stop_maintenance': f"0 {stop_utc.hour} * * 1-4",     # Mon-Thu stop
            'start_after_maintenance': f"{start_minute} {start_hour} * * 1-4",  # Mon-Thu restart
            'stop_friday': f"0 {stop_utc.hour} * * 5",            # Friday stop
            'health_check': "*/5 * * * *"                         # Every 5 minutes
        }
        
        return cron_entries
    
    def is_collection_time(self, check_time: datetime = None) -> bool:
        """
        Check if this is a valid time for data collection.
        
        Args:
            check_time: Time to check (defaults to current time)
            
        Returns:
            bool: True if collection should be active
        """
        state = self.get_market_state(check_time)
        return state == MarketState.OPEN
    
    def time_until_next_event(self, from_time: datetime = None) -> Tuple[timedelta, str]:
        """
        Calculate time until next market event.
        
        Args:
            from_time: Starting time (defaults to current time)
            
        Returns:
            Tuple[timedelta, str]: Time delta and event description
        """
        if from_time is None:
            from_time = self.get_current_et_time()
        
        current_state = self.get_market_state(from_time)
        
        if current_state == MarketState.OPEN:
            next_close = self.get_next_market_close(from_time)
            time_diff = next_close - from_time
            return time_diff, f"Market close at {next_close.strftime('%Y-%m-%d %H:%M:%S ET')}"
        
        elif current_state == MarketState.MAINTENANCE:
            next_open = self.get_next_market_open(from_time)
            time_diff = next_open - from_time
            return time_diff, f"Market open at {next_open.strftime('%Y-%m-%d %H:%M:%S ET')}"
        
        elif current_state in [MarketState.WEEKEND, MarketState.CLOSED]:
            next_open = self.get_next_market_open(from_time)
            time_diff = next_open - from_time
            return time_diff, f"Market open at {next_open.strftime('%Y-%m-%d %H:%M:%S ET')}"
        
        return timedelta(0), "Unknown market state"


def main():
    """Demo and testing function."""
    logging.basicConfig(level=logging.INFO)
    
    market_hours = CMEGlobexHours()
    current_time = market_hours.get_current_et_time()
    
    print(f"Current time (ET): {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"Current time (UTC): {market_hours.et_to_utc(current_time).strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"DST Active: {market_hours.is_dst_active()}")
    print(f"Market State: {market_hours.get_market_state().value}")
    print(f"Collection Active: {market_hours.is_collection_time()}")
    
    time_diff, event_desc = market_hours.time_until_next_event()
    print(f"Next Event: {event_desc}")
    print(f"Time Until: {time_diff}")
    
    print("\nNext few market events:")
    schedule = market_hours.get_collection_schedule(3)
    for session in schedule[:5]:
        print(f"  {session['session_type']}: {session['start_time_et'].strftime('%a %H:%M ET')} - {session['stop_time_et'].strftime('%a %H:%M ET')}")
    
    print("\nCron Schedule (UTC):")
    cron_schedule = market_hours.get_cron_schedule_utc()
    for name, schedule in cron_schedule.items():
        print(f"  {name}: {schedule}")


if __name__ == "__main__":
    main()