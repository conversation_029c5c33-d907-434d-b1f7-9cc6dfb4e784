#!/usr/bin/env python3
"""
Automated ES Futures Data Collection Script

Comprehensive data collection for all ES (E-mini S&P 500) futures contracts:
- Discovers all available ES contracts with every expiration date
- Subscribes to Level 1 market data (Best Bid/Offer and Last Trade)
- Subscribes to Level 3 market data (Depth by Order)
- Persists all data to database with comprehensive field coverage
- Handles market hours integration and graceful shutdown
- Provides robust error handling and automatic reconnection

Usage:
    python es_futures_collector.py [--dry-run] [--single-contract SYMBOL] [--max-contracts N]
"""

import asyncio
import sys
import os
import signal
import logging
from datetime import datetime, timezone
from typing import List, Dict, Set, Optional, Tuple
import json
from dataclasses import dataclass
import argparse
from enum import Enum

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, os.path.join(parent_dir, 'simple-demos'))

from shared import (
    connect_and_authenticate, disconnect, send_heartbeat, search_contracts, 
    TemplateIDs, MarketDataParsers, safe_get_field, safe_get_repeated_string_field,
    format_symbol_info, ProtobufParsingError, RithmicAPIError, 
    UnexpectedTemplateError, FieldValidationError
)
from shared_database import get_simple_database

# Import market hours calculator
from market_hours import CMEGlobexHours, MarketState

# Add proto_generated directory for protobuf imports
proto_dir = os.path.join(parent_dir, 'proto_generated')
sys.path.append(proto_dir)

import request_login_pb2
import request_market_data_update_pb2
import request_depth_by_order_snapshot_pb2
import request_depth_by_order_updates_pb2
import request_search_symbols_pb2
import response_search_symbols_pb2
import base_pb2
import best_bid_offer_pb2
import last_trade_pb2
import depth_by_order_pb2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/es_futures_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    """Connection state tracking for WebSocket management."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


class CircuitBreakerState(Enum):
    """Circuit breaker state for connection failure management."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class ContractInfo:
    """Information about a futures contract."""
    symbol: str
    exchange: str
    product_code: str
    expiration_date: Optional[str] = None
    instrument_type: Optional[str] = None
    is_subscribed_level1: bool = False
    is_subscribed_level3: bool = False
    last_update: Optional[datetime] = None
    error_count: int = 0


@dataclass
class CollectionStats:
    """Statistics for data collection session."""
    session_start: datetime
    contracts_discovered: int = 0
    contracts_subscribed: int = 0
    level1_messages: int = 0
    level3_messages: int = 0
    database_writes: int = 0
    errors: int = 0
    last_heartbeat: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        """Convert stats to dictionary for logging."""
        return {
            'session_start': self.session_start.isoformat(),
            'contracts_discovered': self.contracts_discovered,
            'contracts_subscribed': self.contracts_subscribed,
            'level1_messages': self.level1_messages,
            'level3_messages': self.level3_messages,
            'database_writes': self.database_writes,
            'errors': self.errors,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None
        }


class ESFuturesCollector:
    """
    Automated ES futures data collector with comprehensive market data coverage.
    """
    
    def __init__(self, dry_run: bool = False, max_contracts: Optional[int] = None):
        """
        Initialize the ES futures collector.
        
        Args:
            dry_run: If True, don't write to database
            max_contracts: Maximum number of contracts to subscribe to (for testing)
        """
        self.dry_run = dry_run
        self.max_contracts = max_contracts
        self.ws = None
        self.db = get_simple_database(enable_database=not dry_run)
        self.market_hours = CMEGlobexHours()
        
        # Contract tracking
        self.contracts: Dict[str, ContractInfo] = {}
        self.subscription_queue: List[str] = []
        self.update_buffer: List[bytes] = []
        self.snapshot_received: Set[str] = set()
        
        # Session state
        self.running = True
        self.stats = CollectionStats(session_start=datetime.now(timezone.utc))
        
        # Connection state management
        self.connection_state = ConnectionState.DISCONNECTED
        self.reconnection_lock = asyncio.Lock()
        self.reconnection_attempts = 0
        self.last_connection_time: Optional[datetime] = None
        self.connection_start_time: Optional[datetime] = None
        
        # Circuit breaker for connection failures
        self.circuit_state = CircuitBreakerState.CLOSED
        self.circuit_failure_count = 0
        self.circuit_last_failure_time: Optional[datetime] = None
        self.circuit_open_time: Optional[datetime] = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        logger.info(f"ES Futures Collector initialized (dry_run={dry_run}, max_contracts={max_contracts})")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    def _calculate_reconnection_delay(self) -> float:
        """Calculate exponential backoff delay for reconnection attempts."""
        base_delay = 1.0  # Start with 1 second
        max_delay = 30.0  # Cap at 30 seconds
        delay = min(base_delay * (2 ** self.reconnection_attempts), max_delay)
        return delay
    
    def _should_attempt_reconnection(self) -> bool:
        """Check if reconnection should be attempted based on circuit breaker state."""
        now = datetime.now(timezone.utc)
        
        if self.circuit_state == CircuitBreakerState.OPEN:
            # Check if circuit should transition to HALF_OPEN
            if self.circuit_open_time and (now - self.circuit_open_time).total_seconds() >= 60:
                self.circuit_state = CircuitBreakerState.HALF_OPEN
                logger.info("Circuit breaker transitioning to HALF_OPEN state")
                return True
            else:
                logger.debug("Circuit breaker is OPEN, blocking reconnection attempt")
                return False
        
        return True
    
    def _update_circuit_breaker_on_failure(self):
        """Update circuit breaker state on connection failure."""
        now = datetime.now(timezone.utc)
        self.circuit_failure_count += 1
        self.circuit_last_failure_time = now
        
        # Open circuit if 3 failures within 5 minutes
        if self.circuit_failure_count >= 3:
            if (not self.circuit_last_failure_time or 
                (now - self.circuit_last_failure_time).total_seconds() <= 300):
                self.circuit_state = CircuitBreakerState.OPEN
                self.circuit_open_time = now
                logger.warning(f"Circuit breaker OPENED after {self.circuit_failure_count} failures")
                return
        
        logger.debug(f"Circuit breaker failure count: {self.circuit_failure_count}")
    
    def _update_circuit_breaker_on_success(self):
        """Update circuit breaker state on successful connection."""
        self.circuit_failure_count = 0
        self.circuit_last_failure_time = None
        self.circuit_state = CircuitBreakerState.CLOSED
        if self.circuit_open_time:
            logger.info("Circuit breaker CLOSED after successful reconnection")
        self.circuit_open_time = None
    
    async def _attempt_reconnection(self) -> bool:
        """
        Attempt to reconnect with improved error handling and state management.
        
        Returns:
            bool: True if reconnection successful, False otherwise
        """
        async with self.reconnection_lock:
            if not self._should_attempt_reconnection():
                return False
            
            # Check max reconnection attempts
            max_attempts = 5
            if self.reconnection_attempts >= max_attempts:
                logger.error(f"Maximum reconnection attempts ({max_attempts}) exceeded")
                self._update_circuit_breaker_on_failure()
                return False
            
            self.connection_state = ConnectionState.RECONNECTING
            self.reconnection_attempts += 1
            
            # Calculate delay and wait
            delay = self._calculate_reconnection_delay()
            logger.info(f"Reconnection attempt {self.reconnection_attempts}/{max_attempts} in {delay:.1f}s...")
            await asyncio.sleep(delay)
            
            try:
                # Clean up existing connection
                if self.ws:
                    try:
                        await self.ws.close()
                    except:
                        pass
                    self.ws = None
                
                self.connection_state = ConnectionState.CONNECTING
                logger.info("Establishing new connection...")
                
                # Attempt new connection
                self.ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
                
                if self.ws:
                    # Connection successful
                    self.connection_state = ConnectionState.CONNECTED
                    self.connection_start_time = datetime.now(timezone.utc)
                    self.last_connection_time = self.connection_start_time
                    
                    # Reset reconnection attempts on success
                    self.reconnection_attempts = 0
                    self._update_circuit_breaker_on_success()
                    
                    logger.info("Reconnected successfully")
                    
                    # Resubscribe to existing contracts
                    if self.contracts:
                        symbols = list(self.contracts.keys())
                        await self.subscribe_to_contracts(symbols)
                        logger.info(f"Resubscribed to {len(symbols)} contracts")
                    
                    return True
                else:
                    # Connection failed
                    self.connection_state = ConnectionState.ERROR
                    logger.error("Reconnection failed - authentication unsuccessful")
                    self._update_circuit_breaker_on_failure()
                    return False
                    
            except Exception as e:
                self.connection_state = ConnectionState.ERROR
                logger.error(f"Reconnection attempt {self.reconnection_attempts} failed: {e}")
                self._update_circuit_breaker_on_failure()
                return False
    
    async def discover_es_contracts(self) -> List[str]:
        """
        Discover all available ES futures contracts.
        
        Returns:
            List[str]: List of ES contract symbols
        """
        logger.info("Discovering ES futures contracts...")
        
        try:
            # Search for all ES contracts
            es_symbols = await search_contracts(self.ws, "ES", "CME")
            
            if not es_symbols:
                logger.warning("No ES contracts found, trying broader search...")
                # Try alternative search patterns
                es_symbols = await search_contracts(self.ws, "ES", "")
            
            if es_symbols:
                logger.info(f"Discovered {len(es_symbols)} ES contracts: {', '.join(es_symbols[:10])}")
                if len(es_symbols) > 10:
                    logger.info(f"... and {len(es_symbols) - 10} more contracts")
                
                # Create ContractInfo objects
                for symbol in es_symbols:
                    self.contracts[symbol] = ContractInfo(
                        symbol=symbol,
                        exchange="CME",
                        product_code="ES"
                    )
                
                self.stats.contracts_discovered = len(es_symbols)
                
                # Apply max_contracts limit if specified
                if self.max_contracts and len(es_symbols) > self.max_contracts:
                    es_symbols = es_symbols[:self.max_contracts]
                    logger.info(f"Limited to {self.max_contracts} contracts for testing")
                
                # Log discovery to database
                if self.db.is_enabled():
                    self.db.log_system_event(
                        'CONTRACT_DISCOVERY',
                        f'Discovered {len(es_symbols)} ES contracts',
                        additional_data={'symbols': es_symbols}
                    )
                
                return es_symbols
            else:
                logger.error("No ES contracts found")
                return []
                
        except Exception as e:
            logger.error(f"Error discovering ES contracts: {e}")
            self.stats.errors += 1
            return []
    
    async def subscribe_level1_data(self, symbol: str, exchange: str):
        """
        Subscribe to Level 1 market data (BBO and Last Trade) for a symbol.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
        """
        try:
            md_req = request_market_data_update_pb2.RequestMarketDataUpdate()
            md_req.template_id = TemplateIDs.MARKET_DATA_UPDATE_REQUEST
            md_req.user_msg.append(f"level1_subscription_{symbol}")
            md_req.symbol = symbol
            md_req.exchange = exchange
            md_req.request = request_market_data_update_pb2.RequestMarketDataUpdate.Request.SUBSCRIBE
            md_req.update_bits = (
                request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.LAST_TRADE | 
                request_market_data_update_pb2.RequestMarketDataUpdate.UpdateBits.BBO
            )
            
            logger.debug(f"Subscribing to Level 1 data for {symbol}")
            await self.ws.send(md_req.SerializeToString())
            
            if symbol in self.contracts:
                self.contracts[symbol].is_subscribed_level1 = True
            
        except Exception as e:
            logger.error(f"Error subscribing to Level 1 data for {symbol}: {e}")
            self.stats.errors += 1
    
    async def subscribe_level3_data(self, symbol: str, exchange: str):
        """
        Subscribe to Level 3 market data (Depth by Order) for a symbol.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
        """
        try:
            # Subscribe to depth updates first
            updates_req = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates()
            updates_req.template_id = TemplateIDs.DEPTH_BY_ORDER_UPDATES_REQUEST
            updates_req.user_msg.append(f"depth_updates_{symbol}")
            updates_req.symbol = symbol
            updates_req.exchange = exchange
            updates_req.request = request_depth_by_order_updates_pb2.RequestDepthByOrderUpdates.Request.SUBSCRIBE
            
            logger.debug(f"Subscribing to Level 3 updates for {symbol}")
            await self.ws.send(updates_req.SerializeToString())
            
            # Wait a moment for subscription confirmation
            await asyncio.sleep(0.1)
            
            # Request initial snapshot
            snapshot_req = request_depth_by_order_snapshot_pb2.RequestDepthByOrderSnapshot()
            snapshot_req.template_id = TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_REQUEST
            snapshot_req.user_msg.append(f"depth_snapshot_{symbol}")
            snapshot_req.symbol = symbol
            snapshot_req.exchange = exchange
            
            logger.debug(f"Requesting Level 3 snapshot for {symbol}")
            await self.ws.send(snapshot_req.SerializeToString())
            
            if symbol in self.contracts:
                self.contracts[symbol].is_subscribed_level3 = True
            
        except Exception as e:
            logger.error(f"Error subscribing to Level 3 data for {symbol}: {e}")
            self.stats.errors += 1
    
    async def subscribe_to_contracts(self, symbols: List[str]):
        """
        Subscribe to both Level 1 and Level 3 data for all symbols.
        
        Args:
            symbols: List of symbols to subscribe to
        """
        logger.info(f"Subscribing to market data for {len(symbols)} contracts...")
        
        subscription_tasks = []
        
        for symbol in symbols:
            # Add Level 1 subscription
            subscription_tasks.append(self.subscribe_level1_data(symbol, "CME"))
            # Add Level 3 subscription
            subscription_tasks.append(self.subscribe_level3_data(symbol, "CME"))
            
            # Add small delay between subscriptions to avoid overwhelming the server
            if len(subscription_tasks) >= 10:  # Batch subscriptions
                await asyncio.gather(*subscription_tasks, return_exceptions=True)
                subscription_tasks = []
                await asyncio.sleep(0.5)  # Brief pause between batches
        
        # Handle remaining subscriptions
        if subscription_tasks:
            await asyncio.gather(*subscription_tasks, return_exceptions=True)
        
        self.stats.contracts_subscribed = len(symbols)
        logger.info(f"Completed subscriptions for {len(symbols)} contracts")
        
        # Log subscription status to database
        if self.db.is_enabled():
            self.db.log_system_event(
                'SUBSCRIPTION_COMPLETE',
                f'Subscribed to {len(symbols)} ES contracts',
                additional_data={'contracts': symbols}
            )
    
    async def process_market_data_message(self, msg_data: bytes):
        """
        Process incoming market data messages.
        
        Args:
            msg_data: Raw message data from WebSocket
        """
        try:
            # Identify message template
            base = base_pb2.Base()
            base.ParseFromString(msg_data)
            template_id = base.template_id
            
            # Process different message types
            if template_id == TemplateIDs.BEST_BID_OFFER:
                await self._process_bbo_message(msg_data)
                self.stats.level1_messages += 1
                
            elif template_id == TemplateIDs.LAST_TRADE:
                await self._process_trade_message(msg_data)
                self.stats.level1_messages += 1
                
            elif template_id == TemplateIDs.DEPTH_BY_ORDER:
                await self._process_depth_message(msg_data)
                self.stats.level3_messages += 1
                
            elif template_id == TemplateIDs.DEPTH_BY_ORDER_SNAPSHOT_RESPONSE:
                await self._process_depth_snapshot_response(msg_data)
                self.stats.level3_messages += 1
                
            elif template_id == TemplateIDs.MARKET_DATA_UPDATE_RESPONSE:
                await self._process_market_data_response(msg_data)
                
            elif template_id == TemplateIDs.DEPTH_BY_ORDER_UPDATES_RESPONSE:
                await self._process_depth_updates_response(msg_data)
                
            else:
                logger.debug(f"Unhandled message template ID: {template_id}")
                
        except Exception as e:
            logger.error(f"Error processing market data message: {e}")
            self.stats.errors += 1
    
    async def _process_bbo_message(self, msg_data: bytes):
        """Process Best Bid/Offer message."""
        try:
            bbo = MarketDataParsers.parse_best_bid_offer(msg_data, "ES Collection BBO")
            symbol_info = format_symbol_info(bbo)
            symbol = symbol_info['symbol']
            exchange = symbol_info['exchange']
            
            # Update contract last update time
            if symbol in self.contracts:
                self.contracts[symbol].last_update = datetime.now(timezone.utc)
            
            # Extract BBO data
            bid_price = safe_get_field(bbo, 'bid_price', None)
            bid_size = safe_get_field(bbo, 'bid_size', None)
            ask_price = safe_get_field(bbo, 'ask_price', None)
            ask_size = safe_get_field(bbo, 'ask_size', None)
            
            logger.debug(f"BBO {symbol}: {bid_price}@{bid_size} - {ask_price}@{ask_size}")
            
            # Persist to database
            if self.db.is_enabled():
                # Create market timestamp
                ssboe = safe_get_field(bbo, 'ssboe', None)
                usecs = safe_get_field(bbo, 'usecs', None)
                market_timestamp = None
                if ssboe is not None:
                    market_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                
                # Collect additional fields
                additional_fields = {
                    'bid_orders': safe_get_field(bbo, 'bid_orders', None),
                    'ask_orders': safe_get_field(bbo, 'ask_orders', None),
                    'bid_implicit_size': safe_get_field(bbo, 'bid_implicit_size', None),
                    'ask_implicit_size': safe_get_field(bbo, 'ask_implicit_size', None),
                    'lean_price': safe_get_field(bbo, 'lean_price', None),
                    'presence_bits': safe_get_field(bbo, 'presence_bits', None),
                    'clear_bits': safe_get_field(bbo, 'clear_bits', None),
                    'is_snapshot': safe_get_field(bbo, 'is_snapshot', False),
                    'ssboe': ssboe,
                    'usecs': usecs
                }
                
                self.db.insert_best_bid_offer(
                    symbol=symbol,
                    exchange=exchange,
                    bid_price=float(bid_price) if bid_price is not None else None,
                    bid_size=int(bid_size) if bid_size is not None else None,
                    ask_price=float(ask_price) if ask_price is not None else None,
                    ask_size=int(ask_size) if ask_size is not None else None,
                    market_timestamp=market_timestamp,
                    additional_fields=additional_fields
                )
                self.stats.database_writes += 1
            
        except Exception as e:
            logger.error(f"Error processing BBO message: {e}")
            self.stats.errors += 1
    
    async def _process_trade_message(self, msg_data: bytes):
        """Process Last Trade message."""
        try:
            trade = MarketDataParsers.parse_last_trade(msg_data, "ES Collection Trade")
            symbol_info = format_symbol_info(trade)
            symbol = symbol_info['symbol']
            exchange = symbol_info['exchange']
            
            # Update contract last update time
            if symbol in self.contracts:
                self.contracts[symbol].last_update = datetime.now(timezone.utc)
            
            # Extract trade data
            trade_price = safe_get_field(trade, 'trade_price', None)
            trade_size = safe_get_field(trade, 'trade_size', None)
            volume = safe_get_field(trade, 'volume', None)
            aggressor = safe_get_field(trade, 'aggressor', None)
            
            logger.debug(f"Trade {symbol}: {trade_price} x {trade_size}, Vol: {volume}")
            
            # Persist to database
            if self.db.is_enabled():
                # Create trade timestamp
                ssboe = safe_get_field(trade, 'ssboe', None)
                usecs = safe_get_field(trade, 'usecs', None)
                trade_timestamp = None
                if ssboe is not None:
                    trade_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                
                # Collect additional fields
                additional_fields = {
                    'exchange_order_id': safe_get_field(trade, 'exchange_order_id', None),
                    'aggressor_exchange_order_id': safe_get_field(trade, 'aggressor_exchange_order_id', None),
                    'net_change': safe_get_field(trade, 'net_change', None),
                    'percent_change': safe_get_field(trade, 'percent_change', None),
                    'vwap': safe_get_field(trade, 'vwap', None),
                    'presence_bits': safe_get_field(trade, 'presence_bits', None),
                    'clear_bits': safe_get_field(trade, 'clear_bits', None),
                    'is_snapshot': safe_get_field(trade, 'is_snapshot', False),
                    'ssboe': ssboe,
                    'usecs': usecs,
                    'source_ssboe': safe_get_field(trade, 'source_ssboe', None),
                    'source_usecs': safe_get_field(trade, 'source_usecs', None),
                    'source_nsecs': safe_get_field(trade, 'source_nsecs', None),
                    'jop_ssboe': safe_get_field(trade, 'jop_ssboe', None),
                    'jop_nsecs': safe_get_field(trade, 'jop_nsecs', None)
                }
                
                self.db.insert_last_trade(
                    symbol=symbol,
                    exchange=exchange,
                    trade_price=float(trade_price) if trade_price is not None else None,
                    trade_size=int(trade_size) if trade_size is not None else None,
                    volume=int(volume) if volume is not None else None,
                    aggressor=str(aggressor) if aggressor is not None else None,
                    trade_timestamp=trade_timestamp,
                    additional_fields=additional_fields
                )
                self.stats.database_writes += 1
            
        except Exception as e:
            logger.error(f"Error processing trade message: {e}")
            self.stats.errors += 1
    
    async def _process_depth_message(self, msg_data: bytes):
        """Process Depth by Order message."""
        try:
            depth_data = MarketDataParsers.parse_depth_by_order(msg_data, "ES Collection Depth")
            symbol_info = format_symbol_info(depth_data)
            symbol = symbol_info['symbol']
            exchange = symbol_info['exchange']
            
            # Update contract last update time
            if symbol in self.contracts:
                self.contracts[symbol].last_update = datetime.now(timezone.utc)
            
            # Extract depth data  
            # Note: transaction_type and update_type are repeated fields, so get first element
            order_id_list = safe_get_field(depth_data, 'exchange_order_id', [])
            price_list = safe_get_field(depth_data, 'depth_price', [])
            quantity_list = safe_get_field(depth_data, 'depth_size', [])
            side_list = safe_get_field(depth_data, 'transaction_type', [])
            action_list = safe_get_field(depth_data, 'update_type', [])
            
            # Get first element from each repeated field
            order_id = order_id_list[0] if order_id_list else None
            price = price_list[0] if price_list else None
            quantity = quantity_list[0] if quantity_list else None
            side = side_list[0] if side_list else None
            action = action_list[0] if action_list else None
            
            # Convert enum values to strings for database storage
            # TransactionType: BUY=1, SELL=2 (maps to database ENUM('BUY','SELL'))
            transaction_type_map = {1: 'BUY', 2: 'SELL'}
            # UpdateType: NEW=1, CHANGE=2, DELETE=3 (maps to database ENUM('ADD','MODIFY','DELETE'))
            update_type_map = {1: 'ADD', 2: 'MODIFY', 3: 'DELETE'}
            
            # Convert side (transaction_type) to string - includes NEUTRAL for None values
            side_str = transaction_type_map.get(side, 'NEUTRAL')
            # Convert action (update_type) to string - must be valid ENUM value (ADD/MODIFY/DELETE only)
            action_str = update_type_map.get(action) if action in update_type_map else 'ADD'
            
            # Detailed logging for NEUTRAL messages to understand their content
            if side_str == 'NEUTRAL':
                logger.info(f"🔍 NEUTRAL MESSAGE INSPECTION for {symbol}:")
                logger.info(f"   Raw transaction_type: {side} (type: {type(side)})")
                logger.info(f"   Raw update_type: {action} (type: {type(action)})")
                logger.info(f"   Price: {price}, Quantity: {quantity}")
                logger.info(f"   Order ID: {order_id}")
                logger.info(f"   Full transaction_type list: {side_list}")
                logger.info(f"   Full update_type list: {action_list}")
                logger.info(f"   Full price list: {price_list}")
                logger.info(f"   Full quantity list: {quantity_list}")
                logger.info(f"   Full order_id list: {order_id_list}")
                
                # Log additional protobuf fields
                sequence_number = safe_get_field(depth_data, 'sequence_number', None)
                ssboe = safe_get_field(depth_data, 'ssboe', None)
                usecs = safe_get_field(depth_data, 'usecs', None)
                logger.info(f"   Sequence: {sequence_number}, SSBOE: {ssboe}, Usecs: {usecs}")
                
                # Log all available fields in the protobuf message
                logger.info(f"   All protobuf fields: {[field.name for field in depth_data.DESCRIPTOR.fields]}")
                
            logger.debug(f"Depth {symbol}: {action_str} {side_str} {price}@{quantity} [{order_id}]")
            
            # Persist to database
            if self.db.is_enabled():
                # Create market timestamp
                ssboe = safe_get_field(depth_data, 'ssboe', None)
                usecs = safe_get_field(depth_data, 'usecs', None)
                market_timestamp = None
                if ssboe is not None:
                    market_timestamp = datetime.fromtimestamp(ssboe, tz=timezone.utc)
                
                # Create level data for database with string enum values
                level_data = {
                    'update_type': action_str,
                    'transaction_type': side_str,
                    'depth_price': price,
                    'depth_size': quantity,
                    'exchange_order_id': order_id
                }
                
                self.db.insert_depth_snapshot(
                    symbol=symbol,
                    exchange=exchange,
                    levels=[level_data] if any(level_data.values()) else [],
                    market_timestamp=market_timestamp,
                    additional_fields={
                        'ssboe': ssboe,
                        'usecs': usecs,
                        'is_realtime_update': True
                    }
                )
                self.stats.database_writes += 1
            
        except Exception as e:
            logger.error(f"Error processing depth message: {e}")
            self.stats.errors += 1
    
    async def _process_depth_snapshot_response(self, msg_data: bytes):
        """Process Depth by Order Snapshot Response."""
        try:
            snapshot_response = MarketDataParsers.parse_depth_snapshot_response(msg_data, "ES Collection Depth Snapshot")
            symbol_info = format_symbol_info(snapshot_response)
            symbol = symbol_info['symbol']
            
            logger.debug(f"Depth snapshot received for {symbol}")
            self.snapshot_received.add(symbol)
            
        except Exception as e:
            logger.error(f"Error processing depth snapshot response: {e}")
            self.stats.errors += 1
    
    async def _process_market_data_response(self, msg_data: bytes):
        """Process Market Data Update Response."""
        try:
            response = MarketDataParsers.parse_market_data_response(msg_data, "ES Collection MD Response")
            logger.debug(f"Market data response: {response}")
            
        except Exception as e:
            logger.error(f"Error processing market data response: {e}")
            self.stats.errors += 1
    
    async def _process_depth_updates_response(self, msg_data: bytes):
        """Process Depth by Order Updates Response."""
        try:
            response = MarketDataParsers.parse_depth_updates_response(msg_data, "ES Collection Depth Updates Response")
            logger.debug(f"Depth updates response: {response}")
            
        except Exception as e:
            logger.error(f"Error processing depth updates response: {e}")
            self.stats.errors += 1
    
    async def check_market_hours(self) -> bool:
        """
        Check if collection should be active based on market hours.
        
        Returns:
            bool: True if collection should continue
        """
        if not self.market_hours.is_collection_time():
            current_state = self.market_hours.get_market_state()
            time_diff, next_event = self.market_hours.time_until_next_event()
            
            logger.info(f"Market is {current_state.value}. {next_event}")
            logger.info(f"Collection will pause. Next event in: {time_diff}")
            
            if self.db.is_enabled():
                self.db.log_system_event(
                    'MARKET_CLOSED',
                    f'Market state: {current_state.value}',
                    additional_data={'next_event': next_event, 'time_until': str(time_diff)}
                )
            
            return False
        
        return True
    
    async def log_session_stats(self):
        """Log current session statistics."""
        runtime = datetime.now(timezone.utc) - self.stats.session_start
        
        logger.info(f"Session Statistics (Runtime: {runtime}):")
        logger.info(f"  Contracts Discovered: {self.stats.contracts_discovered}")
        logger.info(f"  Contracts Subscribed: {self.stats.contracts_subscribed}")
        logger.info(f"  Level 1 Messages: {self.stats.level1_messages}")
        logger.info(f"  Level 3 Messages: {self.stats.level3_messages}")
        logger.info(f"  Database Writes: {self.stats.database_writes}")
        logger.info(f"  Errors: {self.stats.errors}")
        
        if self.db.is_enabled():
            self.db.log_system_event(
                'SESSION_STATS',
                f'Session statistics after {runtime}',
                additional_data=self.stats.to_dict()
            )
    
    async def run_collection(self):
        """Main collection loop."""
        logger.info("Starting ES Futures Data Collection System")
        
        try:
            # Check market hours before starting
            if not await self.check_market_hours():
                logger.info("Market is closed, exiting")
                return
            
            # Connect and authenticate
            self.connection_state = ConnectionState.CONNECTING
            self.ws = await connect_and_authenticate(request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT)
            if not self.ws:
                self.connection_state = ConnectionState.ERROR
                logger.error("Authentication failed")
                return
            
            # Set connection state and timing
            self.connection_state = ConnectionState.CONNECTED
            self.connection_start_time = datetime.now(timezone.utc)
            self.last_connection_time = self.connection_start_time
            logger.info("Connected to Rithmic Ticker Plant")
            
            # Discover ES contracts
            es_symbols = await self.discover_es_contracts()
            if not es_symbols:
                logger.error("No ES contracts found, exiting")
                return
            
            # Subscribe to market data
            await self.subscribe_to_contracts(es_symbols)
            
            # Main message processing loop
            last_stats_log = datetime.now(timezone.utc)
            last_market_check = datetime.now(timezone.utc)
            
            logger.info("Starting market data collection...")
            
            last_heartbeat = datetime.now(timezone.utc)
            heartbeat_interval = 10  # Send heartbeat every 10 seconds
            
            while self.running:
                try:
                    # Check for incoming messages with shorter timeout
                    msg_data = await asyncio.wait_for(self.ws.recv(), timeout=2)
                    await self.process_market_data_message(msg_data)
                    
                except asyncio.TimeoutError:
                    # Send periodic heartbeat to maintain connection
                    now = datetime.now(timezone.utc)
                    if (now - last_heartbeat).total_seconds() >= heartbeat_interval:
                        try:
                            await send_heartbeat(self.ws)
                            self.stats.last_heartbeat = now
                            last_heartbeat = now
                            logger.debug("Heartbeat sent to maintain connection")
                        except Exception as hb_error:
                            logger.warning(f"Failed to send heartbeat: {hb_error}")
                            # Don't break the loop immediately, try to continue
                    
                    # Check market hours periodically
                    now = datetime.now(timezone.utc)
                    if (now - last_market_check).total_seconds() > 300:  # Check every 5 minutes
                        if not await self.check_market_hours():
                            logger.info("Market closed during session, stopping collection")
                            break
                        last_market_check = now
                    
                    # Log statistics periodically
                    if (now - last_stats_log).total_seconds() > 1800:  # Every 30 minutes
                        await self.log_session_stats()
                        last_stats_log = now
                
                except Exception as msg_error:
                    # Handle connection errors with improved reconnection logic
                    if "keepalive ping timeout" in str(msg_error) or "connection" in str(msg_error).lower():
                        logger.warning(f"Connection error detected: {msg_error}")
                        self.connection_state = ConnectionState.ERROR
                        
                        # Attempt reconnection with exponential backoff and circuit breaker
                        if await self._attempt_reconnection():
                            # Successful reconnection, reset heartbeat timer and continue
                            last_heartbeat = datetime.now(timezone.utc)
                            continue
                        else:
                            # Reconnection failed, exit collection loop
                            logger.error("All reconnection attempts failed, stopping collection")
                            break
                    else:
                        # For other errors, log and continue
                        logger.error(f"Message processing error: {msg_error}")
                        self.stats.errors += 1
                
        except Exception as e:
            logger.error(f"Critical error in main collection: {e}")
            self.stats.errors += 1
        
        finally:
            # Final statistics and cleanup
            await self.log_session_stats()
            
            if self.ws:
                logger.info("Disconnecting from Rithmic...")
                await disconnect(self.ws)
            
            if self.db.is_enabled():
                self.db.log_system_event(
                    'SESSION_END',
                    'ES futures collection session ended',
                    additional_data=self.stats.to_dict()
                )
            
            logger.info("ES Futures Data Collection completed")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Automated ES Futures Data Collection System',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Run without writing to database (testing mode)'
    )
    
    parser.add_argument(
        '--single-contract',
        type=str,
        help='Subscribe to single contract only (for testing)'
    )
    
    parser.add_argument(
        '--max-contracts',
        type=int,
        help='Maximum number of contracts to subscribe to (for testing)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Set logging level'
    )
    
    return parser.parse_args()


async def main():
    """Main entry point."""
    args = parse_arguments()
    
    # Configure logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Create collector instance
    collector = ESFuturesCollector(
        dry_run=args.dry_run,
        max_contracts=args.max_contracts
    )
    
    # Handle single contract mode
    if args.single_contract:
        collector.max_contracts = 1
        # Override discovery to use specific contract
        original_discover = collector.discover_es_contracts
        async def single_contract_discover():
            collector.contracts[args.single_contract] = ContractInfo(
                symbol=args.single_contract,
                exchange="CME",
                product_code="ES"
            )
            collector.stats.contracts_discovered = 1
            return [args.single_contract]
        collector.discover_es_contracts = single_contract_discover
    
    # Run collection
    await collector.run_collection()


if __name__ == "__main__":
    asyncio.run(main())