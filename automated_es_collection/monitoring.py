#!/usr/bin/env python3
"""
Health Monitoring and Metrics Collection for ES Futures Data Collection

Comprehensive monitoring system that tracks:
- System resource usage (CPU, memory, disk, network)
- Database connectivity and performance metrics
- Data collection rates and message processing statistics
- Process health and uptime monitoring
- Alert generation for critical conditions
- Performance dashboards and reporting

Usage:
    python monitoring.py --dashboard          # Show real-time dashboard
    python monitoring.py --alerts            # Check for alert conditions
    python monitoring.py --metrics           # Collect and store metrics
    python monitoring.py --report            # Generate summary report
"""

import asyncio
import psutil
import sys
import os
import time
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple, NamedTuple
import json
import argparse
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
import socket
import subprocess

# Add parent directory to path for shared imports
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, os.path.join(parent_dir, 'simple-demos'))

# Import shared database with corrected path
import sys
sys.path.append(os.path.join(parent_dir, 'simple-demos'))
from shared_database import get_simple_database
from market_hours import CMEGlobexHours, MarketState

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/monitoring.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class MetricType(Enum):
    """Types of metrics we collect."""
    SYSTEM = "SYSTEM"
    DATABASE = "DATABASE"
    NETWORK = "NETWORK"
    APPLICATION = "APPLICATION"
    MARKET_DATA = "MARKET_DATA"


@dataclass
class SystemMetrics:
    """System resource metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    load_average: List[float]
    process_count: int
    uptime_seconds: float


@dataclass
class DatabaseMetrics:
    """Database connectivity and performance metrics."""
    timestamp: datetime
    connection_available: bool
    connection_time_ms: float
    query_time_ms: Optional[float] = None
    table_count: Optional[int] = None
    total_rows: Optional[int] = None
    database_size_mb: Optional[float] = None
    recent_inserts_per_minute: Optional[float] = None
    connection_pool_size: Optional[int] = None
    error_count: int = 0


@dataclass
class ApplicationMetrics:
    """Application-specific metrics."""
    timestamp: datetime
    process_running: bool
    process_pid: Optional[int] = None
    process_memory_mb: Optional[float] = None
    process_cpu_percent: Optional[float] = None
    contracts_subscribed: Optional[int] = None
    messages_per_minute: Optional[float] = None
    level1_messages: Optional[int] = None
    level3_messages: Optional[int] = None
    database_writes: Optional[int] = None
    error_count: int = 0
    uptime_seconds: Optional[float] = None


@dataclass
class Alert:
    """System alert information."""
    timestamp: datetime
    severity: AlertSeverity
    category: str
    message: str
    details: Dict[str, Any]
    acknowledged: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['severity'] = self.severity.value
        return data


class NetworkConnectivity:
    """Network connectivity checker."""
    
    @staticmethod
    def check_internet_connectivity() -> Tuple[bool, float]:
        """
        Check internet connectivity.
        
        Returns:
            Tuple[bool, float]: (is_connected, latency_ms)
        """
        try:
            start_time = time.time()
            socket.create_connection(("*******", 53), timeout=10)
            latency = (time.time() - start_time) * 1000
            return True, latency
        except OSError:
            return False, 0.0
    
    @staticmethod
    def check_rithmic_connectivity() -> Tuple[bool, float]:
        """
        Check connectivity to Rithmic servers.
        
        Returns:
            Tuple[bool, float]: (is_connected, latency_ms)
        """
        # Rithmic gateway endpoints (these are examples - adjust for actual endpoints)
        rithmic_hosts = [
            ("rituz00100.rithmic.com", 443),
            ("rituz00101.rithmic.com", 443)
        ]
        
        for host, port in rithmic_hosts:
            try:
                start_time = time.time()
                socket.create_connection((host, port), timeout=10)
                latency = (time.time() - start_time) * 1000
                return True, latency
            except OSError:
                continue
        
        return False, 0.0


class SystemMonitor:
    """System resource monitoring."""
    
    def __init__(self):
        """Initialize system monitor."""
        self.boot_time = psutil.boot_time()
        self.network_counters = psutil.net_io_counters()
        
    def collect_system_metrics(self) -> SystemMetrics:
        """
        Collect comprehensive system metrics.
        
        Returns:
            SystemMetrics: Current system metrics
        """
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory metrics
        memory = psutil.virtual_memory()
        memory_used_mb = (memory.total - memory.available) / (1024 * 1024)
        memory_available_mb = memory.available / (1024 * 1024)
        
        # Disk metrics
        disk = psutil.disk_usage('/')
        disk_used_gb = disk.used / (1024 * 1024 * 1024)
        disk_free_gb = disk.free / (1024 * 1024 * 1024)
        
        # Network metrics
        net_io = psutil.net_io_counters()
        
        # Load average (Unix/Linux only)
        try:
            load_avg = list(os.getloadavg())
        except (OSError, AttributeError):
            load_avg = [0.0, 0.0, 0.0]
        
        # Process count
        process_count = len(psutil.pids())
        
        # System uptime
        uptime_seconds = time.time() - self.boot_time
        
        return SystemMetrics(
            timestamp=datetime.now(timezone.utc),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory_used_mb,
            memory_available_mb=memory_available_mb,
            disk_percent=disk.percent,
            disk_used_gb=disk_used_gb,
            disk_free_gb=disk_free_gb,
            network_bytes_sent=net_io.bytes_sent,
            network_bytes_recv=net_io.bytes_recv,
            load_average=load_avg,
            process_count=process_count,
            uptime_seconds=uptime_seconds
        )


class DatabaseMonitor:
    """Database connectivity and performance monitoring."""
    
    def __init__(self):
        """Initialize database monitor."""
        self.db = get_simple_database()
        
    def collect_database_metrics(self) -> DatabaseMetrics:
        """
        Collect database performance metrics.
        
        Returns:
            DatabaseMetrics: Current database metrics
        """
        timestamp = datetime.now(timezone.utc)
        connection_available = False
        connection_time_ms = 0.0
        query_time_ms = None
        table_count = None
        total_rows = None
        database_size_mb = None
        recent_inserts_per_minute = None
        error_count = 0
        
        if self.db.is_enabled():
            try:
                # Test connection time
                start_time = time.time()
                with self.db.db_manager.get_connection() as conn:
                    connection_time_ms = (time.time() - start_time) * 1000
                    connection_available = True
                    
                    cursor = conn.cursor()
                    
                    # Test query time
                    query_start = time.time()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    query_time_ms = (time.time() - query_start) * 1000
                    
                    # Get table count
                    cursor.execute("""
                        SELECT COUNT(*) 
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE()
                    """)
                    table_count = cursor.fetchone()[0]
                    
                    # Get approximate total rows (quick estimates)
                    cursor.execute("""
                        SELECT SUM(table_rows) 
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE() 
                        AND table_type = 'BASE TABLE'
                    """)
                    result = cursor.fetchone()
                    total_rows = result[0] if result and result[0] else 0
                    
                    # Get database size
                    cursor.execute("""
                        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) 
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE()
                    """)
                    result = cursor.fetchone()
                    database_size_mb = result[0] if result and result[0] else 0
                    
                    # Get recent insert rate (last hour)
                    one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
                    cursor.execute("""
                        SELECT COUNT(*) 
                        FROM best_bid_offer 
                        WHERE received_at >= %s
                    """, (one_hour_ago,))
                    result = cursor.fetchone()
                    if result and result[0]:
                        recent_inserts_per_minute = result[0] / 60.0
                    
            except Exception as e:
                logger.error(f"Database monitoring error: {e}")
                error_count = 1
                connection_available = False
        
        return DatabaseMetrics(
            timestamp=timestamp,
            connection_available=connection_available,
            connection_time_ms=connection_time_ms,
            query_time_ms=query_time_ms,
            table_count=table_count,
            total_rows=total_rows,
            database_size_mb=database_size_mb,
            recent_inserts_per_minute=recent_inserts_per_minute,
            error_count=error_count
        )


class ApplicationMonitor:
    """Application-specific monitoring."""
    
    def __init__(self, process_name: str = "es_futures_collector.py"):
        """Initialize application monitor."""
        self.process_name = process_name
        self.db = get_simple_database()
    
    def find_collection_process(self) -> Optional[psutil.Process]:
        """
        Find the ES futures collection process.
        
        Returns:
            Optional[psutil.Process]: Collection process if found
        """
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if self.process_name in cmdline:
                    return proc
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def collect_application_metrics(self) -> ApplicationMetrics:
        """
        Collect application-specific metrics.
        
        Returns:
            ApplicationMetrics: Current application metrics
        """
        timestamp = datetime.now(timezone.utc)
        process_running = False
        process_pid = None
        process_memory_mb = None
        process_cpu_percent = None
        uptime_seconds = None
        error_count = 0
        
        # Application metrics from database logs
        contracts_subscribed = None
        messages_per_minute = None
        level1_messages = None
        level3_messages = None
        database_writes = None
        
        # Find collection process
        proc = self.find_collection_process()
        if proc:
            try:
                process_running = True
                process_pid = proc.pid
                
                # Get memory usage
                memory_info = proc.memory_info()
                process_memory_mb = memory_info.rss / (1024 * 1024)
                
                # Get CPU usage
                process_cpu_percent = proc.cpu_percent()
                
                # Get process uptime
                create_time = proc.create_time()
                uptime_seconds = time.time() - create_time
                
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                logger.error(f"Error accessing process info: {e}")
                error_count = 1
        
        # Collect metrics from database if available
        if self.db.is_enabled():
            try:
                with self.db.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Get recent message counts (last hour)
                    one_hour_ago = timestamp - timedelta(hours=1)
                    
                    # Count recent BBO messages
                    cursor.execute("""
                        SELECT COUNT(*) 
                        FROM best_bid_offer 
                        WHERE received_at >= %s
                    """, (one_hour_ago,))
                    bbo_count = cursor.fetchone()[0] or 0
                    
                    # Count recent trade messages
                    cursor.execute("""
                        SELECT COUNT(*) 
                        FROM last_trades 
                        WHERE received_at >= %s
                    """, (one_hour_ago,))
                    trade_count = cursor.fetchone()[0] or 0
                    
                    level1_messages = bbo_count + trade_count
                    messages_per_minute = level1_messages / 60.0
                    database_writes = level1_messages
                    
                    # Count unique symbols (approximation of contracts)
                    cursor.execute("""
                        SELECT COUNT(DISTINCT symbol) 
                        FROM best_bid_offer 
                        WHERE received_at >= %s
                    """, (one_hour_ago,))
                    result = cursor.fetchone()
                    contracts_subscribed = result[0] if result else 0
                    
            except Exception as e:
                logger.error(f"Error collecting application metrics from database: {e}")
                error_count += 1
        
        return ApplicationMetrics(
            timestamp=timestamp,
            process_running=process_running,
            process_pid=process_pid,
            process_memory_mb=process_memory_mb,
            process_cpu_percent=process_cpu_percent,
            contracts_subscribed=contracts_subscribed,
            messages_per_minute=messages_per_minute,
            level1_messages=level1_messages,
            level3_messages=level3_messages,
            database_writes=database_writes,
            error_count=error_count,
            uptime_seconds=uptime_seconds
        )


class AlertManager:
    """Alert generation and management."""
    
    def __init__(self):
        """Initialize alert manager."""
        self.alerts: List[Alert] = []
        self.market_hours = CMEGlobexHours()
        
        # Alert thresholds
        self.cpu_warning_threshold = 80.0
        self.cpu_critical_threshold = 95.0
        self.memory_warning_threshold = 80.0
        self.memory_critical_threshold = 95.0
        self.disk_warning_threshold = 85.0
        self.disk_critical_threshold = 95.0
        self.connection_timeout_threshold = 5000.0  # ms
    
    def check_system_alerts(self, metrics: SystemMetrics) -> List[Alert]:
        """
        Check for system-level alerts.
        
        Args:
            metrics: System metrics to check
            
        Returns:
            List[Alert]: Generated alerts
        """
        alerts = []
        
        # CPU usage alerts
        if metrics.cpu_percent >= self.cpu_critical_threshold:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.CRITICAL,
                category="system",
                message=f"Critical CPU usage: {metrics.cpu_percent:.1f}%",
                details={"cpu_percent": metrics.cpu_percent}
            ))
        elif metrics.cpu_percent >= self.cpu_warning_threshold:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.WARNING,
                category="system",
                message=f"High CPU usage: {metrics.cpu_percent:.1f}%",
                details={"cpu_percent": metrics.cpu_percent}
            ))
        
        # Memory usage alerts
        if metrics.memory_percent >= self.memory_critical_threshold:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.CRITICAL,
                category="system",
                message=f"Critical memory usage: {metrics.memory_percent:.1f}%",
                details={
                    "memory_percent": metrics.memory_percent,
                    "memory_used_mb": metrics.memory_used_mb
                }
            ))
        elif metrics.memory_percent >= self.memory_warning_threshold:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.WARNING,
                category="system",
                message=f"High memory usage: {metrics.memory_percent:.1f}%",
                details={
                    "memory_percent": metrics.memory_percent,
                    "memory_used_mb": metrics.memory_used_mb
                }
            ))
        
        # Disk usage alerts
        if metrics.disk_percent >= self.disk_critical_threshold:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.CRITICAL,
                category="system",
                message=f"Critical disk usage: {metrics.disk_percent:.1f}%",
                details={
                    "disk_percent": metrics.disk_percent,
                    "disk_free_gb": metrics.disk_free_gb
                }
            ))
        elif metrics.disk_percent >= self.disk_warning_threshold:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.WARNING,
                category="system",
                message=f"High disk usage: {metrics.disk_percent:.1f}%",
                details={
                    "disk_percent": metrics.disk_percent,
                    "disk_free_gb": metrics.disk_free_gb
                }
            ))
        
        return alerts
    
    def check_database_alerts(self, metrics: DatabaseMetrics) -> List[Alert]:
        """
        Check for database-related alerts.
        
        Args:
            metrics: Database metrics to check
            
        Returns:
            List[Alert]: Generated alerts
        """
        alerts = []
        
        # Database connection alerts
        if not metrics.connection_available:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.CRITICAL,
                category="database",
                message="Database connection unavailable",
                details={"error_count": metrics.error_count}
            ))
        
        # Connection performance alerts
        if (metrics.connection_available and 
            metrics.connection_time_ms > self.connection_timeout_threshold):
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.WARNING,
                category="database",
                message=f"Slow database connection: {metrics.connection_time_ms:.1f}ms",
                details={"connection_time_ms": metrics.connection_time_ms}
            ))
        
        # Data ingestion rate alerts
        if (metrics.recent_inserts_per_minute is not None and 
            metrics.recent_inserts_per_minute == 0 and 
            self.market_hours.is_collection_time()):
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.ERROR,
                category="database",
                message="No recent data inserts during market hours",
                details={"inserts_per_minute": metrics.recent_inserts_per_minute}
            ))
        
        return alerts
    
    def check_application_alerts(self, metrics: ApplicationMetrics) -> List[Alert]:
        """
        Check for application-specific alerts.
        
        Args:
            metrics: Application metrics to check
            
        Returns:
            List[Alert]: Generated alerts
        """
        alerts = []
        
        # Process running alerts
        should_be_running = self.market_hours.is_collection_time()
        if should_be_running and not metrics.process_running:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.CRITICAL,
                category="application",
                message="Collection process not running during market hours",
                details={
                    "should_be_running": should_be_running,
                    "process_running": metrics.process_running
                }
            ))
        
        # Message rate alerts
        if (metrics.messages_per_minute is not None and 
            metrics.messages_per_minute == 0 and 
            should_be_running):
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.ERROR,
                category="application",
                message="No market data messages received during market hours",
                details={"messages_per_minute": metrics.messages_per_minute}
            ))
        
        # Process resource usage alerts
        if metrics.process_memory_mb and metrics.process_memory_mb > 1000:  # 1GB
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                severity=AlertSeverity.WARNING,
                category="application",
                message=f"High process memory usage: {metrics.process_memory_mb:.1f}MB",
                details={"process_memory_mb": metrics.process_memory_mb}
            ))
        
        return alerts
    
    def generate_alerts(self, system_metrics: SystemMetrics, 
                       database_metrics: DatabaseMetrics,
                       application_metrics: ApplicationMetrics) -> List[Alert]:
        """
        Generate all alerts for current metrics.
        
        Args:
            system_metrics: System metrics
            database_metrics: Database metrics
            application_metrics: Application metrics
            
        Returns:
            List[Alert]: All generated alerts
        """
        all_alerts = []
        
        all_alerts.extend(self.check_system_alerts(system_metrics))
        all_alerts.extend(self.check_database_alerts(database_metrics))
        all_alerts.extend(self.check_application_alerts(application_metrics))
        
        # Store alerts
        self.alerts.extend(all_alerts)
        
        return all_alerts


class MonitoringDashboard:
    """Real-time monitoring dashboard."""
    
    def __init__(self):
        """Initialize monitoring dashboard."""
        self.system_monitor = SystemMonitor()
        self.database_monitor = DatabaseMonitor()
        self.application_monitor = ApplicationMonitor()
        self.alert_manager = AlertManager()
        self.market_hours = CMEGlobexHours()
    
    def collect_all_metrics(self) -> Tuple[SystemMetrics, DatabaseMetrics, ApplicationMetrics]:
        """
        Collect all metrics.
        
        Returns:
            Tuple: (system_metrics, database_metrics, application_metrics)
        """
        system_metrics = self.system_monitor.collect_system_metrics()
        database_metrics = self.database_monitor.collect_database_metrics()
        application_metrics = self.application_monitor.collect_application_metrics()
        
        return system_metrics, database_metrics, application_metrics
    
    def display_dashboard(self):
        """Display real-time monitoring dashboard."""
        while True:
            try:
                # Clear screen
                os.system('clear' if os.name == 'posix' else 'cls')
                
                # Collect metrics
                system_metrics, db_metrics, app_metrics = self.collect_all_metrics()
                
                # Generate alerts
                alerts = self.alert_manager.generate_alerts(
                    system_metrics, db_metrics, app_metrics
                )
                
                # Get market state
                market_state = self.market_hours.get_market_state()
                should_run = self.market_hours.is_collection_time()
                
                # Display header
                print("=" * 80)
                print("ES FUTURES DATA COLLECTION - MONITORING DASHBOARD")
                print("=" * 80)
                print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Market State: {market_state.value} | Should Run: {should_run}")
                print()
                
                # System metrics
                print("SYSTEM METRICS:")
                print(f"  CPU Usage:    {system_metrics.cpu_percent:6.1f}%")
                print(f"  Memory Usage: {system_metrics.memory_percent:6.1f}% ({system_metrics.memory_used_mb:7.1f} MB)")
                print(f"  Disk Usage:   {system_metrics.disk_percent:6.1f}% ({system_metrics.disk_free_gb:7.1f} GB free)")
                print(f"  Load Average: {system_metrics.load_average[0]:6.2f}, {system_metrics.load_average[1]:6.2f}, {system_metrics.load_average[2]:6.2f}")
                print()
                
                # Database metrics
                print("DATABASE METRICS:")
                print(f"  Connection:   {'✓' if db_metrics.connection_available else '✗'}")
                print(f"  Conn Time:    {db_metrics.connection_time_ms:6.1f} ms")
                if db_metrics.query_time_ms:
                    print(f"  Query Time:   {db_metrics.query_time_ms:6.1f} ms")
                if db_metrics.database_size_mb:
                    print(f"  DB Size:      {db_metrics.database_size_mb:6.1f} MB")
                if db_metrics.recent_inserts_per_minute:
                    print(f"  Insert Rate:  {db_metrics.recent_inserts_per_minute:6.1f} /min")
                print()
                
                # Application metrics
                print("APPLICATION METRICS:")
                print(f"  Process:      {'✓' if app_metrics.process_running else '✗'}")
                if app_metrics.process_pid:
                    print(f"  PID:          {app_metrics.process_pid}")
                if app_metrics.process_memory_mb:
                    print(f"  Memory:       {app_metrics.process_memory_mb:6.1f} MB")
                if app_metrics.contracts_subscribed:
                    print(f"  Contracts:    {app_metrics.contracts_subscribed}")
                if app_metrics.messages_per_minute:
                    print(f"  Msg Rate:     {app_metrics.messages_per_minute:6.1f} /min")
                if app_metrics.uptime_seconds:
                    uptime_hours = app_metrics.uptime_seconds / 3600
                    print(f"  Uptime:       {uptime_hours:6.1f} hours")
                print()
                
                # Alerts
                if alerts:
                    print("ACTIVE ALERTS:")
                    for alert in alerts[-5:]:  # Show last 5 alerts
                        severity_symbol = {
                            AlertSeverity.INFO: "ℹ",
                            AlertSeverity.WARNING: "⚠",
                            AlertSeverity.ERROR: "⚠",
                            AlertSeverity.CRITICAL: "🔴"
                        }.get(alert.severity, "•")
                        print(f"  {severity_symbol} {alert.severity.value}: {alert.message}")
                    print()
                else:
                    print("ALERTS: No active alerts")
                    print()
                
                # Network connectivity
                internet_ok, internet_latency = NetworkConnectivity.check_internet_connectivity()
                rithmic_ok, rithmic_latency = NetworkConnectivity.check_rithmic_connectivity()
                
                print("NETWORK CONNECTIVITY:")
                print(f"  Internet:     {'✓' if internet_ok else '✗'} ({internet_latency:.1f} ms)")
                print(f"  Rithmic:      {'✓' if rithmic_ok else '✗'} ({rithmic_latency:.1f} ms)")
                print()
                
                print("Press Ctrl+C to exit")
                print("=" * 80)
                
                # Wait before refresh
                time.sleep(5)
                
            except KeyboardInterrupt:
                print("\nDashboard stopped")
                break
            except Exception as e:
                print(f"Dashboard error: {e}")
                time.sleep(10)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='ES Futures Collection Monitoring System',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--dashboard',
        action='store_true',
        help='Show real-time monitoring dashboard'
    )
    
    parser.add_argument(
        '--alerts',
        action='store_true',
        help='Check for alert conditions and exit'
    )
    
    parser.add_argument(
        '--metrics',
        action='store_true',
        help='Collect and display current metrics'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='Generate summary report'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Set logging level'
    )
    
    args = parser.parse_args()
    
    # Configure logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Create dashboard
    dashboard = MonitoringDashboard()
    
    if args.dashboard:
        dashboard.display_dashboard()
    
    elif args.alerts:
        # Check for alerts and exit
        system_metrics, db_metrics, app_metrics = dashboard.collect_all_metrics()
        alerts = dashboard.alert_manager.generate_alerts(
            system_metrics, db_metrics, app_metrics
        )
        
        if alerts:
            print("ACTIVE ALERTS:")
            for alert in alerts:
                print(f"  {alert.severity.value}: {alert.message}")
            sys.exit(1)  # Exit with error code if alerts found
        else:
            print("No active alerts")
            sys.exit(0)
    
    elif args.metrics:
        # Display current metrics
        system_metrics, db_metrics, app_metrics = dashboard.collect_all_metrics()
        
        print("CURRENT METRICS:")
        print(f"System - CPU: {system_metrics.cpu_percent:.1f}%, Memory: {system_metrics.memory_percent:.1f}%")
        print(f"Database - Available: {db_metrics.connection_available}, Time: {db_metrics.connection_time_ms:.1f}ms")
        print(f"Application - Running: {app_metrics.process_running}, Messages/min: {app_metrics.messages_per_minute or 0:.1f}")
    
    elif args.report:
        # Generate summary report
        print("Monitoring report generation not implemented yet")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()