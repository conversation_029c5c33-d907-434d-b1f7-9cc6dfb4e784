#!/usr/bin/env python3
"""
Rebuilt ES Futures Data Collection Script

Comprehensive data collection for ALL ES (E-mini S&P 500) futures contracts using
unified shared components. Fixes the critical contract discovery issue where the
system was collecting from generic "ES" instead of actual monthly contracts.

Key Improvements:
- Uses shared components for unified architecture
- Discovers ALL available ES contracts (not just front months)
- Environment-based configuration (no hardcoded values)
- Server-provided heartbeat intervals
- Comprehensive error handling and reconnection logic
- Modular, maintainable code structure

Usage:
    python es_futures_collector_v2.py [--dry-run] [--max-contracts N] [--level1-only] [--level3-only]
"""

import asyncio
import sys
import os
import signal
import logging
import argparse
from datetime import datetime, timezone
from typing import List, Dict, Set, Optional
import json
from dataclasses import dataclass, field
from enum import Enum

# Add shared components to path
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.insert(0, parent_dir)

# Import shared components
from shared.config import get_config, RithmicConfig
from shared.connection import RithmicWebSocketClient, ConnectionState as WSConnectionState
from shared.contracts import ESContractManager, ContractInfo
from shared.market_data import (
    MarketDataSubscriptionManager, MarketDataUpdate, MarketDataType,
    SubscriptionType, SubscriptionState
)
from shared.database import (
    RithmicDatabaseClient, MarketDataPersistenceManager, 
    MarketDataPersistenceHandler, PersistenceMode
)

# Import market hours (existing)
from market_hours import CMEGlobexHours, MarketState

# Add proto_generated for infrastructure type constants
proto_dir = os.path.join(parent_dir, 'proto_generated')
sys.path.append(proto_dir)
import request_login_pb2

# Configure logging
log_dir = os.path.join(script_dir, 'logs')
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'es_futures_collector_v2.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CollectorState(Enum):
    """Overall collector state."""
    STARTING = "starting"
    DISCOVERING_CONTRACTS = "discovering_contracts"
    SUBSCRIBING = "subscribing"
    COLLECTING = "collecting"
    RECONNECTING = "reconnecting"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class CollectionSession:
    """Session information and statistics."""
    session_id: str
    start_time: datetime
    config_summary: Dict
    
    # Contract statistics
    contracts_discovered: int = 0
    contracts_subscribed_level1: int = 0
    contracts_subscribed_level3: int = 0
    
    # Message statistics  
    bbo_messages: int = 0
    trade_messages: int = 0
    depth_messages: int = 0
    
    # Error statistics
    connection_errors: int = 0
    subscription_errors: int = 0
    parsing_errors: int = 0
    database_errors: int = 0
    
    # Timing
    last_message_time: Optional[datetime] = None
    last_heartbeat_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        """Convert session to dictionary for logging."""
        return {
            'session_id': self.session_id,
            'start_time': self.start_time.isoformat(),
            'config_summary': self.config_summary,
            'contracts_discovered': self.contracts_discovered,
            'contracts_subscribed_level1': self.contracts_subscribed_level1,
            'contracts_subscribed_level3': self.contracts_subscribed_level3,
            'bbo_messages': self.bbo_messages,
            'trade_messages': self.trade_messages,
            'depth_messages': self.depth_messages,
            'connection_errors': self.connection_errors,
            'subscription_errors': self.subscription_errors,
            'parsing_errors': self.parsing_errors,
            'database_errors': self.database_errors,
            'last_message_time': self.last_message_time.isoformat() if self.last_message_time else None,
            'last_heartbeat_time': self.last_heartbeat_time.isoformat() if self.last_heartbeat_time else None
        }


class ESFuturesCollectorV2:
    """
    Rebuilt ES futures data collector using shared components.
    
    This version fixes the critical contract discovery issue and uses
    the unified component architecture for maintainability.
    """
    
    def __init__(self, args):
        """
        Initialize the rebuilt ES futures collector.
        
        Args:
            args: Parsed command line arguments
        """
        self.args = args
        self.running = True
        self.state = CollectorState.STARTING
        
        # Load configuration from environment
        self.config = get_config()
        logger.info("Configuration loaded from environment")
        
        # Initialize session
        self.session = CollectionSession(
            session_id=f"es_collector_v2_{int(datetime.now().timestamp())}",
            start_time=datetime.now(timezone.utc),
            config_summary={
                'system': self.config.connection.system,
                'gateway': self.config.connection.gateway,
                'user': self.config.connection.user,
                'database_enabled': self.config.database.enabled,
                'search_exchange': self.config.search.exchange,
                'persistence_mode': 'immediate' if not args.batch_mode else 'batched'
            }
        )
        
        # Initialize components
        self.ws_client = None
        self.contract_manager = ESContractManager()
        self.subscription_manager = None
        self.db_client = None
        self.persistence_manager = None
        
        # Market hours integration
        self.market_hours = CMEGlobexHours()
        
        # Tracking
        self.discovered_contracts: List[ContractInfo] = []
        self.active_subscriptions: Set[str] = set()
        
        # Setup signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        logger.info(f"ES Futures Collector V2 initialized: {self.session.session_id}")
        logger.info(f"Configuration: {json.dumps(self.session.config_summary, indent=2)}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    async def run(self):
        """Main collection loop."""
        try:
            logger.info("Starting ES futures data collection...")
            self.state = CollectorState.STARTING
            
            # Initialize components
            await self._initialize_components()
            
            # Check market hours if not in dry run mode
            if not self.args.dry_run:
                market_state = self.market_hours.get_current_state()
                logger.info(f"Current market state: {market_state}")
                
                if market_state == MarketState.CLOSED and not self.args.force_run:
                    logger.info("Market is closed. Use --force-run to collect data anyway.")
                    return
            
            # Connect and authenticate
            await self._connect_and_authenticate()
            
            # Discover contracts
            await self._discover_contracts()
            
            # Subscribe to market data
            await self._subscribe_to_market_data()
            
            # Start collection loop
            await self._collection_loop()
            
        except KeyboardInterrupt:
            logger.info("Collection interrupted by user")
        except Exception as e:
            logger.error(f"Fatal error in collection: {e}", exc_info=True)
            self.session.connection_errors += 1
        finally:
            await self._cleanup()
    
    async def _initialize_components(self):
        """Initialize all shared components."""
        logger.info("Initializing shared components...")
        
        # Initialize WebSocket client
        self.ws_client = RithmicWebSocketClient(self.config)
        
        # Initialize database client
        self.db_client = RithmicDatabaseClient(
            self.config, 
            session_id=self.session.session_id
        )
        
        # Initialize persistence manager
        persistence_mode = PersistenceMode.BATCHED if self.args.batch_mode else PersistenceMode.IMMEDIATE
        self.persistence_manager = MarketDataPersistenceManager(
            self.config,
            self.db_client,
            mode=persistence_mode,
            batch_size=self.args.batch_size,
            batch_timeout=self.args.batch_timeout
        )
        
        # Log system startup
        self.persistence_manager.log_system_event(
            'STARTUP',
            f'ES Futures Collector V2 started: {self.session.session_id}',
            additional_data={'config': self.session.config_summary}
        )
        
        logger.info("Shared components initialized successfully")
    
    async def _connect_and_authenticate(self):
        """Connect and authenticate with Rithmic."""
        logger.info("Connecting and authenticating with Rithmic...")
        
        try:
            # Connect and authenticate for ticker plant (market data)
            infra_type = request_login_pb2.RequestLogin.SysInfraType.TICKER_PLANT
            auth_result = await self.ws_client.connect_and_authenticate(
                infra_type=infra_type,
                validate_ssl=True,
                validate_system=True
            )
            
            logger.info("Successfully connected and authenticated")
            logger.info(f"Authentication result: {auth_result.get('success', False)}")
            
            # Initialize subscription manager with the connected websocket
            self.subscription_manager = MarketDataSubscriptionManager(
                self.ws_client.websocket,
                parser=None  # Uses default parser
            )
            
            # Add persistence handler for automatic database writes
            persistence_handler = MarketDataPersistenceHandler(self.persistence_manager)
            self.subscription_manager.add_message_handler(persistence_handler.handle_market_data)
            
            # Add statistics handler
            self.subscription_manager.add_message_handler(self._handle_message_statistics)
            
        except Exception as e:
            logger.error(f"Failed to connect and authenticate: {e}")
            self.session.connection_errors += 1
            raise
    
    async def _discover_contracts(self):
        """Discover ALL available ES futures contracts."""
        logger.info("Discovering ES futures contracts...")
        self.state = CollectorState.DISCOVERING_CONTRACTS
        
        try:
            # Use the shared contract manager to discover ALL ES contracts
            contracts = await self.contract_manager.discover_all_es_contracts(
                self.ws_client.websocket,
                exchange=self.config.search.exchange
            )
            
            if not contracts:
                raise RuntimeError("No ES contracts discovered")
            
            self.discovered_contracts = contracts
            self.session.contracts_discovered = len(contracts)
            
            # Apply contract limit if specified
            if self.args.max_contracts:
                self.discovered_contracts = contracts[:self.args.max_contracts]
                logger.info(f"Limited to first {self.args.max_contracts} contracts")
            
            # Log contract summary
            contract_summary = self.contract_manager.get_contract_summary(self.discovered_contracts)
            logger.info(f"Contract discovery complete:")
            logger.info(f"  Total contracts: {contract_summary['total']}")
            logger.info(f"  Front month: {contract_summary['front_month']}")
            logger.info(f"  Back months: {len(contract_summary['back_months'])}")
            logger.info(f"  Symbols: {', '.join(contract_summary['symbols'][:10])}{'...' if len(contract_summary['symbols']) > 10 else ''}")
            
            # Log system event
            self.persistence_manager.log_system_event(
                'CONTRACT_DISCOVERY',
                f'Discovered {len(self.discovered_contracts)} ES contracts',
                additional_data=contract_summary
            )
            
        except Exception as e:
            logger.error(f"Contract discovery failed: {e}")
            self.session.connection_errors += 1
            raise
    
    async def _subscribe_to_market_data(self):
        """Subscribe to market data for all discovered contracts."""
        logger.info("Subscribing to market data...")
        self.state = CollectorState.SUBSCRIBING
        
        subscription_tasks = []
        
        for contract in self.discovered_contracts:
            # Subscribe to Level 1 data (unless level3-only mode)
            if not self.args.level3_only:
                task = self._subscribe_level1(contract)
                subscription_tasks.append(task)
            
            # Subscribe to Level 3 data (unless level1-only mode)
            if not self.args.level1_only:
                task = self._subscribe_level3(contract)
                subscription_tasks.append(task)
        
        # Execute subscriptions with concurrency control
        semaphore = asyncio.Semaphore(5)  # Limit concurrent subscriptions
        
        async def limited_subscribe(task):
            async with semaphore:
                return await task
        
        logger.info(f"Executing {len(subscription_tasks)} subscription tasks...")
        results = await asyncio.gather(*[limited_subscribe(task) for task in subscription_tasks], return_exceptions=True)
        
        # Count successful subscriptions
        level1_success = 0
        level3_success = 0
        errors = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Subscription {i} failed: {result}")
                errors += 1
                self.session.subscription_errors += 1
            else:
                subscription_id, sub_type = result
                self.active_subscriptions.add(subscription_id)
                if 'level1' in sub_type or 'bbo' in sub_type or 'trades' in sub_type:
                    level1_success += 1
                elif 'level3' in sub_type:
                    level3_success += 1
        
        self.session.contracts_subscribed_level1 = level1_success
        self.session.contracts_subscribed_level3 = level3_success
        
        logger.info(f"Subscription summary:")
        logger.info(f"  Level 1 subscriptions: {level1_success}")
        logger.info(f"  Level 3 subscriptions: {level3_success}")
        logger.info(f"  Errors: {errors}")
        
        if not self.active_subscriptions:
            raise RuntimeError("No successful subscriptions")
        
        # Log system event
        self.persistence_manager.log_system_event(
            'SUBSCRIPTIONS_COMPLETE',
            f'Subscribed to {len(self.active_subscriptions)} market data feeds',
            additional_data={
                'level1_subscriptions': level1_success,
                'level3_subscriptions': level3_success,
                'errors': errors
            }
        )
    
    async def _subscribe_level1(self, contract: ContractInfo) -> tuple:
        """Subscribe to Level 1 data for a contract."""
        try:
            subscription_id = await self.subscription_manager.subscribe_level1(
                contract.symbol,
                contract.exchange
            )
            logger.debug(f"Level 1 subscription successful: {contract.symbol}")
            return subscription_id, "level1"
        except Exception as e:
            logger.error(f"Level 1 subscription failed for {contract.symbol}: {e}")
            raise
    
    async def _subscribe_level3(self, contract: ContractInfo) -> tuple:
        """Subscribe to Level 3 data for a contract."""
        try:
            subscription_id = await self.subscription_manager.subscribe_level3(
                contract.symbol,
                contract.exchange,
                request_snapshot=True
            )
            logger.debug(f"Level 3 subscription successful: {contract.symbol}")
            return subscription_id, "level3"
        except Exception as e:
            logger.error(f"Level 3 subscription failed for {contract.symbol}: {e}")
            raise
    
    async def _collection_loop(self):
        """Main data collection loop."""
        logger.info("Starting data collection loop...")
        self.state = CollectorState.COLLECTING
        
        # Statistics logging interval
        last_stats_time = datetime.now(timezone.utc)
        stats_interval = 60  # Log stats every 60 seconds
        
        try:
            while self.running and self.ws_client.is_connected():
                try:
                    # Receive and handle market data message
                    raw_data = await asyncio.wait_for(
                        self.ws_client.websocket.recv(),
                        timeout=30.0
                    )
                    
                    # Handle the message through subscription manager
                    await self.subscription_manager.handle_message(raw_data)
                    
                    # Update session timing
                    self.session.last_message_time = datetime.now(timezone.utc)
                    
                    # Periodic statistics logging
                    now = datetime.now(timezone.utc)
                    if (now - last_stats_time).total_seconds() >= stats_interval:
                        await self._log_session_statistics()
                        last_stats_time = now
                    
                except asyncio.TimeoutError:
                    logger.warning("Message receive timeout - checking connection health")
                    if not self.ws_client.is_connected():
                        logger.error("WebSocket connection lost")
                        break
                
                except Exception as e:
                    logger.error(f"Error in collection loop: {e}")
                    self.session.parsing_errors += 1
                    # Continue collection on non-fatal errors
                    
        except Exception as e:
            logger.error(f"Fatal error in collection loop: {e}", exc_info=True)
            raise
    
    async def _handle_message_statistics(self, update: MarketDataUpdate):
        """Handle message statistics tracking."""
        if update.data_type == MarketDataType.BEST_BID_OFFER:
            self.session.bbo_messages += 1
        elif update.data_type == MarketDataType.LAST_TRADE:
            self.session.trade_messages += 1
        elif update.data_type == MarketDataType.DEPTH_BY_ORDER:
            self.session.depth_messages += 1
    
    async def _log_session_statistics(self):
        """Log current session statistics."""
        stats = self.session.to_dict()
        
        # Add persistence statistics
        persistence_stats = self.persistence_manager.get_statistics()
        stats['persistence'] = persistence_stats
        
        # Add connection info
        stats['connection'] = self.ws_client.get_connection_info()
        
        logger.info(f"Session statistics: {json.dumps(stats, indent=2)}")
        
        # Log to database
        self.persistence_manager.log_system_event(
            'STATISTICS',
            'Session statistics update',
            additional_data=stats
        )
    
    async def _cleanup(self):
        """Cleanup resources and log final statistics."""
        logger.info("Starting cleanup...")
        self.state = CollectorState.STOPPING
        
        try:
            # Flush any pending database writes
            if self.persistence_manager:
                await self.persistence_manager.flush_batches()
            
            # Unsubscribe from all market data
            if self.subscription_manager:
                for subscription_id in list(self.active_subscriptions):
                    try:
                        await self.subscription_manager.unsubscribe(subscription_id)
                    except Exception as e:
                        logger.warning(f"Error unsubscribing from {subscription_id}: {e}")
            
            # Disconnect WebSocket
            if self.ws_client:
                await self.ws_client.disconnect()
            
            # Close persistence manager
            if self.persistence_manager:
                await self.persistence_manager.close()
            
            # Log final statistics
            await self._log_final_statistics()
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        finally:
            self.state = CollectorState.STOPPED
            logger.info("Cleanup completed")
    
    async def _log_final_statistics(self):
        """Log final session statistics."""
        final_stats = self.session.to_dict()
        
        # Calculate session duration
        session_duration = datetime.now(timezone.utc) - self.session.start_time
        final_stats['session_duration_seconds'] = session_duration.total_seconds()
        
        # Add final persistence statistics
        if self.persistence_manager:
            final_stats['final_persistence'] = self.persistence_manager.get_statistics()
        
        logger.info("=== FINAL SESSION STATISTICS ===")
        logger.info(json.dumps(final_stats, indent=2))
        
        # Log to database
        if self.persistence_manager:
            self.persistence_manager.log_system_event(
                'SESSION_COMPLETE',
                f'ES Futures collection session completed: {self.session.session_id}',
                additional_data=final_stats
            )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Rebuilt ES Futures Data Collector using shared components'
    )
    
    # Collection options
    parser.add_argument('--dry-run', action='store_true',
                       help='Run without database writes (console output only)')
    parser.add_argument('--max-contracts', type=int,
                       help='Maximum number of contracts to subscribe to')
    parser.add_argument('--level1-only', action='store_true',
                       help='Subscribe to Level 1 data only (BBO and trades)')
    parser.add_argument('--level3-only', action='store_true',
                       help='Subscribe to Level 3 data only (depth by order)')
    parser.add_argument('--force-run', action='store_true',
                       help='Run even when market is closed')
    
    # Persistence options
    parser.add_argument('--batch-mode', action='store_true',
                       help='Use batched database writes for better performance')
    parser.add_argument('--batch-size', type=int, default=100,
                       help='Number of records per batch (default: 100)')
    parser.add_argument('--batch-timeout', type=float, default=5.0,
                       help='Maximum seconds to wait for batch completion (default: 5.0)')
    
    return parser.parse_args()


async def main():
    """Main entry point."""
    args = parse_arguments()
    
    # Validate arguments
    if args.level1_only and args.level3_only:
        logger.error("Cannot specify both --level1-only and --level3-only")
        return 1
    
    collector = ESFuturesCollectorV2(args)
    
    try:
        await collector.run()
        return 0
    except Exception as e:
        logger.error(f"Collection failed: {e}", exc_info=True)
        return 1


if __name__ == '__main__':
    exit_code = asyncio.run(main())
    sys.exit(exit_code)