# Environment Configuration for ES Futures Collector V2
# Copy this file to .env and fill in your values

# ==============================================================================
# RITHMIC CONNECTION SETTINGS (REQUIRED)
# ==============================================================================
RITHMIC_URI=wss://rprotocol.rithmic.com:443
RITHMIC_SYSTEM=Rithmic Paper Trading
RITHMIC_GATEWAY=Chicago Area
RITHMIC_USER=your_username_here
RITHMIC_PASSWORD=your_password_here

# ==============================================================================
# APPLICATION SETTINGS
# ==============================================================================
RITHMIC_APP_NAME=ESFuturesCollectorV2
RITHMIC_APP_VERSION=2.0.0
RITHMIC_TEMPLATE_VERSION=3.9

# ==============================================================================
# CONTRACT DISCOVERY SETTINGS
# ==============================================================================
# Exchange for ES futures
SEARCH_EXCHANGE=CME

# Search pattern for ES contracts (ES* finds all ES contracts)
SEARCH_PATTERN=ES*

# Product code for ES futures
SEARCH_PRODUCT_CODE=ES

# Instrument type (1=FUTURE)
SEARCH_INSTRUMENT_TYPE=1

# Pattern matching type (2=CONTAINS for broader search)
SEARCH_PATTERN_TYPE=2

# ==============================================================================
# DATABASE SETTINGS
# ==============================================================================
# Enable database persistence (set to 'false' for console-only mode)
ENABLE_DATABASE=true

# MySQL connection settings
MYSQL_HOST=**************
MYSQL_USER=root
MYSQL_PASSWORD=debian
MYSQL_DATABASE=rithmic_api
MYSQL_PORT=3306

# Database performance settings
MYSQL_POOL_SIZE=10
MYSQL_MAX_OVERFLOW=15
MYSQL_POOL_TIMEOUT=30

# Async insert settings for high-frequency data
MYSQL_ASYNC_INSERTS=false
MYSQL_ASYNC_QUEUE_SIZE=1000
MYSQL_BULK_BATCH_SIZE=100

# ==============================================================================
# NOTES FOR USERS
# ==============================================================================
#
# 1. REQUIRED CHANGES:
#    - Set RITHMIC_USER to your actual username
#    - Set RITHMIC_PASSWORD to your actual password
#    - Verify RITHMIC_SYSTEM matches your account type
#
# 2. CONTRACT DISCOVERY:
#    - This configuration will discover ALL ES contracts (all expiration months)
#    - No filtering for liquidity - collects from all contracts as requested
#    - Uses server-provided heartbeat intervals automatically
#
# 3. DATABASE:
#    - Database settings match the existing infrastructure
#    - Set ENABLE_DATABASE=false for testing without database writes
#
# 4. TROUBLESHOOTING:
#    - Check the logs/es_futures_collector_v2.log file for detailed information
#    - Use --dry-run flag for testing without database writes
#    - Use --max-contracts flag to limit contracts during testing