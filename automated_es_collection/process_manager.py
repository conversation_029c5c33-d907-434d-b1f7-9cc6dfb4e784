#!/usr/bin/env python3
"""
Process Manager for ES Futures Data Collection

Manages the lifecycle of the ES futures data collection process:
- Starts/stops collection based on market hours
- Monitors process health and handles restarts
- Provides graceful shutdown and process control
- Integrates with cron for automated scheduling
- Handles error recovery and process resilience

Usage:
    python process_manager.py start    # Start collection process
    python process_manager.py stop     # Stop collection process
    python process_manager.py restart  # Restart collection process
    python process_manager.py status   # Check process status
    python process_manager.py monitor  # Monitor process continuously
"""

import asyncio
import subprocess
import psutil
import signal
import sys
import os
import time
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, Tuple
import json
import argparse
from enum import Enum
from dataclasses import dataclass, asdict
import fcntl

# Import market hours calculator
from market_hours import CMEGlobexHours, MarketState

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/process_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ProcessState(Enum):
    """Process state enumeration."""
    STOPPED = "STOPPED"
    STARTING = "STARTING"
    RUNNING = "RUNNING"
    STOPPING = "STOPPING"
    ERROR = "ERROR"
    UNKNOWN = "UNKNOWN"


@dataclass
class ProcessInfo:
    """Information about the collection process."""
    pid: Optional[int] = None
    state: ProcessState = ProcessState.STOPPED
    start_time: Optional[datetime] = None
    stop_time: Optional[datetime] = None
    restart_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    memory_usage: Optional[float] = None  # MB
    cpu_usage: Optional[float] = None     # Percentage
    command_line: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, ProcessState):
                data[key] = value.value
        return data


class ESCollectionProcessManager:
    """
    Process manager for ES futures data collection.
    
    Provides comprehensive process lifecycle management including:
    - Market hours-aware start/stop scheduling
    - Process health monitoring and automatic restart
    - Resource usage tracking and limits
    - Graceful shutdown handling
    - State persistence and recovery
    """
    
    def __init__(self, script_path: str = None, work_dir: str = None):
        """
        Initialize the process manager.
        
        Args:
            script_path: Path to the collection script
            work_dir: Working directory for the collection process
        """
        self.script_path = script_path or os.path.join(
            os.path.dirname(__file__), 'es_futures_collector.py'
        )
        self.work_dir = work_dir or os.path.dirname(__file__)
        self.state_file = os.path.join(self.work_dir, 'logs', 'process_state.json')
        self.pid_file = os.path.join(self.work_dir, 'logs', 'collector.pid')
        self.lock_file = os.path.join(self.work_dir, 'logs', 'process_manager.lock')
        
        self.market_hours = CMEGlobexHours()
        self.process_info = ProcessInfo()
        self.running = True
        
        # Process management settings
        self.max_restart_attempts = 5
        self.restart_delay = 30  # seconds
        self.health_check_interval = 60  # seconds
        self.max_memory_mb = 1000  # MB
        self.max_cpu_percent = 80.0  # percent
        
        # Setup signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        logger.info(f"Process Manager initialized for script: {self.script_path}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating shutdown...")
        self.running = False
    
    def _acquire_lock(self) -> bool:
        """
        Acquire process manager lock to prevent multiple instances.
        
        Returns:
            bool: True if lock acquired successfully
        """
        try:
            os.makedirs(os.path.dirname(self.lock_file), exist_ok=True)
            self.lock_fd = open(self.lock_file, 'w')
            fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            self.lock_fd.write(str(os.getpid()))
            self.lock_fd.flush()
            return True
        except (OSError, IOError):
            return False
    
    def _release_lock(self):
        """Release process manager lock."""
        try:
            if hasattr(self, 'lock_fd'):
                fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_UN)
                self.lock_fd.close()
                os.unlink(self.lock_file)
        except (OSError, IOError):
            pass
    
    def save_state(self):
        """Save current process state to file."""
        try:
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            with open(self.state_file, 'w') as f:
                json.dump(self.process_info.to_dict(), f, indent=2)
        except Exception as e:
            logger.error(f"Error saving process state: {e}")
    
    def load_state(self) -> bool:
        """
        Load process state from file.
        
        Returns:
            bool: True if state loaded successfully
        """
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    data = json.load(f)
                
                # Reconstruct ProcessInfo object
                self.process_info.pid = data.get('pid')
                self.process_info.state = ProcessState(data.get('state', 'STOPPED'))
                self.process_info.restart_count = data.get('restart_count', 0)
                self.process_info.error_count = data.get('error_count', 0)
                self.process_info.last_error = data.get('last_error')
                self.process_info.command_line = data.get('command_line')
                
                # Parse datetime fields
                if data.get('start_time'):
                    self.process_info.start_time = datetime.fromisoformat(data['start_time'])
                if data.get('stop_time'):
                    self.process_info.stop_time = datetime.fromisoformat(data['stop_time'])
                
                return True
        except Exception as e:
            logger.error(f"Error loading process state: {e}")
        
        return False
    
    def update_process_stats(self):
        """Update process resource usage statistics."""
        if not self.process_info.pid:
            return
        
        try:
            process = psutil.Process(self.process_info.pid)
            
            # Update memory usage (in MB)
            memory_info = process.memory_info()
            self.process_info.memory_usage = memory_info.rss / (1024 * 1024)
            
            # Update CPU usage
            self.process_info.cpu_usage = process.cpu_percent()
            
            # Verify process is still the collection script
            cmdline = ' '.join(process.cmdline())
            if 'es_futures_collector.py' not in cmdline:
                logger.warning(f"PID {self.process_info.pid} is not the collection script")
                self.process_info.pid = None
                self.process_info.state = ProcessState.STOPPED
            
        except psutil.NoSuchProcess:
            logger.info(f"Process {self.process_info.pid} no longer exists")
            self.process_info.pid = None
            self.process_info.state = ProcessState.STOPPED
        except Exception as e:
            logger.error(f"Error updating process stats: {e}")
    
    def is_process_running(self) -> bool:
        """
        Check if the collection process is currently running.
        
        Returns:
            bool: True if process is running
        """
        if not self.process_info.pid:
            return False
        
        try:
            process = psutil.Process(self.process_info.pid)
            return process.is_running() and process.status() != psutil.STATUS_ZOMBIE
        except psutil.NoSuchProcess:
            return False
        except Exception:
            return False
    
    def start_collection_process(self) -> bool:
        """
        Start the ES futures collection process.
        
        Returns:
            bool: True if process started successfully
        """
        if self.is_process_running():
            logger.info("Collection process is already running")
            return True
        
        logger.info("Starting ES futures collection process...")
        
        try:
            # Prepare command
            cmd = [sys.executable, self.script_path]
            
            # Add any additional arguments here if needed
            # cmd.extend(['--log-level', 'INFO'])
            
            # Start process
            self.process_info.state = ProcessState.STARTING
            self.save_state()
            
            process = subprocess.Popen(
                cmd,
                cwd=self.work_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                start_new_session=True  # Create new process group
            )
            
            # Update process info
            self.process_info.pid = process.pid
            self.process_info.start_time = datetime.now(timezone.utc)
            self.process_info.state = ProcessState.RUNNING
            self.process_info.command_line = ' '.join(cmd)
            
            # Save PID to file
            os.makedirs(os.path.dirname(self.pid_file), exist_ok=True)
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
            
            self.save_state()
            
            logger.info(f"Collection process started with PID: {process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting collection process: {e}")
            self.process_info.last_error = str(e)
            self.process_info.error_count += 1
            self.process_info.state = ProcessState.ERROR
            self.save_state()
            return False
    
    def stop_collection_process(self, timeout: int = 30) -> bool:
        """
        Stop the ES futures collection process gracefully.
        
        Args:
            timeout: Maximum time to wait for graceful shutdown
            
        Returns:
            bool: True if process stopped successfully
        """
        if not self.is_process_running():
            logger.info("Collection process is not running")
            self.process_info.state = ProcessState.STOPPED
            self.save_state()
            return True
        
        logger.info(f"Stopping collection process (PID: {self.process_info.pid})...")
        
        try:
            process = psutil.Process(self.process_info.pid)
            
            # Try graceful shutdown first
            self.process_info.state = ProcessState.STOPPING
            self.save_state()
            
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout)
                logger.info("Process terminated gracefully")
            except psutil.TimeoutExpired:
                logger.warning("Process did not terminate gracefully, forcing kill...")
                process.kill()
                process.wait(5)  # Wait for kill to complete
                logger.info("Process killed forcefully")
            
            # Update state
            self.process_info.pid = None
            self.process_info.stop_time = datetime.now(timezone.utc)
            self.process_info.state = ProcessState.STOPPED
            
            # Remove PID file
            if os.path.exists(self.pid_file):
                os.unlink(self.pid_file)
            
            self.save_state()
            return True
            
        except psutil.NoSuchProcess:
            logger.info("Process already terminated")
            self.process_info.pid = None
            self.process_info.state = ProcessState.STOPPED
            self.save_state()
            return True
        except Exception as e:
            logger.error(f"Error stopping process: {e}")
            self.process_info.last_error = str(e)
            self.process_info.error_count += 1
            self.save_state()
            return False
    
    def restart_collection_process(self) -> bool:
        """
        Restart the collection process.
        
        Returns:
            bool: True if restart successful
        """
        logger.info("Restarting collection process...")
        
        # Stop current process
        if not self.stop_collection_process():
            logger.error("Failed to stop process for restart")
            return False
        
        # Wait before restart
        time.sleep(self.restart_delay)
        
        # Start new process
        if self.start_collection_process():
            self.process_info.restart_count += 1
            self.save_state()
            logger.info("Process restart successful")
            return True
        else:
            logger.error("Failed to start process after restart")
            return False
    
    def check_process_health(self) -> bool:
        """
        Check process health and restart if necessary.
        
        Returns:
            bool: True if process is healthy or was successfully restarted
        """
        if not self.is_process_running():
            logger.warning("Collection process is not running")
            
            # Check if we should restart based on market hours
            if self.market_hours.is_collection_time():
                logger.info("Market is open, attempting to restart process")
                return self.restart_collection_process()
            else:
                logger.info("Market is closed, not restarting process")
                return True
        
        # Update process statistics
        self.update_process_stats()
        
        # Check resource usage
        health_issues = []
        
        if (self.process_info.memory_usage and 
            self.process_info.memory_usage > self.max_memory_mb):
            health_issues.append(f"High memory usage: {self.process_info.memory_usage:.1f}MB")
        
        if (self.process_info.cpu_usage and 
            self.process_info.cpu_usage > self.max_cpu_percent):
            health_issues.append(f"High CPU usage: {self.process_info.cpu_usage:.1f}%")
        
        if health_issues:
            logger.warning(f"Process health issues detected: {', '.join(health_issues)}")
            
            # Restart if critical issues
            if (self.process_info.memory_usage and 
                self.process_info.memory_usage > self.max_memory_mb * 1.5):
                logger.error("Critical memory usage, restarting process")
                return self.restart_collection_process()
        
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get comprehensive process status.
        
        Returns:
            Dict[str, Any]: Status information
        """
        self.update_process_stats()
        
        # Get market information
        market_state = self.market_hours.get_market_state()
        should_be_running = self.market_hours.is_collection_time()
        time_diff, next_event = self.market_hours.time_until_next_event()
        
        status = {
            'process': self.process_info.to_dict(),
            'market': {
                'state': market_state.value,
                'should_be_running': should_be_running,
                'next_event': next_event,
                'time_until_next_event': str(time_diff)
            },
            'system': {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'script_path': self.script_path,
                'work_dir': self.work_dir
            }
        }
        
        return status
    
    def run_monitor_loop(self):
        """Run continuous monitoring loop."""
        logger.info("Starting process monitoring loop...")
        
        if not self._acquire_lock():
            logger.error("Another process manager instance is already running")
            return False
        
        try:
            # Load previous state
            self.load_state()
            
            last_health_check = datetime.now()
            
            while self.running:
                try:
                    # Check if we should be running based on market hours
                    should_be_running = self.market_hours.is_collection_time()
                    is_running = self.is_process_running()
                    
                    if should_be_running and not is_running:
                        logger.info("Market is open but process not running, starting...")
                        self.start_collection_process()
                    elif not should_be_running and is_running:
                        logger.info("Market is closed but process running, stopping...")
                        self.stop_collection_process()
                    
                    # Periodic health check
                    now = datetime.now()
                    if (now - last_health_check).total_seconds() >= self.health_check_interval:
                        self.check_process_health()
                        last_health_check = now
                    
                    # Save current state
                    self.save_state()
                    
                    # Sleep before next check
                    time.sleep(10)
                    
                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt, shutting down...")
                    break
                except Exception as e:
                    logger.error(f"Error in monitor loop: {e}")
                    time.sleep(30)  # Wait longer on error
            
            # Cleanup on exit
            logger.info("Shutting down process manager...")
            if self.is_process_running():
                self.stop_collection_process()
            
        finally:
            self._release_lock()
        
        logger.info("Process monitoring stopped")
        return True


def main():
    """Main entry point for process manager."""
    parser = argparse.ArgumentParser(
        description='ES Futures Collection Process Manager',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        'action',
        choices=['start', 'stop', 'restart', 'status', 'monitor'],
        help='Action to perform'
    )
    
    parser.add_argument(
        '--script-path',
        help='Path to collection script'
    )
    
    parser.add_argument(
        '--work-dir',
        help='Working directory for collection process'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Set logging level'
    )
    
    args = parser.parse_args()
    
    # Configure logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Create process manager
    manager = ESCollectionProcessManager(
        script_path=args.script_path,
        work_dir=args.work_dir
    )
    
    # Execute requested action
    if args.action == 'start':
        success = manager.start_collection_process()
        sys.exit(0 if success else 1)
    
    elif args.action == 'stop':
        success = manager.stop_collection_process()
        sys.exit(0 if success else 1)
    
    elif args.action == 'restart':
        success = manager.restart_collection_process()
        sys.exit(0 if success else 1)
    
    elif args.action == 'status':
        status = manager.get_status()
        print(json.dumps(status, indent=2))
        
        # Print summary
        process_state = status['process']['state']
        market_state = status['market']['state']
        should_run = status['market']['should_be_running']
        
        print(f"\nSummary:")
        print(f"  Process State: {process_state}")
        print(f"  Market State: {market_state}")
        print(f"  Should Be Running: {should_run}")
        
        if status['process']['pid']:
            print(f"  PID: {status['process']['pid']}")
            if status['process']['memory_usage']:
                print(f"  Memory: {status['process']['memory_usage']:.1f} MB")
            if status['process']['cpu_usage']:
                print(f"  CPU: {status['process']['cpu_usage']:.1f}%")
        
        sys.exit(0)
    
    elif args.action == 'monitor':
        success = manager.run_monitor_loop()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()