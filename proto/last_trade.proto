
package rti;

message LastTrade
	{                                     
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	// below enum is just for reference only, not used in this message
	enum PresenceBits {
	                  LAST_TRADE      =   1;
			  NET_CHANGE      =   2;
		          PRECENT_CHANGE  =   4;
		          VOLUME          =   8;
			  VWAP		  =  16;
                          }

        enum TransactionType {
	                      BUY  = 1;
		              SELL = 2;
	                     }

	required int32            template_id                  = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	optional string           symbol                       = 110100;    // PB_OFFSET + MNM_SYMBOL
	optional string           exchange                     = 110101;    // PB_OFFSET + MNM_EXCHANGE

	optional uint32           presence_bits                = 149138;    // PB_OFFSET + MNM_PRICING_INDICATOR
	optional uint32           clear_bits                   = 154571;    // PB_OFFSET + MNM_DISPLAY_INDICATOR
	optional bool             is_snapshot                  = 110121;    // PB_OFFSET + MNM_UPDATE_TYPE               

	optional double           trade_price                  = 100006;    // PB_OFFSET + MNM_TRADE_PRICE
	optional int32            trade_size                   = 100178;    // PB_OFFSET + MNM_TRADE_SIZE
	optional TransactionType  aggressor                    = 112003;    // PB_OFFSET + MNM_TRANSACTION_TYPE

	optional string           exchange_order_id            = 149238;    // PB_OFFSET + MNM_EXCH_ORD_ID
	optional string           aggressor_exchange_order_id  = 154641;    // PB_OFFSET + MNM_AGGRESSOR_EXCH_ORD_ID

	optional double           net_change                   = 100011;    // PB_OFFSET + MNM_NET_CHANGE
	optional double           percent_change               = 100056;    // PB_OFFSET + MNM_PERCENT_CHANGE
	optional uint64           volume                       = 100032;    // PB_OFFSET + MNM_TRADE_VOLUME
	optional double           vwap                         = 101379;    // PB_OFFSET + MNM_VWAP	

	optional string           trade_time                   = 100379;

	optional int32            ssboe                        = 150100;    // PB_OFFSET + MNM_SECONDS_SINCE_BOE
	optional int32            usecs                        = 150101;    // PB_OFFSET + MNM_USECS

	optional int32            source_ssboe                 = 150400;    // PB_OFFSET + MNM_SOURCE_SSBOE
	optional int32            source_usecs                 = 150401;    // PB_OFFSET + MNM_SOURCE_USECS
	optional int32            source_nsecs                 = 150404;    // PB_OFFSET + MNM_SOURCE_NSECS

	optional int32            jop_ssboe                    = 150600;    // PB_OFFSET + MNM_JOP_SSBOE
	optional int32            jop_nsecs                    = 150604;    // PB_OFFSET + MNM_JOP_NSECS
	}
