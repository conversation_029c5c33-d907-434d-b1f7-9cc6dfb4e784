
package rti;

message UpdateEasyToBorrowList
	{       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32   template_id       = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID

	optional string  broker_dealer     = 154612;    // PB_OFFSET + MNM_BROKER_DEALER
	optional string  symbol            = 110100;    // PB_OFFSET + MNM_SYMBOL
	optional string  symbol_name       = 100003;    // PB_OFFSET + MNM_SYMBOL_NAME

	optional int32   qty_available     = 154613;    // PB_OFFSET + MNM_TOTAL_AVAILABLE_QTY
	optional int32   qty_needed        = 154614;    // PB_OFFSET + MNM_TOTAL_USED_QTY

	optional bool    borrowable        = 110353;    // PB_OFFSET + MNM_SHORT_LIST_INDICATOR
	}
