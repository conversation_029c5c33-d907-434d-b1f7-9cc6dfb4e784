
package rti;

message RequestEasyToBorrowList
	{                                
	// PB_OFFSET = 100000, is the offset added for each MNM field id

        enum Request {
		      SUBSCRIBE   = 1;
		      UNSUBSCRIBE = 2;
	             } 

	required int32    template_id  = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string   user_msg     = 132760;    // PB_OFFSET + MNM_USER_MSG
        optional Request  request      = 100000;    // PB_OFFSET + MNM_REQUEST
	}
