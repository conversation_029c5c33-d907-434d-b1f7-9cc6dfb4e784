
package rti;

message BracketUpdates
	{                                
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32    template_id               = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID

	optional string   fcm_id                    = 154013;    // PB_OFFSET + MNM_FCM_ID
	optional string   ib_id                     = 154014;    // PB_OFFSET + MNM_IB_ID
	optional string   account_id                = 154008;    // PB_OFFSET + MNM_ACCOUNT_ID
	optional string   basket_id                 = 110300;    // PB_OFFSET + MNM_BASKET_ID 

	optional int32    stop_ticks                = 154458;    // PB_OFFSET + MNM_STOP_TICKS
	optional int32    stop_quantity             = 154459;    // PB_OFFSET + MNM_STOP_QUANTITY
	optional int32    stop_quantity_released    = 154466;    // PB_OFFSET + MNM_STOP_QUANTITY_RELEASED

	optional int32    target_ticks              = 154456;    // PB_OFFSET + MNM_TARGET_TICKS
	optional int32    target_quantity           = 154457;    // PB_OFFSET + MNM_TARGET_QUANTITY
	optional int32    target_quantity_released  = 154460;    // PB_OFFSET + MNM_TARGET_QUANTITY_RELEASED
	}
