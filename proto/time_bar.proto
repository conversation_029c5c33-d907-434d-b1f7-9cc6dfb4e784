
package rti;

message TimeBar
	{                                     
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	enum BarType {
	              SECOND_BAR    =  1;
		      MINUTE_BAR    =  2;
		      DAILY_BAR     =  3;
		      WEEKLY_BAR    =  4;
	             }

	required int32   template_id                 = 154467;      // PB_OFFSET + MNM_TEMPLATE_ID

	optional string  symbol                      = 110100;      // PB_OFFSET + MNM_SYMBOL
	optional string  exchange                    = 110101;      // PB_OFFSET + MNM_EXCHANGE

	optional BarType type                        = 119200;      // PB_OFFSET + MNM_DATA_BAR_TYPE
	optional string  period                      = 119112;      // PB_OFFSET + MNM_TIME_BAR_PERIOD
	optional int32   marker                      = 119100;      // PB_OFFSET + MNM_TIME_BAR_MARKER
	optional uint64  num_trades                  = 119109;      // PB_OFFSET + MNM_TIME_BAR_NUM_TRADES	  
	optional uint64  volume                      = 119110;      // PB_OFFSET + MNM_TIME_BAR_TRADE_VOLUME
	optional uint64  bid_volume                  = 119117;      // PB_OFFSET + MNM_TIME_BAR_BID_VOLUME
	optional uint64  ask_volume                  = 119118;      // PB_OFFSET + MNM_TIME_BAR_ASK_VOLUME
	optional double  open_price                  = 100019;      // PB_OFFSET + MNM_OPEN_PRICE
	optional double  close_price                 = 100021;      // PB_OFFSET + MNM_CLOSE_TRADE_PRICE
	optional double  high_price                  = 100012;      // PB_OFFSET + MNM_HIGH_PRICE
	optional double  low_price                   = 100013;      // PB_OFFSET + MNM_LOW_PRICE 
	optional double  settlement_price            = 100070;      // PB_OFFSET + MNM_SETTLEMENT_PRICE
	optional bool    has_settlement_price        = 149138;      // PB_OFFSET + MNM_PRICING_INDICATOR
	optional bool    must_clear_settlement_price = 154571;      // PB_OFFSET + MNM_DISPLAY_INDICATOR
	}
