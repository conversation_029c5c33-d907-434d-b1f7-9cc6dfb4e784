
package rti;

message ResponseProductCodes
	{       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32    template_id                  = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string   user_msg                     = 132760;    // PB_OFFSET + MNM_USER_MSG	
	repeated string   rq_handler_rp_code           = 132764;    // PB_OFFSET + MNM_REQUEST_HANDLER_RESPONSE_CODE
	repeated string   rp_code                      = 132766;    // PB_OFFSET + MNM_RESPONSE_CODE

	optional string   exchange                     = 110101;  // PB_OFFSET + MNM_EXCHANGE
	optional string   symbol_name                  = 100003;  // PB_OFFSET + MNM_SYMBOL_NAME	
	optional string   product_code                 = 100749;  // PB_OFFSET + MNM_PRODUCT_CODE
	optional string   timezone_time_of_interest    = 154682;  // PB_OFFSET + MNM_TIMEZONE_TIME_OF_INTEREST

	optional int32    begin_time_of_interest_msm   = 154680;  // PB_OFFSET + MNM_BEGIN_TIME_OF_INTEREST_MSM
	optional int32    end_time_of_interest_msm     = 154681;  // PB_OFFSET + MNM_END_TIME_OF_INTEREST_MSM
	}
