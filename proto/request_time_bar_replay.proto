
package rti;

message RequestTimeBarReplay
	{          
	enum BarType {
	              SECOND_BAR    =  1;
		      MINUTE_BAR    =  2;
		      DAILY_BAR     =  3;
		      WEEKLY_BAR    =  4;
	             }

        enum Direction {
	                FIRST = 1;
			LAST  = 2;
	               }

	enum TimeOrder {
	                 FORWARDS  = 1;
		         BACKWARDS = 2;
	               }

                            
	required int32      template_id        = 154467;
	repeated string     user_msg           = 132760;

	optional string     symbol             = 110100;
	optional string     exchange           = 110101;
 	optional BarType    bar_type           = 119200;

	optional int32      bar_type_period    = 119112;
	optional int32      start_index        = 153002;
	optional int32      finish_index       = 153003;
	optional int32      user_max_count     = 154020;

	optional Direction  direction          = 149253;
	optional TimeOrder  time_order         = 149307;

	optional bool       resume_bars        = 153642;
	}
