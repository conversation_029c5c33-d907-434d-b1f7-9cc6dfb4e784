
package rti;

message OpenInterest
	{                                     
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32  template_id        = 154467;      // PB_OFFSET + MNM_TEMPLATE_ID
	optional string symbol             = 110100;      // PB_OFFSET + MNM_SYMBOL
	optional string exchange           = 110101;      // PB_OFFSET + MNM_EXCHANGE

	optional bool   is_snapshot        = 110121;      // PB_OFFSET + MNM_UPDATE_TYPE               
	optional bool   should_clear       = 154571;      // PB_OFFSET + MNM_DISPLAY_INDICATOR
	optional uint64 open_interest      = 100064;      // PB_OFFSET + MNM_OPEN_INTEREST

	optional int32  ssboe              = 150100;      // PB_OFFSET + MNM_SECONDS_SINCE_BOE
	optional int32  usecs              = 150101;      // PB_OFFSET + MNM_USECS
	}
