
package rti;

message FrontMonthContractUpdate
	{                                
	// PB_OFFSET = 100000, is the offset added for each MNM field id
     
	required int32    template_id             = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID

	optional string   symbol                  = 110100;    // PB_OFFSET + MNM_SYMBOL
	optional string   exchange                = 110101;    // PB_OFFSET + MNM_EXCHANGE

	optional bool     is_front_month_symbol   = 149166;    // PB_OFFSET + MNM_STATUS_INDICATOR 
	optional string   symbol_name             = 100003;    // PB_OFFSET + MNM_SYMBOL_NAME	
	optional string   trading_symbol          = 157095;    // PB_OFFSET + MNM_TRADING_SYMBOL
	optional string   trading_exchange        = 157096;    // PB_OFFSET + MNM_TRADING_EXCHANGE
	}
