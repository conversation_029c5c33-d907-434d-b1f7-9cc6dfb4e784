
package rti;

message ResponseTradeRoutes
	{       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32    template_id               = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string   user_msg                  = 132760;    // PB_OFFSET + MNM_USER_MSG	
	repeated string   rq_handler_rp_code        = 132764;    // PB_OFFSET + MNM_REQUEST_HANDLER_RESPONSE_CODE
	repeated string   rp_code                   = 132766;    // PB_OFFSET + MNM_RESPONSE_CODE

	optional string   fcm_id                    = 154013;    // PB_OFFSET + MNM_FCM_ID
	optional string   ib_id                     = 154014;    // PB_OFFSET + MNM_IB_ID
	optional string   exchange                  = 110101;    // PB_OFFSET + MNM_EXCHANGE
	optional string   trade_route               = 112016;    // PB_OFFSET + MNM_TRADE_ROUTE
	optional string   status                    = 131407;    // PB_OFFSET + MNM_SERVICE_STATE
	optional bool     is_default                = 154689;    // PB_OFFSET + MNM_DEFAULT_ROUTE
	}
