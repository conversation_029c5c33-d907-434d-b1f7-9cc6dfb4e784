
package rti;

message ResponseListExchangePermissions
	{       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	enum EntitlementFlag {
	                      ENABLED   =  1;
			      DISABLED  =  2;
	                     }

	required int32           template_id          = 154467;  // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string          user_msg             = 132760;  // PB_OFFSET + MNM_USER_MSG	
	repeated string          rq_handler_rp_code   = 132764;  // PB_OFFSET + MNM_REQUEST_HANDLER_RESPONSE_CODE
	repeated string          rp_code              = 132766;  // PB_OFFSET + MNM_RESPONSE_CODE

	optional string          exchange             = 110101;  // PB_OFFSET + MNM_EXCHANGE
	optional EntitlementFlag entitlement_flag     = 153400;  // PB_OFFSET + MNM_ENTITLEMENT_FLAG
	}
