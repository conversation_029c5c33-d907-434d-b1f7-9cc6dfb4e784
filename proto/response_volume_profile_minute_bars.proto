
package rti;

message ResponseVolumeProfileMinuteBars
	{                       
	required int32   template_id                    = 154467;
	optional string  request_key                    = 132758;
	repeated string  user_msg                       = 132760;
	repeated string  rq_handler_rp_code             = 132764;
	repeated string  rp_code                        = 132766;

	optional string  symbol                         = 110100;
	optional string  exchange                       = 110101;

	optional string  period                         = 119215;
	optional int32   marker                         = 119100;
	optional uint64  num_trades                     = 119204;
	optional uint64  volume                         = 119205;
	optional uint64  bid_volume                     = 119213;
	optional uint64  ask_volume                     = 119214;
	optional double  open_price                     = 100019;
	optional double  close_price                    = 100021;
	optional double  high_price                     = 100012;
	optional double  low_price                      = 100013;

	repeated double  profile_price                  = 119216;
	repeated int32   profile_no_aggressor_volume    = 119217;
	repeated int32   profile_bid_volume             = 119218;
	repeated int32   profile_ask_volume             = 119219;
	repeated int32   profile_no_aggressor_trades    = 119220;
	repeated int32   profile_bid_aggressor_trades   = 119221;
	repeated int32   profile_ask_aggressor_trades   = 119222;
	}
