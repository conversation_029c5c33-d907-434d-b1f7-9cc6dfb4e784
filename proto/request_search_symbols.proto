
package rti;

message RequestSearchSymbols
	{                                
	// PB_OFFSET = 100000, is the offset added for each MNM field id

        enum Pattern {
	               EQUALS   =  1;
		       CONTAINS =  2;
                     } 

        enum InstrumentType {
	                      FUTURE            =   1;
			      FUTURE_OPTION     =   2;
			      FUTURE_STRATEGY   =   3;
			      EQUITY            =   4;
			      EQUITY_OPTION	=   5;	      
			      EQUITY_STRATEGY	=   6; 
			      INDEX             =   7;
			      INDEX_OPTION      =   8;
			      SPREAD            =   9;
			      SYNTHETIC         =  10;
                            }
     
	required int32           template_id         = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string          user_msg            = 132760;    // PB_OFFSET + MNM_USER_MSG

	optional string          search_text         = 120008;    // PB_OFFSET + MNM_TEXT
	optional string          exchange            = 110101;    // PB_OFFSET + MNM_EXCHANGE
	optional string          product_code        = 100749;    // PB_OFFSET + MNM_PRODUCT_CODE
	optional InstrumentType  instrument_type     = 110116;    // PB_OFFSET + MNM_INSTRUMENT_TYPE
	optional Pattern         pattern             = 155008;    // PB_OFFSET + MNM_SEARCH_PATTERN
	}
