
package rti;

message RequestOCOOrder
	{                                
        enum TransactionType {
	                      BUY  = 1;
		              SELL = 2;
	                     }

        enum Duration {
	               DAY  = 1;
		       GTC  = 2;
		       IOC  = 3;
		       FOK  = 4;
                      }
 
        enum PriceType {
	                LIMIT        = 1;
			MARKET       = 2;
			STOP_LIMIT   = 3;
			STOP_MARKET  = 4;
                       }

        enum OrderPlacement {
	                     MANUAL  = 1;
			     AUTO    = 2;
	                    }

	required int32             template_id            = 154467;
	repeated string            user_msg               = 132760;
	repeated string            user_tag               = 154119;
	repeated string            window_name            = 154629;

	optional string            fcm_id                 = 154013;
	optional string            ib_id                  = 154014;
	optional string            account_id             = 154008;

	repeated string            symbol                 = 110100;
	repeated string            exchange               = 110101;

	repeated  int32            quantity               = 112004;
	repeated  double           price                  = 110306;
	repeated  double           trigger_price          = 149247;

	repeated  TransactionType  transaction_type       = 112003;
	repeated  Duration         duration               = 112005;
	repeated  PriceType        price_type             = 112008;

	repeated  string           trade_route            = 112016;
	repeated  OrderPlacement   manual_or_auto         = 154710;

	repeated  bool             trailing_stop          = 157063;
        repeated  int32            trail_by_ticks         = 157064;   
        repeated  int32            trail_by_price_id      = 157065;

	optional  int32            cancel_at_ssboe        = 157085;
	optional  int32            cancel_at_usecs        = 157086;
        optional  int32            cancel_after_secs      = 154488;
	}
