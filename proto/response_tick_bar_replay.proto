
package rti;

message ResponseTickBarReplay
	{
	enum BarType {
	              TICK_BAR     =  1;
		      RANGE_BAR    =  2;
		      VOLUME_BAR   =  3;
	             }

        enum BarSubType {
	                 REGULAR  = 1;
			 CUSTOM   = 2;
	                }
              
	required int32      template_id                  = 154467;
	optional string     request_key                  = 132758;
	repeated string     user_msg                     = 132760;
	repeated string     rq_handler_rp_code           = 132764;
	repeated string     rp_code                      = 132766;

	optional string     symbol                       = 110100;
	optional string     exchange                     = 110101;

	optional BarType    type                         = 119200;
	optional BarSubType sub_type                     = 119208;
	optional string     type_specifier               = 148162;
	optional uint64     num_trades                   = 119204;
	optional uint64     volume                       = 119205;
	optional uint64     bid_volume                   = 119213;
	optional uint64     ask_volume                   = 119214;
	optional double     open_price                   = 100019;
	optional double     close_price                  = 100021;
	optional double     high_price                   = 100012;
	optional double     low_price                    = 100013;
	optional int32      custom_session_open_ssm      = 119209;
	repeated int32      data_bar_ssboe               = 119202;
	repeated int32	    data_bar_usecs               = 119203;
	}
