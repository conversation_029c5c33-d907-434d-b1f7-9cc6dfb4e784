
package rti;

message UserAccountUpdate
	{       
	enum UpdateType {
	                 ADD      =  1;
			 REMOVE   =  2;
	                }

	enum AccessType {
	                 READ_ONLY   =  0;
			 READ_WRITE  =  1;
	                }

	required int32       template_id      = 154467;

	optional UpdateType  update_type      = 154288;
	optional AccessType  access_type      = 154000;

	optional string      source_user_id   = 154247;
	optional string      user             = 131003;
	optional string      fcm_id           = 154013;
	optional string      ib_id            = 154014;
	optional string      account_id       = 154008;
	optional string      account_name     = 154002;

	optional int32       ssboe            = 150100;
	optional int32       usecs            = 150101;
	}
