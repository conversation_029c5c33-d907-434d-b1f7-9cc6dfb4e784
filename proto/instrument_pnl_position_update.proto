
package rti;

message InstrumentPnLPositionUpdate
	{       
	required int32    template_id                = 154467;
	optional bool     is_snapshot                = 110121;

	optional string   fcm_id                     = 154013;
	optional string   ib_id                      = 154014;
	optional string   account_id                 = 154008;
	
	optional string   symbol                     = 110100;
	optional string   exchange                   = 110101;
	optional string   product_code               = 100749;
	optional string   instrument_type            = 110116;

	optional int32    fill_buy_qty               = 154041;
	optional int32    fill_sell_qty              = 154042;

	optional int32    order_buy_qty              = 154037;
	optional int32    order_sell_qty             = 154038;

	optional int32    buy_qty                    = 154260;
	optional int32    sell_qty                   = 154261;

	optional double   avg_open_fill_price        = 154434;

	optional double   day_open_pnl               = 157954;
        optional double   day_closed_pnl             = 157955;
	optional double   day_pnl                    = 157956;
	optional double   day_open_pnl_offset        = 157957;
	optional double   day_closed_pnl_offset      = 157958;

	optional string   mtm_security               = 154263;
	optional string   open_long_options_value    = 157105;
	optional string   open_short_options_value   = 157106;
	optional string   closed_options_value       = 157107;
	optional string   option_cash_reserved       = 157111;

	optional string   open_position_pnl          = 156961;
	optional int32    open_position_quantity     = 156962;
	optional string   closed_position_pnl        = 156963;

	optional int32    closed_position_quantity   = 156964;
	optional int32    net_quantity               = 156967;

	optional int32    ssboe                      = 150100;
	optional int32    usecs                      = 150101;
	}
