
package rti;

message ResponseSearchSymbols
	{       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32    template_id           = 154467;  // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string   user_msg              = 132760;  // PB_OFFSET + MNM_USER_MSG	
	repeated string   rq_handler_rp_code    = 132764;  // PB_OFFSET + MNM_REQUEST_HANDLER_RESPONSE_CODE
	repeated string   rp_code               = 132766;  // PB_OFFSET + MNM_RESPONSE_CODE

	optional string symbol                  = 110100;  // PB_OFFSET + MNM_SYMBOL
	optional string exchange                = 110101;  // PB_OFFSET + MNM_EXCHANGE
	optional string symbol_name             = 100003;  // PB_OFFSET + MNM_SYMBOL_NAME	
	optional string product_code            = 100749;  // PB_OFFSET + MNM_PRODUCT_CODE
	optional string instrument_type         = 110116;  // PB_OFFSET + MNM_INSTRUMENT_TYPE
	optional string expiration_date         = 100067;  // PB_OFFSET + MNM_EXPIRATION_DATE
	}
