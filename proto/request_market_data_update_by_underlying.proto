
package rti;

message RequestMarketDataUpdateByUnderlying
	{                                
	// update bits and Request enum field defined here is an exact copy of request_market_data_update.proto
	// Make sure they are always the same in both proto files.

	// PB_OFFSET = 100000, is the offset added for each MNM field id

	// bit constants are defined using enum

	enum UpdateBits {
	                 LAST_TRADE              =       1;
		         BBO                     =       2;
		         ORDER_BOOK              =       4;
		         OPEN                    =       8;
			 OPENING_INDICATOR       =      16;
		         HIGH_LOW                =      32;
			 HIGH_BID_LOW_ASK        =      64;
		         CLOSE                   =     128;
			 CLOSING_INDICATOR       =     256;
			 SETTLEMENT              =     512;
			 MARKET_MODE             =    1024;
			 OPEN_INTEREST           =    2048;
			 MARGIN_RATE             =    4096;
			 HIGH_PRICE_LIMIT        =    8192;
			 LOW_PRICE_LIMIT         =   16384;
			 PROJECTED_SETTLEMENT    =   32768;
			}
 
        enum Request {
	             SUBSCRIBE   = 1;
		     UNSUBSCRIBE = 2;
	             } 
     
	required int32   template_id        = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string  user_msg           = 132760;    // PB_OFFSET + MNM_USER_MSG

	optional string  underlying_symbol  = 101026;    // PB_OFFSET + MNM_UNDERLYING_SYMBOL
	optional string  exchange           = 110101;    // PB_OFFSET + MNM_EXCHANGE
	optional string  expiration_date    = 100067;    // PB_OFFSET + MNM_EXPIRATION_DATE
        optional Request request            = 100000;    // PB_OFFSET + MNM_REQUEST
	optional uint32  update_bits        = 154211;    // PB_OFFSET + MNM_MESSAGE_ID
	}
