
package rti;

message RequestDepthByOrderSnapshot
	{          
	// PB_OFFSET = 100000, is the offset added for each MNM field id
                            
	required int32    template_id  = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string   user_msg     = 132760;    // PB_OFFSET + MNM_USER_MSG

	optional string   symbol       = 110100;    // PB_OFFSET + MNM_SYMBOL
	optional string   exchange     = 110101;    // PB_OFFSET + MNM_EXCHANGE
 	optional double   depth_price  = 154405;    // PB_OFFSET + MNM_MARKET_DEPTH_PRICE
	}
