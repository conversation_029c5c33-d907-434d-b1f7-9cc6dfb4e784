
package rti;

message RequestBracketOrder
	{                                
	enum UserType {
		      USER_TYPE_ADMIN  = 0;
	              USER_TYPE_FCM    = 1;
		      USER_TYPE_IB     = 2;
		      USER_TYPE_TRADER = 3;
	              }

	enum BracketType {
	                  STOP_ONLY               = 1;
			  TARGET_ONLY             = 2;
			  TARGET_AND_STOP         = 3;
			  STOP_ONLY_STATIC        = 4;
			  TARGET_ONLY_STATIC      = 5;
			  TARGET_AND_STOP_STATIC  = 6;
                         }

        enum TransactionType {
	                      BUY  = 1;
		              SELL = 2;
	                     }

        enum Duration {
	               DAY  = 1;
		       GTC  = 2;
		       IOC  = 3;
		       FOK  = 4;
                      }
 
        enum PriceType {
	                LIMIT              =  1;
			MARKET             =  2;
			STOP_LIMIT         =  3;
			STOP_MARKET        =  4;
			MARKET_IF_TOUCHED  =  5;
			LIMIT_IF_TOUCHED   =  6;
                       }

        enum OrderPlacement {
	                     MANUAL  = 1;
			     AUTO    = 2;
	                    }

	enum PriceField {
	                 BID_PRICE    =  1;
			 OFFER_PRICE  =  2;
			 TRADE_PRICE  =  3;
			 LEAN_PRICE   =  4;
	                }

	enum Condition {
	     	        EQUAL_TO               =  1;
			NOT_EQUAL_TO           =  2;
			GREATER_THAN           =  3;
			GREATER_THAN_EQUAL_TO  =  4;
			LESSER_THAN            =  5;
			LESSER_THAN_EQUAL_TO   =  6;
	               }


	required  int32               template_id                         = 154467;
	repeated  string              user_msg                            = 132760;
	optional  string              user_tag                            = 154119;
	optional  string              window_name                         = 154629;

	optional  string              fcm_id                              = 154013;
	optional  string              ib_id                               = 154014;
	optional  string              account_id                          = 154008;

	optional  string              symbol                              = 110100;
	optional  string              exchange                            = 110101;

	optional  int32               quantity                            = 112004;
	optional  double              price                               = 110306;
	optional  double              trigger_price                       = 149247;

	optional  TransactionType     transaction_type                    = 112003;
	optional  Duration            duration                            = 112005;
	optional  PriceType           price_type                          = 112008;

	optional  string              trade_route                         = 112016;
	optional  OrderPlacement      manual_or_auto                      = 154710;

	optional  UserType            user_type                           = 154036;
	optional  BracketType         bracket_type                        = 157087;

	optional  int32               break_even_ticks                    = 157170;
	optional  int32               break_even_trigger_ticks            = 157172;

	repeated  int32               target_quantity                     = 154457;
	repeated  int32               target_ticks                        = 154456;

	repeated  int32		      stop_quantity                       = 154459;
	repeated  int32		      stop_ticks	                  = 154458;

	optional  int32               trailing_stop_trigger_ticks         = 157124;

	optional  bool	              trailing_stop_by_last_trade_price   = 157062;
	optional  bool                target_market_order_if_touched      = 157151;
	optional  bool                stop_market_on_reject               = 154857;

	optional  int32		      target_market_at_ssboe              = 157145;
	optional  int32		      target_market_at_usecs		  = 157146;

	optional  int32		      stop_market_at_ssboe		  = 157147;
	optional  int32		      stop_market_at_usecs		  = 157148;

	optional  int32		      target_market_order_after_secs	  = 157149;

	optional  int32               release_at_ssboe                    = 154487;
	optional  int32               release_at_usecs                    = 154549;

	optional  int32               cancel_at_ssboe                     = 157085;
	optional  int32               cancel_at_usecs                     = 157086;
        optional  int32               cancel_after_secs                   = 154488;

	optional  string              if_touched_symbol                   = 154451;
	optional  string              if_touched_exchange                 = 154452;
	optional  Condition           if_touched_condition                = 154453;
	optional  PriceField          if_touched_price_field              = 154454;
	optional  double              if_touched_price                    = 153632;
	}
