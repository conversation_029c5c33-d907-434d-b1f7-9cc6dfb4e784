
package rti;

message ResponseDepthByOrderSnapshot
	{                       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

        enum TransactionType {
	                      BUY  = 1;
		              SELL = 2;
	                     }
              
	required int32             template_id            = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string            user_msg               = 132760;    // PB_OFFSET + MNM_USER_MSG	
	repeated string            rq_handler_rp_code     = 132764;    // PB_OFFSET + MNM_REQUEST_HANDLER_RESPONSE_CODE
	repeated string            rp_code                = 132766;    // PB_OFFSET + MNM_RESPONSE_CODE

	optional string            exchange               = 110101;    // PB_OFFSET + MNM_EXCHANGE
	optional string            symbol                 = 110100;    // PB_OFFSET + MNM_SYMBOL
	optional uint64            sequence_number        = 112002;    // PB_OFFSET + MNM_SEQUENCE_NUMBER

	optional TransactionType   depth_side             = 153612;    // PB_OFFSET + MNM_MARKET_DEPTH_SIDE
	optional double            depth_price            = 154405;    // PB_OFFSET + MNM_MARKET_DEPTH_PRICE
	repeated int32             depth_size             = 154406;    // PB_OFFSET + MNM_MARKET_DEPTH_SIZE
	repeated uint64            depth_order_priority   = 153613;    // PB_OFFSET + MNM_MARKET_DEPTH_ORDER_PRIORITY
	repeated string            exchange_order_id      = 149238;    // PB_OFFSET + MNM_EXCH_ORD_ID
	}
