
package rti;

message ResponseEasyToBorrowList
	{       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32   template_id          = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string  user_msg             = 132760;    // PB_OFFSET + MNM_USER_MSG	
	repeated string  rq_handler_rp_code   = 132764;    // PB_OFFSET + MNM_REQUEST_HANDLER_RESPONSE_CODE
	repeated string  rp_code              = 132766;    // PB_OFFSET + MNM_RESPONSE_CODE

	optional string  broker_dealer        = 154612;    // PB_OFFSET + MNM_BROKER_DEALER
	optional string  symbol               = 110100;    // PB_OFFSET + MNM_SYMBOL
	optional string  symbol_name          = 100003;    // PB_OFFSET + MNM_SYMBOL_NAME

	optional int32   qty_available        = 154613;    // PB_OFFSET + MNM_TOTAL_AVAILABLE_QTY
	optional int32   qty_needed           = 154614;    // PB_OFFSET + MNM_TOTAL_USED_QTY

	optional bool    borrowable           = 110353;    // PB_OFFSET + MNM_SHORT_LIST_INDICATOR
	}
