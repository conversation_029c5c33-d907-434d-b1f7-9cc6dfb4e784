
package rti;

message TradeStatistics
	{                                     
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	// below enum is just for reference only, not used in this message
	enum PresenceBits {
	                  OPEN             =    1;
		          HIGH             =    2;
		          LOW              =    4;
                          }


	required int32  template_id        = 154467;      // PB_OFFSET + MNM_TEMPLATE_ID
	optional string symbol             = 110100;      // PB_OFFSET + MNM_SYMBOL
	optional string exchange           = 110101;      // PB_OFFSET + MNM_EXCHANGE

	optional uint32 presence_bits      = 149138;      // PB_OFFSET + MNM_PRICING_INDICATOR
	optional uint32 clear_bits         = 154571;      // PB_OFFSET + MNM_DISPLAY_INDICATOR
	optional bool   is_snapshot        = 110121;      // PB_OFFSET + MNM_UPDATE_TYPE               

	optional double open_price         = 100019;      // PB_OFFSET + MNM_OPEN_PRICE
	optional double high_price         = 100012;      // PB_OFFSET + MNM_HIGH_PRICE
	optional double low_price          = 100013;      // PB_OFFSET + MNM_LOW_PRICE

	optional int32  ssboe              = 150100;      // PB_OFFSET + MNM_SECONDS_SINCE_BOE
	optional int32  usecs              = 150101;      // PB_OFFSET + MNM_USECS

	optional int32  source_ssboe       = 150400;      // PB_OFFSET + MNM_SOURCE_SSBOE
	optional int32  source_usecs       = 150401;      // PB_OFFSET + MNM_SOURCE_USECS
	optional int32  source_nsecs       = 150404;      // PB_OFFSET + MNM_SOURCE_NSECS

	optional int32  jop_ssboe          = 150600;      // PB_OFFSET + MNM_JOP_SSBOE
	optional int32  jop_nsecs          = 150604;      // PB_OFFSET + MNM_JOP_NSECS
	}
