
package rti;

message ResponseShowBracketStops
	{       
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	required int32    template_id                  = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string   user_msg                     = 132760;    // PB_OFFSET + MNM_USER_MSG	
	repeated string   rq_handler_rp_code           = 132764;    // PB_OFFSET + MNM_REQUEST_HANDLER_RESPONSE_CODE
	repeated string   rp_code                      = 132766;    // PB_OFFSET + MNM_RESPONSE_CODE

	optional string   basket_id                    = 110300;    // PB_OFFSET + MNM_BASKET_ID
	optional string   stop_quantity                = 154459;    // PB_OFFSET + MNM_STOP_QUANTITY
	optional string   stop_quantity_released       = 154466;    // PB_OFFSET + MNM_STOP_QUANTITY_RELEASED
	optional string   stop_ticks                   = 154458;    // PB_OFFSET + MNM_STOP_TICKS
	optional string   bracket_trailing_field_id    = 157062;    // PB_OFFSET + MNM_BRACKET_TRAILING_FIELD_ID
	optional string   trailing_stop_trigger_ticks  = 157124;    // PB_OFFSET + MNM_TRAILING_STOP_TRIGGER_TICKS
	}
