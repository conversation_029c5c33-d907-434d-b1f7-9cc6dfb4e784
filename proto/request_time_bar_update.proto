
package rti;

message RequestTimeBarUpdate
	{          
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	enum BarType {
	              SECOND_BAR    =  1;
		      MINUTE_BAR    =  2;
		      DAILY_BAR     =  3;
		      WEEKLY_BAR    =  4;
	             }
 
        enum Request {
	              SUBSCRIBE   = 1;
		      UNSUBSCRIBE = 2;
	             } 
                            
	required int32    template_id      = 154467;    // PB_OFFSET + MNM_TEMPLATE_ID
	repeated string   user_msg         = 132760;    // PB_OFFSET + MNM_USER_MSG

	optional string   symbol           = 110100;    // PB_OFFSET + MNM_SYMBOL
	optional string   exchange         = 110101;    // PB_OFFSET + MNM_EXCHANGE
        optional Request  request          = 100000;    // PB_OFFSET + MNM_REQUEST
	optional BarType  bar_type         = 119200;    // PB_OFFSET + MNM_DATA_BAR_TYPE
	optional int32    bar_type_period  = 119112;    // PB_OFFSET + MNM_TIME_BAR_PERIOD
	}
