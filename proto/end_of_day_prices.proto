
package rti;

message EndOfDayPrices
	{                                     
	// below enum is just for reference only, not used in this message
	enum PresenceBits {
	                  CLOSE                 =  1;
		          SETTLEMENT            =  2;
			  PROJECTED_SETTLEMENT  =  4;
			  ADJUSTED_CLOSE        =  8;
                          }


	required int32  template_id                  = 154467;
	optional string symbol                       = 110100;
	optional string exchange                     = 110101;

	optional uint32 presence_bits                = 149138;
	optional uint32 clear_bits                   = 154571;
	optional bool   is_snapshot                  = 110121;

	optional double close_price                  = 100021;
	optional string close_date                   = 100079;

	optional double adjusted_close_price         = 154124;

	optional double settlement_price             = 100070;
	optional string settlement_date              = 154132;
	optional string settlement_price_type        = 154637;

	optional double projected_settlement_price   = 155005;
  
	optional int32  ssboe                        = 150100;
	optional int32  usecs                        = 150101;
	}
