
package rti;

message TickBar
	{                                     
	// PB_OFFSET = 100000, is the offset added for each MNM field id

	enum BarType {
	              TICK_BAR     =  1;
		      RANGE_BAR    =  2;
		      VOLUME_BAR   =  3;
	             }

        enum BarSubType {
	                 REGULAR  = 1;
			 CUSTOM   = 2;
	                }

	required int32      template_id                  = 154467;      // PB_OFFSET + MNM_TEMPLATE_ID

	optional string     symbol                       = 110100;      // PB_OFFSET + MNM_SYMBOL
	optional string     exchange                     = 110101;      // PB_OFFSET + MNM_EXCHANGE

	optional BarType    type                         = 119200;      // PB_OFFSET + MNM_DATA_BAR_TYPE
	optional BarSubType sub_type                     = 119208;      // PB_OFFSET + MNM_DATA_BAR_SUB_TYPE
	optional string     type_specifier               = 148162;      // PB_OFFSET + MNM_CATEGORY_SPECIFIC_INFO
	optional uint64     num_trades                   = 119204;      // PB_OFFSET + MNM_DATA_BAR_NUM_TRADES
	optional uint64     volume                       = 119205;      // PB_OFFSET + MNM_DATA_BAR_TRADE_VOLUME
	optional uint64     bid_volume                   = 119213;      // PB_OFFSET + MNM_DATA_BAR_BID_VOLUME
	optional uint64     ask_volume                   = 119214;      // PB_OFFSET + MNM_DATA_BAR_ASK_VOLUME
	optional double     open_price                   = 100019;      // PB_OFFSET + MNM_OPEN_PRICE
	optional double     close_price                  = 100021;      // PB_OFFSET + MNM_CLOSE_TRADE_PRICE
	optional double     high_price                   = 100012;      // PB_OFFSET + MNM_HIGH_PRICE
	optional double     low_price                    = 100013;      // PB_OFFSET + MNM_LOW_PRICE 
	optional int32      custom_session_open_ssm      = 119209;      // PB_OFFSET + MNM_CUSTOM_SESSION_OPEN_SSM	
	repeated int32      data_bar_ssboe               = 119202;      // PB_OFFSET + MNM_DATA_BAR_SSBOE
	repeated int32	    data_bar_usecs               = 119203;	// PB_OFFSTE + MNM_DATA_BAR_USECS
	}
