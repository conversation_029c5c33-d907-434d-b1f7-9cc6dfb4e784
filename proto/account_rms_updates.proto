
package rti;

message AccountRmsUpdates
	{
	enum UpdateBits {
	                 AUTO_LIQ_THRESHOLD_CURRENT_VALUE  =  1;
	                }

	required int32    template_id                           = 154467;
	optional int32    update_bits                           = 154211;

	optional string   fcm_id                                = 154013;
	optional string   ib_id                                 = 154014;
	optional string   account_id                            = 154008;
	
	optional string   auto_liq_threshold_current_value      = 131040;
	optional string   auto_liq_peak_account_balance         = 131049;
	optional string   auto_liq_peak_account_balance_ssboe   = 131050;
	}
