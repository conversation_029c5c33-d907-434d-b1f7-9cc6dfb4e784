#!/usr/bin/env python3
"""
Sample Generated Multi-Threaded Database Query Script
Generated by Enhanced Code Generation System
Auto-generated on: 2024-12-25 12:00:00

Features:
- ThreadPoolExecutor for parallel processing
- Connection pooling with load balancing  
- Progress tracking and performance monitoring
- Memory-efficient chunked processing
- Comprehensive error handling and retry logic
"""

import os
import sys
import time
import json
import threading
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from queue import Queue, Empty
from typing import List, Dict, Any, Optional, Tuple
import mysql.connector
from mysql.connector import Error
from dataclasses import dataclass
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'query_symbols_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logger.warning("python-dotenv not available. Install with: pip install python-dotenv")

# Add src directory to path for local imports
script_dir = Path(__file__).parent
src_dir = script_dir / 'src'
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

try:
    from database.database_manager import DatabaseManager
except ImportError:
    logger.warning("DatabaseManager not available. Database operations may not work.")

@dataclass
class QueryTask:
    """Represents a database query task."""
    query: str
    params: tuple
    chunk_id: int
    offset: int
    limit: int

@dataclass
class QueryResult:
    """Represents query execution result."""
    chunk_id: int
    data: List[Dict[str, Any]]
    execution_time: float
    error: Optional[str] = None

class ProgressTracker:
    """Thread-safe progress tracking."""
    
    def __init__(self, total_chunks: int):
        self.total_chunks = total_chunks
        self.completed_chunks = 0
        self.failed_chunks = 0
        self.start_time = time.time()
        self.lock = threading.Lock()
        
    def update(self, success: bool = True):
        with self.lock:
            if success:
                self.completed_chunks += 1
            else:
                self.failed_chunks += 1
                
    def get_progress(self) -> Dict[str, Any]:
        with self.lock:
            elapsed = time.time() - self.start_time
            progress_pct = (self.completed_chunks + self.failed_chunks) / self.total_chunks * 100
            
            return {
                'completed': self.completed_chunks,
                'failed': self.failed_chunks,
                'total': self.total_chunks,
                'progress_pct': progress_pct,
                'elapsed_time': elapsed,
                'estimated_remaining': elapsed / max(1, progress_pct / 100) - elapsed if progress_pct > 0 else 0
            }

class ConnectionPool:
    """Thread-safe database connection pool."""
    
    def __init__(self, config: Dict[str, Any], pool_size: int = 10):
        self.config = config
        self.pool_size = pool_size
        self.connections = Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self._initialize_pool()
        
    def _initialize_pool(self):
        """Initialize connection pool."""
        for _ in range(self.pool_size):
            try:
                conn = mysql.connector.connect(**self.config)
                self.connections.put(conn)
            except Error as e:
                logger.error(f"Failed to create connection: {e}")
                
    def get_connection(self, timeout: float = 30.0):
        """Get connection from pool."""
        try:
            return self.connections.get(timeout=timeout)
        except Empty:
            logger.warning("Connection pool exhausted, creating new connection")
            return mysql.connector.connect(**self.config)
            
    def return_connection(self, conn):
        """Return connection to pool."""
        if conn and conn.is_connected():
            try:
                self.connections.put_nowait(conn)
            except:
                # Pool is full, close connection
                conn.close()
        elif conn:
            conn.close()

# Database configuration from GUI settings
database_config = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'rithmic_api')
}

def get_database_manager():
    """Create and return database manager instance."""
    try:
        return DatabaseManager(database_config)
    except Exception as e:
        print(f"Error creating database manager: {e}")
        return None

class ThreadedQueryExecutor:
    """ThreadPoolExecutor-based query executor."""
    
    def __init__(self, connection_pool: ConnectionPool, max_workers: int = 4):
        self.connection_pool = connection_pool
        self.max_workers = max_workers
        
    def execute_query_chunk(self, task: QueryTask) -> QueryResult:
        """Execute a single query chunk."""
        start_time = time.time()
        conn = None
        
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor(dictionary=True, buffered=True)
            
            cursor.execute(task.query, task.params)
            data = cursor.fetchall()
            cursor.close()
            
            execution_time = time.time() - start_time
            logger.debug(f"Chunk {task.chunk_id} completed: {len(data)} rows in {execution_time:.2f}s")
            
            return QueryResult(
                chunk_id=task.chunk_id,
                data=data,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Error executing chunk {task.chunk_id}: {e}")
            return QueryResult(
                chunk_id=task.chunk_id,
                data=[],
                execution_time=time.time() - start_time,
                error=str(e)
            )
        finally:
            if conn:
                self.connection_pool.return_connection(conn)
                
    def execute_parallel_query(self, table_name: str, total_rows: int, 
                             chunk_size: int = 10000, where_clause: str = "") -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """Execute parallel chunked query."""
        
        # Calculate chunks
        num_chunks = (total_rows + chunk_size - 1) // chunk_size
        logger.info(f"Executing parallel query on {table_name}: {total_rows} rows in {num_chunks} chunks")
        
        # Create query tasks
        tasks = []
        base_query = f"SELECT * FROM `{table_name}`"
        if where_clause:
            base_query += f" WHERE {where_clause}"
            
        for i in range(num_chunks):
            offset = i * chunk_size
            query = f"{base_query} LIMIT {chunk_size} OFFSET {offset}"
            task = QueryTask(
                query=query,
                params=(),
                chunk_id=i,
                offset=offset,
                limit=chunk_size
            )
            tasks.append(task)
            
        # Execute in parallel
        progress_tracker = ProgressTracker(num_chunks)
        all_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_task = {executor.submit(self.execute_query_chunk, task): task for task in tasks}
            
            # Process completed tasks
            for future in as_completed(future_to_task):
                result = future.result()
                
                if result.error is None:
                    all_results.extend(result.data)
                    progress_tracker.update(success=True)
                    logger.info(f"Chunk {result.chunk_id} completed successfully")
                else:
                    progress_tracker.update(success=False)
                    logger.error(f"Chunk {result.chunk_id} failed: {result.error}")
                    
                # Log progress
                progress = progress_tracker.get_progress()
                logger.info(f"Progress: {progress['progress_pct']:.1f}% "
                          f"({progress['completed']}/{progress['total']} chunks, "
                          f"{progress['failed']} failed)")
        
        # Final statistics
        final_progress = progress_tracker.get_progress()
        stats = {
            'total_rows_retrieved': len(all_results),
            'total_chunks': num_chunks,
            'successful_chunks': final_progress['completed'],
            'failed_chunks': final_progress['failed'],
            'total_execution_time': final_progress['elapsed_time'],
            'rows_per_second': len(all_results) / max(1, final_progress['elapsed_time'])
        }
        
        return all_results, stats

def get_table_row_count(table_name: str, db_config: Dict[str, Any], where_clause: str = "") -> int:
    """Get total row count for table."""
    conn = None
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        count_query = f"SELECT COUNT(*) FROM `{table_name}`"
        if where_clause:
            count_query += f" WHERE {where_clause}"
            
        cursor.execute(count_query)
        count = cursor.fetchone()[0]
        cursor.close()
        return count
        
    except Exception as e:
        logger.error(f"Error getting row count: {e}")
        return 0
    finally:
        if conn and conn.is_connected():
            conn.close()

def export_results_to_csv(results: List[Dict[str, Any]], filename: str):
    """Export results to CSV file."""
    if not results:
        logger.warning("No results to export")
        return
        
    import csv
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = list(results[0].keys())
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(results)
            
        logger.info(f"Exported {len(results)} rows to {filename}")
        
    except Exception as e:
        logger.error(f"Error exporting to CSV: {e}")

def main():
    """Main multi-threaded query execution function."""
    logger.info("🚀 Starting Multi-Threaded Database Query")
    logger.info("=" * 60)
    
    # Configuration
    table_name = "symbols"
    chunk_size = 10000
    where_clause = "symbol LIKE 'ES%'"
    max_workers = 4
    export_csv = True
    
    logger.info(f"Table: {table_name}")
    logger.info(f"Chunk size: {chunk_size:,}")
    logger.info(f"Max workers: {max_workers}")
    logger.info(f"Threading strategy: ThreadPoolExecutor")
    if where_clause:
        logger.info(f"Filter: {where_clause}")
    
    # Initialize database manager
    db_manager = get_database_manager()
    if not db_manager:
        logger.error("❌ Failed to initialize database manager")
        return False
        
    # Test database connection
    if not db_manager.test_connection():
        logger.error("❌ Database connection test failed")
        return False
        
    logger.info("✅ Database connection successful")
    
    try:
        # Get total row count
        logger.info("📊 Getting table row count...")
        total_rows = get_table_row_count(table_name, database_config, where_clause)
        logger.info(f"Total rows to process: {total_rows:,}")
        
        if total_rows == 0:
            logger.warning("No rows found to process")
            return True
            
        # Execute parallel query
        # Create connection pool and executor
        connection_pool = ConnectionPool(database_config, pool_size=max_workers)
        executor = ThreadedQueryExecutor(connection_pool, max_workers)
        
        # Execute parallel query
        results, stats = executor.execute_parallel_query(
            table_name=table_name,
            total_rows=total_rows,
            chunk_size=chunk_size,
            where_clause=where_clause
        )
        
        # Display results
        logger.info("\n" + "=" * 60)
        logger.info("📊 EXECUTION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total rows retrieved: {stats['total_rows_retrieved']:,}")
        logger.info(f"Total chunks processed: {stats['total_chunks']}")
        logger.info(f"Successful chunks: {stats['successful_chunks']}")
        logger.info(f"Failed chunks: {stats['failed_chunks']}")
        logger.info(f"Total execution time: {stats['total_execution_time']:.2f} seconds")
        logger.info(f"Processing rate: {stats['rows_per_second']:,.0f} rows/second")
        
        # Export results if requested
        if export_csv and results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"{table_name}_{timestamp}.csv"
            export_results_to_csv(results, csv_filename)
            
        logger.info("✅ Multi-threaded query completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during execution: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)