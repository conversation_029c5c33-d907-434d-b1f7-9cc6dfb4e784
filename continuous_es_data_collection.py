#!/usr/bin/env python3
"""
Continuous ES Futures Market Data Collection

Production deployment script for 24/7 ES futures market data collection.
Automatically restarts on failures and provides comprehensive monitoring.
"""

import subprocess
import time
import signal
import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta
import json
import os

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('continuous_es_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ContinuousESDataCollector:
    """Manages continuous ES futures market data collection with auto-restart"""
    
    def __init__(self):
        self.running = False
        self.level1_process = None
        self.start_time = datetime.now()
        self.restart_count = 0
        self.max_restarts = 100  # Allow up to 100 restarts per day
        self.restart_delay = 30  # Wait 30 seconds between restarts
        self.collection_duration = 3600  # Collect for 1 hour per session
        
        # Statistics
        self.stats = {
            'sessions_completed': 0,
            'total_uptime': timedelta(0),
            'last_restart': None,
            'data_records_collected': 0
        }
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"🛑 Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)
    
    def start_level1_collection(self):
        """Start Level 1 market data collection subprocess"""
        try:
            logger.info("🚀 Starting Level 1 market data collection...")
            
            # Run the Level 1 collection script
            cmd = [
                'python3', '-m', 'src.scripts.subscribe_level1_data', 
                str(self.collection_duration)
            ]
            
            self.level1_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=Path(__file__).parent
            )
            
            logger.info(f"✅ Level 1 collection started with PID: {self.level1_process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start Level 1 collection: {e}")
            return False
    
    def monitor_collection_process(self):
        """Monitor the collection process and handle completion/errors"""
        if not self.level1_process:
            return False
        
        # Check if process is still running
        poll_result = self.level1_process.poll()
        
        if poll_result is None:
            # Process is still running
            return True
        else:
            # Process has completed
            stdout, stderr = self.level1_process.communicate()
            
            if poll_result == 0:
                logger.info("✅ Level 1 collection session completed successfully")
                self.stats['sessions_completed'] += 1
            else:
                logger.warning(f"⚠️ Level 1 collection exited with code {poll_result}")
                if stderr:
                    logger.warning(f"Error output: {stderr}")
            
            self.level1_process = None
            return False
    
    def check_database_health(self):
        """Check database health and recent data collection"""
        try:
            # Add project root to path for imports
            project_root = Path(__file__).parent
            sys.path.insert(0, str(project_root))
            
            # Load environment
            env_file = project_root / '.env'
            if env_file.exists():
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
            
            from src.database.database_manager import get_database_manager
            
            db = get_database_manager()
            with db.get_cursor() as (cursor, conn):
                # Check recent data (last 10 minutes)
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM best_bid_offer 
                    WHERE received_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                """)
                recent_bbo = cursor.fetchone()['count']
                
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM last_trades 
                    WHERE received_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                """)
                recent_trades = cursor.fetchone()['count']
                
                # Update statistics
                self.stats['data_records_collected'] = recent_bbo + recent_trades
                
                if recent_bbo > 0 or recent_trades > 0:
                    logger.info(f"💾 Database health: {recent_bbo} BBO + {recent_trades} trades in last 10 min")
                    return True
                else:
                    logger.warning("⚠️ No recent data in database (last 10 minutes)")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Database health check failed: {e}")
            return False
    
    def should_restart(self):
        """Determine if we should restart collection"""
        if self.restart_count >= self.max_restarts:
            logger.error(f"❌ Maximum restart limit reached ({self.max_restarts})")
            return False
        
        # Check if enough time has passed since last restart
        if self.stats['last_restart']:
            time_since_restart = datetime.now() - self.stats['last_restart']
            if time_since_restart.total_seconds() < self.restart_delay:
                logger.info(f"⏳ Waiting {self.restart_delay - time_since_restart.total_seconds():.1f} seconds before next restart")
                return False
        
        return True
    
    def restart_collection(self):
        """Restart the data collection process with retry logic"""
        if not self.should_restart():
            return False
        
        logger.info("🔄 Restarting ES market data collection...")
        
        # Stop current process if running
        if self.level1_process:
            try:
                logger.info("📡 Terminating current collection process...")
                self.level1_process.terminate()
                self.level1_process.wait(timeout=10)
                logger.info("✅ Process terminated successfully")
            except:
                logger.warning("⚠️ Force killing collection process...")
                self.level1_process.kill()
        
        # Wait before restart
        logger.info(f"⏳ Waiting {self.restart_delay} seconds before restart...")
        time.sleep(self.restart_delay)
        
        # Update restart statistics
        self.restart_count += 1
        self.stats['last_restart'] = datetime.now()
        
        # Try to start new collection with retry logic
        max_start_attempts = 3
        for attempt in range(1, max_start_attempts + 1):
            logger.info(f"🚀 Starting collection attempt {attempt}/{max_start_attempts}")
            
            success = self.start_level1_collection()
            if success:
                logger.info(f"✅ Collection started successfully on attempt {attempt}")
                return True
            else:
                if attempt < max_start_attempts:
                    logger.warning(f"⚠️ Start attempt {attempt} failed, retrying in 10 seconds...")
                    time.sleep(10)
                else:
                    logger.error(f"❌ All {max_start_attempts} start attempts failed")
        
        return False
    
    def log_statistics(self):
        """Log current statistics"""
        uptime = datetime.now() - self.start_time
        
        logger.info("📊 ES DATA COLLECTION STATISTICS")
        logger.info(f"  ⏱️ Total Uptime: {uptime}")
        logger.info(f"  🔄 Sessions Completed: {self.stats['sessions_completed']}")
        logger.info(f"  🔄 Restart Count: {self.restart_count}")
        logger.info(f"  💾 Recent Data Records: {self.stats['data_records_collected']}")
        logger.info(f"  🕐 Last Restart: {self.stats['last_restart']}")
    
    def run(self):
        """Main continuous collection loop"""
        logger.info("🎯 STARTING CONTINUOUS ES FUTURES DATA COLLECTION")
        logger.info("=" * 60)
        logger.info(f"📅 Start Time: {self.start_time}")
        logger.info(f"🎯 Target: ESU5 (ES front month)")
        logger.info(f"📊 Collection Type: Level 1 market data")
        logger.info(f"⏱️ Session Duration: {self.collection_duration} seconds")
        logger.info(f"🔄 Max Restarts: {self.max_restarts}")
        logger.info("=" * 60)
        
        self.setup_signal_handlers()
        self.running = True
        
        # Start initial collection
        if not self.start_level1_collection():
            logger.error("❌ Failed to start initial collection")
            return
        
        statistics_interval = 300  # Log statistics every 5 minutes
        last_stats_time = time.time()
        
        try:
            consecutive_restart_failures = 0
            max_consecutive_failures = 3
            
            while self.running:
                # Monitor current collection process
                if not self.monitor_collection_process():
                    # Process has stopped, restart it
                    restart_success = self.restart_collection()
                    
                    if restart_success:
                        consecutive_restart_failures = 0
                        logger.info("✅ Collection restarted successfully")
                    else:
                        consecutive_restart_failures += 1
                        logger.warning(f"⚠️ Restart failed (failure #{consecutive_restart_failures})")
                        
                        if consecutive_restart_failures >= max_consecutive_failures:
                            logger.error(f"❌ {max_consecutive_failures} consecutive restart failures, stopping...")
                            break
                        else:
                            logger.info(f"⏳ Waiting before next restart attempt ({consecutive_restart_failures}/{max_consecutive_failures} failures)")
                            time.sleep(60)  # Wait 1 minute before trying again
                
                # Check database health periodically
                current_time = time.time()
                if current_time - last_stats_time > statistics_interval:
                    self.check_database_health()
                    self.log_statistics()
                    last_stats_time = current_time
                
                # Wait before next check
                time.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("🛑 Interrupted by user")
        except Exception as e:
            logger.error(f"💥 Unexpected error: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop continuous collection"""
        logger.info("🔄 Stopping continuous ES data collection...")
        
        self.running = False
        
        # Stop collection process
        if self.level1_process:
            try:
                logger.info("📡 Terminating Level 1 collection process...")
                self.level1_process.terminate()
                self.level1_process.wait(timeout=10)
                logger.info("✅ Level 1 collection stopped")
            except:
                logger.warning("⚠️ Force killing Level 1 collection process...")
                self.level1_process.kill()
        
        # Final statistics
        total_uptime = datetime.now() - self.start_time
        logger.info("📊 FINAL COLLECTION STATISTICS")
        logger.info(f"  ⏱️ Total Runtime: {total_uptime}")
        logger.info(f"  🔄 Sessions Completed: {self.stats['sessions_completed']}")
        logger.info(f"  🔄 Total Restarts: {self.restart_count}")
        
        logger.info("✅ Continuous ES data collection stopped")

def main():
    """Main function"""
    collector = ContinuousESDataCollector()
    
    try:
        collector.run()
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("🏁 ES Continuous Data Collection Complete")

if __name__ == "__main__":
    main()